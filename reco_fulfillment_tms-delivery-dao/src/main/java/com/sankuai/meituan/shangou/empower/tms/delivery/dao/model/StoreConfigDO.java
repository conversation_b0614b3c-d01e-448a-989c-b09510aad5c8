package com.sankuai.meituan.shangou.empower.tms.delivery.dao.model;

import java.time.LocalDateTime;

public class StoreConfigDO {
    private Long id;

    private Long tenantId;

    private Long storeId;

    private String address;

    private String contactPhone;

    private Integer deliveryStrategy;

    private String deliveryStrategyConfig;

    private Integer deliveryLaunchPoint;

    private Integer deliveryLaunchDelayMinutes;

    private Integer bookingOrderDeliveryLaunchPoint;

    private Integer bookingOrderDeliveryLaunchMinutes;

    private String orderPlatformDeliveryConfig;

    private Integer enabled;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer isAutoLaunch;

    private Integer deliveryLaunchRule;

    private Integer openAggrPlatform;

    private Integer cityCode;

    private Integer channelType;

    private Integer lastPlatformType;

    private Integer isShowItemNumber;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    public Integer getDeliveryStrategy() {
        return deliveryStrategy;
    }

    public void setDeliveryStrategy(Integer deliveryStrategy) {
        this.deliveryStrategy = deliveryStrategy;
    }

    public String getDeliveryStrategyConfig() {
        return deliveryStrategyConfig;
    }

    public void setDeliveryStrategyConfig(String deliveryStrategyConfig) {
        this.deliveryStrategyConfig = deliveryStrategyConfig == null ? null : deliveryStrategyConfig.trim();
    }

    public Integer getDeliveryLaunchPoint() {
        return deliveryLaunchPoint;
    }

    public void setDeliveryLaunchPoint(Integer deliveryLaunchPoint) {
        this.deliveryLaunchPoint = deliveryLaunchPoint;
    }

    public Integer getDeliveryLaunchDelayMinutes() {
        return deliveryLaunchDelayMinutes;
    }

    public void setDeliveryLaunchDelayMinutes(Integer deliveryLaunchDelayMinutes) {
        this.deliveryLaunchDelayMinutes = deliveryLaunchDelayMinutes;
    }

    public Integer getBookingOrderDeliveryLaunchPoint() {
        return bookingOrderDeliveryLaunchPoint;
    }

    public void setBookingOrderDeliveryLaunchPoint(Integer bookingOrderDeliveryLaunchPoint) {
        this.bookingOrderDeliveryLaunchPoint = bookingOrderDeliveryLaunchPoint;
    }

    public Integer getBookingOrderDeliveryLaunchMinutes() {
        return bookingOrderDeliveryLaunchMinutes;
    }

    public void setBookingOrderDeliveryLaunchMinutes(Integer bookingOrderDeliveryLaunchMinutes) {
        this.bookingOrderDeliveryLaunchMinutes = bookingOrderDeliveryLaunchMinutes;
    }

    public String getOrderPlatformDeliveryConfig() {
        return orderPlatformDeliveryConfig;
    }

    public void setOrderPlatformDeliveryConfig(String orderPlatformDeliveryConfig) {
        this.orderPlatformDeliveryConfig = orderPlatformDeliveryConfig == null ? null : orderPlatformDeliveryConfig.trim();
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsAutoLaunch() {
        return isAutoLaunch;
    }

    public void setIsAutoLaunch(Integer isAutoLaunch) {
        this.isAutoLaunch = isAutoLaunch;
    }

    public Integer getDeliveryLaunchRule() {
        return deliveryLaunchRule;
    }

    public void setDeliveryLaunchRule(Integer deliveryLaunchRule) {
        this.deliveryLaunchRule = deliveryLaunchRule;
    }

    public Integer getOpenAggrPlatform() {
        return openAggrPlatform;
    }

    public void setOpenAggrPlatform(Integer openAggrPlatform) {
        this.openAggrPlatform = openAggrPlatform;
    }

    public Integer getCityCode() {
        return cityCode;
    }

    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Integer getLastPlatformType() {
        return lastPlatformType;
    }

    public void setLastPlatformType(Integer lastPlatformType) {
        this.lastPlatformType = lastPlatformType;
    }

    public Integer getIsShowItemNumber() {
        return isShowItemNumber;
    }

    public void setIsShowItemNumber(Integer isShowItemNumber) {
        this.isShowItemNumber = isShowItemNumber;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StoreConfigDO other = (StoreConfigDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()))
            && (this.getStoreId() == null ? other.getStoreId() == null : this.getStoreId().equals(other.getStoreId()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getContactPhone() == null ? other.getContactPhone() == null : this.getContactPhone().equals(other.getContactPhone()))
            && (this.getDeliveryStrategy() == null ? other.getDeliveryStrategy() == null : this.getDeliveryStrategy().equals(other.getDeliveryStrategy()))
            && (this.getDeliveryStrategyConfig() == null ? other.getDeliveryStrategyConfig() == null : this.getDeliveryStrategyConfig().equals(other.getDeliveryStrategyConfig()))
            && (this.getDeliveryLaunchPoint() == null ? other.getDeliveryLaunchPoint() == null : this.getDeliveryLaunchPoint().equals(other.getDeliveryLaunchPoint()))
            && (this.getDeliveryLaunchDelayMinutes() == null ? other.getDeliveryLaunchDelayMinutes() == null : this.getDeliveryLaunchDelayMinutes().equals(other.getDeliveryLaunchDelayMinutes()))
            && (this.getBookingOrderDeliveryLaunchPoint() == null ? other.getBookingOrderDeliveryLaunchPoint() == null : this.getBookingOrderDeliveryLaunchPoint().equals(other.getBookingOrderDeliveryLaunchPoint()))
            && (this.getBookingOrderDeliveryLaunchMinutes() == null ? other.getBookingOrderDeliveryLaunchMinutes() == null : this.getBookingOrderDeliveryLaunchMinutes().equals(other.getBookingOrderDeliveryLaunchMinutes()))
            && (this.getOrderPlatformDeliveryConfig() == null ? other.getOrderPlatformDeliveryConfig() == null : this.getOrderPlatformDeliveryConfig().equals(other.getOrderPlatformDeliveryConfig()))
            && (this.getEnabled() == null ? other.getEnabled() == null : this.getEnabled().equals(other.getEnabled()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsAutoLaunch() == null ? other.getIsAutoLaunch() == null : this.getIsAutoLaunch().equals(other.getIsAutoLaunch()))
            && (this.getDeliveryLaunchRule() == null ? other.getDeliveryLaunchRule() == null : this.getDeliveryLaunchRule().equals(other.getDeliveryLaunchRule()))
            && (this.getOpenAggrPlatform() == null ? other.getOpenAggrPlatform() == null : this.getOpenAggrPlatform().equals(other.getOpenAggrPlatform()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getChannelType() == null ? other.getChannelType() == null : this.getChannelType().equals(other.getChannelType()))
            && (this.getLastPlatformType() == null ? other.getLastPlatformType() == null : this.getLastPlatformType().equals(other.getLastPlatformType()))
            && (this.getIsShowItemNumber() == null ? other.getIsShowItemNumber() == null : this.getIsShowItemNumber().equals(other.getIsShowItemNumber()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        result = prime * result + ((getStoreId() == null) ? 0 : getStoreId().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getContactPhone() == null) ? 0 : getContactPhone().hashCode());
        result = prime * result + ((getDeliveryStrategy() == null) ? 0 : getDeliveryStrategy().hashCode());
        result = prime * result + ((getDeliveryStrategyConfig() == null) ? 0 : getDeliveryStrategyConfig().hashCode());
        result = prime * result + ((getDeliveryLaunchPoint() == null) ? 0 : getDeliveryLaunchPoint().hashCode());
        result = prime * result + ((getDeliveryLaunchDelayMinutes() == null) ? 0 : getDeliveryLaunchDelayMinutes().hashCode());
        result = prime * result + ((getBookingOrderDeliveryLaunchPoint() == null) ? 0 : getBookingOrderDeliveryLaunchPoint().hashCode());
        result = prime * result + ((getBookingOrderDeliveryLaunchMinutes() == null) ? 0 : getBookingOrderDeliveryLaunchMinutes().hashCode());
        result = prime * result + ((getOrderPlatformDeliveryConfig() == null) ? 0 : getOrderPlatformDeliveryConfig().hashCode());
        result = prime * result + ((getEnabled() == null) ? 0 : getEnabled().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsAutoLaunch() == null) ? 0 : getIsAutoLaunch().hashCode());
        result = prime * result + ((getDeliveryLaunchRule() == null) ? 0 : getDeliveryLaunchRule().hashCode());
        result = prime * result + ((getOpenAggrPlatform() == null) ? 0 : getOpenAggrPlatform().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getChannelType() == null) ? 0 : getChannelType().hashCode());
        result = prime * result + ((getLastPlatformType() == null) ? 0 : getLastPlatformType().hashCode());
        result = prime * result + ((getIsShowItemNumber() == null) ? 0 : getIsShowItemNumber().hashCode());
        return result;
    }
}