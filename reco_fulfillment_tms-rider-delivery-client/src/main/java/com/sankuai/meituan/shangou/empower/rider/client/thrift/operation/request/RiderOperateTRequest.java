package com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/14
 */
@TypeDoc(
        description = "骑手配送操作请求体",
        authors = {
                "qianteng"
        }
)
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
@Data
public class RiderOperateTRequest {


    @FieldDoc(
            description = "配送运单号",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public Long deliveryOrderId;


    @FieldDoc(
            description = "操作人id",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(2)
    public Long operatorId;

    @FieldDoc(
            description = "操作人名称",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(3)
    public String operatorName;

    @FieldDoc(
            description = "操作人号码",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(4)
    public String operatorPhone;

    @FieldDoc(
            description = "骑手经度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(5)
    private String longitude;

    @FieldDoc(
            description = "骑手纬度",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(6)
    private String latitude;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(7)
    private Long storeId;

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.OPTIONAL
    )
    @ThriftField(8)
    private Long tenantId;

    public String validate() {
        if (operatorId == null || operatorId <= 0L) {
            return "操作人不合法";
        }
        if (StringUtils.isBlank(operatorPhone)) {
            return "操作人手机号不合法";
        }

        return null;
    }
}
