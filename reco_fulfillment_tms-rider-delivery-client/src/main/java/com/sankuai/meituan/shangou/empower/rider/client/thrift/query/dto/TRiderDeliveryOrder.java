package com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 骑手运单信息.
 *
 * <AUTHOR>
 * @since 2021/6/14 18:07
 */
@TypeDoc(
        description = "骑手运单信息",
        authors = {
                "liyang176"
        }
)
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
public class TRiderDeliveryOrder {

    @FieldDoc(
            description = "运单 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long deliveryOrderId;

    @FieldDoc(
            description = "租户 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "赋能门店 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "赋能订单 ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long orderId;

    @FieldDoc(
            description = "渠道订单 ID",
            requiredness = Requiredness.REQUIRED
    )
    private String channelOrderId;

    @FieldDoc(
            description = "订单业务类型Code，参照 com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum",
            requiredness = Requiredness.REQUIRED
    )
    private Integer orderBizTypeCode;

    @FieldDoc(
            description = "预计送达时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Long estimatedDeliveryTime;

    @FieldDoc(
            description = "预计送达结束时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Long estimatedDeliveryEndTime;

    @FieldDoc(
            description = "运单状态",
            requiredness = Requiredness.REQUIRED
    )
    private Integer deliveryStatus;

    @FieldDoc(
            description = "收货人信息",
            requiredness = Requiredness.REQUIRED
    )
    private TReceiver receiver;

    @FieldDoc(
            description = "运单送达时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Long deliveryDoneTime;

    @FieldDoc(
            description = "转单前的骑手",
            requiredness = Requiredness.OPTIONAL
    )
    @Getter
    @Setter
    @ThriftField(11)
    public TStaffRider changeFromRider;


    @FieldDoc(
            description = "运单状态是否被锁定：0-未锁定；1-锁定",
            example = {}
    )
    public Integer statusLocked;


    @FieldDoc(
            description = "运单状态是否可以被锁定：0-不可以；1-可以",
            example = {}
    )
    public Integer canStatusBeLocked;

    @FieldDoc(
            description = "配送距离",
            example = {}
    )
    private Long distance;

    @FieldDoc(
            description = "是否可以上传送达照片",
            example = {}
    )
    private Boolean couldPostDeliveryProofPhoto;


    @FieldDoc(
            description = "当前骑手信息",
            example = {}
    )
    private TStaffRider currentRider;

    @FieldDoc(
            description = "签收信息",
            example = {}
    )
    private Integer signType;

    @FieldDoc(
            description = "代收点",
            example = {}
    )
    private String signPosition;

    @FieldDoc(
            description = "是否是预订单",
            example = {}
    )
    private Boolean isReserved;

    @FieldDoc(
            description = "是否是一元单",
            example = {}
    )
    private Boolean isOneYuanOrder;

    @FieldDoc(
            description = "是否是一元单",
            example = {}
    )
    private Long assessDeliveryTime;

    @FieldDoc(
            description = "定价路线信息",
            example = {}
    )
    private TPricingRouteInfo pricingRouteInfo;

    @FieldDoc(
            description = "上一次事件的时间",
            example = {}
    )
    private Long lastEventTime;

    @FieldDoc(
            description = "创建时间",
            example = {}
    )
    private Long createTime;

    @FieldDoc(
            description = "拣配分离标记",
            example = {}
    )
    private Boolean pickDeliverySplitTag;

    @FieldDoc(
            description = "是否为三方运单，兼容三方运单",
            example = {}
    )
    private Boolean isThirdDelivery;

    @FieldDoc(
            description = "是否为三方异常单，兼容三方运单",
            example = {}
    )
    private Boolean isThirdException;

    @FieldDoc(
            description = "是否为三方异常单，兼容三方运单",
            example = {}
    )
    private Integer originDeliveryStatus;

    @FieldDoc(
            description = "是否为三方异常单，兼容三方运单",
            example = {}
    )
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "是否为三方异常单，兼容三方运单",
            example = {}
    )
    private Integer deliveryPlatformCode;

    @FieldDoc(
            description = "是否为三方异常单，兼容三方运单",
            example = {}
    )
    private String deliveryPlatformDesc;

    @FieldDoc(
            description = "奖励类型",
            example = {}
    )
    private Integer rewardType;

    @ThriftField(1)
    public Long getDeliveryOrderId() {
        return deliveryOrderId;
    }

    @ThriftField
    public void setDeliveryOrderId(Long deliveryOrderId) {
        this.deliveryOrderId = deliveryOrderId;
    }

    @ThriftField(2)
    public Long getTenantId() {
        return tenantId;
    }

    @ThriftField
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @ThriftField(3)
    public Long getStoreId() {
        return storeId;
    }

    @ThriftField
    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    @ThriftField(4)
    public Long getOrderId() {
        return orderId;
    }

    @ThriftField
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @ThriftField(5)
    public String getChannelOrderId() {
        return channelOrderId;
    }

    @ThriftField
    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    @ThriftField(6)
    public Integer getOrderBizTypeCode() {
        return orderBizTypeCode;
    }

    @ThriftField
    public void setOrderBizTypeCode(Integer orderBizTypeCode) {
        this.orderBizTypeCode = orderBizTypeCode;
    }

    @ThriftField(7)
    public Long getEstimatedDeliveryTime() {
        return estimatedDeliveryTime;
    }

    @ThriftField
    public void setEstimatedDeliveryTime(Long estimatedDeliveryTime) {
        this.estimatedDeliveryTime = estimatedDeliveryTime;
    }

    @ThriftField(8)
    public Long getEstimatedDeliveryEndTime() {
        return estimatedDeliveryEndTime;
    }

    @ThriftField
    public void setEstimatedDeliveryEndTime(Long estimatedDeliveryEndTime) {
        this.estimatedDeliveryEndTime = estimatedDeliveryEndTime;
    }

    @ThriftField(9)
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    @ThriftField
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @ThriftField(10)
    public TReceiver getReceiver() {
        return receiver;
    }

    @ThriftField
    public void setReceiver(TReceiver receiver) {
        this.receiver = receiver;
    }

    @ThriftField
    public void setCanStatusBeLocked(Integer canStatusBeLocked) {
        this.canStatusBeLocked = canStatusBeLocked;
    }

    @ThriftField(12)
    public Long getDeliveryDoneTime() {
        return deliveryDoneTime;
    }

    @ThriftField
    public void setDeliveryDoneTime(Long deliveryDoneTime) {
        this.deliveryDoneTime = deliveryDoneTime;
    }

    @ThriftField(13)
    public Integer getStatusLocked() {
        return statusLocked;
    }

    @ThriftField
    public void setStatusLocked(Integer statusLocked) {
        this.statusLocked = statusLocked;
    }

    @ThriftField(14)
    public Integer getCanStatusBeLocked() {
        return canStatusBeLocked;
    }

    @ThriftField
    public void setDistance(Long distance) {
        this.distance = distance;
    }

    @ThriftField(15)
    public Long getDistance(){
        return distance;
    }


    @ThriftField(16)
    public Boolean getCouldPostDeliveryProofPhoto() {
        return this.couldPostDeliveryProofPhoto;
    }

    @ThriftField
    public void setCouldPostDeliveryProofPhoto(Boolean couldPostDeliveryProofPhoto) {
        this.couldPostDeliveryProofPhoto = couldPostDeliveryProofPhoto;
    }


    @ThriftField(17)
    public TStaffRider getCurrentRider() {
        return this.currentRider;
    }

    @ThriftField
    public void setCurrentRider(TStaffRider currentRider) {
        this.currentRider = currentRider;
    }


    @ThriftField(18)
    public Integer getSignType() {
        return this.signType;
    }

    @ThriftField
    public void setSignType(Integer signType) {
        this.signType = signType;
    }


    @ThriftField(19)
    public String getSignPosition() {
        return this.signPosition;
    }

    @ThriftField
    public void setSignPosition(String signPosition) {
        this.signPosition = signPosition;
    }


    @ThriftField(20)
    public Boolean getIsReserved() {
        return this.isReserved;
    }

    @ThriftField
    public void setIsReserved(Boolean isReserved) {
        this.isReserved = isReserved;
    }


    @ThriftField(21)
    public Boolean getIsOneYuanOrder() {
        return this.isOneYuanOrder;
    }

    @ThriftField
    public void setIsOneYuanOrder(Boolean isOneYuanOrder) {
        this.isOneYuanOrder = isOneYuanOrder;
    }

    @ThriftField(22)
    public Long getAssessDeliveryTime() {
        return assessDeliveryTime;
    }

    @ThriftField
    public void setAssessDeliveryTime(Long assessDeliveryTime) {
        this.assessDeliveryTime = assessDeliveryTime;
    }

    @ThriftField(23)
    public TPricingRouteInfo getPricingRouteInfo() {
        return this.pricingRouteInfo;
    }

    @ThriftField
    public void setPricingRouteInfo(TPricingRouteInfo pricingRouteInfo) {
        this.pricingRouteInfo = pricingRouteInfo;
    }

    @ThriftField(24)
    public Long getLastEventTime() {
        return this.lastEventTime;
    }

    @ThriftField
    public void setLastEventTime(Long lastEventTime) {
        this.lastEventTime = lastEventTime;
    }

    @ThriftField(25)
    public Long getCreateTime() {
        return createTime;
    }
    @ThriftField
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }


    @ThriftField(26)
    public Boolean getPickDeliverySplitTag() {
        return this.pickDeliverySplitTag;
    }

    @ThriftField
    public void setPickDeliverySplitTag(Boolean pickDeliverySplitTag) {
        this.pickDeliverySplitTag = pickDeliverySplitTag;
    }

    @ThriftField(27)
    public Boolean getThirdDelivery() {
        return isThirdDelivery;
    }

    @ThriftField
    public void setThirdDelivery(Boolean thirdDelivery) {
        isThirdDelivery = thirdDelivery;
    }

    @ThriftField(28)
    public Boolean getThirdException() {
        return isThirdException;
    }

    @ThriftField
    public void setThirdException(Boolean thirdException) {
        isThirdException = thirdException;
    }

    @ThriftField(29)
    public Integer getOriginDeliveryStatus() {
        return originDeliveryStatus;
    }

    @ThriftField
    public void setOriginDeliveryStatus(Integer originDeliveryStatus) {
        this.originDeliveryStatus = originDeliveryStatus;
    }

    @ThriftField(30)
    public Integer getDeliveryChannelId() {
        return deliveryChannelId;
    }

    @ThriftField
    public void setDeliveryChannelId(Integer deliveryChannelId) {
        this.deliveryChannelId = deliveryChannelId;
    }

    @ThriftField(31)
    public Integer getDeliveryPlatformCode() {
        return deliveryPlatformCode;
    }

    @ThriftField
    public void setDeliveryPlatformCode(Integer deliveryPlatformCode) {
        this.deliveryPlatformCode = deliveryPlatformCode;
    }

    @ThriftField(32)
    public String getDeliveryPlatformDesc() {
        return deliveryPlatformDesc;
    }

    @ThriftField
    public void setDeliveryPlatformDesc(String deliveryPlatformDesc) {
        this.deliveryPlatformDesc = deliveryPlatformDesc;
    }

    @ThriftField(33)
    public Integer getRewardType() {
        return rewardType;
    }

    @ThriftField
    public void setRewardType(Integer rewardType) {
        this.rewardType = rewardType;
    }
}
