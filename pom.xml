<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.meituan.xframe</groupId>
        <artifactId>xframe-starter-parent</artifactId>
        <version>0.0.26</version>
    </parent>

    <groupId>com.sankuai.shopmgmt.pieapi</groupId>
    <artifactId>reco_shopmgmt_pieapi</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>war</packaging>


    <properties>
        <start-class>com.sankuai.meituan.reco.shopmgmt.pieapi.StartApp</start-class>
        <xstream.version>1.4.18</xstream.version>
        <empower.idl.version>4.2.4.48</empower.idl.version>
        <empower.client.version>2.0.0</empower.client.version>
        <order-mng.client.version>1.5.40</order-mng.client.version>
        <order.platform.common.version>1.7.39</order.platform.common.version>
        <ocms.service-client.version>3.26.10</ocms.service-client.version>
        <order.biz-client.version>2.1.64</order.biz-client.version>
        <pick.select-client.version>2.0.19</pick.select-client.version>
        <pick.select-idl.version>6.1.78</pick.select-idl.version>
        <waimai.order.client.version>4.179.0</waimai.order.client.version>
        <pick.select-login.version>1.9.3</pick.select-login.version>
        <message.client.version>1.3.0</message.client.version>
        <tenant.client.version>3.3.34</tenant.client.version>
        <task.mng.version>1.0.0</task.mng.version>
        <fastjson-version>1.2.83_noneautotype</fastjson-version>
        <gson-version>2.8.6</gson-version>
        <price.management.client.version>1.6.26</price.management.client.version>
        <intelligent-sdk.version>1.0.1${type}</intelligent-sdk.version>
        <zebra.version>3.3.2</zebra.version>
        <productbiz.client.version>1.15.45</productbiz.client.version>
        <kms-java-client.version>0.13.1</kms-java-client.version>
        <ocms.channel.client.version>2.24.17</ocms.channel.client.version>
        <auth.client.version>1.6.15</auth.client.version>
        <sac.client.version>2.2.0</sac.client.version>
        <log4j2.version>2.10.0</log4j2.version>
        <sgdata.version>1.0.3</sgdata.version>
        <saasdata.version>2.0.32</saasdata.version>
        <stock-biz-version>4.8.1</stock-biz-version>
        <kms-pangolin-sdk.version>0.10.0</kms-pangolin-sdk.version>
        <idl-kms.version>1.4.4.1</idl-kms.version>
        <dh-wms.version>8.33.0</dh-wms.version>
        <inf-bom.version>1.4.10.2</inf-bom.version>
        <munich.client.version>1.6.15</munich.client.version>
        <mns-invoker.version>1.15.1</mns-invoker.version>
        <duty.center.version>1.1.0</duty.center.version>
        <dhprocess.center.version>1.17.3</dhprocess.center.version>
    </properties>

    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.49</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.waima</groupId>
                <artifactId>support-api</artifactId>
                <version>1.0.16${version.type}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>0.9.3-mt</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.octo</groupId>
                <artifactId>idl-common</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.inf.navigator</groupId>
                <artifactId>navigator-sdk</artifactId>
                <version>1.5.1</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.octo</groupId>
                <artifactId>mns-invoker</artifactId>
                <version>${mns-invoker.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimai.service.order</groupId>
                <artifactId>waimai_service_order_clientassembly</artifactId>
                <version>${waimai.order.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai.service.order</groupId>
                <artifactId>waimai_c_order_core</artifactId>
                <version>2.73.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>waimai-thrift-tools</artifactId>
                <version>1.4.12.1.30</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-pangolin-sdk</artifactId>
                <version>0.7.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.octo</groupId>
                        <artifactId>mns-invoker</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>idl-kms</artifactId>
                <version>1.4.4.1</version>
            </dependency>

            <dependency>
                <artifactId>fastjson</artifactId>
                <groupId>com.alibaba</groupId>
                <version>${fastjson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                <artifactId>pick-select-service-idl</artifactId>
                <version>${pick.select-idl.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                        <artifactId>fulfill-constants</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower.product.intelligent</groupId>
                <artifactId>intelligent-sdk</artifactId>
                <version>${intelligent-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-java-client</artifactId>
                <version>${kms-java-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>xmd-log4j2</artifactId>
                <version>2.0.4</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>xmd-common-log4j2</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.waimai</groupId>
                <artifactId>waimai_set_router-client</artifactId>
                <version>1.3.18</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.31.Final</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-pangolin-sdk</artifactId>
                <version>${kms-pangolin-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>idl-kms</artifactId>
                <version>${idl-kms.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>2.17.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-client</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-redis-squirrel</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-cluster-limiter</artifactId>
                <version>1.5.0</version>
            </dependency>
            <!-- PowerMockito -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.10.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.jsonzou</groupId>
                <artifactId>jmockdata</artifactId>
                <version>4.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>mss-java-sdk-s3</artifactId>
                <version>1.9.14</version>
            </dependency>
            <dependency>
                <artifactId>aws-java-sdk-core</artifactId>
                <groupId>com.amazonaws</groupId>
                <optional>false</optional>
                <version>1.11.0</version>
            </dependency>
            <dependency>
                <artifactId>kms-tls-sdk</artifactId>
                <groupId>com.meituan.service.inf</groupId>
                <version>0.2.9</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-thrift-augment</artifactId>
                <version>1.3.0</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>mtthrift</artifactId>
                <version>2.11.2</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
                <artifactId>shango_dh_labor-types</artifactId>
                <version>1.2.24</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-thrift-publisher</artifactId>
                <version>2.7.7${version.type}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-sdk</artifactId>
                <version>1.0.7</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
            <version>1.0.10</version>
            <exclusions>
                <exclusion>
                    <artifactId>thrift-xframe-boot-starter</artifactId>
                    <groupId>com.meituan.xframe</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zebra-xframe-boot-starter</artifactId>
                    <groupId>com.meituan.xframe</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
            <artifactId>shango_dh_labor-types</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>idl-kms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.product.intelligent</groupId>
            <artifactId>intelligent-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.3</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.9.0</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.fulfill</groupId>
            <artifactId>pick-select-query-idl</artifactId>
            <version>1.0.14</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dianping.dpsf</groupId>
            <artifactId>dpsf-net</artifactId>
            <version>3.8.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.service.inf</groupId>
                    <artifactId>kms-java-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.service.inf.pki</groupId>
                    <artifactId>mt-cert-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.taobao.tair</groupId>
            <artifactId>tair3-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
            <version>4.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
            <version>${zebra.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
            <version>${zebra.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>zebra-api</artifactId>
                    <groupId>com.dianping.zebra</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
            <version>${zebra.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.xsupply</groupId>
            <artifactId>price-management-client</artifactId>
            <version>1.0.10</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.goodscenter</groupId>
            <artifactId>goods_center_client</artifactId>
            <version>1.0.12${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
            <version>2.3.21.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-redis-squirrel</artifactId>
            <version>1.2.6.4</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>xframe-boot-starter-rest</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rhino-client</artifactId>
                    <groupId>com.dianping.rhino</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zebra-api</artifactId>
                    <groupId>com.dianping.zebra</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zebra-dao</artifactId>
                    <groupId>com.dianping.zebra</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zebra-ds-monitor-client</artifactId>
                    <groupId>com.dianping.zebra</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>stock-biz-client</artifactId>
            <version>${stock-biz-version}${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>stock-biz-base-idl</artifactId>
            <version>${stock-biz-version}${version.type}</version>
            <exclusions>
                <exclusion>
                    <artifactId>kms-tls-sdk</artifactId>
                    <groupId>com.meituan.service.inf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store</groupId>
            <artifactId>store-saas-infrastructure-shield-common</artifactId>
            <version>1.0.2.1${type}</version>
            <exclusions>
                <exclusion>
                    <artifactId>tair3-client</artifactId>
                    <groupId>com.taobao.tair</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mafka-client_2.10</artifactId>
                    <groupId>com.meituan.mafka</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
        </dependency>
        <dependency>
            <artifactId>mafka-client_2.10</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.service.mobile</groupId>
                    <artifactId>mtthrift</artifactId>
                </exclusion>
            </exclusions>
            <groupId>com.meituan.mafka</groupId>
            <version>3.9.4</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
            <version>2.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>store-saas-common</artifactId>
            <version>2.1.16</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mtrace</groupId>
                    <artifactId>mtrace</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.dianping.dpsf</groupId>
                    <artifactId>dpsf-net</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.taobao.tair</groupId>
                    <artifactId>tair3-client</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.dianping.zebra</groupId>
                    <artifactId>zebra-api</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.meituan.inf</groupId>
                    <artifactId>xmd-log4j2</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.xmlbeans</groupId>
                    <artifactId>xmlbeans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-scratchpad</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml-schemas</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>empower-task-service-idl</artifactId>
            <version>${empower.idl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>wms-api</artifactId>
            <version>${empower.idl.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>shangou-supplychain-api</artifactId>
                    <groupId>com.sankuai.shangou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store</groupId>
            <artifactId>store-management-receipt-client</artifactId>
            <version>2.4.0${type}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.store</groupId>
                    <artifactId>store-management-receipt-idl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store</groupId>
            <artifactId>store-management-receipt-idl</artifactId>
            <version>2.20.0${type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>oio-api</artifactId>
            <version>1.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-storage-bin-client</artifactId>
            <version>3.0${type}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                    <artifactId>pick-select-storage-bin-idl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                    <artifactId>reco-store-management-warehouse-idl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-service-client</artifactId>
            <version>${pick.select-idl.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>pick-select-service-idl</artifactId>
                    <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
            <version>${ocms.service.client.version.profile}</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
            <version>2.1.136</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.reco.pickselect</groupId>
                    <artifactId>pick-select-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-service-idl</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                    <artifactId>fulfill-constants</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>fulfill-constants</artifactId>
            <version>1.0.1${type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-logic-idl</artifactId>
            <version>${pick.select.login.version.profile}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.platform</groupId>
            <artifactId>shangou_empower_product_client</artifactId>
            <version>2.13.13${type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco</groupId>
            <artifactId>scgateway_client</artifactId>
            <version>0.0.5${type}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.sjst</groupId>
                    <artifactId>scm-soa-log-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas.resource.management</groupId>
            <artifactId>resource_management_client</artifactId>
            <version>2.3.4.9${type}</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.oslo</groupId>
            <artifactId>oslo-appraise-client</artifactId>
            <version>1.0.12${type}</version>
        </dependency>

        <!--swagger依赖-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.7.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-annotations</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.7.0</version>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <version>1.7.4</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>1.7.4</version>
            <scope>test</scope>
        </dependency>

        <!--图片服务venus-->
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
            <!--<version>1.1.3</version>-->
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.3</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-ebase-idl</artifactId>
            <version>2.13.74</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_saas_auth_client</artifactId>
            <version>${auth.client.version}${type}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>inf-bom</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
            <version>${inf-bom.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>reco_store_saas_message_management_client</artifactId>
            <version>${saas.message.client.version.profile}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>reco_store_saas_task_mng_client</artifactId>
            <version>${task.mng.version}${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_management_client</artifactId>
            <version>1.6.76</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.shangou.saas</groupId>
                    <artifactId>reco_store_saas_order_platform_common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                    <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.settlement</groupId>
            <artifactId>reco_shopmgmt_settlement_service-client</artifactId>
            <version>1.0.174${type}</version>
        </dependency>

        <!--外卖门店查询-->
        <dependency>
            <groupId>com.sankuai.meituan.waimai.poisearch</groupId>
            <artifactId>waimai_service_poisearch_client</artifactId>
            <version>1.0.0${type}</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-common</artifactId>
            <version>3.9.47</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_common</artifactId>
            <version>1.7.48</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
            <version>3.0.1-b02</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.web</groupId>
            <artifactId>javax.el</artifactId>
            <version>2.2.6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.service.order</groupId>
            <artifactId>waimai_service_order_clientassembly</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.empower</groupId>
            <artifactId>reco_qnh_tenant_biz-client</artifactId>
            <version>1.0.9${version.type}</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
            <version>${tenant.client.version.profile}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>store-saas-data-client</artifactId>
            <version>2.0.21${version.type}</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.sgshopcrm</groupId>
            <artifactId>reco_store_saas_price_management-client</artifactId>
            <version>${price.management.client.version}${version.type}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
            <version>1.3.9</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtconfig-client</artifactId>
            <version>2.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
            <version>********</version>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
            <version>1.3.7</version>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-remote</artifactId>
            <version>1.3.7</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.epassport</groupId>
            <artifactId>epassport-service-client</artifactId>
            <version>2.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
            <artifactId>productplatform-sdk</artifactId>
            <version>1.10.77${type}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>store-saas-data-third-client</artifactId>
            <version>0.2${version.type}</version>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.3.0.Final</version>
        </dependency>


        <dependency>
            <groupId>com.sankuai.meituan.shangou.xsupply</groupId>
            <artifactId>shangou-scm-supplynetwork-api-client</artifactId>
            <version>1.0.4${version.type}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.3.0.Final</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.munich</groupId>
            <artifactId>munich-assistant-client</artifactId>
            <version>${munich.client.version}</version>
        </dependency>


        <dependency>
            <groupId>com.meituan.shangou.linz</groupId>
            <artifactId>linz-product</artifactId>
            <version>1.0.9.9</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.linz</groupId>
            <artifactId>linz-thrift</artifactId>
            <version>1.0.9.7</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
            <version>2.2.82</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
            <version>2.2.78${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgshopmgmt.shangou.empower.regionselection</groupId>
            <artifactId>reco_shopmgmt_region_select-sdk</artifactId>
            <version>2.4.3${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
            <artifactId>reco_store_saas_product_biz-client</artifactId>
            <version>${productbiz.client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-java-client</artifactId>
            <version>${kms-java-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>mtthrift</artifactId>
                    <groupId>com.meituan.service.mobile</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
            <version>${ocms.channel.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.rhino</groupId>
                    <artifactId>rhino-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.log</groupId>
                    <artifactId>scribe-log4j2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
            <version>1.3.9.4</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-java-client</artifactId>
            <version>${kms-java-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>reco_store_saas_promotion_client</artifactId>
            <version>1.45${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
            <artifactId>bizmanagement-client</artifactId>
            <version>2.18.6${version.type}</version>
        </dependency>


        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_sac_client</artifactId>
            <version>${sac.client.version}${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgdata</groupId>
            <artifactId>query-api-sdk</artifactId>
            <version>${sgdata.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-over-slf4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.waimai</groupId>
            <artifactId>dws-openapi-client</artifactId>
            <version>1.2.6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.saas.crm</groupId>
            <artifactId>store-saas-data-client</artifactId>
            <version>${saasdata.version}${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.supplychain.purchase</groupId>
            <artifactId>reco-supplychain-purchase-client</artifactId>
            <version>2.22.71</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>idl-kms</artifactId>
            <version>1.4.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.it.sso</groupId>
            <artifactId>sso-java-sdk</artifactId>
            <version>2.5.5</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sc</groupId>
            <artifactId>fulfillment-client</artifactId>
            <version>1.2.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>dh-wms-idl</artifactId>
            <version>${dh-wms.version}${version.type}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.service.mobile</groupId>
                    <artifactId>mtthrift</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>shangou-supplychain-api</artifactId>
                    <groupId>com.sankuai.shangou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.store.management</groupId>
            <artifactId>dh-wms-stock-operate-center-idl</artifactId>
            <version>${dh-wms.version}${version.type}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>empty_version</version>
        </dependency>

        <!-- PowerMockito -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.10.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.jsonzou</groupId>
            <artifactId>jmockdata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <version>1.2.12</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
            <artifactId>shango_dh_labor-sdk</artifactId>
            <version>1.2.24${version.type}</version>
            <exclusions>
                <exclusion>
                    <artifactId>linz-boot</artifactId>
                    <groupId>com.meituan.shangou.linz</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate.validator</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.shangou</groupId>
                    <artifactId>shangou-thrift-publisher</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
            <version>1.9.14</version>
        </dependency>
        <dependency>
            <artifactId>aws-java-sdk-core</artifactId>
            <groupId>com.amazonaws</groupId>
            <optional>false</optional>
            <version>1.11.0</version>
        </dependency>
        <dependency>
            <artifactId>kms-tls-sdk</artifactId>
            <groupId>com.meituan.service.inf</groupId>
            <version>0.3.6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-augment</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>mtthrift</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-supplychain-api</artifactId>
            <version>2.10.8${version.type}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>wio-client</artifactId>
            <version>1.1.5${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-publisher</artifactId>
            <version>2.7.0${version.type}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.2.4.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
            <version>1.5.8</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-api</artifactId>
            <version>1.3.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
            <version>1.3.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>idl-mtrace</artifactId>
            <version>1.3.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace-agent</artifactId>
            <version>1.3.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>wio-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.shangou</groupId>
                    <artifactId>shangou-supplychain-api</artifactId>
                </exclusion>
            </exclusions>
            <version>1.1.5${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-publisher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.goodscenter</groupId>
            <artifactId>goods_center_client</artifactId>
            <version>1.0.11</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.infra</groupId>
            <artifactId>osw-api</artifactId>
            <version>1.0.49</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.tsp</groupId>
            <artifactId>waimai_service_privacy_server_client</artifactId>
            <version> 2.41.6</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-service-dh-client</artifactId>
            <version>1.0.0${version.type}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-client</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-common</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sgfnqnh.finance</groupId>
            <artifactId>reco_sgfnqnh_finance_tax-client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.xsupply</groupId>
            <artifactId>product_management-client</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.bizmng</groupId>
            <artifactId>labor-api</artifactId>
            <version>1.0.0${version.type}</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>duty-center-api</artifactId>
            <version>${duty.center.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>shangou-supplychain-api</artifactId>
                    <groupId>com.sankuai.shangou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>dhprocess-api</artifactId>
            <version>${dhprocess.center.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>handle-unit-api</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimai.productlib</groupId>
            <artifactId>waimai_service_product_lib_client</artifactId>
            <version>1.0.58-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sgfulfillment.scm</groupId>
            <artifactId>monitor-api</artifactId>
            <version>1.0.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.shangou.sgfulfillment.purchase.domain.framework</groupId>
                    <artifactId>common-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.sgfulfillment.purchase.domain.framework</groupId>
            <artifactId>common-api</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-sdk</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <configuration>
                    <configurationFile>src/main/resources/base/generatorConfig.xml</configurationFile>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources/${env-conf-dir}/${conf-dir}</directory>
            </resource>
            <resource>
                <directory>src/main/resources/${env-conf-dir}/base</directory>
            </resource>
            <resource>
                <directory>src/main/resources/common</directory>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
    </build>

    <profiles>
        <profile>
            <id>online</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <env-conf-dir>online</env-conf-dir>
            </properties>
        </profile>
        <profile>
            <id>offline</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <env-conf-dir>offline</env-conf-dir>
            </properties>
        </profile>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <distributionManagement>
                <repository>
                    <id>meituan-offline-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-offline-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <properties>
                <type>-SNAPSHOT</type>
                <ocms.service.client.version.profile>${ocms.service-client.version}-SNAPSHOT</ocms.service.client.version.profile>
                <pick.select.client.version.profile>${pick.select-client.version}-SNAPSHOT
                </pick.select.client.version.profile>
                <pick.select.login.version.profile>${pick.select-login.version}-SNAPSHOT
                </pick.select.login.version.profile>
                <saas.message.client.version.profile>${message.client.version}-SNAPSHOT
                </saas.message.client.version.profile>
                <empower.idl.version.profile>${empower.idl.version}</empower.idl.version.profile>
                <tenant.client.version.profile>${tenant.client.version}-SNAPSHOT</tenant.client.version.profile>
                <version.type>-SNAPSHOT</version.type>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <distributionManagement>
                <repository>
                    <id>meituan-offline-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-offline-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <properties>
                <type>-SNAPSHOT</type>
                <ocms.service.client.version.profile>${ocms.service-client.version}-SNAPSHOT</ocms.service.client.version.profile>
                <pick.select.client.version.profile>${pick.select-client.version}-SNAPSHOT
                </pick.select.client.version.profile>
                <pick.select.login.version.profile>${pick.select-login.version}-SNAPSHOT
                </pick.select.login.version.profile>
                <saas.message.client.version.profile>${message.client.version}-SNAPSHOT
                </saas.message.client.version.profile>
                <empower.idl.version.profile>${empower.idl.version}-SNAPSHOT</empower.idl.version.profile>
                <version.type>-SNAPSHOT</version.type>
                <tenant.client.version.profile>${tenant.client.version}-SNAPSHOT</tenant.client.version.profile>
            </properties>
        </profile>
        <profile>
            <id>beta</id>
            <properties>
                <type>-SNAPSHOT</type>
                <ocms.service.client.version.profile>${ocms.service-client.version}-SNAPSHOT</ocms.service.client.version.profile>
                <pick.select.client.version.profile>${pick.select-client.version}-SNAPSHOT
                </pick.select.client.version.profile>
                <pick.select.login.version.profile>${pick.select-login.version}-SNAPSHOT
                </pick.select.login.version.profile>
                <saas.message.client.version.profile>${message.client.version}</saas.message.client.version.profile>
                <empower.idl.version.profile>${empower.idl.version}</empower.idl.version.profile>
                <version.type>-SNAPSHOT</version.type>
                <tenant.client.version.profile>${tenant.client.version}-SNAPSHOT</tenant.client.version.profile>
            </properties>
        </profile>
        <profile>
            <id>clean</id>
            <properties>
                <type>-SNAPSHOT</type>
                <ocms.service.client.version.profile>${ocms.service-client.version}-SNAPSHOT</ocms.service.client.version.profile>
                <pick.select.client.version.profile>${pick.select-client.version}-SNAPSHOT
                </pick.select.client.version.profile>
                <pick.select.login.version.profile>${pick.select-login.version}-SNAPSHOT
                </pick.select.login.version.profile>
                <saas.message.client.version.profile>${message.client.version}-SNAPSHOT
                </saas.message.client.version.profile>
                <empower.idl.version.profile>${empower.idl.version}-SNAPSHOT</empower.idl.version.profile>
                <version.type>-SNAPSHOT</version.type>
                <tenant.client.version.profile>${tenant.client.version}-SNAPSHOT</tenant.client.version.profile>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <distributionManagement>
                <repository>
                    <id>meituan-offline-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://maven.offline.sankuai.com/nexus/content/repositories/releases/</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-offline-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/offline-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
            <properties>
                <type>-SNAPSHOT</type>
                <ocms.service.client.version.profile>${ocms.service-client.version}-SNAPSHOT</ocms.service.client.version.profile>
                <pick.select.client.version.profile>${pick.select-client.version}-SNAPSHOT
                </pick.select.client.version.profile>
                <pick.select.login.version.profile>${pick.select-login.version}-SNAPSHOT
                </pick.select.login.version.profile>
                <saas.message.client.version.profile>${message.client.version}-SNAPSHOT
                </saas.message.client.version.profile>
                <empower.idl.version.profile>${empower.idl.version}</empower.idl.version.profile>
                <version.type>-SNAPSHOT</version.type>
                <tenant.client.version.profile>${tenant.client.version}-SNAPSHOT</tenant.client.version.profile>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/releases</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
            <properties>
                <type></type>
                <ocms.service.client.version.profile>${ocms.service-client.version}</ocms.service.client.version.profile>
                <pick.select.client.version.profile>${pick.select-client.version}</pick.select.client.version.profile>
                <pick.select.login.version.profile>${pick.select-login.version}</pick.select.login.version.profile>
                <saas.message.client.version.profile>${message.client.version}</saas.message.client.version.profile>
                <empower.idl.version.profile>${empower.idl.version}</empower.idl.version.profile>
                <tenant.client.version.profile>${tenant.client.version}</tenant.client.version.profile>
                <version.type></version.type>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/releases</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
            <properties>
                <type></type>
                <ocms.service.client.version.profile>${ocms.service-client.version}</ocms.service.client.version.profile>
                <pick.select.client.version.profile>${pick.select-client.version}</pick.select.client.version.profile>
                <pick.select.login.version.profile>${pick.select-login.version}</pick.select.login.version.profile>
                <saas.message.client.version.profile>${message.client.version}</saas.message.client.version.profile>
                <empower.idl.version.profile>${empower.idl.version}</empower.idl.version.profile>
                <tenant.client.version.profile>${tenant.client.version}</tenant.client.version.profile>
                <version.type></version.type>
            </properties>
        </profile>
    </profiles>
</project>
