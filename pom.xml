<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xframe-starter-parent</artifactId>
        <groupId>com.meituan.xframe</groupId>
        <version>2.3.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>reco_fulfillment_tms</name>

    <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
    <artifactId>reco_fulfillment_tms</artifactId>
    <version>${revision}</version>

    <packaging>pom</packaging>

    <modules>
        <module>reco_fulfillment_tms-delivery-dao</module>
        <module>reco_fulfillment_tms-server</module>
        <module>reco_fulfillment_tms-delivery-client</module>
        <module>reco_fulfillment_tms-rider-delivery-client</module>
        <module>tms-delivery-poi</module>
        <module>tms-delivery-rider</module>
        <module>tms-delivery-access</module>
        <module>tms-delivery-platform</module>
        <module>tms-delivery-task</module>
        <module>dms-delivery-base</module>
    </modules>

    <properties>
        <env-suffix>-SNAPSHOT</env-suffix>
        <unified-logistics-accept-platform-client-env-suffix>-SNAPSHOT</unified-logistics-accept-platform-client-env-suffix>
        <revision>2.2.86-pick-fusion-SNAPSHOT</revision>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <mns-invoker.version>1.15.1</mns-invoker.version>
        <sac.client.version>1.1.9</sac.client.version>
        <jts.version>1.18.2</jts.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower</groupId>
                <artifactId>reco_store_sac_client</artifactId>
                <version>${sac.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.octo</groupId>
                <artifactId>idl-common</artifactId>
                <version>1.10.0</version>
            </dependency>

            <dependency>
                <artifactId>kms-tls-sdk</artifactId>
                <groupId>com.meituan.service.inf</groupId>
                <version>0.3.6</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-java-client</artifactId>
                <version>0.13.1</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-pangolin-sdk</artifactId>
                <version>0.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.tair</groupId>
                <artifactId>tair3-client</artifactId>
                <version>4.6.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>3.0.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.thrift</groupId>
                <artifactId>libthrift</artifactId>
                <version>0.9.3-mt</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.3.3</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.5</version>
            </dependency>


            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>1.3.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>1.3.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>1.2.4.RELEASE</version>
            </dependency>


            <dependency>
                <groupId>com.sankuai.map.maf</groupId>
                <artifactId>openplatform-dependency</artifactId>
                <version>1.1.89</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.octo</groupId>
                <artifactId>mns-invoker</artifactId>
                <version>${mns-invoker.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>2.2.9.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
                <artifactId>reco_qnh_poi_api-client</artifactId>
                <version>1.0.10</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_platform_common</artifactId>
                <version>1.9.38</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.meituan.shangou</groupId>
                        <artifactId>store-saas-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mtthrift</artifactId>
                        <groupId>com.meituan.service.mobile</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou</groupId>
                <artifactId>store-saas-common</artifactId>
                <version>2.1.16</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.25</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>tms-delivery-poi</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>reco_fulfillment_tms-delivery-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>tms-delivery-platform</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>tms-delivery-access</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>tms-delivery-rider</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>tms-delivery-task</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>dms-delivery-base</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>dms-delivery-self</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                <artifactId>pick-select-ebase-idl</artifactId>
                <version>2.13.28${env-suffix}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.security</groupId>
                <artifactId>sec-sdk</artifactId>
                <version>1.3.9</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.mafka</groupId>
                <artifactId>mafka-client_2.10</artifactId>
                <version>3.9.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mtthrift</artifactId>
                        <groupId>com.meituan.service.mobile</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.cat</groupId>
                        <artifactId>cat-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower</groupId>
                <artifactId>reco_store_saas_auth_client</artifactId>
                <version>1.4.17${env-suffix}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mtthrift</artifactId>
                        <groupId>com.meituan.service.mobile</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou.platform</groupId>
                <artifactId>shangou_empower_product_client</artifactId>
                <version>1.1.1${env-suffix}</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_tenant_client</artifactId>
                <version>3.5.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
                <version>2.18.30</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou</groupId>
                <artifactId>reco_store_saas_message_management_client</artifactId>
                <version>2.0.33${env-suffix}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
                <version>2.28.29</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_biz_client</artifactId>
                <version>2.3.48</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.locationtech.jts</groupId>
                <artifactId>jts-core</artifactId>
                <version>${jts.version}</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_management_client</artifactId>
                <version>1.6.05</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.meituan.shangou</groupId>
                        <artifactId>store-saas-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.shangou.saas</groupId>
                        <artifactId>reco_store_saas_order_platform_common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                        <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
                <artifactId>shango_dh_labor-sdk</artifactId>
                <version>1.1.2${env-suffix}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-thrift-publisher</artifactId>
                <version>2.7.0${env-suffix}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-message-pusher</artifactId>
                <version>1.4.0${env-suffix}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mtthrift</artifactId>
                        <groupId>com.meituan.service.mobile</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_platform_client</artifactId>
                <version>1.9.124</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                <artifactId>pick-select-service-idl</artifactId>
                <version>6.1.30${env-suffix}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>dmp-sdk</artifactId>
                <version>1.0.11</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.shangou</groupId>
                        <artifactId>shangou-common-lang</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
                <artifactId>bizmanagement-client</artifactId>
                <version>1.10.1${env-suffix}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>xm-pub-api-client</artifactId>
                <version>1.5.7</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.alibaba</groupId>-->
<!--                <artifactId>easyexcel</artifactId>-->
<!--                <version>1.1.2-beta4</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.26.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.7</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.7</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.dbunit</groupId>
                <artifactId>dbunit</artifactId>
                <version>2.5.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.github.springtestdbunit</groupId>
                <artifactId>spring-test-dbunit</artifactId>
                <version>1.3.0</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.sankuai.banma.deliverywaybill.order</groupId>
                <artifactId>ldp-client</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.banma.deliverywaybill.order</groupId>
                <artifactId>logistic-order-callback-client</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma</groupId>
                <artifactId>banma_open_client</artifactId>
                <version>1.0.1${env-suffix}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.banma.logistics</groupId>
                <artifactId>banma_service_logistics_meta_center_client</artifactId>
                <version>1.0.1${env-suffix}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>mtconfig-client</artifactId>
                <version>2.2.0</version>
            </dependency>

            <dependency>
                <groupId>com.dianping.lion</groupId>
                <artifactId>lion-client</artifactId>
                <version>0.10.9.2</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.0.13.Final</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.banma.package</groupId>
                <artifactId>unified-logistics-accept-platform-client</artifactId>
                <version>1.0.11${unified-logistics-accept-platform-client-env-suffix}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.data</groupId>
                        <artifactId>spring-data-redis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.reco.pickselect</groupId>
                <artifactId>pick-select-common</artifactId>
                <version>3.10.6</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>xmd-log4j2</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>xmd-common-log4j2</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.log</groupId>
                <artifactId>scribe-log4j2</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.wmarch.map</groupId>
                <artifactId>maf-client</artifactId>
                <version>3.0.20</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>kms-pangolin-sdk</artifactId>
                <version>0.10.0</version>
            </dependency>
            <dependency>
                <artifactId>fastjson</artifactId>
                <groupId>com.alibaba</groupId>
                <version>1.2.83_noneautotype</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-thrift-augment</artifactId>
                <version>2.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>mtthrift</artifactId>
                <version>2.11.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-client</artifactId>
                <version>1.5.0</version>
            </dependency>

            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-redis-squirrel</artifactId>
                <version>1.5.0</version>
            </dependency>

            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-cluster-limiter</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.rhino</groupId>
                <artifactId>rhino-cluster-common</artifactId>
                <version>1.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>
                <artifactId>reco_store_saas_task-framework</artifactId>
                <version>1.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.jackson</groupId>
                        <artifactId>jackson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.zebra</groupId>
                        <artifactId>zebra-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.dianping.rhino</groupId>
                        <artifactId>rhino-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.service.inf</groupId>
                        <artifactId>kms-java-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mafka</groupId>
                        <artifactId>mafka-client_2.10</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.mafka</groupId>
                        <artifactId>mafka-client_2.9</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Yaml -->
            <dependency>
                <groupId>com.esotericsoftware.yamlbeans</groupId>
                <artifactId>yamlbeans</artifactId>
                <version>1.15</version>
            </dependency>

            <!-- S3 -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>mss-java-sdk-s3</artifactId>
                <version>1.9.14</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.inf.leaf</groupId>
                <artifactId>leaf-idl</artifactId>
                <version>1.0.2</version>
            </dependency>

            <!-- Excel -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <artifactId>poi-ooxml-schemas</artifactId>
                <groupId>org.apache.poi</groupId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.1.6</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-api</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-ds-monitor-client</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-dao</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.mtrace</groupId>
                <artifactId>mtrace-api</artifactId>
                <version>1.3.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mtrace</groupId>
                <artifactId>mtrace</artifactId>
                <version>1.3.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mtrace</groupId>
                <artifactId>idl-mtrace</artifactId>
                <version>1.3.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.mtrace</groupId>
                <artifactId>mtrace-agent</artifactId>
                <version>1.3.1.1</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.16</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>4.4.16</version>
            </dependency>

            <dependency>
                <groupId>com.dianping.dpsf</groupId>
                <artifactId>dpsf-net</artifactId>
                <version>3.8.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.dianping.squirrel</groupId>
                <artifactId>squirrel-client</artifactId>
                <version>2.3.21.3</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.infra</groupId>
                <artifactId>osw-api</artifactId>
                <version>1.0.39</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.qnh.ofc</groupId>
                <artifactId>qnh_ofc_ebase-client</artifactId>
                <version>1.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.waimaidata</groupId>
                <artifactId>waimaidata_disaster_client</artifactId>
                <version>1.0.19</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
                <artifactId>reco_qnh_poi_base-client</artifactId>
                <version>1.0.14</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>dev</id>
        </profile>
        <profile>
            <id>test</id>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <env-suffix/>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env-suffix/>
                <unified-logistics-accept-platform-client-env-suffix/>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <!--<version>1.2.2</version>-->
                <configuration>
                </configuration>
                <executions>
                    <!-- enable flattening -->
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <!-- ensure proper cleanup -->
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <!--外部依赖方目前是使用的1.7，因此client暂时无法升级至1.8-->
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.meituan.log</groupId>-->
<!--                <artifactId>scribelog4j2-maven-plugin</artifactId>-->
<!--                <version>1.1.2</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <goals>-->
<!--                            <goal>checkPom</goal>-->
<!--                        </goals>-->
<!--                        <phase>compile</phase>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
        </plugins>
    </build>

<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>meituan-nexus-releases</id>-->
<!--            <name>Meituan Nexus Repository</name>-->
<!--            <url>http://maven.sankuai.com/nexus/content/repositories/releases/</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>meituan-nexus-snapshots</id>-->
<!--            <name>Meituan Nexus Repository</name>-->
<!--            <url>http://maven.sankuai.com/nexus/content/repositories/snapshots/</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->
</project>
