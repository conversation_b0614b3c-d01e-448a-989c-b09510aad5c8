package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.delivery.configure.value.interval.Interval;
import com.sankuai.shangou.logistics.delivery.configure.value.interval.IntervalNumber;
import com.sankuai.shangou.logistics.delivery.configure.value.interval.IntervalTypeEnum;
import org.junit.Test;

/**
 * 测试Interval序列化格式
 */
public class IntervalSerializationTest {

    @Test
    public void testIntervalSerialization() {
        Interval interval = new Interval();
        interval.setIntervalType(IntervalTypeEnum.RIGHT_OPEN.getType());
//        interval.setValues(Lists.newArrayList(new IntervalNumber("0"), new IntervalNumber("1000")));
        
        System.out.println("Interval序列化结果:");
        System.out.println(JSON.toJSONString(interval, true));
        
        System.out.println("values()方法结果:");
        System.out.println(interval.values());
        
        System.out.println("getValues()方法结果:");
        System.out.println(interval.getValues());
    }
    
    @Test
    public void testIntervalNumberSerialization() {
        IntervalNumber number = new IntervalNumber("1000");
        System.out.println("IntervalNumber序列化结果:");
        System.out.println(JSON.toJSONString(number, true));
    }

    @Test
    public void testCustomIntervalSerialization() {
        // 测试自定义Interval的序列化
        com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO.IntervalVO intervalVO =
            new com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO.IntervalVO();
        intervalVO.setValues(com.google.common.collect.Lists.newArrayList("0", "1000"));
        intervalVO.setIntervalType(2);

        // 使用转换器的方法创建Interval
        java.lang.reflect.Method method;
        try {
            method = BookingPushDownTimeConfigConverter.class.getDeclaredMethod("convertToInterval",
                com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO.IntervalVO.class);
            method.setAccessible(true);
            com.sankuai.shangou.logistics.delivery.configure.value.interval.Interval customInterval =
                (com.sankuai.shangou.logistics.delivery.configure.value.interval.Interval) method.invoke(null, intervalVO);

            System.out.println("自定义Interval序列化结果:");
            System.out.println(JSON.toJSONString(customInterval, true));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
