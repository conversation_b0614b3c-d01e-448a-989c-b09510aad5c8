package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.value.ExpressionNode;
import com.sankuai.shangou.logistics.delivery.enums.DeliveryConfigOrderTagEnum;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

/**
 * BookingPushDownTimeConfigConverter测试类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 * @email <EMAIL>
 */
public class BookingPushDownTimeConfigConverterTest {

    @Test
    public void testConvertToExpressionNodes_WithAllOrderTag() {
        // 准备测试数据 - orderTags为[0]的情况
        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO configVO = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();
        configVO.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.ALL.getType()));
        
        // 创建条件
        DeliveryConfigDetailVO.ConditionVO condition = new DeliveryConfigDetailVO.ConditionVO();
        condition.setIdentifer("distance");
        condition.setValue("30");
        
        DeliveryConfigDetailVO.IntervalVO interval = new DeliveryConfigDetailVO.IntervalVO();
        interval.setValues(Lists.newArrayList("0", "1000"));
        interval.setIntervalType(2);
        condition.setInterval(interval);
        
        configVO.setCondition(Lists.newArrayList(condition));
        
        // 执行转换
        List<ExpressionNode> result = BookingPushDownTimeConfigConverter.convertToExpressionNodes(Lists.newArrayList(configVO));
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        ExpressionNode rootNode = result.get(0);
        assertNull(rootNode.getCondition());
        assertNotNull(rootNode.getSubs());
        assertEquals(1, rootNode.getSubs().size());
        
        // 验证预约类型条件
        ExpressionNode reserveTypeNode = rootNode.getSubs().get(0);
        assertNotNull(reserveTypeNode.getCondition());
        assertEquals("@{reserve_type}", reserveTypeNode.getCondition().getIdentifier());
        assertEquals("订单预约类型", reserveTypeNode.getCondition().getName());
        
        System.out.println("转换结果 (orderTags=[0]):");
        System.out.println(JSON.toJSONString(result, true));
    }

    @Test
    public void testConvertToExpressionNodes_WithRestaurantOrderTag() {
        // 准备测试数据 - orderTags为[2010]的情况
        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO configVO = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();
        configVO.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.RESTAURANT.getType()));
        
        // 创建条件
        DeliveryConfigDetailVO.ConditionVO condition = new DeliveryConfigDetailVO.ConditionVO();
        condition.setIdentifer("distance");
        condition.setValue("120");
        
        DeliveryConfigDetailVO.IntervalVO interval = new DeliveryConfigDetailVO.IntervalVO();
        interval.setValues(Lists.newArrayList("0", "infinity"));
        interval.setIntervalType(2);
        condition.setInterval(interval);
        
        configVO.setCondition(Lists.newArrayList(condition));
        
        // 执行转换
        List<ExpressionNode> result = BookingPushDownTimeConfigConverter.convertToExpressionNodes(Lists.newArrayList(configVO));
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        ExpressionNode rootNode = result.get(0);
        assertNull(rootNode.getCondition());
        assertNotNull(rootNode.getSubs());
        assertEquals(1, rootNode.getSubs().size());
        
        // 验证预约类型条件
        ExpressionNode reserveTypeNode = rootNode.getSubs().get(0);
        assertNotNull(reserveTypeNode.getCondition());
        assertEquals("@{reserve_type}", reserveTypeNode.getCondition().getIdentifier());
        
        // 验证餐馆场景条件
        assertNotNull(reserveTypeNode.getSubs());
        assertEquals(1, reserveTypeNode.getSubs().size());
        ExpressionNode restaurantSceneNode = reserveTypeNode.getSubs().get(0);
        assertNotNull(restaurantSceneNode.getCondition());
        assertEquals("@{restaurant_scene}", restaurantSceneNode.getCondition().getIdentifier());
        assertEquals("餐馆场景", restaurantSceneNode.getCondition().getName());
        
        System.out.println("转换结果 (orderTags=[2010]):");
        System.out.println(JSON.toJSONString(result, true));
    }

    @Test
    public void testConvertToExpressionNodes_WithMultipleConditions() {
        // 准备测试数据 - 多个条件的情况
        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO configVO = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();
        configVO.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.ALL.getType()));
        
        // 创建第一个条件
        DeliveryConfigDetailVO.ConditionVO condition1 = new DeliveryConfigDetailVO.ConditionVO();
        condition1.setIdentifer("distance");
        condition1.setValue("30");
        
        DeliveryConfigDetailVO.IntervalVO interval1 = new DeliveryConfigDetailVO.IntervalVO();
        interval1.setValues(Lists.newArrayList("0", "1000"));
        interval1.setIntervalType(2);
        condition1.setInterval(interval1);
        
        // 创建第二个条件
        DeliveryConfigDetailVO.ConditionVO condition2 = new DeliveryConfigDetailVO.ConditionVO();
        condition2.setIdentifer("distance");
        condition2.setValue("60");
        
        DeliveryConfigDetailVO.IntervalVO interval2 = new DeliveryConfigDetailVO.IntervalVO();
        interval2.setValues(Lists.newArrayList("1000", "infinity"));
        interval2.setIntervalType(2);
        condition2.setInterval(interval2);
        
        configVO.setCondition(Lists.newArrayList(condition1, condition2));
        
        // 执行转换
        List<ExpressionNode> result = BookingPushDownTimeConfigConverter.convertToExpressionNodes(Lists.newArrayList(configVO));
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        System.out.println("转换结果 (多个条件):");
        System.out.println(JSON.toJSONString(result, true));
    }

    @Test
    public void testConvertFromExpressionNodes() {
        // 先创建ExpressionNode数据
        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO originalConfigVO = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();
        originalConfigVO.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.RESTAURANT.getType()));
        
        DeliveryConfigDetailVO.ConditionVO condition = new DeliveryConfigDetailVO.ConditionVO();
        condition.setIdentifer("distance");
        condition.setValue("120");
        
        DeliveryConfigDetailVO.IntervalVO interval = new DeliveryConfigDetailVO.IntervalVO();
        interval.setValues(Lists.newArrayList("0", "infinity"));
        interval.setIntervalType(2);
        condition.setInterval(interval);
        
        originalConfigVO.setCondition(Lists.newArrayList(condition));
        
        // 转换为ExpressionNode
        List<ExpressionNode> expressionNodes = BookingPushDownTimeConfigConverter.convertToExpressionNodes(Lists.newArrayList(originalConfigVO));
        
        System.out.println("原始ExpressionNode:");
        System.out.println(JSON.toJSONString(expressionNodes, true));
        
        // 再转换回VO
        List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> result = BookingPushDownTimeConfigConverter.convertFromExpressionNodes(expressionNodes);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO resultConfigVO = result.get(0);
        assertNotNull(resultConfigVO.getOrderTags());
        assertTrue(resultConfigVO.getOrderTags().contains(DeliveryConfigOrderTagEnum.RESTAURANT.getType()));
        
        assertNotNull(resultConfigVO.getCondition());
        assertEquals(1, resultConfigVO.getCondition().size());
        
        DeliveryConfigDetailVO.ConditionVO resultCondition = resultConfigVO.getCondition().get(0);
        assertEquals("distance", resultCondition.getIdentifer());
        assertEquals("120", resultCondition.getValue());
        
        System.out.println("反向转换结果:");
        System.out.println(JSON.toJSONString(result, true));
    }

    @Test
    public void testRoundTripConversion() {
        // 测试完整的往返转换
        List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> originalConfigs = createTestData();
        
        System.out.println("原始数据:");
        System.out.println(JSON.toJSONString(originalConfigs, true));
        
        // VO -> ExpressionNode
        List<ExpressionNode> expressionNodes = BookingPushDownTimeConfigConverter.convertToExpressionNodes(originalConfigs);
        
        System.out.println("转换为ExpressionNode:");
        System.out.println(JSON.toJSONString(expressionNodes, true));
        
        // ExpressionNode -> VO
        List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> convertedConfigs = BookingPushDownTimeConfigConverter.convertFromExpressionNodes(expressionNodes);
        
        System.out.println("反向转换结果:");
        System.out.println(JSON.toJSONString(convertedConfigs, true));
        
        // 验证数据一致性
        assertNotNull(convertedConfigs);
        assertEquals(originalConfigs.size(), convertedConfigs.size());
    }

    private List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> createTestData() {
        List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> configs = Lists.newArrayList();
        
        // 第一个配置 - orderTags=[0]
        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO config1 = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();
        config1.setOrderTags(Lists.newArrayList(0));
        
        DeliveryConfigDetailVO.ConditionVO condition1_1 = new DeliveryConfigDetailVO.ConditionVO();
        condition1_1.setIdentifer("distance");
        condition1_1.setValue("30");
        DeliveryConfigDetailVO.IntervalVO interval1_1 = new DeliveryConfigDetailVO.IntervalVO();
        interval1_1.setValues(Lists.newArrayList("0", "1000"));
        interval1_1.setIntervalType(2);
        condition1_1.setInterval(interval1_1);
        
        DeliveryConfigDetailVO.ConditionVO condition1_2 = new DeliveryConfigDetailVO.ConditionVO();
        condition1_2.setIdentifer("distance");
        condition1_2.setValue("60");
        DeliveryConfigDetailVO.IntervalVO interval1_2 = new DeliveryConfigDetailVO.IntervalVO();
        interval1_2.setValues(Lists.newArrayList("1000", "infinity"));
        interval1_2.setIntervalType(2);
        condition1_2.setInterval(interval1_2);
        
        config1.setCondition(Lists.newArrayList(condition1_1, condition1_2));
        configs.add(config1);
        
        // 第二个配置 - orderTags=[2010]
        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO config2 = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();
        config2.setOrderTags(Lists.newArrayList(2010));
        
        DeliveryConfigDetailVO.ConditionVO condition2 = new DeliveryConfigDetailVO.ConditionVO();
        condition2.setIdentifer("distance");
        condition2.setValue("120");
        DeliveryConfigDetailVO.IntervalVO interval2 = new DeliveryConfigDetailVO.IntervalVO();
        interval2.setValues(Lists.newArrayList("0", "infinity"));
        interval2.setIntervalType(2);
        condition2.setInterval(interval2);
        
        config2.setCondition(Lists.newArrayList(condition2));
        configs.add(config2);
        
        return configs;
    }

    @Test
    public void testConvertAssertTimeToExpressionNodes() {
        // 测试AssertTimeVO转换为ExpressionNode
        DeliveryConfigDetailVO.AssertTimeVO assertTimeVO = new DeliveryConfigDetailVO.AssertTimeVO();
        assertTimeVO.setType(1);
        assertTimeVO.setTimeConfigType(2);
        assertTimeVO.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.ALL.getType()));

        DeliveryConfigDetailVO.ConditionVO condition = new DeliveryConfigDetailVO.ConditionVO();
        condition.setIdentifer("distance");
        condition.setValue("30");

        DeliveryConfigDetailVO.IntervalVO interval = new DeliveryConfigDetailVO.IntervalVO();
        interval.setValues(Lists.newArrayList("0", "1000"));
        interval.setIntervalType(2);
        condition.setInterval(interval);

        assertTimeVO.setCondition(Lists.newArrayList(condition));

        // 执行转换
        List<ExpressionNode> result = BookingPushDownTimeConfigConverter.convertAssertTimeToExpressionNodes(Lists.newArrayList(assertTimeVO));

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        System.out.println("AssertTimeVO转换结果:");
        System.out.println(JSON.toJSONString(result, true));
    }

    @Test
    public void testConvertFromExpressionNodesToAssertTime() {
        // 先创建ExpressionNode
        DeliveryConfigDetailVO.AssertTimeVO originalAssertTimeVO = new DeliveryConfigDetailVO.AssertTimeVO();
        originalAssertTimeVO.setType(1);
        originalAssertTimeVO.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.RESTAURANT.getType()));

        DeliveryConfigDetailVO.ConditionVO condition = new DeliveryConfigDetailVO.ConditionVO();
        condition.setIdentifer("distance");
        condition.setValue("60");

        DeliveryConfigDetailVO.IntervalVO interval = new DeliveryConfigDetailVO.IntervalVO();
        interval.setValues(Lists.newArrayList("1000", "infinity"));
        interval.setIntervalType(2);
        condition.setInterval(interval);

        originalAssertTimeVO.setCondition(Lists.newArrayList(condition));

        // 转换为ExpressionNode
        List<ExpressionNode> expressionNodes = BookingPushDownTimeConfigConverter.convertAssertTimeToExpressionNodes(Lists.newArrayList(originalAssertTimeVO));

        System.out.println("原始ExpressionNode:");
        System.out.println(JSON.toJSONString(expressionNodes, true));

        // 再转换回AssertTimeVO
        List<DeliveryConfigDetailVO.AssertTimeVO> result = BookingPushDownTimeConfigConverter.convertFromExpressionNodesToAssertTime(expressionNodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        DeliveryConfigDetailVO.AssertTimeVO resultAssertTimeVO = result.get(0);
        assertNotNull(resultAssertTimeVO.getOrderTags());
        assertTrue(resultAssertTimeVO.getOrderTags().contains(DeliveryConfigOrderTagEnum.RESTAURANT.getType()));

        assertNotNull(resultAssertTimeVO.getCondition());
        assertEquals(1, resultAssertTimeVO.getCondition().size());

        DeliveryConfigDetailVO.ConditionVO resultCondition = resultAssertTimeVO.getCondition().get(0);
        assertEquals("distance", resultCondition.getIdentifer());
        assertEquals("60", resultCondition.getValue());

        System.out.println("AssertTimeVO反向转换结果:");
        System.out.println(JSON.toJSONString(result, true));
    }

    @Test
    public void testAssertTimeFormatFix() {
        // 测试修复后的格式 - 使用您示例中的数据
        DeliveryConfigDetailVO.AssertTimeVO assertTimeVO1 = new DeliveryConfigDetailVO.AssertTimeVO();
        assertTimeVO1.setType(1);
        assertTimeVO1.setOrderTags(Lists.newArrayList(0)); // 对应示例中的第一个配置

        DeliveryConfigDetailVO.ConditionVO condition1 = new DeliveryConfigDetailVO.ConditionVO();
        condition1.setIdentifer("distance");
        condition1.setValue("30");

        DeliveryConfigDetailVO.IntervalVO interval1 = new DeliveryConfigDetailVO.IntervalVO();
        interval1.setValues(Lists.newArrayList("0", "1000"));
        interval1.setIntervalType(2);
        condition1.setInterval(interval1);

        DeliveryConfigDetailVO.ConditionVO condition2 = new DeliveryConfigDetailVO.ConditionVO();
        condition2.setIdentifer("distance");
        condition2.setValue("60");

        DeliveryConfigDetailVO.IntervalVO interval2 = new DeliveryConfigDetailVO.IntervalVO();
        interval2.setValues(Lists.newArrayList("1000", "infinity"));
        interval2.setIntervalType(2);
        condition2.setInterval(interval2);

        assertTimeVO1.setCondition(Lists.newArrayList(condition1, condition2));

        // 第二个配置 - orderTags为2010
        DeliveryConfigDetailVO.AssertTimeVO assertTimeVO2 = new DeliveryConfigDetailVO.AssertTimeVO();
        assertTimeVO2.setType(1);
        assertTimeVO2.setOrderTags(Lists.newArrayList(2010)); // 餐馆

        DeliveryConfigDetailVO.ConditionVO condition3 = new DeliveryConfigDetailVO.ConditionVO();
        condition3.setIdentifer("distance");
        condition3.setValue("120");

        DeliveryConfigDetailVO.IntervalVO interval3 = new DeliveryConfigDetailVO.IntervalVO();
        interval3.setValues(Lists.newArrayList("0", "infinity"));
        interval3.setIntervalType(2);
        condition3.setInterval(interval3);

        assertTimeVO2.setCondition(Lists.newArrayList(condition3));

        // 执行转换
        List<ExpressionNode> result = BookingPushDownTimeConfigConverter.convertAssertTimeToExpressionNodes(
            Lists.newArrayList(assertTimeVO1, assertTimeVO2));

        System.out.println("修复后的转换结果 (应该匹配您的示例):");
        // 使用SimpleExpressionNode进行正确的JSON序列化
        List<BookingPushDownTimeConfigConverter.SimpleExpressionNode> simpleNodes =
            BookingPushDownTimeConfigConverter.convertToSimpleNodes(result);
        System.out.println(JSON.toJSONString(simpleNodes, true));

        // 验证格式
        assertNotNull(result);
        assertEquals(2, result.size()); // 应该有两个顶级节点

        // 验证第一个节点的结构
        ExpressionNode firstRootNode = result.get(0);
        assertNotNull(firstRootNode.getSubs());
        assertEquals(1, firstRootNode.getSubs().size());

        // 验证预约类型条件
        ExpressionNode reserveTypeNode = firstRootNode.getSubs().get(0);
        assertNotNull(reserveTypeNode.getCondition());
        assertEquals("@{reserve_type}", reserveTypeNode.getCondition().getIdentifier());
        assertEquals("订单预约类型", reserveTypeNode.getCondition().getName());
    }
}
