package com.sankuai.shangou.logistics.delivery.configure.mq;

import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaConsumer;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryBatchConfigService;
import com.sankuai.shangou.logistics.delivery.configure.pojo.msg.BatchConfigTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 批量配置任务消费组
 * @date 2025-07-01
 */
@Slf4j
@Component
@MafkaConsumer(namespace = "com.sankuai.mafka.castle.daojiacommon",
        topic = "batch_config_task_topic",
        group = "batch_config_task_consumer",
        deadLetter = true)
public class BatchConfigTaskConsumer implements IMessageListener<BatchConfigTaskMessage> {
    @Resource
    private DeliveryBatchConfigService deliveryBatchConfigService;

    @Override
    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext context) {
        try {
            log.info("开始消费批量配置任务消息: {}", mafkaMessage);
            BatchConfigTaskMessage baseMessage = JacksonUtils.parse(mafkaMessage.getBody().toString(), BatchConfigTaskMessage.class);
            if (Objects.isNull(baseMessage)) {
                log.info("messageBody is null");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            switch (baseMessage.getBatchTaskType()) {
                case QNH_DELIVERY_CONFIG_TEMPLATE_SYNC:
                    deliveryBatchConfigService.syncDeliveryConfigByTemplateTask(baseMessage.getConfigTaskId());
                    break;
                case QNH_DELIVERY_CONFIG_POI:
                    deliveryBatchConfigService.batchPoiConfigTask(baseMessage.getConfigTaskId());
                    break;
                default:
                    break;
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("消费批量配置任务消息失败:", e);
            return ConsumeStatus.RECONSUME_LATER;
        }
    }
}
