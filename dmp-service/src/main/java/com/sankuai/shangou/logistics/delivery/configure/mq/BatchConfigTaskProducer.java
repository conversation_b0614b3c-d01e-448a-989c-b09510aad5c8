package com.sankuai.shangou.logistics.delivery.configure.mq;

import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.xframe.boot.mafka.autoconfigure.annotation.MafkaProducer;
import com.sankuai.shangou.logistics.delivery.configure.pojo.msg.BatchConfigTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-01
 */
@Slf4j
@Component
public class BatchConfigTaskProducer {

    @MafkaProducer(namespace = "com.sankuai.mafka.castle.daojiacommon", topic = "batch_config_task_topic")
    private IProducerProcessor batchConfigTaskProducer;


    public void sendMessage(BatchConfigTaskMessage message) {
        String msg = null;
        try {
            msg = JacksonUtils.toJson(message);
            ProducerResult result = batchConfigTaskProducer.sendMessage(msg);
            log.info("batchConfigTask sendMessage success message={}, messageID={}", msg, result.getMessageID());
        } catch (Exception e) {
            log.error("batchConfigTask sendMessage fail, message:{}", msg, e);
        }
    }

}
