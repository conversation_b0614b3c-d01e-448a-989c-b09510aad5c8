package com.sankuai.shangou.logistics.delivery.configure.pojo.vo;

import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateDO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-26
 */
@Data
public class BatchTemplateVo {

    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 配置类型
     *
     * @see DeliveryConfigTypeEnum code
     */
    private List<Integer> configTypeList;
    /**
     * 操作时间
     */
    private Long operateTime;
    /**
     * 操作人（牵牛花账号）
     */
    private String operator;
    /**
     * 操作人姓名
     */
    private String operatorName;

    public static BatchTemplateVo convert(ConfigTemplateDO configTemplateDO, List<Integer> configTypeList) {
        BatchTemplateVo vo = new BatchTemplateVo();
        vo.setTaskId(configTemplateDO.getId());
        vo.setTaskName(configTemplateDO.getName());
        vo.setConfigTypeList(configTypeList);
        vo.setOperateTime(configTemplateDO.getCreatedAt().getTime());
        vo.setOperator(configTemplateDO.getCreateBy());
        vo.setOperatorName(configTemplateDO.getCreateName());
        return vo;
    }
}
