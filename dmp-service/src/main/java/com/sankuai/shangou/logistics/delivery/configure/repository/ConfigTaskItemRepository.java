package com.sankuai.shangou.logistics.delivery.configure.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskStatusEnum;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTaskItemModel;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTaskItemDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTaskItemDOExMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDOExample;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 配置任务明细
 * @date 2025-07-02
 */
@Service
public class ConfigTaskItemRepository {
    @Resource
    private ConfigTaskItemDOExMapper configTaskItemDOExMapper;
    @Resource
    private ConfigTaskItemDOMapper configTaskItemDOMapper;


    public List<ConfigTaskItemModel> batchQuery(Long taskId, List<Long> storeIds) {
        ConfigTaskItemDOExample example = new ConfigTaskItemDOExample();
        example.createCriteria().andTaskIdEqualTo(taskId).andStoreIdIn(storeIds);
        List<ConfigTaskItemDO> taskItemDOS = configTaskItemDOMapper.selectByExample(example);
        return taskItemDOS.stream().map(this::translate).collect(Collectors.toList());
    }

    public int batchInsert(List<ConfigTaskItemModel> itemModels) {
        if (CollectionUtils.isEmpty(itemModels)) {
            return 0;
        }
        List<ConfigTaskItemDO> itemDOS = itemModels.stream().map(this::covert).collect(Collectors.toList());
        int i = configTaskItemDOExMapper.batchInsert(itemDOS);
        // 回设id (taskId一致)
        ConfigTaskItemDO configTaskItemDO = itemDOS.get(0);
        if (Objects.nonNull(configTaskItemDO.getStoreId())) {
            Map<Long, Long> map = itemDOS.stream().collect(Collectors.toMap(ConfigTaskItemDO::getStoreId, ConfigTaskItemDO::getId));
            itemModels.forEach(item -> item.setId(map.get(item.getStoreId())));
        }
        if (Objects.nonNull(configTaskItemDO.getChannelType()) && Objects.nonNull(configTaskItemDO.getChannelStoreId())) {
            Map<String, Long> map = itemDOS.stream()
                    .collect(Collectors.toMap(item -> item.getChannelType() + "_" + item.getChannelStoreId(), ConfigTaskItemDO::getId));
            itemModels.forEach(item -> item.setId(map.get(item.getChannelType().getChannelId() + "_" + item.getChannelStoreId())));
        }
        return i;
    }

    public int batchUpdate(Collection<ConfigTaskItemModel> itemModels) {
        List<ConfigTaskItemDO> collect = itemModels.stream()
                .map(this::covert)
                .collect(Collectors.toList());
        return configTaskItemDOExMapper.batchUpdate(collect);
    }

    private ConfigTaskItemModel translate(ConfigTaskItemDO item) {
        ConfigTaskItemModel model = new ConfigTaskItemModel();
        model.setId(item.getId());
        model.setTaskId(item.getTaskId());
        model.setStoreId(item.getStoreId());
        if (item.getChannelType() != null) {
            model.setChannelType(DynamicChannelType.findOf(item.getChannelType()));
            model.setChannelStoreId(model.getChannelStoreId());
        }
        model.setChannelStoreId(item.getChannelStoreId());
        model.setStatus(BatchTaskStatusEnum.enumOf(item.getStatus()));
        model.setCreatedAt(Objects.isNull(item.getCreatedAt()) ? null : DateUtil.toLocalDateTime(item.getCreatedAt()));
        model.setUpdatedAt(Objects.isNull(item.getUpdatedAt()) ? null : DateUtil.toLocalDateTime(item.getUpdatedAt()));
        model.setFinishedAt(Objects.isNull(item.getFinishedAt()) ? null : DateUtil.toLocalDateTime(item.getFinishedAt()));
        parseExtInfo(item.getExtInfo(), model);
        return model;
    }

    private ConfigTaskItemDO covert(ConfigTaskItemModel model) {
        ConfigTaskItemDO itemDO = new ConfigTaskItemDO();
        itemDO.setId(model.getId());
        itemDO.setTaskId(model.getTaskId());
        itemDO.setStoreId(model.getStoreId());
        if (model.getChannelType() != null) {
            itemDO.setChannelType(model.getChannelType().getChannelId());
            itemDO.setChannelStoreId(model.getChannelStoreId());
        }
        itemDO.setStatus(model.getStatus().getCode());
        itemDO.setFinishedAt(DateUtil.toDate(model.getFinishedAt()));
        itemDO.setExtInfo(covertExtInfo(model));
        return itemDO;
    }


    private String covertExtInfo(ConfigTaskItemModel model) {
        Map<String, Object> extInfoMap = new HashMap<>();
        if (StringUtils.isNotBlank(model.getFailReason())) {
            extInfoMap.put("failReason", model.getFailReason());
        }
        return JSON.toJSONString(extInfoMap);
    }

    private void parseExtInfo(String extInfoStr, ConfigTaskItemModel model) {
        if (StringUtils.isBlank(extInfoStr)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(extInfoStr);
        if (jsonObject.containsKey("failReason")) {
            model.setFailReason(jsonObject.getString("failReason"));
        }
    }
}
