package com.sankuai.shangou.logistics.delivery.poi.client.dto;

import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiManageModeEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 门店基础信息
 * @date 2025-07-02
 */
@Builder
@Getter
public class PoiBaseInfo {
    private Long tenantId;

    private Long poiId;

    private String poiName;

    /**
     * 门店类型
     * @see PoiEntityTypeEnum
     */
    private Integer entityType;

    /**
     * 经营模式 1.直营 2.加盟
     * @see PoiManageModeEnum
     */
    public Integer manageMode;

    private String mobile;

    private Integer cityId;
}
