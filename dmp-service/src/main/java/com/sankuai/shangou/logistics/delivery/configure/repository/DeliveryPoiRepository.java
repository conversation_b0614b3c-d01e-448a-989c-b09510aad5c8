package com.sankuai.shangou.logistics.delivery.configure.repository;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.enums.Address;
import com.sankuai.shangou.logistics.delivery.configure.enums.CoordinatePoint;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryPoi;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.BatchTaskConfigContent;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTemplateItemModel;
import com.sankuai.shangou.logistics.delivery.configure.service.DeliveryConfigService;
import com.sankuai.shangou.logistics.delivery.configure.service.converter.DeliveryConfigConverter;
import com.sankuai.shangou.logistics.delivery.configure.value.BookingOrderLaunchTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryLaunchPoint;
import com.sankuai.shangou.logistics.delivery.configure.value.SecondDeliveryConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.shangou.logistics.delivery.configure.value.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.StoreConfigDOExMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample;
import com.sankuai.shangou.logistics.delivery.poi.client.PoiQueryClient;
import com.sankuai.shangou.logistics.delivery.poi.client.dto.PoiBaseInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.logistics.delivery.configure.value.launchpoint.ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT;
import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/28
 */
@Slf4j
@Repository
public class DeliveryPoiRepository {

    private static final int ENABLED = 1;
    private static final int NON_DELETED = 0;
    public static final String DISTANCE_IDENTIFIER = "distance";

    @Resource
    private StoreConfigDOMapper storeConfigDOMapper;
    @Resource
    private StoreConfigDOExMapper storeConfigDOExMapper;
    @Resource
    private ConfigTemplateItemRepository configTemplateItemRepository;
    @Resource
    private PoiQueryClient poiQueryClient;
    @Resource
    private DeliveryConfigConverter deliveryConfigConverter;

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public List<DeliveryPoi> queryDeliveryPoi(Long tenantId, Long storeId, List<Integer> channelTypes) {
        // 查询现有配置
        StoreConfigDOExample example = new StoreConfigDOExample();
        example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andStoreIdEqualTo(storeId)
                .andEnabledEqualTo(ENABLED);
        List<StoreConfigDO> records = storeConfigDOMapper.selectByExample(example);

        // 转换现有配置为 DeliveryPoi 对象
        List<DeliveryPoi> existingDeliveryPois = translateRecordsToDeliveryPois(records);

        // 查找缺失的渠道类型并补充配置
        List<DeliveryPoi> missingDeliveryPois = processMissingChannelTypes(tenantId, storeId, channelTypes, existingDeliveryPois);

        // 合并结果
        List<DeliveryPoi> result = new ArrayList<>(existingDeliveryPois);
        result.addAll(missingDeliveryPois);

        //再过滤一遍
        result = result
                .stream()
                .filter(deliveryPoi -> channelTypes.contains(deliveryPoi.getChannelType()))
                //单独处理预订单下发时间，见方法备注
                .collect(Collectors.toList());
        return result;
    }

    /**
     * 将数据库记录转换为 DeliveryPoi 对象列表
     */
    private List<DeliveryPoi> translateRecordsToDeliveryPois(List<StoreConfigDO> records) {
        List<DeliveryPoi> deliveryPois = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return deliveryPois;
        }

        for (StoreConfigDO configDO : records) {
            DeliveryPoi deliveryPoi = translate(configDO);
            if (Objects.nonNull(deliveryPoi)) {
                deliveryPois.add(deliveryPoi);
            }
        }
        return deliveryPois;
    }

    /**
     * 处理缺失的渠道类型，创建相应的配送配置
     */
    private List<DeliveryPoi> processMissingChannelTypes(Long tenantId, Long storeId, List<Integer> channelTypes, List<DeliveryPoi> existingDeliveryPois) {
        // 获取已存在的渠道类型
        Set<Integer> existingChannelTypes = existingDeliveryPois.stream()
                .map(DeliveryPoi::getChannelType)
                .collect(Collectors.toSet());

        // 查找美团配送配置
        DeliveryPoi meituanDeliveryPoi = findMeituanDeliveryPoi(existingDeliveryPois);

        // 处理缺失的渠道类型
        List<DeliveryPoi> missingDeliveryPois = new ArrayList<>();

        //查询租户侧门店信息
        PoiBaseInfo poiBaseInfo = null;
        Map<Integer, DeliveryConfigDetailVO.DeliveryPlatformConfigVO> channelTypePlatformConfigMap = Maps.newHashMap();
        if (!existingChannelTypes.containsAll(channelTypes)) {
            poiBaseInfo = poiQueryClient.querySinglePoiBaseInfo(tenantId, storeId);
            //1. 优先找模版
            Map<DeliveryConfigTypeEnum, ConfigTemplateItemModel> configTypeAndTemplateItemMap = configTemplateItemRepository.queryEffectiveConfigTemplateItemModel(tenantId, poiQueryClient.extractOperationMode(poiBaseInfo), DeliveryConfigTypeEnum.getChannelDimensionTypeEnums());
            if (CollectionUtils.isNotEmpty(configTypeAndTemplateItemMap.values())) {
                List<BatchTaskConfigContent> batchTaskConfigContents = IListUtils.mapTo(configTypeAndTemplateItemMap.values(), item -> new BatchTaskConfigContent(item.getTemplateType(), item.getConfigContent()));
                List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfigVOS = deliveryConfigConverter.covertDeliveryPlatformConfigVOs(batchTaskConfigContents);
                channelTypePlatformConfigMap = IListUtils.nullSafeAndOverrideCollectToMap(deliveryPlatformConfigVOS, DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType, Function.identity());
            }
        }

        for (Integer channelType : channelTypes) {
            if (existingChannelTypes.contains(channelType)) {
                continue;
            }

            DeliveryPoi deliveryPoi = createDeliveryPoiForMissingChannel(tenantId, storeId, channelType, meituanDeliveryPoi, poiBaseInfo, channelTypePlatformConfigMap.get(channelType));
            if (Objects.nonNull(deliveryPoi)) {
                missingDeliveryPois.add(deliveryPoi);
            }
        }

        return missingDeliveryPois;
    }

    /**
     * 查找美团配送配置
     */
    private DeliveryPoi findMeituanDeliveryPoi(List<DeliveryPoi> deliveryPois) {
        return deliveryPois.stream()
                .filter(poi -> poi.getChannelType().equals(DynamicChannelType.MEITUAN.getChannelId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 为缺失的渠道创建配送配置
     */
    public DeliveryPoi createDeliveryPoiForMissingChannel(Long tenantId, Long storeId, Integer channelType, DeliveryPoi meituanDeliveryPoi, PoiBaseInfo poiBaseInfo, DeliveryConfigDetailVO.DeliveryPlatformConfigVO deliveryPlatformConfigVO) {

        DeliveryPoi deliveryPoi;

        //2. 没有走默认逻辑
        if (meituanDeliveryPoi == null) {
            deliveryPoi = tryMakeUpMissingSelfBuiltDeliveryPoi(tenantId, storeId, channelType, poiBaseInfo, deliveryPlatformConfigVO);
        } else {
            deliveryPoi = mtDeliveryPoiToOther(tenantId, storeId, meituanDeliveryPoi, channelType, deliveryPlatformConfigVO);
            saveDeliveryPoi(deliveryPoi);
        }
        return deliveryPoi;
    }

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public List<DeliveryPoi> batchQueryDeliveryPoi(Long tenantId, List<Long> storeIds, List<Integer> channelTypes) {
        StoreConfigDOExample example = new StoreConfigDOExample();
        example.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andStoreIdIn(storeIds)
                .andChannelTypeIn(channelTypes)
                .andEnabledEqualTo(ENABLED);
        List<StoreConfigDO> records = storeConfigDOMapper.selectByExample(example);

        List<DeliveryPoi> resultList = records.stream()
                .map(this::translate)
                .collect(Collectors.toList());

        Map<Long, List<DeliveryPoi>> map = resultList.stream().collect(groupingBy(DeliveryPoi::getStoreId));

        // 初始化一个空的Map，用于存储需要初始化的数据。
        Map<Long/*storeId*/, List<Integer>/*channelTypes*/> initMap = new HashMap<>();
        storeIds.forEach(storeId -> {
            List<Integer> channelTypeInDB = map.get(storeId).stream().map(DeliveryPoi::getChannelType).collect(Collectors.toList());
            for (Integer channelType : channelTypes) {
                if (channelTypeInDB.contains(channelType)) {
                    continue;
                }
                initMap.computeIfAbsent(storeId, k -> new ArrayList<>()).add(channelType);
            }
        });

        if (MapUtils.isEmpty(initMap)) {
            return resultList;
        }

        List<DeliveryPoi> initDeliveryPois = batchTryMakeUpMissingSelfBuiltDeliveryPoi(tenantId, initMap);
        resultList.addAll(initDeliveryPois);
        return resultList;
    }


    /**
     * isQueryTenantAndStoreConfig参数在translateSelfBuiltDeliveryPoi方法里生效
     * 如果isQueryTenantAndStoreConfig为true,构建SelfBuiltDeliveryPoi时,查tenantConfigMap和storeConfigMap会查DB
     * 因为translate方法会在外部被循环调用,有些查询场景不需要tenantConfigMap和storeConfigMap,此时isQueryTenantAndStoreConfig可以传false
     */
    private DeliveryPoi translate(StoreConfigDO record) {
        if (record == null) {
            return null;
        }

        DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(record.getOpenAggrPlatform());
        if (deliveryPlatform == null) {
            log.error("Unknown deliveryPlatform[{}] from store[{}], will treat as null", record.getOpenAggrPlatform(), record.getStoreId());
            return null;
        }


        return translateDeliveryPoi(record, deliveryPlatform);
    }


    private DeliveryPoi tryMakeUpMissingSelfBuiltDeliveryPoi(Long tenantId, Long storeId, Integer channelType, PoiBaseInfo poiBaseInfo, DeliveryConfigDetailVO.DeliveryPlatformConfigVO deliveryPlatformConfigVO) {

        if (poiBaseInfo == null) {
            return null;
        }
        DeliveryPoi deliveryPoi = builtInitDeliveryPoi(tenantId, storeId, channelType, poiBaseInfo, deliveryPlatformConfigVO);
        saveDeliveryPoi(deliveryPoi);

        return deliveryPoi;
    }

    private DeliveryPoi mtDeliveryPoiToOther(Long tenantId, Long storeId, DeliveryPoi mtDeliveryPoi, Integer channelType, DeliveryConfigDetailVO.DeliveryPlatformConfigVO deliveryPlatformConfigVO) {
        DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig jdLunchPointConfig = new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 0);
        if (mtDeliveryPoi == null || mtDeliveryPoi.getDeliveryPlatform() != DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM) {
            DeliveryLaunchPoint deliveryLaunchPoint = new DeliveryLaunchPoint();
            if (channelType == DynamicChannelType.JD2HOME.getChannelId()) {
                deliveryLaunchPoint.setImmediateOrderDeliveryLaunchPointConfig(jdLunchPointConfig);
            }
            return new DeliveryPoi(
                    null,
                    tenantId,
                    storeId,
                    null,
                    null,
                    DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM,
                    deliveryLaunchPoint,
                    DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY,
                    channelType,
                    null,
                    DeliveryIsShowItemNumberEnum.NO,
                    null,
                    null,
                    null
            );
        }

        DeliveryLaunchPoint deliveryLaunchPoint = new DeliveryLaunchPoint();
        if (channelType == DynamicChannelType.JD2HOME.getChannelId()) {
            deliveryLaunchPoint.setImmediateOrderDeliveryLaunchPointConfig(jdLunchPointConfig);
        }
        return new DeliveryPoi(
                null,
                tenantId,
                storeId,
                mtDeliveryPoi.getCityCode(),
                mtDeliveryPoi.getContactPhone(),
                DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM,
                deliveryLaunchPoint,
                mtDeliveryPoi.getDeliveryLaunchType(),
                channelType,
                mtDeliveryPoi.getDeliveryPlatform(),
                DeliveryIsShowItemNumberEnum.NO,
                mtDeliveryPoi.getStoreAddress(),
                mtDeliveryPoi.getSelfAssessDeliveryConfig(),
                mtDeliveryPoi.getSecondDeliveryPlatform()
        );
    }

    private List<DeliveryPoi> batchTryMakeUpMissingSelfBuiltDeliveryPoi(Long tenantId, Map<Long/*storeId*/, List<Integer>/*channelTypes*/> initMap) {
        //查询租户侧门店信息
        Map<Long, PoiBaseInfo> poiBaseInfoMap = poiQueryClient.queryPoiBaseInfoMap(tenantId, new ArrayList<>(initMap.keySet()));
        List<DeliveryPoi> initDeliveryPoiList = new ArrayList<>();
        initMap.forEach((storeId, channelTypes) -> {
            channelTypes.forEach(channelType -> initDeliveryPoiList.add(builtInitDeliveryPoi(tenantId, storeId, channelType, poiBaseInfoMap.get(storeId), null)));
        });
        batchSaveDeliveryPoi(initDeliveryPoiList);
        return initDeliveryPoiList;
    }

    private DeliveryPoi builtInitDeliveryPoi(Long tenantId, Long storeId, Integer channelType, PoiBaseInfo poiBaseInfo, DeliveryConfigDetailVO.DeliveryPlatformConfigVO deliveryPlatformConfigVO) {
        DeliveryLaunchPoint deliveryLaunchPoint = new DeliveryLaunchPoint(
                new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(MERCHANT_ACCEPT, 0),
                new DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig()
        );
        if (channelType == DynamicChannelType.JD2HOME.getChannelId()) {
            deliveryLaunchPoint.setImmediateOrderDeliveryLaunchPointConfig(new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 0));
        }
        //补建一个配送门店，并保存
        DeliveryPoi deliveryPoi = new DeliveryPoi(
                null,
                tenantId,
                storeId,
                Optional.ofNullable(poiBaseInfo).map(PoiBaseInfo::getCityId).orElse(null),
                Optional.ofNullable(poiBaseInfo).map(PoiBaseInfo::getMobile).orElse(null),
                DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM,
                deliveryLaunchPoint,
                DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY,
                channelType,
                null,
                DeliveryIsShowItemNumberEnum.NO,
                null,
                null,
                null
        );

        //用模版的覆盖
        if (Objects.nonNull(deliveryPlatformConfigVO)) {
            deliveryPoi = deliveryConfigConverter.updateDeliveryPoiFromRequest(tenantId, storeId, deliveryPoi, deliveryPlatformConfigVO);
        }

        return deliveryPoi;
    }


    public void saveDeliveryPoi(DeliveryPoi deliveryPoi) {
        Preconditions.checkNotNull(deliveryPoi, "deliveryPoi is null");

        StoreConfigDO storeConfigDO = translate(deliveryPoi);
        if (Objects.nonNull(deliveryPoi.getId())) {
            storeConfigDOMapper.updateByPrimaryKeySelective(storeConfigDO);
        } else {
            storeConfigDOMapper.insertSelective(storeConfigDO);
            deliveryPoi.setId(storeConfigDO.getId());
        }
    }

    public int batchUpdateById(List<DeliveryPoi> deliveryPois) {
        if (CollectionUtils.isEmpty(deliveryPois)) {
            return 0;
        }
        List<StoreConfigDO> storeConfigDOS = deliveryPois.stream()
                .map(this::translate)
                .peek(item -> item.setUpdateTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant())))
                .collect(Collectors.toList());
        return storeConfigDOExMapper.batchUpdateByPrimaryKey(storeConfigDOS);
    }

    private void batchSaveDeliveryPoi(List<DeliveryPoi> initDeliveryPoiList) {
        List<StoreConfigDO> list = initDeliveryPoiList.stream().map(this::translate).collect(Collectors.toList());
        storeConfigDOMapper.batchInsert(list);
        initDeliveryPoiList.forEach(deliveryPoi -> {
            StoreConfigDO storeConfigDO = list.stream()
                    .filter(item -> item.getStoreId().equals(deliveryPoi.getStoreId()))
                    .filter(item -> item.getChannelType().equals(deliveryPoi.getChannelType()))
                    .findFirst().orElse(new StoreConfigDO());
            deliveryPoi.setId(storeConfigDO.getId());
        });
    }


    private DeliveryLaunchPoint translateDeliveryLaunchPoint(StoreConfigDO record) {
        return new DeliveryLaunchPoint(
                new DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig(
                        ImmediateOrderDeliveryLaunchPointEnum.enumOf(record.getDeliveryLaunchPoint()),
                        record.getDeliveryLaunchDelayMinutes()
                ),
                new DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig(
                        BookingOrderDeliveryLaunchPointEnum.enumOf(record.getBookingOrderDeliveryLaunchPoint()),
                        record.getBookingOrderDeliveryLaunchMinutes()
                )
        );
    }


    private DeliveryPoi translateDeliveryPoi(StoreConfigDO record, DeliveryPlatformEnum deliveryPlatform) {
        DeliveryPoi deliveryPoi = new DeliveryPoi(
                record.getId(),
                record.getTenantId(),
                record.getStoreId(),
                record.getCityCode(),
                record.getContactPhone(),
                deliveryPlatform,
                translateDeliveryLaunchPoint(record),
                DeliveryLaunchTypeEnum.enumOf(record.getIsAutoLaunch()),
                record.getChannelType(),
                DeliveryPlatformEnum.enumOf(record.getLastPlatformType()),
                DeliveryIsShowItemNumberEnum.enumOf(record.getIsShowItemNumber()),
                fromAddressJson(record.getAddress()),
                parseFromJsonFiled(record.getSelfAssessDeliveryConfig(), BookingOrderLaunchTimeConfig.class),
                parseFromJsonFiled(record.getSecondDeliveryPlatform(), SecondDeliveryConfig.class)
        );
        return deliveryPoi;
    }

    private <T> T parseFromJsonFiled(String json, Class<T> clazz) {
        try {
            if (StringUtils.isBlank(json)) {
                return null;
            }
            return JSON.parseObject(json, clazz);
        } catch (Exception e) {
            log.error("parse json filed error", e);
            return null;
        }
    }

    private static Address fromAddressJson(String json) {
        AddressJson dto = JsonUtil.fromJson(json, AddressJson.class);
        if (dto == null) {
            return null;

        } else {
            return new Address(
                    dto.getAddressDetail(),
                    CoordinateTypeEnum.valueOf(dto.getCoordinateType()),
                    StringUtils.isNotBlank(dto.getLongitude()) && StringUtils.isNotBlank(dto.getLatitude()) ?
                            new CoordinatePoint(dto.getLongitude(), dto.getLatitude()) : null
            );
        }
    }

    public static String toAddressJson(Address address) {
        if (address == null) {
            return null;
        }

        return JsonUtil.toJson(
                new AddressJson(
                        address.getAddressDetail(),
                        address.getCoordinateType().getCode(),
                        Optional.ofNullable(address.getCoordinatePoint()).map(CoordinatePoint::getLongitude).orElse(null),
                        Optional.ofNullable(address.getCoordinatePoint()).map(CoordinatePoint::getLatitude).orElse(null)
                )
        );
    }

    private StoreConfigDO translate(DeliveryPoi deliveryPoi) {
        StoreConfigDO record = new StoreConfigDO();
        record.setId(deliveryPoi.getId());
        record.setTenantId(deliveryPoi.getTenantId());
        record.setStoreId(deliveryPoi.getStoreId());
        record.setContactPhone(deliveryPoi.getContactPhone());
        record.setDeliveryLaunchPoint(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode());
        record.setDeliveryLaunchDelayMinutes(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes());
        record.setBookingOrderDeliveryLaunchPoint(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode());
        record.setBookingOrderDeliveryLaunchMinutes(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes());
        record.setEnabled(ENABLED);
        record.setIsAutoLaunch(deliveryPoi.getDeliveryLaunchType().getCode());
        record.setOpenAggrPlatform(deliveryPoi.getDeliveryPlatform().getCode());
        record.setCityCode(deliveryPoi.getCityCode());
        record.setChannelType(deliveryPoi.getChannelType());
        record.setAddress(toAddressJson(deliveryPoi.getStoreAddress()));

        // 设置配送相关配置
        record.setSelfAssessDeliveryConfig(JSON.toJSONString(deliveryPoi.getSelfAssessDeliveryConfig()));
        record.setSecondDeliveryPlatform(JSON.toJSONString(deliveryPoi.getSecondDeliveryPlatform()));

        if (deliveryPoi.getLastDeliveryPlatform() != null) {
            record.setLastPlatformType(deliveryPoi.getLastDeliveryPlatform().getCode());
        }

        if (deliveryPoi.getDeliveryIsShowItemNumberEnum() != null) {
            record.setIsShowItemNumber(deliveryPoi.getDeliveryIsShowItemNumberEnum().getCode());
        }

        return record;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressJson {

        private String addressDetail;

        private Integer coordinateType;

        private String longitude;

        private String latitude;

    }

}
