package com.sankuai.shangou.logistics.delivery.configure.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.logistics.delivery.common.enums.DimensionEnum;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryBatchConfigService;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskStatusEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskTypeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryDimensionPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeModel;
import com.sankuai.shangou.logistics.delivery.configure.mq.BatchConfigTaskProducer;
import com.sankuai.shangou.logistics.delivery.configure.pojo.dto.BatchTaskCountDTO;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.BatchTaskConfigContent;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTaskItemModel;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTaskModel;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTemplateModel;
import com.sankuai.shangou.logistics.delivery.configure.pojo.msg.BatchConfigTaskMessage;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.DeliveryConfigBatchSaveParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.DeliveryConfigTaskDetailParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.QueryBatchTaskItemPageParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.QueryBatchTaskParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTaskItemVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTaskVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTemplateVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.DeliveryConfigTaskDetailVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.PoiVo;
import com.sankuai.shangou.logistics.delivery.configure.repository.ConfigTaskItemRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.ConfigTaskRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.ConfigTemplateItemRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.ConfigTemplateRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryDimensionPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.service.converter.DeliveryConfigConverter;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.DapChannelServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.MaltFarmChannelServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.value.BookingOrderLaunchTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryCompleteMode;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryLaunchPoint;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryPlatformConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryRemindConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.NavigationConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.RiderTransConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.SecondDeliveryForbiddenRuleConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.SecondDeliveryWayConfig;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTaskItemDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTaskDOExMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTemplateDOExMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskItemDOExample;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.condition.BatchTaskQuery;
import com.sankuai.shangou.logistics.delivery.dao.config.model.condition.BatchTemplateQuery;
import com.sankuai.shangou.logistics.delivery.enums.OperationModeEnum;
import com.sankuai.shangou.logistics.delivery.poi.client.PoiQueryClient;
import com.sankuai.shangou.logistics.delivery.poi.client.dto.PoiBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @description 配送配置服务
 * @date 2025-06-25
 */
@Slf4j
@Service
public class DeliveryBatchConfigServiceImpl implements DeliveryBatchConfigService {
    @Resource
    private ConfigTemplateDOExMapper configTemplateDOExMapper;
    @Resource
    private ConfigTaskDOExMapper configTaskDOExMapper;
    @Resource
    private ConfigTemplateItemRepository configTemplateItemRepository;
    @Resource
    private ConfigTemplateRepository configTemplateRepository;
    @Resource
    private BatchConfigTaskProducer batchConfigTaskProducer;
    @Resource
    private ConfigTaskRepository configTaskRepository;
    @Resource
    private ConfigTaskItemRepository configTaskItemRepository;
    @Resource
    private PoiQueryClient poiQueryClient;
    @Resource
    private ConfigTaskItemDOMapper configTaskItemDOMapper;
    @Resource
    private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private DeliveryConfigConverter deliveryConfigConverter;
    @Resource
    private MaltFarmChannelServiceWrapper maltfarmChannelServiceWrapper;
    @Resource
    private DapChannelServiceWrapper dapChannelServiceWrapper;

    private static final int BATCH_SIZE = 500;

    @Override
    public PageInfo<BatchTemplateVo> queryBatchTemplateList(QueryBatchTaskParam req, Long tenantId) {
        if (!DimensionEnum.isTemplateEnums(req.getTabType())) {
            throw new BizException("不支持的维度类型");
        }

        PageInfo<ConfigTemplateDO> pageInfo;
        BatchTemplateQuery batchTemplateQuery = buildBatchTemplateQuery(req, tenantId);
        try (Page<ConfigTemplateDO> page = PageHelper.startPage(req.getPageNum(), req.getPageSize())) {
            if (CollectionUtils.isEmpty(req.getConfigTypeList())) {
                // 不带配置类型
                List<ConfigTemplateDO> list = configTemplateDOExMapper.queryBatchTaskListExcludeConfigType(batchTemplateQuery);
                pageInfo = new PageInfo<>(list);
            } else {
                // 带配置类型
                List<ConfigTemplateDO> list = configTemplateDOExMapper.queryBatchTaskListIncludeConfigType(batchTemplateQuery);
                pageInfo = new PageInfo<>(list);
            }
        }

        Map<Long, List<Integer>> itemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            List<Long> templateIds = pageInfo.getList().stream().map(ConfigTemplateDO::getId).collect(Collectors.toList());
            itemMap = configTemplateItemRepository.queryTemplateTypeMap(templateIds);
        }
        PageInfo<BatchTemplateVo> page = new PageInfo<>();
        page.setPageNum(pageInfo.getPageNum());
        page.setPageSize(pageInfo.getPageSize());
        page.setTotal(pageInfo.getTotal());
        List<BatchTemplateVo> list = new ArrayList<>();
        for (ConfigTemplateDO configTemplateDO : pageInfo.getList()) {
            BatchTemplateVo vo = BatchTemplateVo.convert(configTemplateDO, itemMap.get(configTemplateDO.getId()));
            list.add(vo);
        }
        page.setList(list);
        return page;
    }

    @Override
    public PageInfo<BatchTaskVo> queryBatchTaskPoiSettingList(QueryBatchTaskParam req, Long tenantId) {
        if (DimensionEnum.isTemplateEnums(req.getTabType())) {
            throw new BizException("不支持的维度类型");
        }
        BatchTaskQuery batchTemplateQuery = buildBatchTaskQuery(req, tenantId);
        try (Page<ConfigTaskDO> page = PageHelper.startPage(req.getPageNum(), req.getPageSize())) {
            List<ConfigTaskDO> list = configTaskDOExMapper.queryBatchTaskList(batchTemplateQuery);
            PageInfo<ConfigTaskDO> configTaskDOPageInfo = new PageInfo<>(list);
            PageInfo<BatchTaskVo> pageInfo = new PageInfo<>();
            pageInfo.setPageNum(pageInfo.getPageNum());
            pageInfo.setPageSize(pageInfo.getPageSize());
            pageInfo.setTotal(pageInfo.getTotal());
            pageInfo.setList(configTaskDOPageInfo.getList().stream().map(BatchTaskVo::convert).collect(Collectors.toList()));
            return pageInfo;
        }
    }

    @Override
    public DeliveryConfigTaskDetailVo queryBatchTaskDetail(DeliveryConfigTaskDetailParam param) {
        DeliveryConfigTaskDetailVo vo = new DeliveryConfigTaskDetailVo();
        List<BatchTaskConfigContent> configContents;
        if (DimensionEnum.isTemplateEnums(param.getTabType())) {
            ConfigTemplateModel configTemplateModel = configTemplateRepository.selectByPrimaryKey(param.getTaskId(), true);
            configContents = configTemplateModel.getConfigContents();
            vo.setTaskId(configTemplateModel.getId());
            vo.setTaskName(configTemplateModel.getName());
            vo.setIsSyncWhenSave(configTemplateModel.getIsSyncWhenCreate());
        } else if (Objects.equals(DimensionEnum.POI.getCode(), param.getTabType())) {
            ConfigTaskModel configTaskModel = configTaskRepository.selectByPrimaryKey(param.getTaskId());
            configContents = configTaskModel.getConfigContents();
            List<Long> poiIds = configTaskModel.getPoiIds();
            List<PoiBaseInfo> poiBaseInfos = poiQueryClient.queryPoiBaseInfo(configTaskModel.getTenantId(), poiIds);
            List<PoiVo> poiVoList = poiBaseInfos.stream().map(item -> new PoiVo(item.getPoiId(), item.getPoiName())).collect(Collectors.toList());
            vo.setTaskId(configTaskModel.getId());
            vo.setTaskName(configTaskModel.getTaskName());
            vo.setIsSyncWhenSave(true);
            vo.setPoiList(poiVoList);
        } else {
            throw new BizException("不支持的维度类型");
        }
        log.info("queryBatchTaskDetail configContents:{}", JacksonUtils.toJson(configContents));
        List<Integer> configTypeList = configContents.stream()
                .map(item -> item.getType().getCode())
                .collect(Collectors.toList());
        vo.setConfigTypeList(configTypeList);
        List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfigVos = deliveryConfigConverter.covertDeliveryPlatformConfigVOs(configContents);
        vo.setDeliveryPlatformConfig(deliveryPlatformConfigVos);
        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig = deliveryConfigConverter.covertSelfDeliveryConfigVO(configContents);
        vo.setSelfDeliveryConfig(selfDeliveryConfig);
        log.info("queryBatchTaskDetail vo:{}", JacksonUtils.toJson(vo));
        return vo;
    }

    @Override
    public PageInfo<BatchTaskItemVo> queryBatchTaskItemList(QueryBatchTaskItemPageParam param) {
        Long taskId;
        if (DimensionEnum.isTemplateEnums(param.getTabType())) {
            ConfigTaskModel configTaskModel = configTaskRepository.queryByConfigTemplateId(param.getTaskId());
            if (Objects.isNull(configTaskModel)) {
                log.info("queryBatchTaskItemList configTaskModel is null, param:{}", JacksonUtils.toJson(param));
                return new PageInfo<>();
            }
            taskId = configTaskModel.getId();
        } else if (Objects.equals(DimensionEnum.POI.getCode(), param.getTabType())) {
            taskId = param.getTaskId();
        } else {
            throw new BizException("不支持的维度类型");
        }
        ConfigTaskItemDOExample example = new ConfigTaskItemDOExample();
        example.createCriteria().andTaskIdEqualTo(taskId);
        try (Page<ConfigTaskItemDO> page = PageHelper.startPage(param.getPageNum(), param.getPageSize())) {
            List<ConfigTaskItemDO> itemList = configTaskItemDOMapper.selectByExample(example);
            PageInfo<ConfigTaskItemDO> pageInfo = new PageInfo<>(itemList);
            Map<Long, String> poiNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(itemList)) {
                List<Long> poiIds = itemList.stream().map(ConfigTaskItemDO::getStoreId).collect(Collectors.toList());
                List<PoiBaseInfo> poiBaseInfos = poiQueryClient.queryPoiBaseInfo(null, poiIds);
                poiBaseInfos.forEach(item -> poiNameMap.put(item.getPoiId(), item.getPoiName()));
            }
            PageInfo<BatchTaskItemVo> result = new PageInfo<>();
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setTotal(pageInfo.getTotal());
            result.setList(pageInfo.getList().stream().map(item -> {
                BatchTaskItemVo vo = new BatchTaskItemVo();
                vo.setPoiId(item.getStoreId());
                vo.setPoiName(poiNameMap.get(item.getStoreId()));
                vo.setStatus(item.getStatus());
                vo.setUpdateTime(item.getUpdatedAt().getTime());
                return vo;
            }).collect(Collectors.toList()));
            return result;
        }
    }

    @Override
    public void batchSaveQnhConfig(DeliveryConfigBatchSaveParam param) {
        List<BatchTaskConfigContent> contents = covertVo2Item(param);
        ConfigTaskModel taskModel = new ConfigTaskModel();
        taskModel.setTenantId(param.getTenantId());
        taskModel.setTaskName(param.getTaskName());
        taskModel.setOperator(param.getOperator());
        taskModel.setOperatorName(param.getOperatorName());
        // 租户或者经营模式，保存模版信息
        if (DimensionEnum.isTemplateEnums(param.getTabType())) {
            Long templateId = saveTemplateInfo(param, contents);
            if (Boolean.TRUE.equals(param.getIsSyncWhenSave())) {
                taskModel.setConfigTemplateId(templateId);
                taskModel.setTaskType(BatchTaskTypeEnum.QNH_DELIVERY_CONFIG_TEMPLATE_SYNC);
                taskModel.setStatus(BatchTaskStatusEnum.INIT);
                configTaskRepository.createBatchTask(taskModel);
                // 异步处理
                BatchConfigTaskMessage message = new BatchConfigTaskMessage();
                message.setBatchTaskType(BatchTaskTypeEnum.QNH_DELIVERY_CONFIG_TEMPLATE_SYNC);
                message.setConfigTaskId(taskModel.getId());
                batchConfigTaskProducer.sendMessage(message);
            }
        } else if (Objects.equals(DimensionEnum.POI.getCode(), param.getTabType())) {
            // 指定门店设置
            taskModel.setPoiIds(param.getPoiList());
            taskModel.setConfigTemplateId(0L);
            taskModel.setTaskType(BatchTaskTypeEnum.QNH_DELIVERY_CONFIG_POI);
            taskModel.setStatus(BatchTaskStatusEnum.INIT);
            taskModel.setConfigContents(contents);
            configTaskRepository.createBatchTask(taskModel);

            // 异步处理
            BatchConfigTaskMessage message = new BatchConfigTaskMessage();
            message.setBatchTaskType(BatchTaskTypeEnum.QNH_DELIVERY_CONFIG_POI);
            message.setConfigTaskId(taskModel.getId());
            batchConfigTaskProducer.sendMessage(message);
        } else {
            throw new BizException("不支持的tab类型");
        }
    }

    @Override
    public void syncDeliveryConfigByTemplateTask(Long configTaskId) {
        ConfigTaskModel configTaskModel = configTaskRepository.selectByPrimaryKey(configTaskId);
        if (!taskCheck(configTaskModel)) return;
        ConfigTemplateModel configTemplateModel = configTemplateRepository.selectByPrimaryKey(configTaskModel.getConfigTemplateId(), true);
        if (Objects.isNull(configTemplateModel)) {
            log.info("模版不存在");
            return;
        }

        Long tenantId = configTaskModel.getTenantId();
        List<PoiBaseInfo> poiBaseInfos;
        if (configTemplateModel.getDimensionType() == DimensionEnum.TENANT) {
            // 查询租户下所有门店和共享前置仓
            poiBaseInfos = poiQueryClient.queryAllStoreByTenant(tenantId);
        } else if (configTemplateModel.getDimensionType() == DimensionEnum.OPERATION_MODE) {
            // 查询经营模式下所有门店和共享前置仓
            poiBaseInfos = poiQueryClient.queryAllStoreByManageMode(tenantId, configTemplateModel.getOperationMode().getType());
        } else {
            poiBaseInfos = new ArrayList<>();
            log.info("不支持的维度类型");
        }
        List<Long> poiIds = poiBaseInfos.stream().map(PoiBaseInfo::getPoiId).collect(Collectors.toList());
        extractedBatchTsk(configTaskId, poiIds, configTemplateModel.getConfigContents(), tenantId, configTaskModel);
    }

    @Override
    public void batchPoiConfigTask(Long configTaskId) {
        ConfigTaskModel configTaskModel = configTaskRepository.selectByPrimaryKey(configTaskId);
        if (!taskCheck(configTaskModel)) return;

        List<Long> poiList = configTaskModel.getPoiIds();
        extractedBatchTsk(configTaskId, poiList, configTaskModel.getConfigContents(), configTaskModel.getTenantId(), configTaskModel);
    }

    private List<BatchTaskConfigContent> covertVo2Item(DeliveryConfigBatchSaveParam param) {
        List<BatchTaskConfigContent> contents = new ArrayList<>();
        List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfigVoList = param.getDeliveryPlatformConfig();
        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig = param.getSelfDeliveryConfig();
        param.getConfigTypeList().forEach(type -> {
            BatchTaskConfigContent content = new BatchTaskConfigContent();
            content.setType(DeliveryConfigTypeEnum.enumOf(type));
            String jsonStr = "";
            switch (DeliveryConfigTypeEnum.enumOf(type)) {
                case RULES:
                    Map<String, Object> ruleMap = new HashMap<>();
                    deliveryPlatformConfigVoList.forEach(item -> {
                        DeliveryLaunchPoint deliveryLaunchPoint = deliveryConfigConverter.buildDeliveryLaunchPointFromRequest(item);
                        BookingOrderLaunchTimeConfig bookingOrderLaunchTimeConfig = deliveryConfigConverter.buildBookingPushDownTimeConfigFromRequest(item.getSelfDeliveryBookingDeliveryRule());
                        DeliveryPlatformConfig deliveryPlatformConfig = new DeliveryPlatformConfig();
                        deliveryPlatformConfig.setChannelType(item.getChannelType());
                        deliveryPlatformConfig.setPlatformCode(item.getPlatformCode());
                        deliveryPlatformConfig.setStatus(item.getStatus());
                        deliveryPlatformConfig.setDeliveryLaunchDelayMinutes(item.getDeliveryLaunchDelayMinutes());
                        deliveryPlatformConfig.setBookingOrderDeliveryLaunchMinutes(item.getBookingOrderDeliveryLaunchMinutes());
                        deliveryPlatformConfig.setDeliveryLaunchPoint(deliveryLaunchPoint);
                        deliveryPlatformConfig.setBookingOrderLaunchTimeConfig(bookingOrderLaunchTimeConfig);
                        ruleMap.put(String.valueOf(item.getChannelType()), deliveryPlatformConfig);
                    });
                    jsonStr = JSONObject.toJSONString(ruleMap);
                    break;
                case RULES_HELPER_WAY:
                    Map<String, Object> wayMap = new HashMap<>();
                    deliveryPlatformConfigVoList.forEach(item -> {
                        DeliveryConfigDetailVO.SecondDeliveryConfigVO secondDeliveryConfig = item.getSecondDeliveryConfig();
                        SecondDeliveryWayConfig secondDeliveryWayConfig = new SecondDeliveryWayConfig();
                        secondDeliveryWayConfig.setChannelType(item.getChannelType());
                        secondDeliveryWayConfig.setSecondPlatformCodeCodes(secondDeliveryConfig.getSecondPlatformCode());
                        wayMap.put(String.valueOf(item.getChannelType()), secondDeliveryWayConfig);
                    });
                    jsonStr = JSONObject.toJSONString(wayMap);
                    break;
                case RULES_HELPER_RULES:
                    Map<String, Object> helperRuleMap = new HashMap<>();
                    deliveryPlatformConfigVoList.forEach(item -> {
                        DeliveryConfigDetailVO.SecondDeliveryConfigVO secondDeliveryConfig = item.getSecondDeliveryConfig();
                        DeliveryConfigDetailVO.ForbiddenConditionVO forbiddenCondition = secondDeliveryConfig.getForbiddenCondition();
                        SecondDeliveryForbiddenRuleConfig secondDeliveryForbiddenRuleConfig = new SecondDeliveryForbiddenRuleConfig();
                        secondDeliveryForbiddenRuleConfig.setChannelType(item.getChannelType());
                        secondDeliveryForbiddenRuleConfig.setOrderTags(forbiddenCondition.getOrderTags());
                        secondDeliveryForbiddenRuleConfig.setOrderActualPayment(forbiddenCondition.getOrderActualPayment());
                        helperRuleMap.put(String.valueOf(item.getChannelType()), secondDeliveryForbiddenRuleConfig);
                    });
                    jsonStr = JSONObject.toJSONString(helperRuleMap);
                    break;
                case DISPLAY_TIME_REMAINING:
                    List<DeliveryConfigDetailVO.AssertTimeVO> assertTimes = selfDeliveryConfig.getAssertTime();
                    if (CollectionUtils.isEmpty(assertTimes)) {
                        break;
                    }
                    List<AssessTimeConfig> assessTimeConfigs = deliveryConfigConverter.buildAssessTimeConfigFromRequest(assertTimes);
                    AssessTimeModel assessTimeModel = new AssessTimeModel(assessTimeConfigs);
                    jsonStr = JSONObject.toJSONString(assessTimeModel);
                    break;
                case DISPLAY_CONFIRM_ACTION:
                    DeliveryConfigDetailVO.DeliveryCompleteModeVO deliveryCompleteModeVo = selfDeliveryConfig.getDeliveryCompleteMode();
                    DeliveryCompleteMode deliveryCompleteMode = deliveryConfigConverter.buildDeliveryCompleteModeFromRequest(deliveryCompleteModeVo);
                    jsonStr = JSONObject.toJSONString(deliveryCompleteMode);
                    break;
                case DISPLAY_RIDER_TRANS:
                    List<Long> riderTransRoles = selfDeliveryConfig.getRiderTransRoles();
                    RiderTransConfig riderTransConfig = new RiderTransConfig();
                    riderTransConfig.setRiderTransRoles(Objects.isNull(riderTransRoles) ? Collections.emptyList() : riderTransRoles);
                    jsonStr = JSONObject.toJSONString(riderTransConfig);
                    break;
                case DISPLAY_NAVIGATION:
                    Integer navigationMode = selfDeliveryConfig.getInternalNavigationMode();
                    NavigationConfig navigationConfig = new NavigationConfig(navigationMode);
                    jsonStr = JSONObject.toJSONString(navigationConfig);
                    break;
                case REMIND:
                    DeliveryConfigDetailVO.DeliveryRemindConfigVO deliveryRemindConfigVo = selfDeliveryConfig.getDeliveryRemindConfig();
                    DeliveryRemindConfig deliveryRemindConfig = deliveryConfigConverter.buildDeliveryRemindConfigFromRequest(deliveryRemindConfigVo);
                    jsonStr = JSONObject.toJSONString(deliveryRemindConfig);
                    break;
                default:
                    break;
            }
            if (StringUtils.isNotBlank(jsonStr)) {
                content.setContent(JSONObject.parseObject(jsonStr));
            }
            contents.add(content);
        });
        return contents;
    }

    private void extractedBatchTsk(Long configTaskId, List<Long> poiIds, List<BatchTaskConfigContent> configContents, Long tenantId, ConfigTaskModel configTaskModel) {
        // 初始化任务明细
        List<ConfigTaskItemModel> taskItems = poiIds.stream().map(poiId -> {
            ConfigTaskItemModel itemModel = new ConfigTaskItemModel();
            itemModel.setTaskId(configTaskId);
            itemModel.setStoreId(poiId);
            itemModel.setStatus(BatchTaskStatusEnum.PROCESSING);
            itemModel.setFinishedAt(LocalDateTime.now());
            return itemModel;
        }).collect(Collectors.toList());
        Lists.partition(taskItems, BATCH_SIZE).forEach(list -> configTaskItemRepository.batchInsert(list));
        Map<Long, ConfigTaskItemModel> taskItemMap = taskItems.stream().collect(Collectors.toMap(ConfigTaskItemModel::getStoreId, Function.identity()));

        // 执行任务
        BatchTaskCountDTO countDto = new BatchTaskCountDTO();
        Lists.partition(poiIds, BATCH_SIZE).forEach(storeIds -> {
            Map<Long, ConfigTaskItemModel> partitionTaskItemMap = new HashMap<>(storeIds.size());
            storeIds.forEach(storeId -> partitionTaskItemMap.put(storeId, taskItemMap.get(storeId)));
            executeDeliveryConfigTask(partitionTaskItemMap, tenantId, storeIds, configContents, countDto);
        });

        // 更新任务计数
        countDto.setTotalNum(taskItems.size());
        configTaskModel.setTotalNum(countDto.getTotalNum());
        configTaskModel.setSuccessNum(countDto.getSuccessNum());
        configTaskModel.setFailNum(countDto.getFailNum());
        configTaskRepository.completed(configTaskModel);
    }

    private void executeDeliveryConfigTask(Map<Long, ConfigTaskItemModel> taskItemMap, Long tenantId, List<Long> poiIds, List<BatchTaskConfigContent> configContents, BatchTaskCountDTO countDto) {
        Long taskId = taskItemMap.values().iterator().next().getTaskId();
        try {
            // 自送配置
            executeDeliveryDimensionPoiConfig(tenantId, poiIds, configContents, taskItemMap, countDto);
            // 渠道平台配置
            executeDeliveryPoiConfig(tenantId, poiIds, configContents, taskItemMap, countDto);
        } catch (Exception e) {
            log.error("批量设置门店配送配置异常 configTaskId: {}, poiIds:{}", taskId, poiIds, e);
            taskItemMap.values().forEach(item -> {
                item.setStatus(BatchTaskStatusEnum.FAIL);
                item.setFailReason("系统异常");
                item.setFinishedAt(null);
            });
            countDto.setFailNum(countDto.getFailNum() + poiIds.size());
        }
        configTaskItemRepository.batchUpdate(taskItemMap.values());
    }

    private void executeDeliveryPoiConfig(Long tenantId, List<Long> poiIds, List<BatchTaskConfigContent> configContents,
                                          Map<Long, ConfigTaskItemModel> taskItemMap, BatchTaskCountDTO taskCount) {
        boolean deliveryPoiFlag = configContents.stream().anyMatch(item -> DeliveryConfigTypeEnum.DELIVERY_CHANNEL_POI_CONFIG_TYPES.contains(item.getType()));
        if (!deliveryPoiFlag) {
            log.info("无门店维度配置");
            return;
        }
        List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfigVos = deliveryConfigConverter.covertDeliveryPlatformConfigVOs(configContents);
        List<Integer> channelTypes = deliveryPlatformConfigVos.stream().map(DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType).collect(Collectors.toList());
        // 查询DB数据
        List<DeliveryPoi> deliveryPois = deliveryPoiRepository.batchQueryDeliveryPoi(tenantId, poiIds, channelTypes);
        Map<Long, List<DeliveryPoi>> poiMap = deliveryPois.stream().collect(groupingBy(DeliveryPoi::getStoreId));
        List<DeliveryPoi> updateList = new ArrayList<>(deliveryPois.size());
        poiMap.forEach((poiId, deliveryPoiList) -> {
            ConfigTaskItemModel configTaskItem = taskItemMap.get(poiId);
            try {
                List<DeliveryPoi> updateDeliveryPoiList = new ArrayList<>();
                for (DeliveryPoi deliveryPoi : deliveryPoiList) {
                    DeliveryConfigDetailVO.DeliveryPlatformConfigVO deliveryPlatformConfigVO = deliveryPlatformConfigVos.stream()
                            .filter(item -> item.getChannelType().equals(deliveryPoi.getChannelType()))
                            .findFirst().orElse(null);
                    if (Objects.isNull(deliveryPlatformConfigVO)) {
                        continue;
                    }
                    DeliveryPoi updateDeliveryPoi = deliveryConfigConverter.updateDeliveryPoiFromRequest(tenantId, deliveryPoi.getStoreId(), deliveryPoi, deliveryPlatformConfigVO);
                    updateDeliveryPoiList.add(updateDeliveryPoi);
                }

                updateList.addAll(updateDeliveryPoiList);
                // 聚合平台建店
                createAggDeliveryShop(updateDeliveryPoiList, deliveryPlatformConfigVos);
                configTaskItem.setStatus(BatchTaskStatusEnum.COMPLETED);
                taskCount.addSuccessNum();
            } catch (BizException e) {
                configTaskItem.setStatus(BatchTaskStatusEnum.FAIL);
                configTaskItem.setFailReason(e.getMessage());
                taskCount.addFailNum();
                log.error("批量设置门店配送配置异常 poiId: {}", poiId, e);
            } catch (Exception e) {
                configTaskItem.setStatus(BatchTaskStatusEnum.FAIL);
                configTaskItem.setFailReason("系统异常");
                taskCount.addFailNum();
                log.error("批量设置门店配送配置异常 poiId: {}", poiId, e);
            }
        });
        deliveryPoiRepository.batchUpdateById(updateList);
    }

    private void createAggDeliveryShop(List<DeliveryPoi> updateDeliveryPoiList, List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> deliveryPlatformConfigVos) {
        // 开通麦芽田平台门店
        List<Integer> maltFarmChannelTypeList = deliveryPlatformConfigVos
                .stream()
                .filter(item -> Objects.equals(item.getStatus(), 1))
                .filter(item -> Objects.equals(item.getPlatformCode(), DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode()))
                .map(DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType)
                .collect(Collectors.toList());
        Optional<DeliveryPoi> maltFarmDeliveryPoi = updateDeliveryPoiList.stream()
                .filter(deliveryPoi -> maltFarmChannelTypeList.contains(deliveryPoi.getChannelType()))
                .findFirst();
        maltFarmDeliveryPoi.ifPresent(item -> maltfarmChannelServiceWrapper.createShop(item));

        // 开通青云门店
        List<Integer> dapChannelTypeList = deliveryPlatformConfigVos
                .stream()
                .filter(item -> Objects.equals(item.getStatus(), 1))
                .filter(item -> Objects.equals(item.getPlatformCode(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()))
                .map(DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType)
                .collect(Collectors.toList());
        Optional<DeliveryPoi> dapDeliveryPoi = updateDeliveryPoiList.stream()
                .filter(deliveryPoi -> dapChannelTypeList.contains(deliveryPoi.getChannelType()))
                .findFirst();
        dapDeliveryPoi.ifPresent(item -> dapChannelServiceWrapper.createShop(item));
    }

    private void executeDeliveryDimensionPoiConfig(Long tenantId, List<Long> poiIds, List<BatchTaskConfigContent> configContents,
                                                   Map<Long, ConfigTaskItemModel> taskItemMap, BatchTaskCountDTO taskCount) {
        boolean deliveryDimensionFlag = configContents.stream()
                .anyMatch(item -> DeliveryConfigTypeEnum.deliveryDimensionConfigTypes.contains(item.getType()));
        if (!deliveryDimensionFlag) {
            log.info("无配送维度配置");
            return;
        }
        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfigVO = deliveryConfigConverter.covertSelfDeliveryConfigVO(configContents);
        // 查询DB数据
        List<DeliveryDimensionPoi> deliveryDimensionPois = deliveryDimensionPoiRepository.batchQueryByStoreIds(tenantId, poiIds);
        List<DeliveryDimensionPoi> updateList = new ArrayList<>(deliveryDimensionPois.size());
        deliveryDimensionPois.forEach(item -> {
            ConfigTaskItemModel configTaskItem = taskItemMap.get(item.getStoreId());
            try {
                DeliveryDimensionPoi deliveryDimensionPoi = deliveryConfigConverter.updateDeliveryDimensionPoiFromRequest(item, selfDeliveryConfigVO);
                updateList.add(deliveryDimensionPoi);
                configTaskItem.setStatus(BatchTaskStatusEnum.COMPLETED);
                taskCount.addSuccessNum();
            } catch (BizException e) {
                configTaskItem.setStatus(BatchTaskStatusEnum.FAIL);
                configTaskItem.setFailReason(e.getMessage());
                taskCount.addFailNum();
            } catch (Exception e) {
                configTaskItem.setStatus(BatchTaskStatusEnum.FAIL);
                configTaskItem.setFailReason("系统异常");
                taskCount.addFailNum();
            }
        });
        deliveryDimensionPoiRepository.batchUpdateById(updateList);
    }

    private BatchTemplateQuery buildBatchTemplateQuery(QueryBatchTaskParam req, Long tenantId) {
        BatchTemplateQuery query = new BatchTemplateQuery();
        query.setTenantId(tenantId);
        query.setDimensionType(req.getTabType());
        // 经营模式
        if (Objects.equals(DimensionEnum.OPERATION_MODE.getCode(), req.getTabType())) {
            // 校验
            OperationModeEnum.enumOf(req.getTabChildType());
            query.setDimensionId(req.getTabChildType());
        }
        query.setTemplateTypeList(req.getConfigTypeList());
        query.setBeginTime(DateUtil.getLocalDateTime(req.getBeginTime()));
        query.setEndTime(DateUtil.getLocalDateTime(req.getEndTime()).withHour(23).withMinute(59).withSecond(59).withNano(999));
        if (StringUtils.isNotBlank(req.getTaskName())) {
            query.setTaskName(req.getTaskName());
        }
        if (StringUtils.isNotBlank(req.getOperator())) {
            query.setOperator(req.getOperator());
        }
        return query;
    }

    private BatchTaskQuery buildBatchTaskQuery(QueryBatchTaskParam req, Long tenantId) {
        LocalDateTime beginTime = DateUtil.getLocalDateTime(req.getBeginTime());
        LocalDateTime endTime = DateUtil.getLocalDateTime(req.getEndTime());
        BatchTaskQuery query = new BatchTaskQuery();
        query.setTenantId(tenantId);
        query.setStatus(1);
        query.setConfigTemplateId(0L);
        query.setTaskType(BatchTaskTypeEnum.QNH_DELIVERY_CONFIG_POI.getCode());
        query.setBeginTime(beginTime);
        query.setEndTime(endTime.withHour(23).withMinute(59).withSecond(59).withNano(999));
        // 任务名称
        if (StringUtils.isNotBlank(req.getTaskName())) {
            query.setTaskName(req.getTaskName());
        }
        // 操作人
        if (StringUtils.isNotBlank(req.getOperator())) {
            query.setOperator(req.getOperator());
        }
        return query;
    }

    private Long saveTemplateInfo(DeliveryConfigBatchSaveParam param, List<BatchTaskConfigContent> contents) {
        log.info("saveTemplateInfo: param={}; contents={}", JacksonUtils.toJson(param), JacksonUtils.toJson(contents));
        ConfigTemplateModel model = new ConfigTemplateModel();
        model.setTenantId(param.getTenantId());
        model.setName(param.getTaskName());
        model.setDimensionType(DimensionEnum.enumOf(param.getTabType()));
        if (DimensionEnum.TEMPLATE_ENUMS.contains(model.getDimensionType())) {
            model.setOperationMode(OperationModeEnum.enumOf(param.getTabChildType()));
        }
        model.setIsSyncWhenCreate(param.getIsSyncWhenSave());
        model.setConfigContents(contents);
        model.setCreateBy(param.getOperator());
        model.setCreateName(param.getOperatorName());
        configTemplateRepository.save(model);
        return model.getId();
    }

    private boolean taskCheck(ConfigTaskModel configTaskModel) {
        if (Objects.isNull(configTaskModel)) {
            log.info("任务不存在");
            return false;
        }
        if (BatchTaskStatusEnum.INIT != configTaskModel.getStatus()) {
            log.info("任务状态不是初始化状态，不处理");
            return false;
        }
        configTaskRepository.updateStatus(configTaskModel.getId(), BatchTaskStatusEnum.PROCESSING);
        return true;
    }
}
