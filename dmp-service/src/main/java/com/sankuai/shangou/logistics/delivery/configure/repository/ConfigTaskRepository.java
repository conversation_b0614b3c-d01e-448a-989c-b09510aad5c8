package com.sankuai.shangou.logistics.delivery.configure.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.BatchTaskConfigContent;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTaskModel;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskStatusEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.BatchTaskTypeEnum;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTaskDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDOExample;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-02
 */
@Service
public class ConfigTaskRepository {
    @Resource
    private ConfigTaskDOMapper configTaskDOMapper;

    public boolean createBatchTask(ConfigTaskModel model) {
        ConfigTaskDO taskDO = new ConfigTaskDO();
        taskDO.setTenantId(model.getTenantId());
        taskDO.setConfigTemplateId(model.getConfigTemplateId());
        taskDO.setTaskType(model.getTaskType().getCode());
        taskDO.setTaskName(model.getTaskName());
        if (CollectionUtils.isNotEmpty(model.getChannelTypes())) {
            List<Integer> channelIds = model.getChannelTypes().stream()
                    .map(DynamicChannelType::getChannelId)
                    .collect(Collectors.toList());
            taskDO.setChannelType(channelIds.toString());
        }
        taskDO.setStatus(model.getStatus().getCode());
        taskDO.setConfigDetail(JSON.toJSONString(model.getConfigContents()));
        taskDO.setOperator(model.getOperator());
        taskDO.setOperatorName(model.getOperatorName());
        taskDO.setExtInfo(JSON.toJSONString(covertExtInfo(model)));
        configTaskDOMapper.insertSelective(taskDO);
        model.setId(taskDO.getId());
        return true;
    }

    public ConfigTaskModel selectByPrimaryKey(Long configTaskId) {
        if (configTaskId == null) {
            return null;
        }
        ConfigTaskDO configTaskDO = configTaskDOMapper.selectByPrimaryKey(configTaskId);
        if (configTaskDO == null) {
            return null;
        }
        return translate(configTaskDO);
    }

    public void updateStatus(Long id, BatchTaskStatusEnum batchTaskStatusEnum) {
        ConfigTaskDO configTaskDO = new ConfigTaskDO();
        configTaskDO.setId(id);
        configTaskDO.setStatus(batchTaskStatusEnum.getCode());
        configTaskDOMapper.updateByPrimaryKeySelective(configTaskDO);
    }

    public void completed(ConfigTaskModel model) {
        ConfigTaskDO configTaskDO = new ConfigTaskDO();
        configTaskDO.setId(model.getId());
        configTaskDO.setStatus(BatchTaskStatusEnum.COMPLETED.getCode());
        configTaskDO.setFinishedAt(new Date());
        configTaskDO.setTotalNum(model.getTotalNum());
        configTaskDO.setSuccessNum(model.getSuccessNum());
        configTaskDO.setFailNum(model.getFailNum());
        configTaskDOMapper.updateByPrimaryKeySelective(configTaskDO);
    }

    public ConfigTaskModel queryByConfigTemplateId(Long configTemplateId) {
        ConfigTaskDOExample example = new ConfigTaskDOExample();
        example.createCriteria().andConfigTemplateIdEqualTo(configTemplateId);
        List<ConfigTaskDO> configTaskDOS = configTaskDOMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(configTaskDOS)) {
            return null;
        }
        return translate(configTaskDOS.get(0));
    }

    private ConfigTaskModel translate(ConfigTaskDO configTaskDO) {
        ConfigTaskModel model = new ConfigTaskModel();
        model.setId(configTaskDO.getId());
        model.setTenantId(configTaskDO.getTenantId());
        model.setConfigTemplateId(configTaskDO.getConfigTemplateId());
        model.setTaskType(BatchTaskTypeEnum.enumOf(configTaskDO.getTaskType()));
        model.setTaskName(model.getTaskName());
        model.setStatus(BatchTaskStatusEnum.enumOf(configTaskDO.getStatus()));
        if (StringUtils.isNotBlank(configTaskDO.getConfigDetail())) {
            model.setConfigContents(JSON.parseArray(configTaskDO.getConfigDetail(), BatchTaskConfigContent.class));
        }
        model.setCreatedAt(Objects.isNull(configTaskDO.getCreatedAt()) ? null : DateUtil.toLocalDateTime(configTaskDO.getCreatedAt()));
        model.setUpdatedAt(Objects.isNull(configTaskDO.getUpdatedAt()) ? null : DateUtil.toLocalDateTime(configTaskDO.getUpdatedAt()));
        model.setFinishedAt(Objects.isNull(configTaskDO.getFinishedAt()) ? null : DateUtil.toLocalDateTime(configTaskDO.getFinishedAt()));
        model.setOperator(configTaskDO.getOperator());
        model.setOperatorName(configTaskDO.getOperatorName());
        parseExtInfo(configTaskDO.getExtInfo(), model);
        return model;
    }

    private Map<String, Object> covertExtInfo(ConfigTaskModel model) {
        Map<String, Object> extMap = new HashMap<>();
        if (CollectionUtils.isEmpty(model.getPoiIds())) {
            extMap.put("poiIds", model.getPoiIds());
        }
        return extMap;
    }

    private void parseExtInfo(String extInfoStr, ConfigTaskModel model) {
        if (StringUtils.isBlank(extInfoStr)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(extInfoStr);
        if (jsonObject.containsKey("poiIds")) {
            model.setPoiIds(jsonObject.getJSONArray("poiIds").toJavaList(Long.class));
        }
    }


}
