package com.sankuai.shangou.logistics.delivery.configure.model.value;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.logistics.delivery.configure.value.ExpressionNode;
import lombok.Data;

/**
 * 剩余时长配置（歪马考核时长配置）
 * 注：此配置结构略微复杂，具体字段根据实际业务需求定义
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
public class AssessTimeConfig {

    @FieldDoc(description = "类型")
    private Integer type;

    @FieldDoc(description = "提示")
    private Integer hintType;

    @FieldDoc(description = "时长设置类型，1-固定时长 2-eta+-分钟")
    private Integer timeConfigType;

    @FieldDoc(description = "表达式树")
    private ExpressionNode expressionNode;

}

