package com.sankuai.shangou.logistics.delivery.configure.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiShippingModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.drunkhorsemgmt.labor.types.IntervalNumber;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.ChannelStoreRelation;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.DeliveryShopAuthResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryStoreConfigDto;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.SaveDeliveryConfigDetailResponse;
import com.sankuai.shangou.logistics.delivery.configure.external.TenantStoreClient;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryDimensionPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.pojo.dto.DeliveryPlatformUrlInfo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.dto.TenantChannelStoreInfo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ShopAuthInfo;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryDimensionPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.service.converter.BookingPushDownTimeConfigConverter;
import com.sankuai.shangou.logistics.delivery.configure.service.converter.DeliveryConfigConverter;
import com.sankuai.shangou.logistics.delivery.configure.service.squirrel.ShopAuthSquirrelService;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.DapChannelServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.FulfillConfigServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.MaltFarmChannelServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.service.wrapper.TenantChannelServiceWrapper;
import com.sankuai.shangou.logistics.delivery.configure.value.*;
import com.sankuai.shangou.logistics.delivery.enums.DeliveryConfigOrderTagEnum;
import com.sankuai.shangou.qnh.ofc.ebase.dto.FulfillConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryPoiRepository.DISTANCE_IDENTIFIER;

/**
 * 配送配置服务
 *
 * <AUTHOR>
 * @date 2025-07-04
 * @email <EMAIL>
 */
@Slf4j
@Service
public class DeliveryConfigService {

    @Resource
    private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private TenantChannelServiceWrapper tenantChannelServiceWrapper;
    @Resource
    private FulfillConfigServiceWrapper fulfillConfigServiceWrapper;
    @Resource
    private ShopAuthSquirrelService shopAuthSquirrelService;
    @Resource
    private DapChannelServiceWrapper dapChannelServiceWrapper;
    @Resource
    private TenantStoreClient tenantStoreClient;
    @Resource
    private MaltFarmChannelServiceWrapper maltfarmChannelServiceWrapper;
    @Resource
    private DeliveryConfigConverter deliveryConfigConverter;



    /**
     * 查询门店配置明细
     *
     * @param tenantId 租户ID
     * @param storeId  门店ID
     * @return 门店配置明细
     */
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public DeliveryConfigDetailVO queryConfigDetail(Long tenantId, Long storeId) {
        try {
            log.info("开始查询门店配置明细, tenantId: {}, storeId: {}", tenantId, storeId);

            // 查询配送维度配置
            DeliveryDimensionPoi deliveryDimensionPoi = deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(tenantId, storeId);

            // 查询基础配送配置
            List<Integer> tenantChannelTypes = tenantChannelServiceWrapper.getTenantChannelType(tenantId);
            List<DeliveryPoi> deliveryPois = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(tenantChannelTypes)) {
                deliveryPois = deliveryPoiRepository.queryDeliveryPoi(tenantId, storeId, tenantChannelTypes);
            }

            //查ofc履约配置
            FulfillConfigDTO fulfillConfigDTO = fulfillConfigServiceWrapper.queryFulfillConfig(tenantId, storeId);

            // 构建返回结果
            DeliveryConfigDetailVO result = new DeliveryConfigDetailVO();
            result.setTenantId(tenantId);
            result.setStoreId(storeId);

            // 构建配送平台配置列表
            if (CollectionUtils.isNotEmpty(deliveryPois)) {
                List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> platformConfigList = deliveryPois.stream().map(this::buildDeliveryPlatformConfig).collect(Collectors.toList());
                result.setDeliveryPlatformConfig(platformConfigList);
            }

            // 构建自送配置
            if (Objects.nonNull(deliveryDimensionPoi)) {
                DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig = buildSelfDeliveryConfig(deliveryDimensionPoi);
                if (Objects.nonNull(fulfillConfigDTO)) {
                    selfDeliveryConfig.setPickDeliveryWorkMode(fulfillConfigDTO.getPickDeliveryWorkMode());
                }
                result.setSelfDeliveryConfig(selfDeliveryConfig);
            }

            log.info("查询门店配置明细成功, tenantId: {}, storeId: {}", tenantId, storeId);
            return result;

        } catch (Exception e) {
            log.error("查询门店配置明细失败, tenantId: {}, storeId: {}", tenantId, storeId, e);
            throw new RuntimeException("查询门店配置明细失败", e);
        }
    }

    /**
     * 保存门店配置明细
     *
     */
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public SaveDeliveryConfigDetailResponse saveConfigDetail(long operatorId, String operatorName, DeliveryConfigDetailVO detailVO) {
        try {
            if (detailVO == null) {
                throw new IllegalArgumentException("配置详情不能为空");
            }
            SaveDeliveryConfigDetailResponse response = new SaveDeliveryConfigDetailResponse();
            Long tenantId = detailVO.getTenantId();
            Long storeId = detailVO.getStoreId();

            log.info("开始保存门店配置明细, tenantId: {}, storeId: {}", tenantId, storeId);

            // 保存配送平台配置
            if (!CollectionUtils.isEmpty(detailVO.getDeliveryPlatformConfig())) {
                List<DeliveryPoi> existDeliveryPois = deliveryPoiRepository.queryDeliveryPoi(tenantId, storeId, IListUtils.mapTo(detailVO.getDeliveryPlatformConfig(), DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType));

                //NOTE: 历史逻辑上，青云如果没授权则不会保存，需要等青云回调后才保存
                List<Integer> noNeedSaveChannelTypes =  Lists.newArrayList();

                //处理青云和麦芽田
                List<Integer> dapChannelTypeList = detailVO
                        .getDeliveryPlatformConfig()
                        .stream()
                        .filter(deliveryPlatformConfigVO -> Objects.equals(deliveryPlatformConfigVO.getStatus(), 1))
                        .filter(deliveryPlatformConfigVO -> Objects.equals(deliveryPlatformConfigVO.getPlatformCode(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()))
                        .map(DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType)
                        .collect(Collectors.toList());
                List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> dapUpdateVO = detailVO
                        .getDeliveryPlatformConfig()
                        .stream()
                        .filter(deliveryPlatformConfigVO -> Objects.equals(deliveryPlatformConfigVO.getStatus(), 1))
                        .filter(deliveryPlatformConfigVO -> Objects.equals(deliveryPlatformConfigVO.getPlatformCode(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()))
                        .collect(Collectors.toList());
                List<DeliveryPoi> updateToDapDeliveryPoi = existDeliveryPois.stream().filter(deliveryPoi -> dapChannelTypeList.contains(deliveryPoi.getChannelType())).collect(Collectors.toList());
                //青云门店授权信息
                if (CollectionUtils.isNotEmpty(dapUpdateVO)) {
                    DeliveryShopAuthResponse deliveryShopAuthResponse = authDapShop(tenantId, storeId, operatorId, operatorName, dapUpdateVO);
                    if (Objects.isNull(deliveryShopAuthResponse) || !deliveryShopAuthResponse.getCode().equals(ResponseCodeEnum.SUCCESS.getValue())) {
                        response.setCode(Objects.isNull(deliveryShopAuthResponse) ? FailureCodeEnum.QUERY_SHOP_AUTH_EXCEPTION.getCode(): deliveryShopAuthResponse.getCode());
                    } else {
                        DeliveryPlatformUrlInfo dapUrlInfo = null;
                        if(!deliveryShopAuthResponse.getIsAuthed()) {
                            Optional<Map<String, DeliveryPlatformUrlInfo>> optionalMap = dapChannelServiceWrapper.queryLinkInfo(updateToDapDeliveryPoi.get(0), Arrays.asList(String.valueOf(storeId)), LinkTypeEnum.SHOP_LINK_TYPE);
                            if (optionalMap.isPresent()) {
                                Map<String, DeliveryPlatformUrlInfo> infoMap = optionalMap.get();
                                if (MapUtils.isNotEmpty(infoMap) && infoMap.containsKey(String.valueOf(storeId))) {
                                    dapUrlInfo = infoMap.get(String.valueOf(storeId));
                                }
                            }
                            noNeedSaveChannelTypes.addAll(dapChannelTypeList);
                        } else {
                            fastAuth(updateToDapDeliveryPoi.get(0));
                        }
                        response.setCode(ResponseCodeEnum.SUCCESS.getValue());
                        response.setIsAuthed(deliveryShopAuthResponse.getIsAuthed()? 1 : 0);
                        response.setDapLinkUrl(null);
                        if (Objects.nonNull(dapUrlInfo)) {
                            response.setDapLinkUrl(dapUrlInfo.getLinkUrl());
                        }
                    }
                }

                //开通麦芽田平台门店
                List<Integer> maltfarmChannelTypeList = detailVO
                        .getDeliveryPlatformConfig()
                        .stream()
                        .filter(deliveryPlatformConfigVO -> Objects.equals(deliveryPlatformConfigVO.getStatus(), 1))
                        .filter(deliveryPlatformConfigVO -> Objects.equals(deliveryPlatformConfigVO.getPlatformCode(), DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode()))
                        .map(DeliveryConfigDetailVO.DeliveryPlatformConfigVO::getChannelType)
                        .collect(Collectors.toList());
                List<DeliveryPoi> updateToMalfarmDeliveryPoi = existDeliveryPois.stream().filter(deliveryPoi -> maltfarmChannelTypeList.contains(deliveryPoi.getChannelType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(updateToMalfarmDeliveryPoi)) {
                    maltfarmChannelServiceWrapper.createShop(updateToMalfarmDeliveryPoi.get(0));
                }
                List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> filterDeliveryPlatformConfigVOs = detailVO.getDeliveryPlatformConfig().stream()
                        .filter(deliveryPlatformConfigVO -> !noNeedSaveChannelTypes.contains(deliveryPlatformConfigVO.getChannelType()))
                        .collect(Collectors.toList());
                saveDeliveryPlatformConfig(tenantId, storeId, existDeliveryPois, filterDeliveryPlatformConfigVOs);
            }

            // 保存自送配置
            if (Objects.nonNull(detailVO.getSelfDeliveryConfig())) {
                saveSelfDeliveryConfig(tenantId, storeId, detailVO.getSelfDeliveryConfig());
            }

            if (Objects.nonNull(detailVO.getSelfDeliveryConfig().getPickDeliveryWorkMode())) {
                fulfillConfigServiceWrapper.saveFulfillConfig(tenantId, storeId, new FulfillConfigDTO(detailVO.getSelfDeliveryConfig().getPickDeliveryWorkMode()));
            }

            log.info("保存门店配置明细成功, tenantId: {}, storeId: {}", tenantId, storeId);
            return response;
        } catch (Exception e) {
            log.error("保存门店配置明细失败, request: {}", JSON.toJSONString(detailVO), e);
            throw new RuntimeException("保存门店配置明细失败", e);
        }
    }

    private DeliveryShopAuthResponse authDapShop(Long tenantId, Long storeId, Long operatorId, String operatorName,List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> dapUpdateVO) {
        List<DeliveryStoreConfigDto> dapDeliveryStoreConfigDtos = dapUpdateVO.stream()
                .map(
                deliveryPlatformConfigVO -> {
                    DeliveryStoreConfigDto deliveryStoreConfigDto = new DeliveryStoreConfigDto();
                    deliveryStoreConfigDto.setPlatformCode(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
                    deliveryStoreConfigDto.setOpenFlag(1);
                    deliveryStoreConfigDto.setChannelType(deliveryPlatformConfigVO.getChannelType());
                    deliveryStoreConfigDto.setDeliveryLaunchPoint(deliveryPlatformConfigVO.getDeliveryLaunchPoint());
                    deliveryStoreConfigDto.setDeliveryLaunchDelayMinutes(deliveryPlatformConfigVO.getDeliveryLaunchDelayMinutes());
                    deliveryStoreConfigDto.setBookingOrderDeliveryLaunchMinutes(deliveryPlatformConfigVO.getBookingOrderDeliveryLaunchMinutes());
                    deliveryStoreConfigDto.setSecondDeliveryConfig(deliveryConfigConverter.buildSecondDeliveryConfigFromRequest(deliveryPlatformConfigVO.getSecondDeliveryConfig()));
                    return deliveryStoreConfigDto;
                }
        ).collect(Collectors.toList());
        shopAuthSquirrelService.set(String.valueOf(storeId), new ShopAuthInfo(storeId, tenantId, operatorId, operatorName, dapDeliveryStoreConfigDtos));

        return dapChannelServiceWrapper.deliveryShopAuth(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM, storeId);
    }

    private void fastAuth(DeliveryPoi deliveryPoi) {
        if (!LionConfigUtils.getTenantPoiFastAuthSwitch()) {
            log.info("绑定信息开关关闭");
            return;
        }
        if (deliveryPoi.getDeliveryPlatform() != DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM) {
            return;
        }
        try {

            PoiInfoDto poiInfoDto = tenantStoreClient.queryStoreDetailInfo(deliveryPoi.getTenantId(),
                    deliveryPoi.getStoreId());
            if (poiInfoDto == null) {
                log.info("门店详情为空");
                return;
            }

            if (poiInfoDto.getEntityType() == null || poiInfoDto.getShippingMode() == null) {
                log.info("门店类型为空");
                return;
            }
            Set<Long> wmPoiIdSet = new HashSet<>();
            List<ChannelStoreRelation> relationList = new ArrayList<>();
            if (poiInfoDto.getEntityType() == PoiEntityTypeEnum.STORE.code()
                    && poiInfoDto.getShippingMode() == PoiShippingModeEnum.SHIP_BY_STORE.code()) {
                PoiDetailInfoDTO poiDetailInfoDTO = tenantStoreClient
                        .queryChannelStoreDetailInfo(deliveryPoi.getTenantId(), deliveryPoi.getStoreId());
                if (poiDetailInfoDTO == null || CollectionUtils.isEmpty(poiDetailInfoDTO.getChannelPoiInfoList())) {
                    return;
                }
                Optional<ChannelPoiInfoDTO> channelStore = Optional.ofNullable(poiDetailInfoDTO.getChannelPoiInfoList())
                        .orElse(Lists.newArrayList()).stream().filter(tenantChannelStoreInfo -> tenantChannelStoreInfo
                                .getChannelId().equals(ChannelType.MEITUAN.getValue()))
                        .findFirst();

                channelStore.ifPresent(tenantChannelStoreInfo -> wmPoiIdSet
                        .add(NumberUtils.toLong(tenantChannelStoreInfo.getChannelPoiId())));

                if(channelStore.isPresent()){
                    String channelPoiId = channelStore.get().getChannelInnerPoiId();
                    if(StringUtils.isEmpty(channelPoiId)){
                        channelPoiId = channelStore.get().getChannelPoiId();
                    }
                    relationList.add(new ChannelStoreRelation(deliveryPoi.getStoreId(),NumberUtils.toLong(channelPoiId)));
                }else {
                    relationList.add(new ChannelStoreRelation(deliveryPoi.getStoreId(),0L));
                }
            } else {
                Map<Long, List<Long>> relationPoiMap = tenantStoreClient.batchQueryRelationMapByPoiIds(
                        deliveryPoi.getTenantId(), Arrays.asList(deliveryPoi.getStoreId()), true);
                if (MapUtils.isEmpty(relationPoiMap)
                        || CollectionUtils.isEmpty(relationPoiMap.get(deliveryPoi.getStoreId()))) {
                    log.info("正向关系为空");
                    return;
                }
                List<Long> relationPoiList = relationPoiMap.get(deliveryPoi.getStoreId());
                if (CollectionUtils.isNotEmpty(relationPoiList)) {
                    Map<Long, List<TenantChannelStoreInfo>> channelStoreMap = tenantStoreClient
                            .queryChannelStoreDetailInfoList(deliveryPoi.getTenantId(),
                                    new ArrayList<>(relationPoiList));
                    if (MapUtils.isNotEmpty(channelStoreMap)) {
                        for (Long poiId : channelStoreMap.keySet()) {
                            List<TenantChannelStoreInfo> channelStoreInfoList = channelStoreMap.get(poiId);
                            if (CollectionUtils.isEmpty(channelStoreInfoList)) {
                                relationList.add(new ChannelStoreRelation(poiId,0L));
                                continue;
                            }
                            Optional<TenantChannelStoreInfo> mtStoreInfo = channelStoreInfoList.stream()
                                    .filter(tenantChannelStoreInfo -> tenantChannelStoreInfo.getChannelId()
                                            .equals(ChannelType.MEITUAN.getValue()))
                                    .findFirst();
                            mtStoreInfo.ifPresent(tenantChannelStoreInfo -> wmPoiIdSet
                                    .add(NumberUtils
                                            .toLong(tenantChannelStoreInfo.getChannelPoiId())));

                            if(mtStoreInfo.isPresent()){
                                String channelPoiId = mtStoreInfo.get().getChannelInnerPoiId();
                                if(StringUtils.isEmpty(channelPoiId)){
                                    channelPoiId = mtStoreInfo.get().getChannelPoiId();
                                }
                                relationList.add(new ChannelStoreRelation(poiId,NumberUtils.toLong(channelPoiId)));
                            }else {
                                relationList.add(new ChannelStoreRelation(poiId,0L));
                            }
                        }
                    }
                }
            }

            dapChannelServiceWrapper.deliveryFastAuth(deliveryPoi, new ArrayList<>(wmPoiIdSet),relationList);
        } catch (Exception e) {
            log.error("fastAuth error deliveryPoi:{}", deliveryPoi, e);
        }
    }

    /**
     * 构建配送平台配置
     */
    private DeliveryConfigDetailVO.DeliveryPlatformConfigVO buildDeliveryPlatformConfig(DeliveryPoi deliveryPoi) {
        DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig = new DeliveryConfigDetailVO.DeliveryPlatformConfigVO();

        // 基础信息
        platformConfig.setChannelType(deliveryPoi.getChannelType());

        // 配送平台
        if(Objects.equals(deliveryPoi.getDeliveryPlatform(), DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM)){
            platformConfig.setStatus(0);
            //默认是麦芽田
            platformConfig.setPlatformCode(Optional.ofNullable(deliveryPoi.getLastDeliveryPlatform()).map(DeliveryPlatformEnum::getCode).orElse(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode()));
        } else {
            platformConfig.setPlatformCode(deliveryPoi.getDeliveryPlatform().getCode());
            platformConfig.setStatus(1);
        }

        // 配送发起时间点配置
        if (deliveryPoi.getDeliveryLaunchPoint() != null) {
            fillPlatformLaunchPointConfig(platformConfig, deliveryPoi.getDeliveryLaunchPoint());
        }

        // 二级配送配置
        DeliveryConfigDetailVO.SecondDeliveryConfigVO secondConfig = buildSecondDeliveryConfig(deliveryPoi);
        platformConfig.setSecondDeliveryConfig(secondConfig);

        if (deliveryPoi.getSelfAssessDeliveryConfig() != null) {
            DeliveryConfigDetailVO.SelfDeliveryBookingDeliveryRuleVO selfDeliveryBookingDeliveryRuleVO = new DeliveryConfigDetailVO.SelfDeliveryBookingDeliveryRuleVO();

            BookingOrderLaunchTimeConfig selfAssessConfig = deliveryPoi.getSelfAssessDeliveryConfig();

            // 设置是否需要营业时间下推
            if (selfAssessConfig.getNeedBusinessHoursPushdown() != null) {
                selfDeliveryBookingDeliveryRuleVO.setNeedBusinessHoursPushdown(selfAssessConfig.getNeedBusinessHoursPushdown());
            }

            // 转换预约下推时间配置列表
            if (CollectionUtils.isNotEmpty(selfAssessConfig.getSubs())) {
                List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> bookingPushDownTimeConfigs =
                    BookingPushDownTimeConfigConverter.convertFromExpressionNodes(selfAssessConfig.getSubs());
                selfDeliveryBookingDeliveryRuleVO.setBookingPushDownTimeConfig(bookingPushDownTimeConfigs);
            }

            platformConfig.setSelfDeliveryBookingDeliveryRule(selfDeliveryBookingDeliveryRuleVO);
        } else {
            /*
             * 注意，这里前端展示是在这里处理的。
             * 为什么不在save的时候处理？因为不是在查询处理，而是默认写入了默认规则，会导致下发时每一单都需要走计算引擎计算。
             * 不想计算的话，也得在计算时识别默认配置而不计算。
             * 以上方案明显不如在配置侧做兼容，不要去动核心流程。
             */
            DeliveryConfigDetailVO.SelfDeliveryBookingDeliveryRuleVO bookingDeliveryRuleVO = new DeliveryConfigDetailVO.SelfDeliveryBookingDeliveryRuleVO();
            bookingDeliveryRuleVO.setNeedBusinessHoursPushdown(false);

            DeliveryConfigDetailVO.BookingPushDownTimeConfigVO bookingPushDownTimeConfig = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();
            bookingPushDownTimeConfig.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.ALL.getType()));

            DeliveryConfigDetailVO.ConditionVO conditionVO = new DeliveryConfigDetailVO.ConditionVO();
            conditionVO.setIdentifer(DISTANCE_IDENTIFIER);
            DeliveryConfigDetailVO.IntervalVO intervalVO = new DeliveryConfigDetailVO.IntervalVO();
            intervalVO.setValues(Lists.newArrayList("0", IntervalNumber.POSITIVE_INFINITY));
            intervalVO.setIntervalType(1);
            conditionVO.setInterval(intervalVO);
            conditionVO.setValue(String.valueOf(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes()));

            bookingPushDownTimeConfig.setCondition(Lists.newArrayList(conditionVO));

            bookingDeliveryRuleVO.setBookingPushDownTimeConfig(Lists.newArrayList(bookingPushDownTimeConfig));
            platformConfig.setSelfDeliveryBookingDeliveryRule(bookingDeliveryRuleVO);
        }

        return platformConfig;
    }

    /**
     * 填充平台配送发起时间点配置
     */
    public void fillPlatformLaunchPointConfig(DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig, DeliveryLaunchPoint launchPoint) {
        // 立即单配送发起时间点配置
        if (launchPoint.getImmediateOrderDeliveryLaunchPointConfig() != null) {
            DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateConfig = launchPoint.getImmediateOrderDeliveryLaunchPointConfig();

            if (immediateConfig.getLaunchPoint() != null) {
                platformConfig.setDeliveryLaunchPoint(immediateConfig.getLaunchPoint().getCode());
            }
            if (immediateConfig.getDelayMinutes() != null) {
                platformConfig.setDeliveryLaunchDelayMinutes(immediateConfig.getDelayMinutes());
            }
        }

        // 预约单配送发起时间点配置
        if (launchPoint.getBookingOrderDeliveryLaunchPointConfig() != null) {
            DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig bookingConfig = launchPoint.getBookingOrderDeliveryLaunchPointConfig();

            if (bookingConfig.getConfigMinutes() != null) {
                platformConfig.setBookingOrderDeliveryLaunchMinutes(bookingConfig.getConfigMinutes());
            }
        }
    }

    /**
     * 构建二级配送配置
     */
    private DeliveryConfigDetailVO.SecondDeliveryConfigVO buildSecondDeliveryConfig(DeliveryPoi deliveryPoi) {
        DeliveryConfigDetailVO.SecondDeliveryConfigVO secondConfig = new DeliveryConfigDetailVO.SecondDeliveryConfigVO();

        // 解析二级配送平台配置
        if (deliveryPoi.getSecondDeliveryPlatform() != null) {
            try {
                SecondDeliveryConfig secondDeliveryConfig =
                        deliveryPoi.getSecondDeliveryPlatform();

                secondConfig.setSecondPlatformCode(secondDeliveryConfig.getSecondPlatformCodeCode());

                // 构建禁用条件
                if (secondDeliveryConfig.getForbiddenCondition() != null) {
                    DeliveryConfigDetailVO.ForbiddenConditionVO forbiddenConditionVO = new DeliveryConfigDetailVO.ForbiddenConditionVO();
                    forbiddenConditionVO.setOrderTags(secondDeliveryConfig.getForbiddenCondition().getOrderTags());
                    forbiddenConditionVO.setOrderActualPayment(secondDeliveryConfig.getForbiddenCondition().getOrderActualPayment());
                    secondConfig.setForbiddenCondition(forbiddenConditionVO);
                }
            } catch (Exception e) {
                log.warn("解析二级配送平台配置失败: {}", e.getMessage());
            }
        }

        return secondConfig;
    }

    /**
     * 构建自送配置
     */
    private DeliveryConfigDetailVO.SelfDeliveryConfigVO buildSelfDeliveryConfig(DeliveryDimensionPoi dimensionPoi) {
        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig = new DeliveryConfigDetailVO.SelfDeliveryConfigVO();


        // 跳转导航模式
        if (dimensionPoi.getInternalNavigationMode() != null) {
            selfDeliveryConfig.setInternalNavigationMode(dimensionPoi.getInternalNavigationMode().getMode());
        }

        // 已送达列表排序模式
        if (dimensionPoi.getCompletedSortMode() != null) {
            selfDeliveryConfig.setCompletedSortMode(dimensionPoi.getCompletedSortMode().getMode());
        }

        // 配送转骑手范围 - 转换Long列表为Integer列表
        selfDeliveryConfig.setRiderTransRoles(Optional.ofNullable(dimensionPoi.getRiderTransRoles()).orElse(Lists.newArrayList()));

        // 剩余时长配置
        List<DeliveryConfigDetailVO.AssertTimeVO> assertTimeList = buildAssertTimeConfig(dimensionPoi.getAssessTimeConfigs());
        selfDeliveryConfig.setAssertTime(assertTimeList);

        // 确认送达操作配置
        if (dimensionPoi.getDeliveryCompleteMode() != null) {
            DeliveryConfigDetailVO.DeliveryCompleteModeVO deliveryCompleteMode = buildDeliveryCompleteModeConfig(dimensionPoi.getDeliveryCompleteMode());
            selfDeliveryConfig.setDeliveryCompleteMode(deliveryCompleteMode);
        }

        // 配送提醒设置
        if (dimensionPoi.getDeliveryRemindConfig() != null) {
            DeliveryConfigDetailVO.DeliveryRemindConfigVO deliveryRemindConfig = buildDeliveryRemindConfig(dimensionPoi.getDeliveryRemindConfig());
            selfDeliveryConfig.setDeliveryRemindConfig(deliveryRemindConfig);
        }

        return selfDeliveryConfig;
    }

    /**
     * 构建考核时间配置
     */
    public List<DeliveryConfigDetailVO.AssertTimeVO> buildAssertTimeConfig(List<AssessTimeConfig> assessTimeConfigs) {
        if (CollectionUtils.isEmpty(assessTimeConfigs)) {
            return Lists.newArrayList(BookingPushDownTimeConfigConverter.buildDefaultAssertTimeVO());
        }

        List<DeliveryConfigDetailVO.AssertTimeVO> result = Lists.newArrayList();

        for (AssessTimeConfig assessTimeConfig : assessTimeConfigs) {
            DeliveryConfigDetailVO.AssertTimeVO assertTimeVO = new DeliveryConfigDetailVO.AssertTimeVO();
            assertTimeVO.setType(assessTimeConfig.getType());
            assertTimeVO.setHintType(assessTimeConfig.getHintType());
            assertTimeVO.setTimeConfigType(assessTimeConfig.getTimeConfigType());

            // 转换ExpressionNode为VO格式
            if (assessTimeConfig.getExpressionNode() != null) {
                List<ExpressionNode> expressionNodes = Lists.newArrayList(assessTimeConfig.getExpressionNode());
                List<DeliveryConfigDetailVO.AssertTimeVO> convertedVOs =
                    BookingPushDownTimeConfigConverter.convertFromExpressionNodesToAssertTime(expressionNodes);

                if (CollectionUtils.isNotEmpty(convertedVOs)) {
                    DeliveryConfigDetailVO.AssertTimeVO convertedVO = convertedVOs.get(0);
                    assertTimeVO.setOrderTags(convertedVO.getOrderTags());
                    assertTimeVO.setCondition(convertedVO.getCondition());
                }
            }

            result.add(assertTimeVO);
        }

        return result;
    }

    /**
     * 构建确认送达操作配置
     */
    public DeliveryConfigDetailVO.DeliveryCompleteModeVO buildDeliveryCompleteModeConfig(DeliveryCompleteMode deliveryCompleteMode) {
        DeliveryConfigDetailVO.DeliveryCompleteModeVO completeModeVO = new DeliveryConfigDetailVO.DeliveryCompleteModeVO();
        completeModeVO.setSignTypeSwitch(Objects.nonNull(deliveryCompleteMode.getDeliveryCompleteConfig()) && CollectionUtils.isNotEmpty(deliveryCompleteMode.getDeliveryCompleteConfig().getSpecialProductUploadPicConfig()) ? 1 : 0);
        completeModeVO.setDistanceReminder(deliveryCompleteMode.getDistanceReminder());

        DeliveryConfigDetailVO.DeliveryCompleteConfigVO configVO = new DeliveryConfigDetailVO.DeliveryCompleteConfigVO();
        if (deliveryCompleteMode.getDeliveryCompleteConfig() != null) {
            DeliveryCompleteMode.DeliveryCompleteConfig config = deliveryCompleteMode.getDeliveryCompleteConfig();

            configVO.setOperationMode(config.getOperationMode());
            configVO.setSendPicToCustomerTips(config.getSendPicToCustomerTips());
            configVO.setNotContactCustomerTips(config.getNotContactCustomerTips());
            configVO.setUploadImageDurationThreshold(config.getUploadImageDurationThreshold());
            configVO.setIsShowNotContactCustomerTips(config.getIsShowNotContactCustomerTips());

            // 转换示例图片信息列表
            if (!CollectionUtils.isEmpty(config.getAllExamplePicInfoList())) {
                List<DeliveryConfigDetailVO.ExamplePicInfoVO> examplePicList = new ArrayList<>();
                config.getAllExamplePicInfoList().forEach(pic -> {
                    DeliveryConfigDetailVO.ExamplePicInfoVO picVO = new DeliveryConfigDetailVO.ExamplePicInfoVO();
                    picVO.setType(pic.getType());
                    picVO.setName(pic.getName());
                    picVO.setPicUrl(pic.getPicUrl());
                    picVO.setOrder(pic.getOrder());
                    examplePicList.add(picVO);
                });
                configVO.setAllExamplePicInfoList(examplePicList);
            }

            // 转换特殊商品上传图片配置
            if (!CollectionUtils.isEmpty(config.getSpecialProductUploadPicConfig())) {
                List<DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO> specialConfigList = new ArrayList<>();
                config.getSpecialProductUploadPicConfig().forEach(specialConfig -> {
                    DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO specialVO = new DeliveryConfigDetailVO.SpecialProductUploadPicConfigVO();
                    specialVO.setProductType(specialConfig.getProductType());
                    specialVO.setPicTypeList(specialConfig.getPicTypeList());
                    specialVO.setIsForceUploadPic(specialConfig.getIsForceUploadPic());
                    specialVO.setNeedUploadPicCount(specialConfig.getNeedUploadPicCount());
                    specialConfigList.add(specialVO);
                });
                configVO.setSpecialProductUploadPicConfig(specialConfigList);
            } else {
                configVO.setSpecialProductUploadPicConfig(Lists.newArrayList());
            }

            completeModeVO.setDeliveryCompleteConfig(configVO);
        }

        return completeModeVO;
    }

    /**
     * 构建配送提醒配置
     */
    public DeliveryConfigDetailVO.DeliveryRemindConfigVO buildDeliveryRemindConfig(DeliveryRemindConfig deliveryRemindConfig) {
        if (Objects.isNull(deliveryRemindConfig)) {
            return new DeliveryConfigDetailVO.DeliveryRemindConfigVO();
        }
        DeliveryConfigDetailVO.DeliveryRemindConfigVO remindConfigVO = new DeliveryConfigDetailVO.DeliveryRemindConfigVO();
        remindConfigVO.setReceiveTimeOutMins(deliveryRemindConfig.getReceiveTimeOutMins());
        remindConfigVO.setSoonDeliveryTimeoutMinsBeforeEta(deliveryRemindConfig.getSoonDeliveryTimeoutMinsBeforeEta());

        return remindConfigVO;
    }

    /**
     * 保存配送平台配置
     */
    private void saveDeliveryPlatformConfig(Long tenantId, Long storeId, List<DeliveryPoi> existDeliveryPois,
                                            List<DeliveryConfigDetailVO.DeliveryPlatformConfigVO> platformConfigList) {
        Map<Integer, DeliveryPoi> channelTypeDeliveryPoiMap = IListUtils.nullSafeAndOverrideCollectToMap(existDeliveryPois, DeliveryPoi::getChannelType, Function.identity());
        for (DeliveryConfigDetailVO.DeliveryPlatformConfigVO platformConfig : platformConfigList) {
            // 查询现有配置
            DeliveryPoi existingPoiOpt = channelTypeDeliveryPoiMap.get(platformConfig.getChannelType());

            // 更新现有配置
            DeliveryPoi deliveryPoi = deliveryConfigConverter.updateDeliveryPoiFromRequest(tenantId, storeId, existingPoiOpt, platformConfig);

            // 保存配置
            deliveryPoiRepository.saveDeliveryPoi(deliveryPoi);
            log.info("保存配送平台配置成功, tenantId: {}, storeId: {}, channelType: {}",
                    tenantId, storeId, platformConfig.getChannelType());
        }
    }

    /**
     * 保存自送配置
     */
    private void saveSelfDeliveryConfig(Long tenantId, Long storeId,
                                        DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfig) {
        // 查询现有配置
        DeliveryDimensionPoi existingDimensionPoi =
                deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(tenantId, storeId);

        if (Objects.nonNull(existingDimensionPoi)) {
            // 更新现有配置
            DeliveryDimensionPoi updatedDimensionPoi = deliveryConfigConverter.updateDeliveryDimensionPoiFromRequest(
                    existingDimensionPoi, selfDeliveryConfig);
            deliveryDimensionPoiRepository.updateDeliveryDimensionPoi(updatedDimensionPoi);
        } else {
            throw new SystemException("没有有效的配置");
        }

        log.info("保存自送配置成功, tenantId: {}, storeId: {}", tenantId, storeId);
    }





}
