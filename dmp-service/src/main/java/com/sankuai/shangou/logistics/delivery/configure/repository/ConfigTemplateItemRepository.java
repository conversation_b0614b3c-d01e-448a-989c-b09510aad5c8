package com.sankuai.shangou.logistics.delivery.configure.repository;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.common.enums.DimensionEnum;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTemplateItemModel;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTemplateItemDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTemplateItemDOExMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTemplateItemDOExample;
import com.sankuai.shangou.logistics.delivery.enums.OperationModeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-30
 */
@Service
public class ConfigTemplateItemRepository {
    @Resource
    private ConfigTemplateItemDOExMapper configTemplateItemDOExMapper;
    @Resource
    private ConfigTemplateItemDOMapper configTemplateItemDOMapper;


    public Map<Long /* templateId */, List<Integer> /* templateType */> queryTemplateTypeMap(Collection<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyMap();
        }
        List<ConfigTemplateItemDO> list = configTemplateItemDOExMapper.queryTemplateTypeByTemplateId(templateIds);
        Map<Long, List<Integer>> templateTypeMap = new HashMap<>();
        for (ConfigTemplateItemDO item : list) {
            if (templateTypeMap.containsKey(item.getConfigTemplateId())) {
                templateTypeMap.get(item.getConfigTemplateId()).add(item.getTemplateType());
            } else {
                templateTypeMap.put(item.getConfigTemplateId(), Lists.newArrayList(item.getTemplateType()));
            }
        }
        return templateTypeMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<ConfigTemplateItemDO> itemDOS) {
        if (CollectionUtils.isEmpty(itemDOS)) {
            return 0;
        }
        return configTemplateItemDOExMapper.batchInsert(itemDOS);
    }

    public Map<DeliveryConfigTypeEnum, ConfigTemplateItemModel> queryEffectiveConfigTemplateItemModel(long tenantId, @Nullable OperationModeEnum operationMode, List<DeliveryConfigTypeEnum> deliveryConfigTypeEnums) {
        if (CollectionUtils.isEmpty(deliveryConfigTypeEnums)) {
            return Collections.emptyMap();
        }
        Map<DeliveryConfigTypeEnum, ConfigTemplateItemModel> resMap = new HashMap<>();
        ConfigTemplateItemDOExample example = new ConfigTemplateItemDOExample();
        example
                .createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andTemplateTypeIn(IListUtils.mapTo(deliveryConfigTypeEnums, DeliveryConfigTypeEnum::getCode))
                .andIsDeleteEqualTo(0);
        List<ConfigTemplateItemDO> configTemplateItemDOS = configTemplateItemDOMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(configTemplateItemDOS)) {
            return Collections.emptyMap();
        }
        Map<Integer/*dimensionType*/, List<ConfigTemplateItemDO>> dimensionTypeTemplateItemMap = configTemplateItemDOS.stream().collect(Collectors.groupingBy(ConfigTemplateItemDO::getTemplateType));

        //1.优先取operationMode的配置
        if (Objects.nonNull(operationMode)) {
            processConfigTemplateItems(dimensionTypeTemplateItemMap.getOrDefault(DimensionEnum.OPERATION_MODE.getCode(), Lists.newArrayList()),
                    operationMode, resMap);
        }

        //2.次取tenant的配置
        processConfigTemplateItems(dimensionTypeTemplateItemMap.getOrDefault(DimensionEnum.TENANT.getCode(), Lists.newArrayList()),
                null, resMap);
        return resMap;
    }

    public List<ConfigTemplateItemModel> queryByConfigTemplateId(Long configTemplateId) {
        if (configTemplateId == null) {
            return Collections.emptyList();
        }
        ConfigTemplateItemDOExample example = new ConfigTemplateItemDOExample();
        example.createCriteria().andConfigTemplateIdEqualTo(configTemplateId)
                .andIsDeleteEqualTo(0);
        List<ConfigTemplateItemDO> itemDOList = configTemplateItemDOMapper.selectByExample(example);
        return itemDOList.stream().map(this::translate).collect(Collectors.toList());
    }

    /**
     * 处理配置模板项，按更新时间降序排序并添加到结果Map中
     *
     * @param configTemplateItems 配置模板项列表
     * @param operationMode       经营模式（为null时不进行过滤）
     * @param resMap              结果Map
     */
    private void processConfigTemplateItems(List<ConfigTemplateItemDO> configTemplateItems,
                                            OperationModeEnum operationMode,
                                            Map<DeliveryConfigTypeEnum, ConfigTemplateItemModel> resMap) {
        if (CollectionUtils.isEmpty(configTemplateItems)) {
            return;
        }

        configTemplateItems.stream()
                // 如果operationMode不为null，则过滤匹配的项
                .filter(item -> operationMode == null || Objects.equals(operationMode.getType(), item.getDimensionType()))
                // 按更新时间降序排序
                .sorted(Comparator.<ConfigTemplateItemDO>comparingLong(item -> item.getUpdateTime().getTime()).reversed())
                // 转换并添加到结果Map中（如果不存在的话）
                .forEach(item -> {
                    DeliveryConfigTypeEnum deliveryConfigTypeEnum = DeliveryConfigTypeEnum.enumOf(item.getTemplateType());
                    resMap.putIfAbsent(deliveryConfigTypeEnum, translate(item));
                });
    }

    private ConfigTemplateItemModel translate(ConfigTemplateItemDO itemDO) {
        ConfigTemplateItemModel model = new ConfigTemplateItemModel();
        model.setId(itemDO.getId());
        model.setTenantId(itemDO.getTenantId());
        model.setConfigTemplateId(itemDO.getConfigTemplateId());
        model.setTemplateType(DeliveryConfigTypeEnum.enumOf(itemDO.getTemplateType()));
        model.setDimensionType(DimensionEnum.enumOf(itemDO.getDimensionType()));
        if (model.getDimensionType() == DimensionEnum.OPERATION_MODE) {
            model.setOperationMode(OperationModeEnum.enumOf(Integer.parseInt(itemDO.getDimensionId())));
        }
        model.setConfigContent(JSON.parseObject(itemDO.getConfigContent()));
        model.setCreatedAt(Objects.isNull(itemDO.getCreatedAt()) ? null : DateUtil.toLocalDateTime(itemDO.getCreatedAt()));
        model.setCreateBy(itemDO.getCreateBy());
        model.setUpdateTime(Objects.isNull(itemDO.getUpdateTime()) ? null : DateUtil.toLocalDateTime(itemDO.getUpdateTime()));
        return model;
    }
}
