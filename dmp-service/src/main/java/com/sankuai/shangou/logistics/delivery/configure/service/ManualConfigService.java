package com.sankuai.shangou.logistics.delivery.configure.service;

import com.alibaba.fastjson.JSON;
import com.meituan.shangou.saas.order.platform.common.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.config.value.ShowCompletedDeliveryStatisticConfigVO;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryDimensionPoi;
import com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryDimensionPoiRepository;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryCompleteMode;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OrderTypeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.consts.PickDeliveryWorkModeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.request.QueryFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.response.QueryFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ManualConfigService {

    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;
    @Resource
    private FulfillmentStoreConfigThriftService fulfillmentStoreConfigThriftService;

    public Integer getOperationMode(Long tenantId, Long storeId) {
        QueryFulfillConfigRequest request = new QueryFulfillConfigRequest();
        request.setTenantId(tenantId);
        request.setWarehouseId(storeId);
        request.setOrderType(OrderTypeEnum.SALE_TYPE.getCode());
        QueryFulfillConfigResponse response = fulfillmentStoreConfigThriftService.queryFulfillConfig(request);
        if (Objects.equals(response.getCode(), ResultCodeEnum.SUCCESS.getCode()) && Objects.nonNull(response.getFulfillConfig())) {
            return response.getFulfillConfig().getPickDeliveryWorkMode();
        }

        if (!LionConfigUtils.getDHTenantIdList().contains(tenantId)) {
            return PickDeliveryWorkModeEnum.PICK_DELIVERY_SPLIT.getCode();
        }
        TResult<SelfDeliveryPoiConfigDTO> result = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(tenantId, storeId);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            throw new BizException("查询错误配送配置错误");
        }
        return result.getData().getEnablePickDeliverySplit();
    }


}
