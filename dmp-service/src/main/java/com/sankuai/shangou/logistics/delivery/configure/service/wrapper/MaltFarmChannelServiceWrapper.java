package com.sankuai.shangou.logistics.delivery.configure.service.wrapper;

import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025-08-23
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class MaltFarmChannelServiceWrapper extends AbstractAggDeliveryPlatformServiceWrapper{

    @Override
    protected DeliveryPlatformEnum getDeliveryPlatformEnum() {
        return DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM;
    }
}
