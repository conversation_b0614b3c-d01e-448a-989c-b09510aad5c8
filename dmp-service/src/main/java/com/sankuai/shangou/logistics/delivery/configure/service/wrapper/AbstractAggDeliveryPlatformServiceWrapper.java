package com.sankuai.shangou.logistics.delivery.configure.service.wrapper;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantInfoDto;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.AggDeliveryPlatformAppDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.CreateAggDeliveryShopRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.CreateAggDeliveryShopResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MaltChannelAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DapCategoryEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.configure.external.TenantStoreClient;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryPoi;
import com.sankuai.shangou.logistics.delivery.configure.pojo.dto.AggDeliveryPlatformAppConfig;
import com.sankuai.shangou.logistics.delivery.configure.pojo.dto.TenantChannelStoreInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.NO_CHANNEL_SUPPORT;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.PLATFORM_CREATE_SHOP_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.TENANT_POI_DELIVERY_POI_LATITUDE_LONGITUDE_EMPTY;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-26
 */
@Slf4j
public abstract class AbstractAggDeliveryPlatformServiceWrapper {

    protected abstract DeliveryPlatformEnum getDeliveryPlatformEnum();

    private static final String DEFAULT_SMKT_CATEGORY_CODE = "g2_smkt_shop";

    @Resource
    protected TenantStoreClient tenantStoreClient;
    @Resource
    protected MaltChannelAggDeliveryThriftService maltChannelAggDeliveryThriftService;
    @Resource
    protected DapChannelAggDeliveryThriftService dapChannelAggDeliveryThriftService;

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "AggDeliveryPlatformServiceWrapper-createShop", fallBackMethod = "createShopFallback", timeoutInMilliseconds = 2000)
    public Optional<Long> createShop(DeliveryPoi deliveryPoi) {
        Optional<CreateAggDeliveryShopRequest> request = buildDeliveryStoreCreateRequestForEmpowerStore(deliveryPoi);

        if (!request.isPresent()) {
            return Optional.empty();
        }
        //最终判断经纬度为空则抛异常
        if (Objects.isNull(request.get().getShopLatitude()) || Objects.isNull(request.get().getShopLongitude())) {
            throw new SystemException(TENANT_POI_DELIVERY_POI_LATITUDE_LONGITUDE_EMPTY.getMessage());
        }

        try {
            CreateAggDeliveryShopResponse response;

            DeliveryPlatformEnum deliveryPlatformEnum = getDeliveryPlatformEnum();
            if (deliveryPlatformEnum == DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM) {
                response = maltChannelAggDeliveryThriftService.createAggDeliveryShop(request.get());
            } else if (deliveryPlatformEnum == DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM) {
                response = dapChannelAggDeliveryThriftService.createAggDeliveryShop(request.get());
            } else {
                throw new SystemException(NO_CHANNEL_SUPPORT.getMessage());
            }
            if (response == null || response.getCode() == null || response.getCode() != BigInteger.ZERO.intValue()) {
                log.error("createShop error request:{},deliveryPlatform:{},response:{}", request.get(), deliveryPoi.getDeliveryPlatform(), response);
                Cat.logEvent("CREATE_DELIVERY_SHOP_FAIL", "CREATE_FAILED");
                throw new SystemException(PLATFORM_CREATE_SHOP_EXCEPTION.getMessage());
            }
            if (response.getAggDeliveryShopId() == null) {
                return Optional.empty();
            }
            return Optional.of(response.getAggDeliveryShopId());
        } catch (Exception e) {
            log.error("createShop error request:{},deliveryPlatform:{}", request.get(), deliveryPoi.getDeliveryPlatform(), e);
            throw new SystemException(PLATFORM_CREATE_SHOP_EXCEPTION.getMessage());
        }
    }

    private Optional<CreateAggDeliveryShopRequest> buildDeliveryStoreCreateRequestForEmpowerStore(DeliveryPoi deliveryPoi) {
        //获取门店信息
        Optional<TenantChannelStoreInfo> channelStore = tenantStoreClient.queryChannelStoreDetailInfoWithAnyChannel(deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), DynamicChannelType.MEITUAN.getChannelId());

        if (!channelStore.isPresent()) {
            log.warn("queryChannelStoreDetailInfo mt error tenantId:{},storeId:{},deliveryPlatform:{} ,result:{}",
                    deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), deliveryPoi.getDeliveryPlatform(), channelStore);
            return Optional.empty();
        }

        TenantInfoDto tenantInfoDto = tenantStoreClient.queryTenantInfo(deliveryPoi.getTenantId());
        String categoryCode = getCategoryCode(deliveryPoi.getTenantId());
        return Optional.of(CreateAggDeliveryShopRequest
                .builder()
                .shopId(channelStore.get().getShopId())
                .aggDeliveryPlatformId(deliveryPoi.getDeliveryPlatform().getCode())
                .cityCode(channelStore.get().getCityCode() + "")
                .districtCode(channelStore.get().getAreaCode() + "")
                .shopAddress(channelStore.get().getAddress())
                .shopLatitude(Double.parseDouble(channelStore.get().getLatitude()))
                .shopLongitude(Double.parseDouble(channelStore.get().getLongitude()))
                .shopName(channelStore.get().getStoreName())
                // 设置请求的发送者电话，根据租户和门店信息获取门店的电话号码
                .shopPhone(channelStore.get().getPhone())
                .tenantId(channelStore.get().getTenantId())
                .mapType(BigInteger.ONE.intValue())
                .category(BigInteger.ONE.intValue())
                .tenantName(tenantInfoDto == null ? "" : tenantInfoDto.getTenantName())
                .appInfo(getAppInfo())
                .categoryCode(categoryCode)
                .build());
    }

    public String getCategoryCode(Long tenantId) {
        Map<String, List<String>> map = LionConfigUtils.getDeliveryPlatformCategoryMapping();
        Map<String, String> tenantMap = LionConfigUtils.getTenantCategoryMapping();
        try {

            if (tenantMap.containsKey(String.valueOf(tenantId))) {
                return map.get(tenantMap.get(String.valueOf(tenantId))).get(0);
            }

            List<String> categories = tenantStoreClient.queryTenantCategory(tenantId);

            // 无主营品类
            if (categories.isEmpty()) {
                return DEFAULT_SMKT_CATEGORY_CODE;
            }
            // 有主营品类
            for (List<String> codes : map.values()) {
                if (codes.containsAll(categories)) {
                    return categories.get(0);
                }
            }
            // 主营类目都在lion映射里
            List<String> allMapping = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            if (allMapping.containsAll(categories)) {
                return map.get(String.valueOf(DapCategoryEnum.FLOWERS.getCode())).get(0);
            }
            // 有主营类目不在lion配置中的
            return DEFAULT_SMKT_CATEGORY_CODE;
        } catch (Exception e) {
            log.info("AggDeliveryWrapper.getCategoryCode error: ", e);
            return DEFAULT_SMKT_CATEGORY_CODE;
        }
    }

    private AggDeliveryPlatformAppDTO getAppInfo() {
        return Optional.ofNullable(getAggDeliveryPlatformAppConfig(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode()))
                .map(it -> new AggDeliveryPlatformAppDTO(it.getAppKey(), it.getAppSecret()))
                .orElse(null);
    }

    private static AggDeliveryPlatformAppConfig getAggDeliveryPlatformAppConfig(Integer platformCode) {
        if (platformCode == null) {
            return null;
        }
        String configValue = LionConfigUtils.getAggDeliveryPlatformAppConfig();
        if (StringUtils.isEmpty(configValue)) {
            return null;
        }
        try {
            Map<String, AggDeliveryPlatformAppConfig> configMap = JsonUtil.fromJson(configValue, new TypeReference<Map<String,
                    AggDeliveryPlatformAppConfig>>() {
            });

            return configMap.get(platformCode.toString());
        } catch (Exception e) {
            log.info("search AggDeliveryPlatformAppConfig error, exception", e);
            return null;
        }
    }

    public Optional<Long> createShopFallback(DeliveryPoi deliveryPoi) {
        log.error("createShopFallback : deliveryPoi:{}", deliveryPoi);
        throw new SystemException(PLATFORM_CREATE_SHOP_EXCEPTION.getMessage());
    }


}
