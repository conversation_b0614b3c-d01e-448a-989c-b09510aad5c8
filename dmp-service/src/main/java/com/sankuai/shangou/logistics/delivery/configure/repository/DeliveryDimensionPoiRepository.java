package com.sankuai.shangou.logistics.delivery.configure.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.enums.CompletedSortModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.InternalNavigationModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.SelfDeliveryModeEnum;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryDimensionPoi;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeConfig;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.BatchTaskConfigContent;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.ConfigTemplateItemModel;
import com.sankuai.shangou.logistics.delivery.configure.service.converter.DeliveryConfigConverter;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryCompleteMode;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryRemindConfig;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreDimensionConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.StoreDimensionConfigDOExMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDOExample;
import com.sankuai.shangou.logistics.delivery.poi.client.PoiQueryClient;
import com.sankuai.shangou.logistics.delivery.poi.client.dto.PoiBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/28
 */
@Slf4j
@Repository
public class DeliveryDimensionPoiRepository {

    private static final int ENABLED = 1;
    private static final int NON_DELETED = 0;

    @Resource
    private StoreDimensionConfigDOMapper storeDimensionConfigDOMapper;
    @Resource
    private StoreDimensionConfigDOExMapper storeDimensionConfigDOExMapper;
    @Resource
    private ConfigTemplateRepository configTemplateRepository;
    @Resource
    private DeliveryConfigConverter deliveryConfigConverter;
    @Resource
    private PoiQueryClient poiQueryClient;
    @Resource
    private ConfigTemplateItemRepository configTemplateItemRepository;

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public DeliveryDimensionPoi queryDeliveryDimensionPoi(Long tenantId, Long storeId) {
        try {
            StoreDimensionConfigDOExample example = new StoreDimensionConfigDOExample();
            example.createCriteria()
                    .andTenantIdEqualTo(tenantId)
                    .andStoreIdEqualTo(storeId)
                    .andEnableEqualTo(ENABLED);

            List<StoreDimensionConfigDO> records = storeDimensionConfigDOMapper.selectByExample(example);

            DeliveryDimensionPoi deliveryDimensionPoi = null;
            if (CollectionUtils.isNotEmpty(records)) {
                StoreDimensionConfigDO record = records.get(0);
                deliveryDimensionPoi = convertToDeliveryDimensionPoi(record);
            } else {
                deliveryDimensionPoi = tryMakeUpMissingDeliveryDimensionPoi(tenantId, storeId);
            }
            return deliveryDimensionPoi;
        } catch (Exception e) {
            log.error("查询配送维度门店配置失败, tenantId: {}, storeId: {}", tenantId, storeId, e);
            return null;
        }
    }

    @CatTransaction
    public List<DeliveryDimensionPoi> batchQueryByStoreIds(Long tenantId, List<Long> storeIds) {
        try {
            StoreDimensionConfigDOExample example = new StoreDimensionConfigDOExample();
            example.createCriteria()
                    .andTenantIdEqualTo(tenantId)
                    .andStoreIdIn(storeIds)
                    .andEnableEqualTo(ENABLED);

            List<StoreDimensionConfigDO> records = storeDimensionConfigDOMapper.selectByExample(example);
            return records.stream()
                    .map(this::convertToDeliveryDimensionPoi)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量查询配送维度门店配置失败, tenantId: {}, storeIds: {}", tenantId, storeIds, e);
            return Collections.emptyList();
        }
    }

    @CatTransaction
    @MethodLog(logRequest = false)
    public void insertDeliveryDimensionPoi(DeliveryDimensionPoi deliveryDimensionPoi) {
        try {
            if (deliveryDimensionPoi == null) {
                log.warn("插入配送维度门店配置失败，deliveryDimensionPoi为空");
                return;
            }

            StoreDimensionConfigDO record = convertToStoreDimensionConfigDO(deliveryDimensionPoi);
            LocalDateTime now = LocalDateTime.now();
            record.setEnable(ENABLED);
            record.setCreatedAt(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
            record.setUpdatedAt(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));

            storeDimensionConfigDOMapper.insertSelective(record);
            log.info("插入配送维度门店配置成功, tenantId: {}, storeId: {}",
                    deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId());
        } catch (Exception e) {
            log.error("插入配送维度门店配置失败, tenantId: {}, storeId: {}",
                    deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId(), e);
            throw new RuntimeException("插入配送维度门店配置失败", e);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true)
    public void updateDeliveryDimensionPoi(DeliveryDimensionPoi deliveryDimensionPoi) {
        try {
            if (deliveryDimensionPoi == null) {
                log.warn("更新配送维度门店配置失败，deliveryDimensionPoi为空");
                return;
            }

            StoreDimensionConfigDO record = convertToStoreDimensionConfigDO(deliveryDimensionPoi);
            record.setUpdatedAt(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));

            int updateCount = storeDimensionConfigDOMapper.updateByPrimaryKeySelective(record);
            if (updateCount > 0) {
                log.info("更新配送维度门店配置成功, tenantId: {}, storeId: {}",
                        deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId());
            } else {
                log.warn("更新配送维度门店配置失败，未找到匹配记录, tenantId: {}, storeId: {}",
                        deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId());
            }
        } catch (Exception e) {
            throw new RuntimeException("更新配送维度门店配置失败", e);
        }
    }

    public int batchUpdateById(List<DeliveryDimensionPoi> deliveryDimensionPois) {
        if (CollectionUtils.isEmpty(deliveryDimensionPois)) {
            return 0;
        }
        List<StoreDimensionConfigDO> list = deliveryDimensionPois.stream()
                .map(this::convertToStoreDimensionConfigDO)
                .peek(item -> item.setUpdatedAt(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant())))
                .collect(Collectors.toList());
        return storeDimensionConfigDOExMapper.batchUpdateByPrimaryKey(list);
    }

    private DeliveryDimensionPoi tryMakeUpMissingDeliveryDimensionPoi(Long tenantId, Long storeId) {

        //补建一个配送门店，并保存
        DeliveryDimensionPoi deliveryDimensionPoi = new DeliveryDimensionPoi(
                0L, tenantId, storeId,
                InternalNavigationModeEnum.GAODE,
                null,
                DeliveryCompleteMode.makeDefault(),
                null,
                null,
                null,
                LocalDateTime.now(),
                LocalDateTime.now()
        );

        //从模版更新
        PoiBaseInfo poiBaseInfo = poiQueryClient.querySinglePoiBaseInfo(tenantId, storeId);
        //先找模版
        Map<DeliveryConfigTypeEnum, ConfigTemplateItemModel> configTypeAndTemplateItemMap = configTemplateItemRepository.queryEffectiveConfigTemplateItemModel(tenantId, poiQueryClient.extractOperationMode(poiBaseInfo), DeliveryConfigTypeEnum.getStoreDimensionTypeEnum());
        if (CollectionUtils.isNotEmpty(configTypeAndTemplateItemMap.values())) {
            List<BatchTaskConfigContent> batchTaskConfigContents = IListUtils.mapTo(configTypeAndTemplateItemMap.values(), item -> new BatchTaskConfigContent(item.getTemplateType(), item.getConfigContent()));
            DeliveryConfigDetailVO.SelfDeliveryConfigVO selfDeliveryConfigVO = deliveryConfigConverter.covertSelfDeliveryConfigVO(batchTaskConfigContents);
            deliveryDimensionPoi = deliveryConfigConverter.updateDeliveryDimensionPoiFromRequest(deliveryDimensionPoi, selfDeliveryConfigVO);
        }

        insertDeliveryDimensionPoi(deliveryDimensionPoi);

        return deliveryDimensionPoi;
    }

    /**
     * 将StoreDimensionConfigDO转换为DeliveryDimensionPoi
     */
    private DeliveryDimensionPoi convertToDeliveryDimensionPoi(StoreDimensionConfigDO record) {
        if (record == null) {
            return null;
        }

        try {
            // 转换枚举类型
            SelfDeliveryModeEnum selfDeliveryMode = SelfDeliveryModeEnum.getByMode(record.getSelfDeliveryMode());
            InternalNavigationModeEnum internalNavigationMode = InternalNavigationModeEnum.getByMode(record.getInternalNavigationMode());
            CompletedSortModeEnum completedSortMode = CompletedSortModeEnum.getByMode(record.getCompletedSortMode());

            // 转换JSON字段
            List<AssessTimeConfig> assessTimeConfigs = Lists.newArrayList();
            if (StringUtils.isNotEmpty(record.getAssessTimeConfig())) {
                assessTimeConfigs = JSON.parseArray(record.getAssessTimeConfig(), AssessTimeConfig.class);
            }
            DeliveryCompleteMode deliveryCompleteMode = parseJsonField(record.getDeliveryCompleteMode(), DeliveryCompleteMode.class);
            DeliveryRemindConfig deliveryRemindConfig = parseJsonField(record.getDeliveryRemindConfig(), DeliveryRemindConfig.class);

            // 转换角色ID列表
            List<Long> riderTransRoles = parseJsonField(record.getRiderTransRoles(), new TypeReference<List<Long>>() {
            });

            return new DeliveryDimensionPoi(
                    record.getId(),
                    record.getTenantId(),
                    record.getStoreId(),
                    internalNavigationMode,
                    assessTimeConfigs,
                    deliveryCompleteMode,
                    riderTransRoles,
                    completedSortMode,
                    deliveryRemindConfig, LocalDateTime.now(), LocalDateTime.now()
            );
        } catch (Exception e) {
            log.error("转换StoreDimensionConfigDO到DeliveryDimensionPoi失败, id: {}", record.getId(), e);
            throw new RuntimeException("转换StoreDimensionConfigDO到DeliveryDimensionPoi失败", e);
        }
    }

    /**
     * 将DeliveryDimensionPoi转换为StoreDimensionConfigDO
     */
    private StoreDimensionConfigDO convertToStoreDimensionConfigDO(DeliveryDimensionPoi deliveryDimensionPoi) {
        if (deliveryDimensionPoi == null) {
            return null;
        }

        try {
            StoreDimensionConfigDO record = new StoreDimensionConfigDO();

            // 设置基本字段
            record.setId(deliveryDimensionPoi.getId());
            record.setTenantId(deliveryDimensionPoi.getTenantId());
            record.setStoreId(deliveryDimensionPoi.getStoreId());

            // 转换枚举字段
            record.setInternalNavigationMode(deliveryDimensionPoi.getInternalNavigationMode() != null ?
                    deliveryDimensionPoi.getInternalNavigationMode().getMode() : null);
            record.setCompletedSortMode(deliveryDimensionPoi.getCompletedSortMode() != null ?
                    deliveryDimensionPoi.getCompletedSortMode().getMode() : null);

            // 转换JSON字段
            record.setAssessTimeConfig(toJsonString(deliveryDimensionPoi.getAssessTimeConfigs()));
            record.setDeliveryCompleteMode(toJsonString(deliveryDimensionPoi.getDeliveryCompleteMode()));
            record.setDeliveryRemindConfig(toJsonString(deliveryDimensionPoi.getDeliveryRemindConfig()));
            record.setRiderTransRoles(toJsonString(deliveryDimensionPoi.getRiderTransRoles()));

            // 设置时间字段
            record.setCreatedAt(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
            record.setUpdatedAt(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));

            return record;
        } catch (Exception e) {
            log.error("转换DeliveryDimensionPoi到StoreDimensionConfigDO失败, tenantId: {}, storeId: {}",
                    deliveryDimensionPoi.getTenantId(), deliveryDimensionPoi.getStoreId(), e);
            throw new RuntimeException("转换DeliveryDimensionPoi到StoreDimensionConfigDO失败", e);
        }
    }

    /**
     * 解析JSON字段为指定类型
     */
    private <T> T parseJsonField(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JSON.parseObject(json, clazz);
        } catch (Exception e) {
            log.warn("解析JSON字段失败, json: {}, class: {}", json, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 解析JSON字段为指定类型（支持泛型）
     */
    private <T> T parseJsonField(String json, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return JSON.parseObject(json, typeReference);
        } catch (Exception e) {
            log.warn("解析JSON字段失败, json: {}, typeReference: {}", json, typeReference.getType(), e);
            return null;
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return JSON.toJSONString(obj);
        } catch (Exception e) {
            log.warn("对象转换为JSON字符串失败, obj: {}", obj, e);
            return null;
        }
    }
}
