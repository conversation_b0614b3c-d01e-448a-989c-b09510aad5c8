package com.sankuai.shangou.logistics.delivery.configure.external;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.TenantThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiRelationTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfosResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiRelationQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiRelationSimpleMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantBaseResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantInfoResponse;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.sgfnqnh.poi.base.client.common.PoiBaseResult;
import com.sankuai.sgfnqnh.poi.base.client.thrift.WarehouseCoreThriftService;
import com.sankuai.sgfnqnh.poi.base.client.thrift.dto.warehouse.WarehouseDetailDTO;
import com.sankuai.shangou.logistics.delivery.configure.pojo.dto.TenantChannelStoreInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户系统客户端实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Slf4j
@Service
public class TenantStoreClient {
	private static final int SUCCESS = 0;

	@Resource
	private ChannelPoiManageThriftService channelPoiManageThriftServiceClient;
	@Resource
	private PoiThriftService poiThriftServiceClient;

	@Autowired
	@Qualifier("tenantThriftService")
	private TenantThriftService tenantThriftService;
    @Resource
    private PoiRelationThriftService poiRelationThriftService;
	@Resource
	private WarehouseCoreThriftService warehouseCoreThriftService;


	@CatTransaction
	public PoiInfoDto queryStoreDetailInfo(Long tenantId, Long storeId) {
		try {
			PoiMapResponse response = poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantId);
			if (response.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())){
                return response.getOne(storeId);
			}
		} catch (Exception e) {
			log.error("queryStoreDetailInfo error", e);
		}
		return null;
	}

	@MethodLog(logRequest = false, logResponse = true)
	public PoiDetailInfoDTO queryChannelStoreDetailInfo(Long tenantId, Long storeId) {
		PoiDetailInfoResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId(tenantId, storeId);
		log.info("channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId reponse = {}", response);
		if (response.getStatus().getCode() != SUCCESS || response.getPoiDetailInfoDTO() == null) {
			throw new SystemException("查询渠道门店失败");
		}
        return response.getPoiDetailInfoDTO();
	}

	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, List<Long>> batchQueryRelationMapByPoiIds(Long tenantId, List<Long> poiIdList,
															   boolean reverseRelation) {
		if (CollectionUtils.isEmpty(poiIdList)) {
			return Collections.emptyMap();
		}
		PoiRelationQueryRequest relationQueryRequest = new PoiRelationQueryRequest();
		relationQueryRequest.setTenantId(tenantId);
		relationQueryRequest.setPoiIdList(poiIdList);
		relationQueryRequest.setRelationType(PoiRelationTypeEnum.STORE_SHAREABLE_WAREHOUSE_RELATION.code());
		relationQueryRequest.setReverseRelation(reverseRelation);
		try {
			PoiRelationSimpleMapResponse response = poiRelationThriftService
					.batchQueryRelationMapByPoiIds(relationQueryRequest);
			if (response == null || MapUtils.isEmpty(response.getPoiRelationMap())) {
				return Collections.emptyMap();
			}
			return new HashMap<>(response.getPoiRelationMap());
		} catch (Exception e) {
			log.error("batchQueryRelationMapByPoiIds error", e);
		}
		return Collections.emptyMap();
	}

	@MethodLog(logRequest = false, logResponse = true)
	public Map<Long, List<TenantChannelStoreInfo>> queryChannelStoreDetailInfoList(Long tenantId, List<Long> storeIds) {
		if (CollectionUtils.isEmpty(storeIds)) {
			return Collections.emptyMap();
		}
		PoiDetailInfosResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds(tenantId, storeIds);
		log.info("channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds response = {}", response);
		if (response.getStatus().getCode() != SUCCESS || response.getPoiDetailInfoDTOs() == null) {
			log.error("invoke channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiIds fail, tenantId:{}, resp:{}", tenantId, response);
			throw new RuntimeException("查询渠道门店信息失败");
		}
		HashMap<Long, List<TenantChannelStoreInfo>> poiChannelInfoMap = new HashMap<>();
		for (PoiDetailInfoDTO poiDetailInfoDTO : response.getPoiDetailInfoDTOs()) {

			List<TenantChannelStoreInfo> poiChannelInfo = Optional.ofNullable(poiDetailInfoDTO.getChannelPoiInfoList())
					.orElse(Collections.emptyList())
					.stream()
					.map(poiInfo -> TenantChannelStoreInfo.builder()
							.cityCode(poiDetailInfoDTO.getDistrict().getCityId())
							.cityName(poiDetailInfoDTO.getDistrict().getCityName())
							.areaCode(poiDetailInfoDTO.getDistrict().getAreaId())
							.phone(poiDetailInfoDTO.getPoiPhoneNum())
							.storeName(poiDetailInfoDTO.getPoiName())
							.latitude(String.valueOf(poiInfo.getLatitude()))
							.longitude(String.valueOf(poiInfo.getLongitude()))
							.channelId(poiInfo.getChannelId())
							.channelPoiId(poiInfo.getChannelPoiId())
							.channelInnerPoiId(poiInfo.getChannelInnerPoiId())
							.address(poiDetailInfoDTO.getPoiAddress())
							.tenantId(tenantId)
							.shopId(poiDetailInfoDTO.getPoiId())
							.build()).collect(Collectors.toList());

			poiChannelInfoMap.put(poiDetailInfoDTO.getPoiId(), poiChannelInfo);
		}

		return poiChannelInfoMap;

	}

	@MethodLog(logRequest = false, logResponse = true)
	public Optional<TenantChannelStoreInfo> queryChannelStoreDetailInfoWithAnyChannel(Long tenantId, Long storeId,Integer channelType) {
		PoiDetailInfoResponse response = channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId(tenantId, storeId);
		log.info("channelPoiManageThriftServiceClient.queryPoiDetailInfoByPoiId reponse = {}", response);
		if (response.getStatus().getCode() != SUCCESS || response.getPoiDetailInfoDTO() == null) {
			return Optional.empty();
		}
		PoiDetailInfoDTO poiDetailInfo = response.getPoiDetailInfoDTO();
		if(poiDetailInfo==null){
			return Optional.empty();
		}

		Double longitude = poiDetailInfo.getLongitude();
		Double latitude = poiDetailInfo.getLatitude();
		if(longitude==null || latitude==null){
			if(CollectionUtils.isNotEmpty(poiDetailInfo.getChannelPoiInfoList())){
				Optional<ChannelPoiInfoDTO> poiInfoDTOOptional = poiDetailInfo.getChannelPoiInfoList()
						.stream()
						.filter(store -> store.getChannelId() == (channelType == null
								? DynamicChannelType.MEITUAN.getChannelId()
								: channelType))
						.findAny();
				if(!poiInfoDTOOptional.isPresent()){
					poiInfoDTOOptional=poiDetailInfo.getChannelPoiInfoList().stream().findAny();
				}
				if(poiInfoDTOOptional.isPresent()){
					longitude = poiInfoDTOOptional.get().getLongitude();
					latitude = poiInfoDTOOptional.get().getLatitude();
				}
			}
		}

		String lat="";
		String lon="";
		if(latitude!=null){
			lat=String.valueOf(latitude);
		}
		if(longitude!=null){
			lon=String.valueOf(longitude);
		}


		return Optional.of(TenantChannelStoreInfo.builder()
				.cityCode(poiDetailInfo.getDistrict().getCityId())
				.areaCode(poiDetailInfo.getDistrict().getAreaId())
				.phone(poiDetailInfo.getPoiPhoneNum())
				.storeName(poiDetailInfo.getPoiName())
				.latitude(lat)
				.longitude(lon)
				.address(poiDetailInfo.getPoiAddress())
				.tenantId(tenantId)
				.shopId(storeId)
				.servicePhone(poiDetailInfo.getServicePhone())
				.build());

	}

	@MethodLog(logRequest = false, logResponse = true)
	public TenantInfoDto queryTenantInfo(Long tenantId){
		try {
			TenantInfoResponse response=tenantThriftService.queryTenantInfoByTenantId(tenantId);
			TenantInfoDto tenantInfoDto=response.getTenantInfo();
			if(tenantInfoDto==null){
				return null;
			}
			return response.getTenantInfo();
		}catch (Exception e){
			log.error("query tenant info error tenantId:{}",tenantId,e);
		}
		return null;
	}

	@MethodLog(logRequest = true, logResponse = true)
	public List<String> queryTenantCategory(Long tenantId) {
		List<String> list = new ArrayList<>();
		try {
			TenantBaseResponse response = tenantThriftService.getTenantBase(tenantId);
			if (response == null || response.getStatus().getCode() != SUCCESS || response.getTenantBaseDto() == null) {
				return list;
			}
			List<String> categories = response.getTenantBaseDto().getCategories();
			if (CollectionUtils.isEmpty(categories)) {
				return list;
			}
			categories.stream().filter(StringUtils::isNotBlank).forEach(item -> list.add(item.trim()));
		} catch (Exception e) {
			log.info("TenantSystemClient.queryTenantCategory error: ", e);
		}
		return list;
	}

	@MethodLog(logRequest = true, logResponse = true)
	public List<WarehouseDetailDTO> queryWarehouseBaseByTenantWarehouseIds(long tenantId, List<Long> warehouseId) {
		PoiBaseResult<List<WarehouseDetailDTO>> result = warehouseCoreThriftService.batchQueryWarehouseDetailByTenantWarehouseIds(tenantId, warehouseId);
		if (!result.isSuccess()) {
			throw new SystemException("查询店仓基础信息失败");
		}
		return result.getData();
	}

	@MethodLog(logRequest = true, logResponse = true)
	public WarehouseDetailDTO queryWarehouseBaseByTenantWarehouseId(long tenantId, long warehouseId) {
		PoiBaseResult<List<WarehouseDetailDTO>> result = warehouseCoreThriftService.batchQueryWarehouseDetailByTenantWarehouseIds(tenantId, Lists.newArrayList(warehouseId));
		if (!result.isSuccess()) {
			throw new SystemException("查询店仓基础信息失败");
		}
		if (CollectionUtils.isEmpty(result.getData())) {
			return null;
		}
		return result.getData().get(0);
	}


}
