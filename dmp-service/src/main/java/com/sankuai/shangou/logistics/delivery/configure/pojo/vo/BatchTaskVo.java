package com.sankuai.shangou.logistics.delivery.configure.pojo.vo;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.logistics.delivery.configure.pojo.model.BatchTaskConfigContent;
import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.dao.config.model.ConfigTaskDO;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-01
 */
@Data
public class BatchTaskVo {
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 配置类型
     *
     * @see DeliveryConfigTypeEnum code
     */
    private List<Integer> configTypeList;
    /**
     * 任务状态: 0-处理中 1-已完成 2-处理失败
     */
    private Integer status;
    /**
     * 任务总数
     */
    private Integer totalNum;
    /**
     * 成功数量
     */
    private Integer successNum;
    /**
     * 失败数量
     */
    private Integer failNum;
    /**
     * 操作时间
     */
    private Long operateTime;
    /**
     * 操作人（牵牛花账号）
     */
    private String operator;
    /**
     * 操作人姓名
     */
    private String operatorName;

    public static BatchTaskVo convert(ConfigTaskDO configTaskDO) {
        List<BatchTaskConfigContent> contentList = JSON.parseArray(configTaskDO.getConfigDetail(), BatchTaskConfigContent.class);
        List<Integer> configTypeList = contentList.stream()
                .map(BatchTaskConfigContent::getType)
                .map(DeliveryConfigTypeEnum::getCode)
                .collect(Collectors.toList());

        BatchTaskVo vo = new BatchTaskVo();
        vo.setTaskId(configTaskDO.getId());
        vo.setTaskName(configTaskDO.getTaskName());
        vo.setStatus(configTaskDO.getStatus());
        vo.setTotalNum(configTaskDO.getTotalNum());
        vo.setSuccessNum(configTaskDO.getSuccessNum());
        vo.setFailNum(configTaskDO.getFailNum());
        vo.setConfigTypeList(configTypeList);
        vo.setOperateTime(configTaskDO.getCreatedAt().getTime());
        vo.setOperator(configTaskDO.getOperator());
        vo.setOperatorName(configTaskDO.getOperatorName());
        return vo;
    }

}
