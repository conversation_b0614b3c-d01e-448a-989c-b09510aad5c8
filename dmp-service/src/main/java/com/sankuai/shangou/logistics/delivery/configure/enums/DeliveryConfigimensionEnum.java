package com.sankuai.shangou.logistics.delivery.configure.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025-08-25
 * @email <EMAIL>
 */
public enum DeliveryConfigimensionEnum {

    CHANNEL_DIMENSION(1, "渠道维度配置"),
    STORE_DIMENSION(2, "门店维度配置"),
    ;

    private final int code;
    private final String desc;

    DeliveryConfigimensionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DeliveryConfigimensionEnum enumOf(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(item.getCode(),code))
                .findFirst().orElse(null);
    }
}
