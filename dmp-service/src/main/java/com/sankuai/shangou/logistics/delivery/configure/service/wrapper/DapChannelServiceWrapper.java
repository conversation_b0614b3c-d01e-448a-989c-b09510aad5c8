package com.sankuai.shangou.logistics.delivery.configure.service.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.meituan.shangou.empower.ocms.channel.enums.DeliveryResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.LinkTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.configure.model.DeliveryPoi;
import com.sankuai.shangou.logistics.delivery.configure.pojo.dto.DeliveryPlatformUrlInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-23
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class DapChannelServiceWrapper extends AbstractAggDeliveryPlatformServiceWrapper {

    @Resource
    protected DapChannelAggDeliveryThriftService dapChannelAggDeliveryThriftService;

    @Override
    protected DeliveryPlatformEnum getDeliveryPlatformEnum() {
        return DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM;
    }

    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public DeliveryShopAuthResponse deliveryShopAuth(DeliveryPlatformEnum deliveryPlatform, Long storeId) {

        DeliveryShopAuthRequest request = new DeliveryShopAuthRequest();
        request.setStoreId(storeId);
        request.setAggDeliveryPlatformId(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
        RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(3,100);

        try{
            DeliveryShopAuthResponse response = retryTemplate.execute(new RetryCallback<DeliveryShopAuthResponse, Exception>() {
                @Override
                public DeliveryShopAuthResponse doWithRetry(RetryContext retryContext) throws Exception {
                    DeliveryShopAuthResponse response = dapChannelAggDeliveryThriftService.deliveryShopAuth(request);
                    if(response ==null || response.getCode()!= FailureCodeEnum.SUCCESS.getCode()){
                        if(response != null && response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()){
                            return response;
                        }
                        throw new SystemException(FailureCodeEnum.QUERY_SHOP_AUTH_EXCEPTION.getMessage());
                    }
                    return response;
                }
            });

            return response;
        }catch (Exception e){
            log.error("deliveryShopAuth error",e);
            return null;
        }
    }

    @CatTransaction
    @Degrade(rhinoKey = "DapDeliveryPlatformClient.queryLinkInfo", fallBackMethod = "queryLinkInfoFallback", timeoutInMilliseconds = 1000)
    public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfo(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum) {
        if(deliveryPoi==null || CollectionUtils.isEmpty(markIdList) || typeEnum==null){
            return Optional.empty();
        }
        Map<String, DeliveryPlatformUrlInfo> deliveryPlatformUrlInfoMap = batchQueryDapLinkInfo(deliveryPoi.getStoreId(), markIdList, typeEnum);
        return Optional.ofNullable(deliveryPlatformUrlInfoMap);
    }

    private Map<String, DeliveryPlatformUrlInfo> batchQueryDapLinkInfo(Long storeId, List<String> markIdList, LinkTypeEnum typeEnum) {
        return batchQueryDapLinkInfoOfToken(storeId, markIdList, typeEnum, null, null);
    }

    private Map<String, DeliveryPlatformUrlInfo> batchQueryDapLinkInfoOfToken(Long storeId, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType) {
        if (!LionConfigUtils.dapLinkNoAuthSwitch()) {
            return Maps.newHashMap();
        }
        if (Objects.isNull(storeId) || CollectionUtils.isEmpty(markIdList) || Objects.isNull(typeEnum)) {
            return Maps.newHashMap();
        }

        Map<String, DeliveryPlatformUrlInfo> result = Maps.newHashMap();

        List<List<String>> partList = Lists.partition(markIdList, LionConfigUtils.dapUrlMarkIdPartNum());
        for (List<String> part : partList){
            GetAggDeliveryLinkRequest request=new GetAggDeliveryLinkRequest();
            request.setShopId(storeId);
            request.setIds(Joiner.on(",").join(part));
            request.setAggDeliveryPlatformId(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
            request.setType(typeEnum.getType());
            request.setToken(token);
            if (Objects.nonNull(siteType)) {
                request.setDeviceType(siteType.getCode());
            }

            RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(3, 100);
            try {
                GetAggDeliveryLinkResponse response = retryTemplate.execute((RetryCallback<GetAggDeliveryLinkResponse, Exception>) retryContext -> {
                    GetAggDeliveryLinkResponse res = dapChannelAggDeliveryThriftService.getAggDeliveryLink(request);
                    if (res == null || res.getCode() != FailureCodeEnum.SUCCESS.getCode()) {
                        if (res != null && res.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()) {
                            return res;
                        }
                        throw new BizException(FailureCodeEnum.QUERY_PLATFORM_LINK_EXCEPTION.getCode(), FailureCodeEnum.QUERY_PLATFORM_LINK_EXCEPTION.getMessage());
                    }
                    return res;
                });
                if (response == null || response.getCode() != FailureCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(response.getLinkDetailList())) {
                    continue;
                }
                for (AggDeliveryLinkDetailDTO linkDetailDTO : response.getLinkDetailList()) {
                    result.put(linkDetailDTO.getId(), DeliveryPlatformUrlInfo.builder()
                            .linkTitle(linkDetailDTO.getTitle())
                            .linkUrl(linkDetailDTO.getUrl())
                            .markId(linkDetailDTO.getId())
                            .build());
                }
            } catch (Exception e) {
                log.error("queryLinkInfo error storeId:{},markIdList:{},typeEnum:{}", storeId, markIdList, typeEnum, e);
            }
        }

        return result;
    }

    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public void deliveryFastAuth(DeliveryPoi deliveryPoi, List<Long> wmStoreIdList, List<ChannelStoreRelation> relationList) {

        DeliveryFastAuthRequest request = new DeliveryFastAuthRequest();
        request.setStoreId(deliveryPoi.getStoreId());
        request.setWmStoreIdList(wmStoreIdList);
        request.setAggDeliveryPlatformId(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
        if(CollectionUtils.isNotEmpty(relationList)){
            request.setRelationList(relationList.stream().map(item->new com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.ChannelStoreRelation(item.getStoreId(),item.getChannelStoreId())).collect(Collectors.toList()));
        }

        try{
            DeliveryFastAuthResponse response = dapChannelAggDeliveryThriftService.deliveryFastAuth(request);
            if (response == null || response.getCode() == null) {
                throw new SystemException(FailureCodeEnum.FAST_AUTH_EXCEPTION.getMessage());
            }
            if (response.getCode() == DeliveryResultCodeEnum.DAP_FALLBACK_ERROR.getCode()) {
                throw new SystemException(FailureCodeEnum.FAST_AUTH_EXCEPTION.getMessage());
            } else if (response.getCode() != DeliveryResultCodeEnum.SUCCESS.getCode()) {
                throw new SystemException(FailureCodeEnum.FAST_AUTH_EXCEPTION.getMessage());
            }
        }catch (Exception e){
            log.error("deliveryFastAuth error",e);
        }
    }

    public Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfoFallback(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum) {
        log.info("queryLinkInfo fall back markIdList:{}",markIdList);
        return Optional.empty();
    }
}
