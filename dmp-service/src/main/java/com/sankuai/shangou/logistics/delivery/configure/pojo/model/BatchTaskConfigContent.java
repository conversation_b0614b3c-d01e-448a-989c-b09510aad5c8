package com.sankuai.shangou.logistics.delivery.configure.pojo.model;

import com.sankuai.shangou.logistics.delivery.configure.enums.DeliveryConfigTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchTaskConfigContent {

    private DeliveryConfigTypeEnum type;
    /**
     * 配置内容
     * @see DeliveryConfigTypeEnum#getCls()
     * 分渠道的配置：key为渠道id， value为配置内容
     */
    private Map<String, Object> content;
}
