package com.sankuai.shangou.logistics.delivery.configure.service.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.sgfnqnh.poi.api.client.thrift.TenantThriftService;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.tenant.ChannelInfoDTO;
import com.sankuai.sgfnqnh.poi.api.client.thrift.dto.tenant.response.TenantChannelMapResponse;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-31
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class TenantChannelServiceWrapper {

    @Autowired
    @Qualifier("qnhTenantThriftService")
    private TenantThriftService tenantThriftService;

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(rhinoKey = "TenantChannelServiceWrapper.getTenantChannelType", fallBackMethod = "getTenantChannelTypeFallback", timeoutInMilliseconds = 2000)
    public List<Integer> getTenantChannelType(Long tenantId) {
        try {
            TenantChannelMapResponse response = tenantThriftService.batchGetTenantChannels(Collections.singletonList(tenantId));
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                log.error("TenantChannelThriftServiceWrapper queryTenantChannel error, response is null");
                return Collections.emptyList();
            }
            if (FailureCodeEnum.SUCCESS.getCode() != response.getStatus().getCode()) {
                log.error("TenantChannelThriftServiceWrapper queryTenantChannel failed, response code is not success");
                return Collections.emptyList();
            }
            Map<Long, List<ChannelInfoDTO>> channelMap = response.getData();
            if (MapUtils.isEmpty(channelMap)) {
                log.info("TenantChannelThriftServiceWrapper queryTenantChannel, channelMap is empty");
                return Collections.emptyList();
            }
            List<ChannelInfoDTO> channelInfoDTOList = channelMap.get(tenantId);
            if (CollectionUtils.isEmpty(channelInfoDTOList)) {
                log.info("TenantChannelThriftServiceWrapper queryTenantChannel, channelInfoDTOList is empty");
                return Collections.emptyList();
            }

            return channelInfoDTOList.stream().map(ChannelInfoDTO::getChannelId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("TenantChannelThriftServiceWrapper queryTenantChannel error", e);
            return Collections.emptyList();
        }
    }

    public List<Integer> getTenantChannelTypeFallback(Long tenantId) {
        log.error("TenantChannelThriftServiceWrapper getTenantChannelTypeFallback, tenantId is {}", tenantId);
        return Collections.emptyList();
    }
}
