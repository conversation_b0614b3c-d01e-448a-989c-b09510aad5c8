package com.sankuai.shangou.logistics.delivery.configure.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Lists;
import com.sankuai.shangou.logistics.delivery.configure.model.value.AssessTimeModel;
import com.sankuai.shangou.logistics.delivery.configure.value.*;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-26
 */
public enum DeliveryConfigTypeEnum {
    RULES(10, "配送规则", DeliveryPlatformConfig.class, DeliveryConfigimensionEnum.CHANNEL_DIMENSION),
    RULES_HELPER_WAY(11, "配送规则-手动转辅配方式(仅可设置转聚合)", SecondDeliveryWayConfig.class, DeliveryConfigimensionEnum.CHANNEL_DIMENSION),
    RULES_HELPER_RULES(12, "配送规则-手动转辅配规则(仅对商家自送生效)", SecondDeliveryForbiddenRuleConfig.class, DeliveryConfigimensionEnum.CHANNEL_DIMENSION),

    DISPLAY(30, "界面信息", null, DeliveryConfigimensionEnum.STORE_DIMENSION),
    DISPLAY_TIME_REMAINING(31, "界面信息-剩余时长", AssessTimeModel.class, DeliveryConfigimensionEnum.STORE_DIMENSION),
    DISPLAY_CONFIRM_ACTION(32, "界面信息-确认送达操作", DeliveryCompleteMode.class, DeliveryConfigimensionEnum.STORE_DIMENSION),
    DISPLAY_RIDER_TRANS(33, "界面信息-配送转骑手范围", RiderTransConfig.class, DeliveryConfigimensionEnum.STORE_DIMENSION),
    DISPLAY_NAVIGATION(34, "界面信息-跳转导航", NavigationConfig.class, DeliveryConfigimensionEnum.STORE_DIMENSION),

    REMIND(50, "配送提醒", DeliveryRemindConfig.class, DeliveryConfigimensionEnum.STORE_DIMENSION);

    private final Integer code;
    @Getter
    private final String desc;
    @Getter
    private final Class<?> cls;
    @Getter
    private final DeliveryConfigimensionEnum deliveryConfigimensionEnum;

    DeliveryConfigTypeEnum(Integer code, String desc, Class<?> cls, DeliveryConfigimensionEnum deliveryConfigimensionEnum) {
        this.code = code;
        this.desc = desc;
        this.cls = cls;
        this.deliveryConfigimensionEnum = deliveryConfigimensionEnum;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    @JsonCreator
    public static DeliveryConfigTypeEnum enumOf(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(item.getCode(), code))
                .findFirst().orElse(null);
    }

    public static List<DeliveryConfigTypeEnum> getChannelDimensionTypeEnums() {
        return Arrays.stream(DeliveryConfigTypeEnum.values()).filter(type -> Objects.equals(DeliveryConfigimensionEnum.CHANNEL_DIMENSION, type.getDeliveryConfigimensionEnum())).collect(Collectors.toList());
    }

    public static List<DeliveryConfigTypeEnum> getStoreDimensionTypeEnum() {
        return Arrays.stream(DeliveryConfigTypeEnum.values()).filter(type -> Objects.equals(DeliveryConfigimensionEnum.STORE_DIMENSION, type.getDeliveryConfigimensionEnum())).collect(Collectors.toList());
    }

    /**
     * 门店配送维度配置类型
     */
    public static final List<DeliveryConfigTypeEnum> deliveryDimensionConfigTypes = Lists.newArrayList(DISPLAY_TIME_REMAINING, DISPLAY_CONFIRM_ACTION,
            DISPLAY_RIDER_TRANS, DISPLAY_NAVIGATION, REMIND);
    /**
     * 渠道门店维度配置类型
     */
    public static final List<DeliveryConfigTypeEnum> DELIVERY_CHANNEL_POI_CONFIG_TYPES = Lists.newArrayList(RULES, RULES_HELPER_WAY, RULES_HELPER_RULES);
}
