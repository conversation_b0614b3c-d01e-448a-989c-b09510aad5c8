package com.sankuai.shangou.logistics.delivery.configure.service.converter;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.drunkhorsemgmt.labor.exception.SystemException;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import com.sankuai.shangou.logistics.delivery.configure.enums.ExpressionSourceDataEnum;
import com.sankuai.shangou.logistics.delivery.configure.enums.TimeConfigTypeEnum;
import com.sankuai.shangou.logistics.delivery.configure.value.ExpressionCondition;
import com.sankuai.shangou.logistics.delivery.configure.value.ExpressionNode;
import com.sankuai.shangou.logistics.delivery.configure.value.interval.Interval;
import com.sankuai.shangou.logistics.delivery.configure.value.interval.IntervalNumber;
import com.sankuai.shangou.logistics.delivery.configure.value.interval.IntervalTypeEnum;
import com.sankuai.shangou.logistics.delivery.enums.DeliveryConfigOrderTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.shangou.logistics.delivery.configure.repository.DeliveryPoiRepository.DISTANCE_IDENTIFIER;


/**
 * 预约下推时间配置转换器
 * 
 * <AUTHOR>
 * @date 2025-08-21
 * @email <EMAIL>
 */
@Slf4j
@Component
public class BookingPushDownTimeConfigConverter {

    private static final String ETA_FORMULA = "${eta_duration}";

    /**
     * VO标识符到ExpressionSourceDataEnum的映射
     */
    private static final Map<String, ExpressionSourceDataEnum> IDENTIFIER_MAPPING = Maps.newHashMap();
    
    /**
     * ExpressionSourceDataEnum到显示名称的映射
     */
    private static final Map<ExpressionSourceDataEnum, String> NAME_MAPPING = Maps.newHashMap();
    
    /**
     * 订单标签到对应条件的映射
     */
    private static final Map<Integer, ExpressionSourceDataEnum> ORDER_TAG_CONDITION_MAPPING = Maps.newHashMap();

    static {
        // 初始化标识符映射
        IDENTIFIER_MAPPING.put("distance", ExpressionSourceDataEnum.DELIVERY_DISTANCE);
        IDENTIFIER_MAPPING.put("eta_duration", ExpressionSourceDataEnum.ETA);
        IDENTIFIER_MAPPING.put("restaurant_scene", ExpressionSourceDataEnum.RESTAURANT_SCENE);
        IDENTIFIER_MAPPING.put("contain_seal_product", ExpressionSourceDataEnum.CONTAIN_SEAL_PRODUCT);
        
        // 初始化名称映射
        NAME_MAPPING.put(ExpressionSourceDataEnum.ORDER_RESERVE_TYPE, "订单预约类型");
        NAME_MAPPING.put(ExpressionSourceDataEnum.DELIVERY_DISTANCE, "配送距离");
        NAME_MAPPING.put(ExpressionSourceDataEnum.ETA, "预计送达时长");
        NAME_MAPPING.put(ExpressionSourceDataEnum.RESTAURANT_SCENE, "餐馆场景");
        NAME_MAPPING.put(ExpressionSourceDataEnum.CONTAIN_SEAL_PRODUCT, "是否包含封签交付商品");
        
        // 初始化订单标签条件映射
        ORDER_TAG_CONDITION_MAPPING.put(DeliveryConfigOrderTagEnum.RESTAURANT.getType(), ExpressionSourceDataEnum.RESTAURANT_SCENE);
        ORDER_TAG_CONDITION_MAPPING.put(DeliveryConfigOrderTagEnum.SEAL_LABEL.getType(), ExpressionSourceDataEnum.CONTAIN_SEAL_PRODUCT);
    }

    /**
     * 将List<BookingPushDownTimeConfigVO>转换为List<ExpressionNode>
     */
    public static List<ExpressionNode> convertToExpressionNodes(List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> configVOs) {
        if (CollectionUtils.isEmpty(configVOs)) {
            return Lists.newArrayList();
        }

        List<ExpressionNode> result = Lists.newArrayList();

        for (DeliveryConfigDetailVO.BookingPushDownTimeConfigVO configVO : configVOs) {
            //预计送达这里是写死的
            List<ExpressionNode> nodes = convertSingleConfigToNodes(TimeConfigTypeEnum.ETA, configVO.getOrderTags(), configVO.getCondition());
            result.addAll(nodes);
        }

        return result;
    }

    /**
     * 将List<AssertTimeVO>转换为List<ExpressionNode>
     */
    public static List<ExpressionNode> convertAssertTimeToExpressionNodes(TimeConfigTypeEnum timeConfigTypeEnum, List<DeliveryConfigDetailVO.AssertTimeVO> assertTimeVOs) {
        if (CollectionUtils.isEmpty(assertTimeVOs)) {
            return Lists.newArrayList();
        }

        List<ExpressionNode> result = Lists.newArrayList();

        for (DeliveryConfigDetailVO.AssertTimeVO assertTimeVO : assertTimeVOs) {
            List<ExpressionNode> nodes = convertSingleConfigToNodes(timeConfigTypeEnum, assertTimeVO.getOrderTags(), assertTimeVO.getCondition());
            result.addAll(nodes);
        }

        return result;
    }

    /**
     * 将单个配置转换为ExpressionNode列表（通用方法）
     */
    private static List<ExpressionNode> convertSingleConfigToNodes(TimeConfigTypeEnum timeConfigTypeEnum, List<Integer> orderTags, List<DeliveryConfigDetailVO.ConditionVO> conditions) {

        // 如果orderTags为空或只包含0(全部)，直接处理条件
        if (CollectionUtils.isEmpty(orderTags) || 
            (orderTags.size() == 1 && DeliveryConfigOrderTagEnum.ALL.getType().equals(orderTags.get(0)))) {
            return Lists.newArrayList(buildExpressionNodeWithReserveType(timeConfigTypeEnum, conditions));
        }

        // 为每个orderTag创建一个ExpressionNode
        List<ExpressionNode> result = Lists.newArrayList();
        for (Integer orderTag : orderTags) {
            if (!DeliveryConfigOrderTagEnum.ALL.getType().equals(orderTag)) {
                ExpressionNode node = buildExpressionNodeWithOrderTag(timeConfigTypeEnum, orderTag, conditions);
                if (node != null) {
                    result.add(node);
                }
            }
        }
        
        return result;
    }

    /**
     * 构建带有预约类型条件的ExpressionNode（用于orderTag为0或空的情况）
     */
    private static ExpressionNode buildExpressionNodeWithReserveType(TimeConfigTypeEnum timeConfigTypeEnum, List<DeliveryConfigDetailVO.ConditionVO> conditions) {
        ExpressionNode rootNode = new ExpressionNode();
        rootNode.setCondition(null);
        rootNode.setFormula(null);

        // 创建预约类型条件子节点
        ExpressionNode reserveTypeNode = new ExpressionNode();
        reserveTypeNode.setCondition(createReserveTypeCondition());
        reserveTypeNode.setFormula(null);

        // 构建条件链
        ExpressionNode conditionChain = buildConditionChain(timeConfigTypeEnum, conditions);
        if (conditionChain != null) {
            reserveTypeNode.setSubs(Lists.newArrayList(conditionChain));
        } else {
            reserveTypeNode.setSubs(Lists.newArrayList());
        }

        rootNode.setSubs(Lists.newArrayList(reserveTypeNode));
        return rootNode;
    }

    /**
     * 构建带有订单标签条件的ExpressionNode
     */
    private static ExpressionNode buildExpressionNodeWithOrderTag(TimeConfigTypeEnum timeConfigTypeEnum, Integer orderTag, List<DeliveryConfigDetailVO.ConditionVO> conditions) {
        ExpressionNode rootNode = new ExpressionNode();
        rootNode.setCondition(null);
        rootNode.setFormula(null);

        // 创建预约类型条件子节点
        ExpressionNode reserveTypeNode = new ExpressionNode();
        reserveTypeNode.setCondition(createReserveTypeCondition());
        reserveTypeNode.setFormula(null);

        // 检查是否需要添加订单标签对应的条件
        ExpressionSourceDataEnum orderTagCondition = ORDER_TAG_CONDITION_MAPPING.get(orderTag);
        if (orderTagCondition != null) {
            // 创建订单标签条件节点
            ExpressionNode orderTagConditionNode = new ExpressionNode();
            orderTagConditionNode.setCondition(createOrderTagCondition(orderTagCondition));
            orderTagConditionNode.setFormula(null);

            // 构建条件链
            ExpressionNode conditionChain = buildConditionChain(timeConfigTypeEnum, conditions);
            if (conditionChain != null) {
                orderTagConditionNode.setSubs(Lists.newArrayList(conditionChain));
            } else {
                orderTagConditionNode.setSubs(Lists.newArrayList());
            }

            reserveTypeNode.setSubs(Lists.newArrayList(orderTagConditionNode));
        } else {
            // 直接构建条件链
            ExpressionNode conditionChain = buildConditionChain(timeConfigTypeEnum, conditions);
            if (conditionChain != null) {
                reserveTypeNode.setSubs(Lists.newArrayList(conditionChain));
            } else {
                reserveTypeNode.setSubs(Lists.newArrayList());
            }
        }

        rootNode.setSubs(Lists.newArrayList(reserveTypeNode));
        return rootNode;
    }

    /**
     * 创建预约类型条件（固定值为0）
     */
    private static ExpressionCondition createReserveTypeCondition() {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setName(NAME_MAPPING.get(ExpressionSourceDataEnum.ORDER_RESERVE_TYPE));
        condition.setIdentifier("@{reserve_type}");
        condition.setFormula(ExpressionSourceDataEnum.ORDER_RESERVE_TYPE.getDataIdentifier());

        Interval interval = createCustomInterval(Lists.newArrayList("0"), IntervalTypeEnum.EQUAL);

        condition.setInterval(interval);
        return condition;
    }

    /**
     * 创建订单标签对应的条件
     */
    private static ExpressionCondition createOrderTagCondition(ExpressionSourceDataEnum sourceDataEnum) {
        ExpressionCondition condition = new ExpressionCondition();
        condition.setName(NAME_MAPPING.get(sourceDataEnum));
        condition.setIdentifier("@{" + sourceDataEnum.getDataIdentifier().substring(2, sourceDataEnum.getDataIdentifier().length() - 1) + "}");
        condition.setFormula(sourceDataEnum.getDataIdentifier());

        Interval interval = createCustomInterval(Lists.newArrayList("0"), IntervalTypeEnum.EQUAL);

        condition.setInterval(interval);
        return condition;
    }

    /**
     * 构建条件链 - 根据示例，所有条件应该在同一级的subs中
     */
    private static ExpressionNode buildConditionChain(TimeConfigTypeEnum timeConfigTypeEnum, List<DeliveryConfigDetailVO.ConditionVO> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return null;
        }

        // 创建一个容器节点来包含所有条件
        ExpressionNode containerNode = new ExpressionNode();
        containerNode.setCondition(null);
        containerNode.setFormula(null);

        // 将所有条件转换为同级的子节点
        List<ExpressionNode> conditionNodes = Lists.newArrayList();
        for (DeliveryConfigDetailVO.ConditionVO conditionVO : conditions) {
            ExpressionNode conditionNode = new ExpressionNode();
            conditionNode.setCondition(convertToExpressionCondition(conditionVO));
            buildFormula(timeConfigTypeEnum, conditionVO, conditionNode);
            conditionNode.setSubs(Lists.newArrayList());
            conditionNodes.add(conditionNode);
        }

        containerNode.setSubs(conditionNodes);
        return containerNode;
    }

    private static void buildFormula(@Nullable TimeConfigTypeEnum timeConfigTypeEnum, DeliveryConfigDetailVO.ConditionVO conditionVO, ExpressionNode conditionNode) {
        if(Objects.isNull(timeConfigTypeEnum) && StringUtils.isNotBlank(conditionVO.getValue())){
            throw new SystemException("公式错误");
        }
        if (Objects.isNull(timeConfigTypeEnum) || StringUtils.isBlank(conditionVO.getValue())) {
            return;
        }
        if (Objects.equals(timeConfigTypeEnum.getType(), TimeConfigTypeEnum.FIXED.getType())) {
            conditionNode.setFormula(conditionVO.getValue());
        } else if(Objects.equals(timeConfigTypeEnum.getType(), TimeConfigTypeEnum.ETA.getType())) {
            if (new BigDecimal(conditionVO.getValue()).compareTo(BigDecimal.ZERO) >= 0) {
                conditionNode.setFormula(
                        new StringBuilder()
                                .append(ETA_FORMULA)
                                .append("+")
                                .append(conditionVO.getValue())
                                .toString()
                );
            } else {
                conditionNode.setFormula(
                        new StringBuilder()
                                .append(ETA_FORMULA)
                                .append(conditionVO.getValue())
                                .toString()
                );
            }

        }
    }

    /**
     * 将ConditionVO转换为ExpressionCondition
     */
    private static ExpressionCondition convertToExpressionCondition(DeliveryConfigDetailVO.ConditionVO conditionVO) {
        if (conditionVO == null) {
            return null;
        }

        ExpressionCondition condition = new ExpressionCondition();
        
        // 根据标识符映射获取对应的ExpressionSourceDataEnum
        ExpressionSourceDataEnum sourceDataEnum = IDENTIFIER_MAPPING.get(conditionVO.getIdentifer());
        if (sourceDataEnum != null) {
            condition.setName(NAME_MAPPING.get(sourceDataEnum));
            condition.setIdentifier("@{" + sourceDataEnum.getDataIdentifier().substring(2, sourceDataEnum.getDataIdentifier().length() - 1) + "}");
            condition.setFormula(sourceDataEnum.getDataIdentifier());
        } else {
            condition.setName(conditionVO.getIdentifer());
            condition.setIdentifier(conditionVO.getIdentifer());
            condition.setFormula(conditionVO.getIdentifer());
        }

        if (conditionVO.getInterval() != null) {
            condition.setInterval(convertToInterval(conditionVO.getInterval()));
        }

        return condition;
    }

    /**
     * 将IntervalVO转换为Interval
     */
    private static Interval convertToInterval(DeliveryConfigDetailVO.IntervalVO intervalVO) {
        if (intervalVO == null) {
            return null;
        }

        Interval interval = new Interval();
        
        if (intervalVO.getIntervalType() != null) {
            interval.setIntervalType(intervalVO.getIntervalType());
        }

        if (CollectionUtils.isNotEmpty(intervalVO.getValues())) {
            IntervalTypeEnum intervalTypeEnum = IntervalTypeEnum.of(intervalVO.getIntervalType());
            return createCustomInterval(intervalVO.getValues(), intervalTypeEnum != null ? intervalTypeEnum : IntervalTypeEnum.EQUAL);
        }

        return interval;
    }

    /**
     * 创建标准Interval（保持现有逻辑）
     */
    private static Interval createCustomInterval(List<String> values, IntervalTypeEnum intervalType) {
        Interval interval = new Interval();
        interval.setIntervalType(intervalType.getType());
        interval.values(values);
        return interval;
    }

    /**
     * 创建用于JSON序列化的简单Interval对象
     */
    private static SimpleInterval createSimpleInterval(List<String> values, IntervalTypeEnum intervalType) {
        return new SimpleInterval(values, intervalType.getType());
    }

    /**
     * 简单的Interval类，专门用于正确的JSON序列化
     */
    public static class SimpleInterval {
        private final List<String> values;
        private final int intervalType;

        public SimpleInterval(List<String> values, int intervalType) {
            this.values = values;
            this.intervalType = intervalType;
        }

        public List<String> getValues() {
            return values;
        }

        public int getIntervalType() {
            return intervalType;
        }
    }

    /**
     * 简单的ExpressionCondition类，专门用于正确的JSON序列化
     */
    public static class SimpleExpressionCondition {
        private final String name;
        private final String identifier;
        private final String formula;
        private final SimpleInterval interval;

        public SimpleExpressionCondition(String name, String identifier, String formula, SimpleInterval interval) {
            this.name = name;
            this.identifier = identifier;
            this.formula = formula;
            this.interval = interval;
        }

        public String getName() {
            return name;
        }

        public String getIdentifier() {
            return identifier;
        }

        public String getFormula() {
            return formula;
        }

        public SimpleInterval getInterval() {
            return interval;
        }
    }

    /**
     * 简单的ExpressionNode类，专门用于正确的JSON序列化
     */
    public static class SimpleExpressionNode {
        private final SimpleExpressionCondition condition;
        private final List<SimpleExpressionNode> subs;
        private final String formula;

        public SimpleExpressionNode(SimpleExpressionCondition condition, List<SimpleExpressionNode> subs, String formula) {
            this.condition = condition;
            this.subs = subs != null ? subs : Lists.newArrayList();
            this.formula = formula;
        }

        public SimpleExpressionCondition getCondition() {
            return condition;
        }

        public List<SimpleExpressionNode> getSubs() {
            return subs;
        }

        public String getFormula() {
            return formula;
        }
    }

    /**
     * 将ExpressionNode转换为SimpleExpressionNode以便正确的JSON序列化
     */
    private static SimpleExpressionNode convertToSimpleNode(ExpressionNode node) {
        if (node == null) {
            return null;
        }

        SimpleExpressionCondition simpleCondition = null;
        if (node.getCondition() != null) {
            ExpressionCondition condition = node.getCondition();
            SimpleInterval simpleInterval = null;

            if (condition.getInterval() != null) {
                Interval interval = condition.getInterval();
                List<String> values = interval.values(); // 使用values()方法获取字符串列表
                int intervalType = interval.getOriginIntervalType() != null ? interval.getOriginIntervalType().getType() : 0;
                simpleInterval = new SimpleInterval(values, intervalType);
            }

            simpleCondition = new SimpleExpressionCondition(
                condition.getName(),
                condition.getIdentifier(),
                condition.getFormula(),
                simpleInterval
            );
        }

        List<SimpleExpressionNode> simpleSubs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(node.getSubs())) {
            for (ExpressionNode subNode : node.getSubs()) {
                SimpleExpressionNode simpleSubNode = convertToSimpleNode(subNode);
                if (simpleSubNode != null) {
                    simpleSubs.add(simpleSubNode);
                }
            }
        }

        return new SimpleExpressionNode(simpleCondition, simpleSubs, node.getFormula());
    }

    /**
     * 将ExpressionNode列表转换为SimpleExpressionNode列表以便正确的JSON序列化
     */
    public static List<SimpleExpressionNode> convertToSimpleNodes(List<ExpressionNode> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Lists.newArrayList();
        }

        List<SimpleExpressionNode> simpleNodes = Lists.newArrayList();
        for (ExpressionNode node : nodes) {
            SimpleExpressionNode simpleNode = convertToSimpleNode(node);
            if (simpleNode != null) {
                simpleNodes.add(simpleNode);
            }
        }
        return simpleNodes;
    }



    /**
     * 将List<ExpressionNode>转换为List<BookingPushDownTimeConfigVO>
     */
    public static List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> convertFromExpressionNodes(List<ExpressionNode> expressionNodes) {
        if (CollectionUtils.isEmpty(expressionNodes)) {
            return Lists.newArrayList();
        }

        List<DeliveryConfigDetailVO.BookingPushDownTimeConfigVO> result = Lists.newArrayList();

        for (ExpressionNode node : expressionNodes) {
            DeliveryConfigDetailVO.BookingPushDownTimeConfigVO configVO = convertSingleNodeToConfig(node);
            if (configVO != null) {
                result.add(configVO);
            }
        }

        return result;
    }

    /**
     * 将单个ExpressionNode转换为BookingPushDownTimeConfigVO
     */
    private static DeliveryConfigDetailVO.BookingPushDownTimeConfigVO convertSingleNodeToConfig(ExpressionNode rootNode) {
        if (rootNode == null || CollectionUtils.isEmpty(rootNode.getSubs())) {
            return null;
        }

        DeliveryConfigDetailVO.BookingPushDownTimeConfigVO configVO = new DeliveryConfigDetailVO.BookingPushDownTimeConfigVO();

        // 解析表达式树，提取orderTags和conditions
        ParseResult parseResult = parseExpressionNode(rootNode);

        configVO.setOrderTags(parseResult.getOrderTags());
        configVO.setCondition(parseResult.getConditions());

        return configVO;
    }

    /**
     * 将List<ExpressionNode>转换为List<AssertTimeVO>
     */
    public static List<DeliveryConfigDetailVO.AssertTimeVO> convertFromExpressionNodesToAssertTime(List<ExpressionNode> expressionNodes) {
        if (CollectionUtils.isEmpty(expressionNodes)) {
            return Lists.newArrayList();
        }

        List<DeliveryConfigDetailVO.AssertTimeVO> result = Lists.newArrayList();

        for (ExpressionNode node : expressionNodes) {
            DeliveryConfigDetailVO.AssertTimeVO assertTimeVO = convertSingleNodeToAssertTime(node);
            if (assertTimeVO != null) {
                result.add(assertTimeVO);
            }
        }

        return result;
    }

    /**
     * 将单个ExpressionNode转换为AssertTimeVO
     */
    private static DeliveryConfigDetailVO.AssertTimeVO convertSingleNodeToAssertTime(ExpressionNode rootNode) {
        if (rootNode == null || CollectionUtils.isEmpty(rootNode.getSubs())) {
            return null;
        }

        DeliveryConfigDetailVO.AssertTimeVO assertTimeVO = new DeliveryConfigDetailVO.AssertTimeVO();

        // 解析表达式树，提取orderTags和conditions
        ParseResult parseResult = parseExpressionNode(rootNode);

        assertTimeVO.setOrderTags(parseResult.getOrderTags());
        assertTimeVO.setCondition(parseResult.getConditions());

        return assertTimeVO;
    }


    public static DeliveryConfigDetailVO.AssertTimeVO buildDefaultAssertTimeVO() {
        DeliveryConfigDetailVO.AssertTimeVO assertTimeVO = new DeliveryConfigDetailVO.AssertTimeVO();
        assertTimeVO.setType(1);
        assertTimeVO.setHintType(2);
        assertTimeVO.setTimeConfigType(2);
        assertTimeVO.setOrderTags(Lists.newArrayList(DeliveryConfigOrderTagEnum.ALL.getType()));
        DeliveryConfigDetailVO.ConditionVO conditionVO = new DeliveryConfigDetailVO.ConditionVO();
        conditionVO.setIdentifer(DISTANCE_IDENTIFIER);
        DeliveryConfigDetailVO.IntervalVO intervalVO = new DeliveryConfigDetailVO.IntervalVO();
        intervalVO.setValues(Lists.newArrayList("0", IntervalNumber.POSITIVE_INFINITY));
        intervalVO.setIntervalType(1);
        conditionVO.setValue("0");
        conditionVO.setInterval(intervalVO);

        assertTimeVO.setCondition(Lists.newArrayList(conditionVO));
        return assertTimeVO;
    }

    /**
     * 解析ExpressionNode，提取orderTags和conditions
     */
    private static ParseResult parseExpressionNode(ExpressionNode rootNode) {
        ParseResult result = new ParseResult();
        result.setOrderTags(Lists.newArrayList());
        result.setConditions(Lists.newArrayList());

        // 收集所有从根到叶子的路径
        List<PathInfo> allPaths = Lists.newArrayList();
        collectPaths(rootNode, Lists.newArrayList(), allPaths);

        if (CollectionUtils.isEmpty(allPaths)) {
            return result;
        }

        // 分析路径，提取orderTags和conditions
        Set<Integer> orderTagsSet = new HashSet<>();
        List<DeliveryConfigDetailVO.ConditionVO> conditions = Lists.newArrayList();

        for (PathInfo path : allPaths) {
            // 提取orderTags
            List<Integer> pathOrderTags = extractOrderTagsFromPath(path);
            orderTagsSet.addAll(pathOrderTags);

            // 提取conditions（排除预约类型和订单标签相关条件）
            List<DeliveryConfigDetailVO.ConditionVO> pathConditions = extractConditionsFromPath(path);
            conditions.addAll(pathConditions);
        }

        // 如果没有提取到orderTags，默认为全部
        if (orderTagsSet.isEmpty()) {
            orderTagsSet.add(DeliveryConfigOrderTagEnum.ALL.getType());
        }

        result.setOrderTags(Lists.newArrayList(orderTagsSet));
        result.setConditions(conditions);

        return result;
    }

    /**
     * 收集所有从根到叶子的路径
     */
    private static void collectPaths(ExpressionNode node, List<ExpressionCondition> currentPath, List<PathInfo> allPaths) {
        if (node == null) {
            return;
        }

        // 将当前节点的条件加入路径（根节点条件为null，不加入）
        List<ExpressionCondition> newPath = Lists.newArrayList(currentPath);
        if (node.getCondition() != null) {
            newPath.add(node.getCondition());
        }

        // 如果有公式，记录完整路径
        if (StringUtils.isNotBlank(node.getFormula())) {
            PathInfo pathInfo = new PathInfo();
            pathInfo.setConditions(Lists.newArrayList(newPath));
            pathInfo.setFormula(node.getFormula());
            allPaths.add(pathInfo);
            return;
        }

        // 继续遍历子节点
        if (CollectionUtils.isNotEmpty(node.getSubs())) {
            for (ExpressionNode subNode : node.getSubs()) {
                collectPaths(subNode, newPath, allPaths);
            }
        }
    }

    /**
     * 从路径中提取orderTags
     */
    private static List<Integer> extractOrderTagsFromPath(PathInfo path) {
        List<Integer> orderTags = Lists.newArrayList();

        for (ExpressionCondition condition : path.getConditions()) {
            // 跳过预约类型条件
            if ("@{reserve_type}".equals(condition.getIdentifier())) {
                continue;
            }

            // 检查是否为订单标签相关条件
            for (Map.Entry<Integer, ExpressionSourceDataEnum> entry : ORDER_TAG_CONDITION_MAPPING.entrySet()) {
                String expectedIdentifier = "@{" + entry.getValue().getDataIdentifier().substring(2, entry.getValue().getDataIdentifier().length() - 1) + "}";
                if (expectedIdentifier.equals(condition.getIdentifier())) {
                    orderTags.add(entry.getKey());
                    break;
                }
            }
        }

        return orderTags;
    }

    /**
     * 从路径中提取conditions（排除预约类型和订单标签相关条件）
     */
    private static List<DeliveryConfigDetailVO.ConditionVO> extractConditionsFromPath(PathInfo path) {
        List<DeliveryConfigDetailVO.ConditionVO> conditions = Lists.newArrayList();

        for (ExpressionCondition condition : path.getConditions()) {
            // 跳过预约类型条件
            if ("@{reserve_type}".equals(condition.getIdentifier())) {
                continue;
            }

            // 跳过订单标签相关条件
            boolean isOrderTagCondition = false;
            for (ExpressionSourceDataEnum sourceDataEnum : ORDER_TAG_CONDITION_MAPPING.values()) {
                String expectedIdentifier = "@{" + sourceDataEnum.getDataIdentifier().substring(2, sourceDataEnum.getDataIdentifier().length() - 1) + "}";
                if (expectedIdentifier.equals(condition.getIdentifier())) {
                    isOrderTagCondition = true;
                    break;
                }
            }

            if (!isOrderTagCondition) {
                DeliveryConfigDetailVO.ConditionVO conditionVO = convertFromExpressionCondition(condition, path.getFormula());
                if (conditionVO != null) {
                    conditions.add(conditionVO);
                }
            }
        }

        return conditions;
    }

    /**
     * 将ExpressionCondition转换为ConditionVO
     */
    private static DeliveryConfigDetailVO.ConditionVO convertFromExpressionCondition(ExpressionCondition condition, String formula) {
        if (condition == null) {
            return null;
        }

        DeliveryConfigDetailVO.ConditionVO conditionVO = new DeliveryConfigDetailVO.ConditionVO();

        // 根据identifier反向映射获取VO中的标识符
        String voIdentifier = getVOIdentifierFromExpressionIdentifier(condition.getIdentifier());
        conditionVO.setIdentifer(voIdentifier);
        conditionVO.setValue(formula);

        if (condition.getInterval() != null) {
            conditionVO.setInterval(convertFromInterval(condition.getInterval()));
        }

        return conditionVO;
    }

    /**
     * 根据ExpressionSourceDataEnum的identifier获取VO中的标识符
     */
    private static String getVOIdentifierFromExpressionIdentifier(String expressionIdentifier) {
        for (Map.Entry<String, ExpressionSourceDataEnum> entry : IDENTIFIER_MAPPING.entrySet()) {
            String expectedIdentifier = "@{" + entry.getValue().getDataIdentifier().substring(2, entry.getValue().getDataIdentifier().length() - 1) + "}";
            if (expectedIdentifier.equals(expressionIdentifier)) {
                return entry.getKey();
            }
        }
        return expressionIdentifier;
    }

    /**
     * 将Interval转换为IntervalVO
     */
    private static DeliveryConfigDetailVO.IntervalVO convertFromInterval(Interval interval) {
        if (interval == null) {
            return null;
        }

        DeliveryConfigDetailVO.IntervalVO intervalVO = new DeliveryConfigDetailVO.IntervalVO();
        intervalVO.setValues(interval.values());
        intervalVO.setIntervalType(interval.getOriginIntervalType() != null ? interval.getOriginIntervalType().getType() : null);

        return intervalVO;
    }

    /**
     * 路径信息，包含条件列表和公式
     */
    private static class PathInfo {
        private List<ExpressionCondition> conditions;
        private String formula;

        public List<ExpressionCondition> getConditions() {
            return conditions;
        }

        public void setConditions(List<ExpressionCondition> conditions) {
            this.conditions = conditions;
        }

        public String getFormula() {
            return formula;
        }

        public void setFormula(String formula) {
            this.formula = formula;
        }
    }

    /**
     * 解析结果
     */
    private static class ParseResult {
        private List<Integer> orderTags;
        private List<DeliveryConfigDetailVO.ConditionVO> conditions;

        public List<Integer> getOrderTags() {
            return orderTags;
        }

        public void setOrderTags(List<Integer> orderTags) {
            this.orderTags = orderTags;
        }

        public List<DeliveryConfigDetailVO.ConditionVO> getConditions() {
            return conditions;
        }

        public void setConditions(List<DeliveryConfigDetailVO.ConditionVO> conditions) {
            this.conditions = conditions;
        }
    }


}
