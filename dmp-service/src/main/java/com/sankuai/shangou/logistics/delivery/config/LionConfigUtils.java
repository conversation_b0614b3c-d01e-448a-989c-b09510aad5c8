package com.sankuai.shangou.logistics.delivery.config;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.shangou.logistics.delivery.offlineboard.define.BaseIndicatorDefine;
import com.sankuai.shangou.logistics.delivery.offlineboard.define.IndicatorCalculatorDefine;
import com.sankuai.shangou.logistics.delivery.offlineboard.enums.BizCalculatorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * <AUTHOR>
 * @date 2024-02-03
 * @email <EMAIL>
 */
@Slf4j
public class LionConfigUtils {

    private LionConfigUtils() {

    }

    private static final String WXMALL_EAPI_APPKEY = "com.sankuai.sgxsupply.wxmall.eapi";
    private static final String OFAPP_APPKEY = "com.sankuai.shangou.supplychain.ofapp";
    private static final String TMS_APPKEY = "com.sankuai.sgfulfillment.tms";

    private static final Splitter DOT_SPLITTER = Splitter.on(",").omitEmptyStrings().trimResults();

    public static List<String> getDHTenantIdList(){
        String tenantIdStr = Lion.getConfigRepository().get("drunk.horse.tenant.id.list", "1000395");
        if(StringUtils.isBlank(tenantIdStr)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(tenantIdStr);
    }

    public static List<Long> getSortByDeliveryDoneTimeTenantIds(){
        return Lion.getConfigRepository().getList("done.time.sort.tenants", Long.class, Lists.newArrayList(1000395L));
    }

    public static List<Long> noNeedCheckPickFinishTenants() {
        return Lion.getConfigRepository().getList("no.need.check.pick.finish.tenants", Long.class, Lists.newArrayList(1000395L));
    }

    public static List<Long> needDeliveryStatsTenants() {
        return Lion.getConfigRepository().getList("need.delivery.stats.tenants", Long.class, Lists.newArrayList(1000395L));
    }

    public static int getQueryDeliveryOrderMaxPage() {
        return Lion.getConfigRepository().getIntValue("query.delivery.order.max.page", 10);
    }

    public static int getQueryDeliveryOrderPageSize() {
        return Lion.getConfigRepository().getIntValue("query.delivery.order.page.size", 1000);
    }

    public static int getStatBatchStoreNum() {
        return Lion.getConfigRepository().getIntValue("stat.batch.store.num", 100);
    }

    public static int getStatRangeTimeMinus() {
        return Lion.getConfigRepository().getIntValue("stat.range.minus", 10);
    }

    public static int getEleStatRange() {
        return Lion.getConfigRepository().getIntValue("ele.stat.range", 10);
    }

    public static int getFetchLimit() {
        return Lion.getConfigRepository().getIntValue("sql.fetch.limit", 500);
    }


    public static IndicatorCalculatorDefine getIndicatorCalculatorDefine(String calculatorName) {
        return JSON.parseObject(Lion.getConfigRepository(WXMALL_EAPI_APPKEY).get(calculatorName), IndicatorCalculatorDefine.class);
    }

    public static IndicatorCalculatorDefine getIndicatorCalculatorDefineForExport(String calculatorName) {
        return JSON.parseObject(Lion.getConfigRepository().get("export_" + calculatorName), IndicatorCalculatorDefine.class);
    }

    public static List<BaseIndicatorDefine> getBaseIndicatorDefineList() {
        return Lion.getConfigRepository(WXMALL_EAPI_APPKEY).getList("offline.dashboard.base.indicator.define", BaseIndicatorDefine.class);
    }

    public static Map<String, String> getExcelHeaderMap(BizCalculatorEnum bizCalculatorEnum, String identify) {
        return Lion.getConfigRepository().getMap("excel_head_" +bizCalculatorEnum.getCalculatorName() + identify, String.class, Maps.newHashMap());
    }

    public static boolean isNewDeliveryListGrayStore(long storeId) {
        List<Long> grayStoreIds = getConfigRepository(OFAPP_APPKEY).getList("new.delivery.list.stores", Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }
        if (grayStoreIds.size() == 1 && grayStoreIds.get(0).equals(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static boolean isNewPreOrderAssessGrayStore(long storeId) {
        List<Long> grayStoreIds = getConfigRepository(OFAPP_APPKEY).getList("new.pre.order.stores", Long.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }
        if (grayStoreIds.size() == 1 && grayStoreIds.get(0).equals(-1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Long preOrderAssessTimePlusMills() {
        return Lion.getConfigRepository(OFAPP_APPKEY).getLongValue("pre.order.plus.mills", 0L);
    }

    public static Map<Integer, List<Integer>> getSceneOrderTagMap() {
        // 从Lion配置中获取原始数据，这里需要正确指定泛型类型
        Map<String, List> rawMap = getConfigRepository().getMap("scene.order.tag", List.class, Maps.newHashMap());

        // 转换key类型从String到Integer，并确保List中的元素类型安全
        Map<Integer, List<Integer>> resultMap = Maps.newHashMap();
        for (Map.Entry<String, List> entry : rawMap.entrySet()) {
            try {
                Integer key = Integer.valueOf(entry.getKey());
                // 安全地转换List中的元素
                List<Integer> integerList = new ArrayList<>();
                if (entry.getValue() != null) {
                    for (Object item : entry.getValue()) {
                        if (item instanceof Integer) {
                            integerList.add((Integer) item);
                        } else if (item instanceof String) {
                            integerList.add(Integer.valueOf((String) item));
                        }
                    }
                }
                resultMap.put(key, integerList);
            } catch (NumberFormatException e) {
                log.error("getSceneOrderTagMap error", e);
            }
        }

        return resultMap;
    }

    public static Boolean getConfig(String configKey, Long tenantId) {
        Map<String, Boolean> configMap = getConfigRepository().getMap(configKey, Boolean.class, Maps.newHashMap());
        for (Map.Entry<String, Boolean> entry : configMap.entrySet()) {
            if ("-1".equals(entry.getKey())) {
                return true;
            }
            if (entry.getKey().contains(String.valueOf(tenantId))) {
                return entry.getValue();
            }
        }
        return false;
    }

    /**
     * 判读当前租户是否使用新接口，-1为全量
     *
     * @return 当前租户是否使用新接口
     */
    public static boolean isNewAuthApiGrayTenant(long tenantId) {
        List<Long> tenantIdList = getConfigRepository(OFAPP_APPKEY).getList("new.auth.api.gray.tenant", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(tenantIdList)) {
            return false;
        }

        if (tenantIdList.size() == 1 && Objects.equals(-1L, tenantIdList.get(0))) {
            return true;
        }

        return tenantIdList.contains(tenantId);
    }

    /**
     * 青云无授权链接开关
     */
    public static boolean dapLinkNoAuthSwitch() {
        return Lion.getConfigRepository(TMS_APPKEY).getBooleanValue("dap.link.no.auth.switch", true);
    }

    public static Integer dapUrlMarkIdPartNum(){
        return Lion.getConfigRepository(TMS_APPKEY).getIntValue("dap.url.mark.id.part.num", 20);
    }

    public static boolean getTenantPoiFastAuthSwitch() {
        return Lion.getConfigRepository(TMS_APPKEY).getBooleanValue("tenant.poi.fast.auth.switch", true);
    }

    public static String getAggDeliveryPlatformAppConfig() {
        try {
            return Lion.getConfigRepository(TMS_APPKEY).get("agg.delivery.platform.app.config", StringUtils.EMPTY);
        } catch (Exception e) {
            log.error("getAggDeliveryPlatformAppConfig error", e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * key-> 平台品类id; value-> 牵牛花主营品类codes
     * 配送平台品类映射
     */
    public static Map<String, List<String>> getDeliveryPlatformCategoryMapping() {
        Map<String, List<String>> map = new HashMap<>();
        Map<String, String> mappingMap = getConfigRepository(TMS_APPKEY).getMap("delivery.platform.category.mapping");
        mappingMap.forEach((categoryId, categoryCodes) -> map.put(categoryId, DOT_SPLITTER.splitToList(categoryCodes)));
        return map;
    }

    public static Map<String,String> getTenantCategoryMapping(){
        return getConfigRepository(TMS_APPKEY).getMap("tenant.category.mapping");
    }
}
