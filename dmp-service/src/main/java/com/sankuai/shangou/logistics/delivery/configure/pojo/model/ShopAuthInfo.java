package com.sankuai.shangou.logistics.delivery.configure.pojo.model;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryStoreConfigDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopAuthInfo {

    private Long storeId;

    private Long tenantId;

    private Long operatorId;

    private String operatorName;

    private List<DeliveryStoreConfigDto> deliveryConfigs;
}
