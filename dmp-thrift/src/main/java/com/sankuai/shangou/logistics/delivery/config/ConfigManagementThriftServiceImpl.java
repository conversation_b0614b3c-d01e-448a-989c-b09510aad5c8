package com.sankuai.shangou.logistics.delivery.config;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.shangou.commons.thrift.publisher.annotation.ShangouThriftServer;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.configure.service.ManualConfigService;
import com.sankuai.shangou.logistics.delivery.enums.StoreConfigKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService;
import com.sankuai.shangou.logistics.delivery.grayconfig.GrayConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/9 16:33
 **/
@Slf4j
@ShangouThriftServer
public class ConfigManagementThriftServiceImpl implements ConfigManagementThriftService {
    @Resource
    private ManualConfigService manualConfigService;

    @Override
    public Boolean queryTenantDeliveryConfig(String grayKey, Long tenantId) {
        return LionConfigUtils.getConfig(grayKey, tenantId);
    }

    @Override
    public TResult<Map<String, Map<String, Integer>>> batchQueryTenantDeliveryConfig(List<Integer> configKeyList, long tenantId, @Nullable Long storeId) {
        try {
            if (CollectionUtils.isEmpty(configKeyList)) {
                return TResult.buildSuccess(Maps.newHashMap());
            }
            Map<String, Map<String, Integer>> configValueMap = new HashMap<>();
            for (Integer key : configKeyList) {
                StoreConfigKeyEnum keyEnum = StoreConfigKeyEnum.keyToEnum(key);
                if (keyEnum == null) {
                    log.error("枚举转换失败 key:{}", key);
                    continue;
                }
                switch (keyEnum) {
                    case OPERATION_MODE:
                        configValueMap.put(key + "", Collections.singletonMap("pickDeliverySplitType", manualConfigService.getOperationMode(tenantId, storeId)));
                        break;
                    case DRUNK_HORSE_TENANT:
                        configValueMap.put(key + "", Collections.singletonMap("isDhTenantId", LionConfigUtils.getDHTenantIdList().contains(String.valueOf(tenantId)) ? 1 : 0));
                    default:
                        log.info("未适配枚举 keyEnum:{}", keyEnum);
                }
            }
            return TResult.buildSuccess(configValueMap);
        } catch (Exception e) {
            log.error("queryTenantDeliveryConfig error:", e);
            return TResult.buildFailure(1, "查询配置错误");
        }
    }
}
