package com.sankuai.shangou.logistics.delivery.configure;

import com.github.pagehelper.PageInfo;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.auth.login.context.LoginUser;
import com.sankuai.shangou.commons.auth.login.context.holder.WebLoginContextHolder;
import com.sankuai.shangou.logistics.delivery.common.vo.PageVO;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.QueryBatchTaskItemPageParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTaskItemVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTaskVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.BatchTemplateVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.DeliveryConfigBatchSaveParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.QueryBatchTaskParam;
import com.sankuai.shangou.logistics.delivery.configure.pojo.vo.DeliveryConfigTaskDetailVo;
import com.sankuai.shangou.logistics.delivery.configure.pojo.request.DeliveryConfigTaskDetailParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 配送配置批量操作相关接口
 * @date 2025-06-25
 */
@Slf4j
@RestController
@RequestMapping("dmp/api/web/config/batch")
public class DeliveryConfigBatchController {

    @Autowired
    private DeliveryBatchConfigService deliveryBatchConfigService;

    @MethodDoc(
            displayName = "查询批量模版列表",
            description = "查询批量模版列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询批量模版列表",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/batch/template/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/template/list")
    @ResponseBody
    public PageVO<BatchTemplateVo> queryTemplateList(@RequestBody QueryBatchTaskParam req) {
        req.validate();
        LoginUser loginUser = WebLoginContextHolder.getWebLoginContext().getLoginUser();
        PageInfo<BatchTemplateVo> page = deliveryBatchConfigService.queryBatchTemplateList(req, loginUser.getTenantId());
        return new PageVO<>(page.getList(), page.getPageNum(), page.getPageSize(), Long.valueOf(page.getTotal()).intValue());
    }

    @MethodDoc(
            displayName = "查询批量任务列表",
            description = "查询批量任务列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询批量任务列表",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/batch/task/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/task/list")
    @ResponseBody
    public PageVO<BatchTaskVo> queryTaskList(@RequestBody QueryBatchTaskParam req) {
        req.validate();
        LoginUser loginUser = WebLoginContextHolder.getWebLoginContext().getLoginUser();
        PageInfo<BatchTaskVo> page = deliveryBatchConfigService.queryBatchTaskPoiSettingList(req, loginUser.getTenantId());
        return new PageVO<>(page.getList(), page.getPageNum(), page.getPageSize(), Long.valueOf(page.getTotal()).intValue());
    }

    @MethodDoc(
            displayName = "获取批量任务执行内容",
            description = "获取批量任务执行内容",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取批量任务执行内容",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/batch/detail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/detail")
    @ResponseBody
    @MethodLog
    public DeliveryConfigTaskDetailVo queryDetail(@RequestBody DeliveryConfigTaskDetailParam req) {
        req.validate();
        return deliveryBatchConfigService.queryBatchTaskDetail(req);
    }

    @MethodDoc(
            displayName = "查询任务明细列表",
            description = "查询任务明细列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询任务明细列表",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/batch/item/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/item/list")
    @ResponseBody
    public PageVO<BatchTaskItemVo> queryItemList(@RequestBody QueryBatchTaskItemPageParam req) {
        req.validate();
        PageInfo<BatchTaskItemVo> pageInfo = deliveryBatchConfigService.queryBatchTaskItemList(req);
        return new PageVO<>(pageInfo.getList(), pageInfo.getPageNum(), pageInfo.getPageSize(), Long.valueOf(pageInfo.getTotal()).intValue());
    }

    @MethodDoc(
            displayName = "配送配置批量保存",
            description = "配送配置批量保存",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "配送配置批量保存",
                            type = void.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "dmp/api/web/config/batch/save",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @PostMapping("/save")
    @ResponseBody
    @MethodLog
    public void save(@RequestBody DeliveryConfigBatchSaveParam req) {
        req.validate();
        LoginUser loginUser = WebLoginContextHolder.getWebLoginContext().getLoginUser();
        req.setTenantId(loginUser.getTenantId());
        req.setOperator(loginUser.getAccountName());
        req.setOperatorName(loginUser.getEmployeeName());
        deliveryBatchConfigService.batchSaveQnhConfig(req);
    }

}
