package com.sankuai.meituan.shangou.dms.base.model.value;

import lombok.*;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * 配送时间线值对象
 * <AUTHOR>
 * @date 2024/4/3
 */
@Data
@AllArgsConstructor
public class DeliveryTimeline {

    /**
     * 运单最有一次事件发生事件
     * 默认值1970-01-01 00:00:00.000
     */
    @Setter
    private LocalDateTime lastEventTime;

    /**
     * 预计送达时间
     * 默认值1970-01-01 00:00:00.000
     */
    private final LocalDateTime estimatedDeliveryTime;

    /**
     * 预计送达结束时间，针对预计送达时间为时间段时有效，否则等于预计送达时间
     * 默认值1970-01-01 00:00:00.000
     */
    private final LocalDateTime estimatedDeliveryEndTime;

    /**
     * 运单送达时间
     * 默认值1970-01-01 00:00:00.000
     */
    private LocalDateTime deliveryDoneTime;

    /**
     * 创建时间
     */
    private final LocalDateTime createTime;

    public boolean isDelayed() {
        return false;
    }
} 