package com.sankuai.meituan.shangou.dms.base.model.value;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 订单唯一标识值对象
 * <AUTHOR>
 * @date 2024/4/3
 */
@Getter
@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class CustomerOrderKey {

    /**
     * 订单主键id
     */
    @Setter
    private Long orderId;

    /**
     * 渠道订单id
     */
    private String channelOrderId;

    /**
     * 订单来源
     *
     */
    private Integer orderBizType;

    /**
     * 订单日流水号
     */
    private Integer daySeq;

    /**
     * 是否为预约单
     */
    private Boolean reserved;

    /**
     * 履约订单号
     */
    private Long fulfillOrderId;

    /**
     * 履约订单号
     */
    private Integer orderSource;
} 