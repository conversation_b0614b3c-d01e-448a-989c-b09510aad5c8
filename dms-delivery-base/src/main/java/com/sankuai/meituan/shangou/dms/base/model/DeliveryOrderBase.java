package com.sankuai.meituan.shangou.dms.base.model;

import com.sankuai.meituan.shangou.dms.base.model.value.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import lombok.*;

/**
 * 配送订单基类（聚合根）
 * <AUTHOR>
 * @date 2024/4/3
 */
@Getter
@ToString
@EqualsAndHashCode
@AllArgsConstructor
public abstract class DeliveryOrderBase {

    public static final long DELIVERY_ORDER_ACTIVE = 0L;

    /**
     * 主键ID
     */
    @Setter
    protected Long id;

    /**
     * 租户ID
     */
    protected Long tenantId;

    /**
     * 门店ID
     */
    protected Long storeId;

    /**
     * 配送渠道
     */
    private DeliveryChannelEnum deliveryChannelEnum;

    /**
     * 订单信息
     */
    protected CustomerOrderKey customerOrderKey;

    /**
     * 配送状态
     */
    protected DeliveryStatusEnum status;

    /**
     * 配送时间线信息
     */
    protected DeliveryTimeline timeline;

    /**
     * 收货人信息
     */
    protected Receiver receiver;


    /**
     * 配送距离
     */
    private Long distance;

    /**
     * 记录版本号
     */
    @Setter
    private Integer version = 0;

}
