package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderDeliveryStatisticDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryRiderStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.DeliveryStoreStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.resp.RiderDurationDeliveryStatistics;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryStatisticsRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StatisticDuration;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(SpringRunner.class)
public class RiderDeliveryStatisticsApplicationServiceTest {
    @Mock
    private RiderDeliveryStatisticsRepository riderDeliveryStatisticsRepository;

    @InjectMocks
    private RiderDeliveryStatisticsApplicationService riderDeliveryStatisticsApplicationService;

    @Test
    public void riderStatistics_test() {
        List<RiderDeliveryOrder> riderDeliveryOrders = buildRiderDeliveryOrderList();
        List<DeliveryRiderStatistics> list = RiderDeliveryStatisticsApplicationService.ParamExchanger.riderStatistics(
                Lists.newArrayList(12345L, 54321L, 11L), riderDeliveryOrders);
        Assert.assertTrue(list.get(0).getDelivering() == 2 && list.get(0).getDeliveringJudgeTimeout() == 1);
        Assert.assertTrue(list.get(1).getDelivering() == 3 && list.get(1).getDeliveringJudgeTimeout() == 1);
        Assert.assertTrue(list.get(2).getDelivering() == 0 && list.get(2).getDeliveringJudgeTimeout() == 0);
        System.out.println(JSON.toJSON(list));
    }

    @Test
    public void storeStatistics_test() {
        List<RiderDeliveryOrder> riderDeliveryOrders = buildRiderDeliveryOrderList();
        List<DeliveryStoreStatistics> list = RiderDeliveryStatisticsApplicationService.ParamExchanger.storeStatistics(
                Lists.newArrayList(12345L, 54321L, 11L), riderDeliveryOrders);
        Assert.assertTrue(list.get(0).getWaitToAccept() == 3 && list.get(0).getWaitToAcceptJudgeTimeout() == 1);
        Assert.assertTrue(list.get(0).getPicking() == 4 && list.get(0).getPickingJudgeTimeout() == 2);
        Assert.assertTrue(list.get(0).getDelivering() == 0 && list.get(0).getDeliveringJudgeTimeout() == 0);
        Assert.assertTrue(list.get(0).getFulfilling() == 7 && list.get(0).getFulfillingRider() == 1);
        Assert.assertTrue(list.get(1).getWaitToAccept() == 0 && list.get(1).getWaitToAcceptJudgeTimeout() == 0);
        Assert.assertTrue(list.get(1).getPicking() == 1 && list.get(1).getPickingJudgeTimeout() == 0);
        Assert.assertTrue(list.get(1).getDelivering() == 5 && list.get(1).getDeliveringJudgeTimeout() == 2);
        Assert.assertTrue(list.get(1).getFulfilling() == 6 && list.get(1).getFulfillingRider() == 2);

        System.out.println(JSON.toJSON(list));
    }


    public List<RiderDeliveryOrder> buildRiderDeliveryOrderList() {
        List<RiderDeliveryOrder> orderList = new ArrayList<>();
        //1、预约单、实付金额小于200，会被过滤，不被统计
        orderList.add(new RiderDeliveryOrder(1L, 1000795L, 12345L, 1L, null,
                1, 1, 1, true, null, null,
                LocalDateTime.now().plusMinutes(30), RiderDeliveryStatusEnum.RIDER_ASSIGNED, null,
                null, new Courier("null", "","123", 12345L), null,
                1, LocalDateTime.now(), 0, 0, null, null, null,null, null, null, 0, null, null));

        //1.1、预约单、实付金额大于200，已分配骑手，未超时
        orderList.add(new RiderDeliveryOrder(11L, 1000795L, 12345L, 11L, null,
                1, 1, 1, true, null, null,
                LocalDateTime.now().plusMinutes(30), RiderDeliveryStatusEnum.RIDER_ASSIGNED, null,
                null, new Courier("null","", "123", 12345L), null,
                1, LocalDateTime.now(), 0, 0, null, null, null,null, null, null, 0, null, null));

        //1.2、预约单、实付金额大于200，已分配骑手，已超时
        orderList.add(new RiderDeliveryOrder(12L, 1000795L, 12345L, 12L, null,
                1, 1, 1, true, null, null,
                LocalDateTime.now().minusMinutes(30), RiderDeliveryStatusEnum.RIDER_ASSIGNED, null,
                null, new Courier("null", "","123", 12345L), null,
                1, LocalDateTime.now(), 0, 0, null, null, null,null, null, null, 0, null, null));

        //2、实时单、实付金额小于200，会被过滤，不被统计
        orderList.add(new RiderDeliveryOrder(2L, 1000795L, 12345L, 2L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(30), RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, null,
                null, null, null,
                1, LocalDateTime.now(), 0, 0, null, null, null,null, null, null, 0, null, null));

        //2.1、实时单、实付金额大于200，还没分配骑手，未超时
        orderList.add(new RiderDeliveryOrder(21L, 1000795L, 12345L, 21L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(30), RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, null,
                null, null, null,
                1, LocalDateTime.now(), 0, 0, null, null, null,null, null, null, 0, null, null));

        //2.2、实时单、实付金额大于200，还没分配骑手，已超时
        orderList.add(new RiderDeliveryOrder(22L, 1000795L, 12345L, 22L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(10), RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, null,
                null, null, null,
                1, LocalDateTime.now().minusMinutes(30), 0, 0, null, null, null,null, null, null, 0, null, null));

        //3、预约单、实付金额大于200 ，还没分配骑手，未超时
        orderList.add(new RiderDeliveryOrder(3L, 1000795L, 12345L, 3L, null,
                1, 1, 1, true, null, null,
                LocalDateTime.now().plusMinutes(30), RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, null,
                null, null, null,
                1, LocalDateTime.now(), 0, 0, null, null, null,null, null, null, 0, null, null));

        //4、预约单、实付金额大于200 ，已分配骑手，未超时
        orderList.add(new RiderDeliveryOrder(4L, 1000795L, 12345L, 4L, null,
                1, 1, 1, true, null, null,
                LocalDateTime.now().plusMinutes(30), RiderDeliveryStatusEnum.RIDER_ASSIGNED, null,
                null, new Courier("null", "","123", 12345L), null,
                1, LocalDateTime.now(), 0, 0, null, null, null,null, null, null, 0, null, null));

        //5、预约单、实付金额大于200 ，已分配骑手，已超时
        orderList.add(new RiderDeliveryOrder(5L, 1000795L, 12345L, 5L, null,
                1, 1, 1, true, null, null,
                LocalDateTime.now().minusMinutes(10), RiderDeliveryStatusEnum.RIDER_ASSIGNED, null,
                null, new Courier("null", "","123", 12345L), null,
                1, LocalDateTime.now(), 0, 0, null, null, null, null, null, null, 0, null, null));

        //5.1、实时单、实付金额大于200 ，已分配骑手，未超时
        orderList.add(new RiderDeliveryOrder(51L, 1000795L, 54321L, 51L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(10), RiderDeliveryStatusEnum.RIDER_ASSIGNED, null,
                null, new Courier("null", "","123", 54321L), null,
                1, LocalDateTime.now().minusMinutes(10), 0, 0, null, null, null, null, null, null, 0, null, null));

        //6、实时单、实付金额大于200 ，骑手已取货，未超时
        orderList.add(new RiderDeliveryOrder(6L, 1000795L, 54321L, 6L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(10), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS, null,
                null, new Courier("null", "","123", 54321L), null,
                1, LocalDateTime.now().minusMinutes(10), 0, 0, null, null, null, null, null, null, 0, null, null));

        //7、实时单、实付金额大于200 ，骑手已取货，未超时
        orderList.add(new RiderDeliveryOrder(7L, 1000795L, 54321L, 7L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(10), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS, null,
                null, new Courier("null", "","123", 54321L), null,
                1, LocalDateTime.now().minusMinutes(10), 0, 0, null, null, null, null, null, null, 0, null, null));

        //8、实时单、实付金额大于200 ，骑手已取货，已超时
        orderList.add(new RiderDeliveryOrder(8L, 1000795L, 54321L, 8L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(10), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS, null,
                null, new Courier("null", "","123", 54321L), null,
                1, LocalDateTime.now().minusMinutes(30), 0, 0, null, null, null, null, null, null, 0, null, null));


        //9、实时单、实付金额大于200 ，骑手已取货，未超时
        orderList.add(new RiderDeliveryOrder(9L, 1000795L, 54321L, 9L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(10), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS, null,
                null, new Courier("null", "","123", 12345L), null,
                1, LocalDateTime.now().minusMinutes(10), 0, 0, null, null, null, null, null, null, 0, null, null));

        //10、实时单、实付金额大于200 ，骑手已取货，已超时
        orderList.add(new RiderDeliveryOrder(10L, 1000795L, 54321L, 10L, null,
                1, 1, 1, false, null, null,
                LocalDateTime.now().plusMinutes(10), RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS, null,
                null, new Courier("null", "","123", 12345L), null,
                1, LocalDateTime.now().minusMinutes(30), 0, 0, null, null, null, null, null, null, 0, null, null));

        return orderList;
    }

    @Test
    //查询范围：昨天 数据未到岗 ==> 返回空指标
    public void test_query_durations_statistics_1() {

        Mockito.when(riderDeliveryStatisticsRepository.checkDataIsReadyByDt(Mockito.any())).thenReturn(false);

        List<RiderDurationDeliveryStatistics> result = riderDeliveryStatisticsApplicationService.queryDurationsStatistics(1000395L, 1234L,
                Collections.singletonList(new StatisticDuration(LocalDate.now().minusDays(1), LocalDate.now().minusDays(1))));

        Assert.assertNull(result.get(0).getDeliveredCount());
        Assert.assertNull(result.get(0).getTimeoutCount());
        Assert.assertNull(result.get(0).getDeliveredRateIn25min());
        Assert.assertNull(result.get(0).getCancelBeforeDeliveredCount());
        Assert.assertNull(result.get(0).getEarlyClickDeliveryCount());
        Assert.assertNull(result.get(0).getAvgFulfillDuration());
        Assert.assertNull(result.get(0).getTimeoutRate());
        Assert.assertEquals(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getBeginDate());
        Assert.assertEquals(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getEndDate());
        Assert.assertEquals(1000395L,result.get(0).getTenantId().longValue());
        Assert.assertEquals(1234L,result.get(0).getRiderAccountId().longValue());
    }

    @Test
    //查询范围：[前天,昨天] 数据未到岗 ==> 返回0指标
    public void test_query_durations_statistics_2() {

        Mockito.when(riderDeliveryStatisticsRepository.checkDataIsReadyByDt(Mockito.any())).thenReturn(false);

        Mockito.when(riderDeliveryStatisticsRepository.queryByAccountIdAndDuration(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any()))
                .thenReturn(Collections.emptyList());

        List<RiderDurationDeliveryStatistics> result = riderDeliveryStatisticsApplicationService.queryDurationsStatistics(1000395L, 1234L,
                Collections.singletonList(new StatisticDuration(LocalDate.now().minusDays(2), LocalDate.now().minusDays(1))));

        Assert.assertEquals(Integer.valueOf(0),result.get(0).getDeliveredCount());
        Assert.assertEquals(Integer.valueOf(0),result.get(0).getTimeoutCount());
        Assert.assertEquals("0.00",result.get(0).getDeliveredRateIn25min());
        Assert.assertEquals(Integer.valueOf(0),result.get(0).getCancelBeforeDeliveredCount());
        Assert.assertEquals(Integer.valueOf(0),result.get(0).getEarlyClickDeliveryCount());
        Assert.assertEquals(0L,result.get(0).getAvgFulfillDuration().longValue());
        Assert.assertEquals("0.00",result.get(0).getTimeoutRate());
        Assert.assertEquals(LocalDate.now().minusDays(2).format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getBeginDate());
        Assert.assertEquals(LocalDate.now().minusDays(2).format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getEndDate());
        Assert.assertEquals(1000395L,result.get(0).getTenantId().longValue());
        Assert.assertEquals(1234L,result.get(0).getRiderAccountId().longValue());
    }

    @Test
    //查询范围：[前天,昨天] 数据到岗 ==> 返回聚合后的指标
    public void test_query_durations_statistics_3() {

        LocalDate beginDate = LocalDate.now().minusDays(2);
        LocalDate endDate = LocalDate.now().minusDays(1);
        RiderDeliveryStatisticDO statisticDO_1 = new RiderDeliveryStatisticDO();
        statisticDO_1.setId(1L);
        statisticDO_1.setDelivered(100);
        statisticDO_1.setCancelBeforeDelivered(3);
        statisticDO_1.setDeliveredExcludeBooking(95);
        statisticDO_1.setDeliveryDuration(150000L);
        statisticDO_1.setDeliveredIn25Mins(88);
        statisticDO_1.setEarlyClick(2);
        statisticDO_1.setDt(Long.parseLong(beginDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        statisticDO_1.setRiderAccountId(1234L);
        statisticDO_1.setTenantId(1000395L);
        statisticDO_1.setTimeout(8);


        RiderDeliveryStatisticDO statisticDO_2 = new RiderDeliveryStatisticDO();
        statisticDO_2.setId(2L);
        statisticDO_2.setDelivered(100);
        statisticDO_2.setCancelBeforeDelivered(4);
        statisticDO_2.setDeliveredExcludeBooking(94);
        statisticDO_2.setDeliveryDuration(160000L);
        statisticDO_2.setDeliveredIn25Mins(85);
        statisticDO_2.setEarlyClick(null);
        statisticDO_2.setDt(Long.parseLong(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        statisticDO_2.setRiderAccountId(1234L);
        statisticDO_2.setTenantId(1000395L);
        statisticDO_2.setTimeout(6);

        List<RiderDeliveryStatisticDO> mockList = Arrays.asList(statisticDO_1,statisticDO_2);
        Mockito.when(riderDeliveryStatisticsRepository.checkDataIsReadyByDt(Mockito.any())).thenReturn(true);

        Mockito.when(riderDeliveryStatisticsRepository.queryByAccountIdAndDuration(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(mockList);

        List<RiderDurationDeliveryStatistics> result = riderDeliveryStatisticsApplicationService.queryDurationsStatistics(1000395L, 1234L,
                Collections.singletonList(new StatisticDuration(beginDate, endDate)));

        Assert.assertEquals(Integer.valueOf(200),result.get(0).getDeliveredCount());
        Assert.assertEquals(Integer.valueOf(14),result.get(0).getTimeoutCount());
        Assert.assertEquals("91.53",result.get(0).getDeliveredRateIn25min());
        Assert.assertEquals(Integer.valueOf(7),result.get(0).getCancelBeforeDeliveredCount());
        Assert.assertEquals(Integer.valueOf(2),result.get(0).getEarlyClickDeliveryCount());
        Assert.assertEquals("1640.21164",result.get(0).getAvgFulfillDuration().toString());
        Assert.assertEquals("7.00",result.get(0).getTimeoutRate());
        Assert.assertEquals(beginDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getBeginDate());
        Assert.assertEquals(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getEndDate());
        Assert.assertEquals(1000395L,result.get(0).getTenantId().longValue());
        Assert.assertEquals(1234L,result.get(0).getRiderAccountId().longValue());
    }


    @Test
    //查询范围：[昨天-3,昨天-2]  ==> 返回聚合后的指标
    public void test_query_durations_statistics_4() {

        LocalDate beginDate = LocalDate.now().minusDays(4);
        LocalDate endDate = LocalDate.now().minusDays(3);
        RiderDeliveryStatisticDO statisticDO_1 = new RiderDeliveryStatisticDO();
        statisticDO_1.setId(1L);
        statisticDO_1.setDelivered(100);
        statisticDO_1.setCancelBeforeDelivered(3);
        statisticDO_1.setDeliveredExcludeBooking(95);
        statisticDO_1.setDeliveryDuration(150000L);
        statisticDO_1.setDeliveredIn25Mins(88);
        statisticDO_1.setEarlyClick(2);
        statisticDO_1.setDt(Long.parseLong(beginDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        statisticDO_1.setRiderAccountId(1234L);
        statisticDO_1.setTenantId(1000395L);
        statisticDO_1.setTimeout(8);


        RiderDeliveryStatisticDO statisticDO_2 = new RiderDeliveryStatisticDO();
        statisticDO_2.setId(2L);
        statisticDO_2.setDelivered(100);
        statisticDO_2.setCancelBeforeDelivered(4);
        statisticDO_2.setDeliveredExcludeBooking(94);
        statisticDO_2.setDeliveryDuration(160000L);
        statisticDO_2.setDeliveredIn25Mins(85);
        statisticDO_2.setEarlyClick(null);
        statisticDO_2.setDt(Long.parseLong(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        statisticDO_2.setRiderAccountId(1234L);
        statisticDO_2.setTenantId(1000395L);
        statisticDO_2.setTimeout(6);

        List<RiderDeliveryStatisticDO> mockList = Arrays.asList(statisticDO_1,statisticDO_2);
        Mockito.when(riderDeliveryStatisticsRepository.checkDataIsReadyByDt(Mockito.any())).thenReturn(true);

        Mockito.when(riderDeliveryStatisticsRepository.queryByAccountIdAndDuration(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(mockList);

        List<RiderDurationDeliveryStatistics> result = riderDeliveryStatisticsApplicationService.queryDurationsStatistics(1000395L, 1234L,
                Collections.singletonList(new StatisticDuration(beginDate, endDate)));

        Assert.assertEquals(Integer.valueOf(200),result.get(0).getDeliveredCount());
        Assert.assertEquals(Integer.valueOf(14),result.get(0).getTimeoutCount());
        Assert.assertEquals("91.53",result.get(0).getDeliveredRateIn25min());
        Assert.assertEquals(Integer.valueOf(7),result.get(0).getCancelBeforeDeliveredCount());
        Assert.assertEquals(Integer.valueOf(2),result.get(0).getEarlyClickDeliveryCount());
        Assert.assertEquals(1640L,result.get(0).getAvgFulfillDuration().longValue());
        Assert.assertEquals("7.00",result.get(0).getTimeoutRate());
        Assert.assertEquals(beginDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getBeginDate());
        Assert.assertEquals(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),result.get(0).getEndDate());
        Assert.assertEquals(1000395L,result.get(0).getTenantId().longValue());
        Assert.assertEquals(1234L,result.get(0).getRiderAccountId().longValue());
    }

    @Test
    public void test(){

    }
}
