package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application;

import com.google.common.collect.Sets;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.cmd.MergeOrderOperateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.leaf.LeafClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.strategy.MergeOrderRecordAddStrategy;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/9/9 21:40
 **/
@RunWith(SpringRunner.class)
public class MergeOrderRecordAddStrategyTest {
    @Mock
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;

    @Mock
    private RiderDeliveryMergeOrderRecordRepository riderDeliveryMergeOrderRecordRepository;

    @InjectMocks
    private MergeOrderRecordAddStrategy mergeOrderRecordAddStrategy;

    @Mock
    private LeafClient leafClient;

    @Test
    //A B C 三个运单 A送达
    public void buildMergeOrderOperateCmdTest1() {
        RiderDeliveryOrder riderDeliveryOrderA = new RiderDeliveryOrder(1234L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0 ,null,null);


        RiderDeliveryOrder riderDeliveryOrderB = new RiderDeliveryOrder(12345L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null,null, null, 0, null, null);

        RiderDeliveryOrder riderDeliveryOrderC = new RiderDeliveryOrder(123456L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null,null, null, 0, null, null);


        Map<Long, Set<Long>> mergeRecords = Collections.emptyMap();
        List<RiderDeliveryOrder> deliveringOrders = Arrays.asList(riderDeliveryOrderB, riderDeliveryOrderC);
        Map<Long, RiderDeliveryOrder> deliveringMap = deliveringOrders.stream()
                .collect(Collectors.toMap(RiderDeliveryOrder::getId, Function.identity()));

        Mockito.when(riderDeliveryOrderRepository.queryRiderDeliveringOrderMap(Mockito.any(),Mockito.any(), null))
                .thenReturn(deliveringMap);

        Mockito.when(riderDeliveryMergeOrderRecordRepository.queryRiderMergeOrderRecordMap(Mockito.any(),Mockito.any()))
                .thenReturn(mergeRecords);

        Mockito.when(leafClient.nextSnowflakeId()).thenReturn(3514254213541L);

        List<MergeOrderOperateCmd> mergeOrderOperateCmdList = mergeOrderRecordAddStrategy.buildMergeOrderOperateCmd(riderDeliveryOrderA);
        List<Long> resultDeliveryIds = Arrays.asList(1234L, 12345L, 123456L);
        Assert.assertEquals(1, mergeOrderOperateCmdList.size());
        Assert.assertEquals(3514254213541L, mergeOrderOperateCmdList.get(0).getMergeId().longValue());
        Assert.assertEquals(3, mergeOrderOperateCmdList.get(0).getRiderDeliveryOrders().size());
        Assert.assertTrue(mergeOrderOperateCmdList.get(0).getRiderDeliveryOrders().stream().allMatch(riderDeliveryOrder -> resultDeliveryIds.contains(riderDeliveryOrder.getId())));
    }



    @Test
    //A B C D A已送达 ==> D送达
    public void buildMergeOrderOperateCmdTest2() {
        RiderDeliveryOrder riderDeliveryOrderA = new RiderDeliveryOrder(1234L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null,null, null, 0, null, null);


        RiderDeliveryOrder riderDeliveryOrderB = new RiderDeliveryOrder(12345L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null,null, null, 0, null, null);

        RiderDeliveryOrder riderDeliveryOrderC = new RiderDeliveryOrder(123456L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null,null, null, 0, null, null);

        RiderDeliveryOrder riderDeliveryOrderD = new RiderDeliveryOrder(1234567L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null,null, null, 0, null, null);

        RiderDeliveryMergeOrderRecord mergeRecord1 = new RiderDeliveryMergeOrderRecord(1L, 1234L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);
        RiderDeliveryMergeOrderRecord mergeRecord2 = new RiderDeliveryMergeOrderRecord(2L, 12345L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);
        RiderDeliveryMergeOrderRecord mergeRecord3 = new RiderDeliveryMergeOrderRecord(3L, 123456L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);
        RiderDeliveryMergeOrderRecord mergeRecord4 = new RiderDeliveryMergeOrderRecord(4L, 1234567L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);

        Map<Long,Set<Long>> mergeRecordsMap = new HashMap<>(Collections.emptyMap());

        mergeRecordsMap.put(3514254213541L, Sets.newHashSet(mergeRecord1.getDeliveryOrderId(), mergeRecord2.getDeliveryOrderId(),
                mergeRecord3.getDeliveryOrderId(), mergeRecord4.getDeliveryOrderId()));


        List<RiderDeliveryOrder> deliveringOrders = Arrays.asList(riderDeliveryOrderB, riderDeliveryOrderC);
        Map<Long, RiderDeliveryOrder> deliveringMap = deliveringOrders.stream()
                .collect(Collectors.toMap(RiderDeliveryOrder::getId, Function.identity()));

        Mockito.when(riderDeliveryOrderRepository.queryRiderDeliveringOrderMap(Mockito.any(),Mockito.any(), null))
                .thenReturn(deliveringMap);

        Mockito.when(riderDeliveryMergeOrderRecordRepository.queryRiderMergeOrderRecordMap(Mockito.any(),Mockito.any()))
                .thenReturn(mergeRecordsMap);

        List<MergeOrderOperateCmd> mergeOrderOperateCmdList = mergeOrderRecordAddStrategy.buildMergeOrderOperateCmd(riderDeliveryOrderD);
        Assert.assertEquals(0, mergeOrderOperateCmdList.size());
    }


    @Test
    //A B C A已送达,又接了新的配送任务D ==> B送达
    public void buildMergeOrderOperateCmdTest3() {
        RiderDeliveryOrder riderDeliveryOrderA = new RiderDeliveryOrder(1234L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);


        RiderDeliveryOrder riderDeliveryOrderB = new RiderDeliveryOrder(12345L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);

        RiderDeliveryOrder riderDeliveryOrderC = new RiderDeliveryOrder(123456L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);

        RiderDeliveryOrder riderDeliveryOrderD = new RiderDeliveryOrder(1234567L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);

        RiderDeliveryMergeOrderRecord mergeRecord1 = new RiderDeliveryMergeOrderRecord(1L, 1234L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);
        RiderDeliveryMergeOrderRecord mergeRecord2 = new RiderDeliveryMergeOrderRecord(2L, 12345L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);
        RiderDeliveryMergeOrderRecord mergeRecord3 = new RiderDeliveryMergeOrderRecord(3L, 123456L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);

        Map<Long,Set<Long>> mergeRecordsMap = new HashMap<>(Collections.emptyMap());
        mergeRecordsMap.put(3514254213541L, Sets.newHashSet(mergeRecord1.getDeliveryOrderId(), mergeRecord2.getDeliveryOrderId(),
                mergeRecord3.getDeliveryOrderId()));

        List<RiderDeliveryOrder> deliveringOrders = Arrays.asList(riderDeliveryOrderC, riderDeliveryOrderD);

        Map<Long, RiderDeliveryOrder> deliveringMap = deliveringOrders.stream()
                .collect(Collectors.toMap(RiderDeliveryOrder::getId, Function.identity()));

        Mockito.when(riderDeliveryOrderRepository.queryRiderDeliveringOrderMap(Mockito.any(),Mockito.any(), null))
                .thenReturn(deliveringMap);

        Mockito.when(riderDeliveryMergeOrderRecordRepository.queryRiderMergeOrderRecordMap(Mockito.any(),Mockito.any()))
                .thenReturn(mergeRecordsMap);

        Mockito.when(leafClient.nextSnowflakeId()).thenReturn(3514254213541L);

        List<MergeOrderOperateCmd> mergeOrderOperateCmdList = mergeOrderRecordAddStrategy.buildMergeOrderOperateCmd(riderDeliveryOrderB);

        List<Long> resultDeliveryIds = Arrays.asList(riderDeliveryOrderB.getId(), riderDeliveryOrderC.getId(), riderDeliveryOrderD.getId());
        Assert.assertEquals(1, mergeOrderOperateCmdList.size());
        Assert.assertEquals(3514254213541L, mergeOrderOperateCmdList.get(0).getMergeId().longValue());
        Assert.assertEquals(3, mergeOrderOperateCmdList.get(0).getRiderDeliveryOrders().size());
        Assert.assertTrue(mergeOrderOperateCmdList.get(0).getRiderDeliveryOrders().stream().allMatch(riderDeliveryOrder -> resultDeliveryIds.contains(riderDeliveryOrder.getId())));
    }

    @Test
    //A B C A已送达,又接了新的配送任务D ==> D送达
    public void buildMergeOrderOperateCmdTest4() {
        RiderDeliveryOrder riderDeliveryOrderA = new RiderDeliveryOrder(1234L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);


        RiderDeliveryOrder riderDeliveryOrderB = new RiderDeliveryOrder(12345L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);

        RiderDeliveryOrder riderDeliveryOrderC = new RiderDeliveryOrder(123456L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);

        RiderDeliveryOrder riderDeliveryOrderD = new RiderDeliveryOrder(1234567L,null,null,null,null,null,null,
                null,null,null,null,null,null,
                null,null, new Courier("hahah","1341354",null,1234L),null,null,null,
                0,null,null,null,null,null, null, null, 0, null, null);

        RiderDeliveryMergeOrderRecord mergeRecord1 = new RiderDeliveryMergeOrderRecord(1L, 1234L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);
        RiderDeliveryMergeOrderRecord mergeRecord2 = new RiderDeliveryMergeOrderRecord(2L, 12345L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);
        RiderDeliveryMergeOrderRecord mergeRecord3 = new RiderDeliveryMergeOrderRecord(3L, 123456L, 3514254213541L, 1L, null, null, 1341354L, null, null, null);

        Map<Long,Set<Long>> mergeRecordsMap = new HashMap<>(Collections.emptyMap());
        mergeRecordsMap.put(3514254213541L, Sets.newHashSet(mergeRecord1.getDeliveryOrderId(), mergeRecord2.getDeliveryOrderId(),
                mergeRecord3.getDeliveryOrderId()));
        List<RiderDeliveryOrder> deliveringOrders = Arrays.asList(riderDeliveryOrderB, riderDeliveryOrderC);

        Map<Long, RiderDeliveryOrder> deliveringMap = deliveringOrders.stream()
                .collect(Collectors.toMap(RiderDeliveryOrder::getId, Function.identity()));

        Mockito.when(riderDeliveryOrderRepository.queryRiderDeliveringOrderMap(Mockito.any(),Mockito.any(), null))
                .thenReturn(deliveringMap);

        Mockito.when(riderDeliveryMergeOrderRecordRepository.queryRiderMergeOrderRecordMap(Mockito.any(),Mockito.any()))
                .thenReturn(mergeRecordsMap);

        Mockito.when(leafClient.nextSnowflakeId()).thenReturn(647236478L);

        List<MergeOrderOperateCmd> mergeOrderOperateCmdList = mergeOrderRecordAddStrategy.buildMergeOrderOperateCmd(riderDeliveryOrderD);

        List<Long> resultDeliveryIds = Arrays.asList(riderDeliveryOrderB.getId(), riderDeliveryOrderC.getId(), riderDeliveryOrderD.getId());
        Assert.assertEquals(1, mergeOrderOperateCmdList.size());
        Assert.assertEquals(647236478L, mergeOrderOperateCmdList.get(0).getMergeId().longValue());
        Assert.assertEquals(3, mergeOrderOperateCmdList.get(0).getRiderDeliveryOrders().size());
        Assert.assertTrue(mergeOrderOperateCmdList.get(0).getRiderDeliveryOrders().stream().allMatch(riderDeliveryOrder -> resultDeliveryIds.contains(riderDeliveryOrder.getId())));
    }

}
