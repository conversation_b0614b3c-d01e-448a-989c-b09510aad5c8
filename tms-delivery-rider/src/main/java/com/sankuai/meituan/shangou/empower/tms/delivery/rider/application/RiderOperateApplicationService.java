package com.sankuai.meituan.shangou.empower.tms.delivery.rider.application;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.BatchPostRiderLocatingLogTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderArrivalLocationTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderLocatingLogTRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.RiderLocationDataDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.creditaudit.CreditAuditClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.link.ShortLinkServiceClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.log.RiderDeliveryOrderLogClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.osw.OswClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.ClientPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.RiderPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.AuditStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.ChangeRiderEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.service.RiderDeliveryChangeNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.DELIVERY_ORDER_STATUS_ERROR;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.EXCEPTION_REPORT_TIMES_ERROR;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.EXCEPTION_TYPE_ERROR;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiderOperateApplicationService {

	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	@Resource
	private RiderArrivalLocationRepository riderArrivalLocationRepository;

	@Resource
	private RiderLocatingExceptionRepository riderLocatingExceptionRepository;
	@Resource
	private RiderDeliveryOrderRepository riderDeliveryOrderRepository;
	@Resource
	private RiderDeliveryExceptionRepository riderDeliveryExceptionRepository;

	@Autowired
	protected TenantSystemClient tenantSystemClient;

	@Value("${dh.clientPush.wxMessageTemplateId}")
	private String wxMessageTemplateId;

	@Value("${dh.clientPush.smsMessageTemplateId}")
	private String smsMessageTemplateId;

	@Resource
	private CreditAuditClient creditAuditClient;

	@Resource
	private ClientPushClient clientPushClient;

	@Resource
	private ShortLinkServiceClient shortLinkServiceClient;

	private final RiderDeliveryOrderLogClient riderDeliveryOrderLogClient;
	private final RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;
	private final RiderPushClient riderPushClient;
	private final TransactionTemplate transactionTemplate;

	@Resource
	private RiderLocationRepository riderLocationRepository;

	@Resource
	private OswClient oswClient;

	@Resource
	private NewSupplyRiderInfoDeliveryRepository newSupplyRiderInfoDeliveryRepository;

	@Resource
	private RiderDeliveryOrderSyncOutClient orderSyncOutClient;
	@Resource
	private RiderDeliveryChangeNotifyService deliveryChangeNotifyService;

	private static final Logger riderLocatingLog = LoggerFactory.getLogger("logger_drunk.horse.rider.locating.log");

	static final Logger riderPostLocationLog = LoggerFactory.getLogger("logger_rider.post.location.log");

	private static final DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	public Optional<Failure> lockDeliveryStatus(Long deliveryOrderId, StaffRider rider, Long tenantId, Long storeId) {
		Optional<RiderDeliveryOrder> deliveryOrderOptional = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		}else {
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(deliveryOrderId);
		}

		return deliveryOrderOptional
				.map(deliveryOrder -> {
					if (DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch() ? nonSelfDeliveryPoi(deliveryOrder) : nonSelfDeliveryOrder(deliveryOrder)) {
						return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_SHOP_CONFIG_ERR));
					} else {
						return deliveryOrder.lockDeliveryStatus(rider);
					}
				})
				.orElseGet(() -> Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST)));
	}

	public Optional<Failure> unlockDeliveryStatus(Long deliveryOrderId, StaffRider rider, Long tenantId, Long storeId) {
		Optional<RiderDeliveryOrder> deliveryOrderOptional = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		}else {
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(deliveryOrderId);
		}
		return deliveryOrderOptional
				.map(deliveryOrder -> {
					if (DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch() ? nonSelfDeliveryPoi(deliveryOrder) : nonSelfDeliveryOrder(deliveryOrder)) {
						return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_SHOP_CONFIG_ERR));
					} else {
						return deliveryOrder.unlockDeliveryStatus(rider);
					}
				})
				.orElseGet(() -> Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST)));
	}

	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> postDeliveryProofPhoto(Long deliveryOrderId, Long riderAccountId, List<String> deliveryProofPhotoUrls, String userIP,Long tenantId,Long storeId) {
		Optional<RiderDeliveryOrder> deliveryOrderOpt = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		}else {
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(deliveryOrderId);
		}

		if (!deliveryOrderOpt.isPresent()) {
			return Optional.of(new Failure(false, DELIVERY_ORDER_NOT_EXIST));
		}

		//只有歪马租户允许操作
		RiderDeliveryOrder deliveryOrder = deliveryOrderOpt.get();

		Optional<Failure> failure = deliveryOrder.saveDeliveryProofPhotoUrls(deliveryProofPhotoUrls, riderAccountId);
		if (failure.isPresent()) {
			return failure;
		}

		//送审
		List<Pair<Long, String>> photoInfoList = deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList()
				.stream().map(photoInfo -> Pair.of(photoInfo.getPicId(), photoInfo.getPhotoUrl()))
				.collect(Collectors.toList());
		creditAuditClient.sendCreditAudit(photoInfoList, deliveryOrderId, userIP, riderAccountId,deliveryOrder.getTenantId(),deliveryOrder.getStoreId());

		//发延迟消息
		creditAuditClient.sendCreditAuditAutomaticPassMessage(deliveryOrderId,deliveryOrder.getTenantId(),deliveryOrder.getStoreId());

		return Optional.empty();
	}


	@CatTransaction
	@MethodLog(logResponse = true)
	public Optional<Failure> accept(Long deliveryOrderId, StaffRider rider, Coordination coordination, Long tenantId, Long storeId, @Nullable Long empowerOrderId) {
		Optional<RiderDeliveryOrder> deliveryOrderOptional = Optional.empty();
		if(Objects.nonNull(deliveryOrderId)){
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		} else if (Objects.nonNull(empowerOrderId)){
			deliveryOrderOptional = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithTenant(empowerOrderId,tenantId,storeId);
		}
		return deliveryOrderOptional
				.map(deliveryOrder -> {
					if (isProcessSelfDeliveryRiderChange(deliveryOrder, rider)) {
						processRiderInfoChange4NewSupplyTenant(deliveryOrder, rider);
					}
					if (nonSelfDeliveryOrder(deliveryOrder)) {
						return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_ERROR));
					} else {
						//获取骑手位置
						RiderLocationDetail finalRiderLocationDetail = null;
						if (DeliveryRiderMccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())
								&& DeliveryRiderMccConfigUtils.isFillRiderLocationGrayStore(deliveryOrder.getStoreId())
						&& DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch()) {
							finalRiderLocationDetail = getAcceptOrTakeAwayRiderLocation(rider, deliveryOrder, coordination);
						} else {
							RiderLocationDetail riderLocationDetail = riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
							finalRiderLocationDetail = fillRiderLocation(riderLocationDetail, rider, deliveryOrder, coordination);
						}

						return deliveryOrder.accept(rider, finalRiderLocationDetail);
					}
				})
				.orElseGet(() -> Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST)));
	}

	private boolean isProcessSelfDeliveryRiderChange(RiderDeliveryOrder deliveryOrder, StaffRider rider) {
		return DeliveryRiderMccConfigUtils.getProcessSelfDeliveryRiderChangeSwitch()
				&& !DeliveryRiderMccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())
				&& Objects.nonNull(rider)
				&& org.apache.commons.lang3.StringUtils.isNotEmpty(rider.getRiderName())
				&& org.apache.commons.lang3.StringUtils.isNotEmpty(rider.getRiderPhone());
	}

	private void processRiderInfoChange4NewSupplyTenant(RiderDeliveryOrder deliveryOrder, StaffRider rider) {
		try {
			Long orderId = deliveryOrder.getCustomerOrderKey().getOrderId();
			String channelOrderId = deliveryOrder.getCustomerOrderKey().getChannelOrderId();
			Optional<Rider> riderOptional = newSupplyRiderInfoDeliveryRepository.queryRiderInfo(orderId);
			if (!riderOptional.isPresent()) {
				Rider riderCmd = new Rider(rider.getRiderName(), rider.getRiderPhone(), StringUtils.EMPTY);
				newSupplyRiderInfoDeliveryRepository.saveRiderInfo(orderId, riderCmd);
				return;
			}

			RiderDeliveryExtInfo extInfo = deliveryOrder.getRiderDeliveryExtInfo();
			if (Objects.isNull(extInfo)) {
				return;
			}
			Integer transType = extInfo.getTransType();
			if (!DeliveryOrderTransEnum.isTrans(transType)) {
				// 目前新供给商家自送发生骑手信息变更仅针对转自送的场景
				return;
			}

			Rider riderCache = riderOptional.get();
			boolean isRiderChange = !Objects.equals(riderCache.getRiderName(), rider.getRiderName()) || !Objects.equals(riderCache.getRiderPhone(), rider.getRiderPhone());
			if (isRiderChange) {
				Rider riderCmd = new Rider(rider.getRiderName(), rider.getRiderPhone(), StringUtils.EMPTY);
				newSupplyRiderInfoDeliveryRepository.saveRiderInfo(orderId, riderCmd);
				// 这里需要先同步渠道骑手信息变更，再同步渠道后续新骑手的配送状态，因此实现为同步调用
				orderSyncOutClient.syncRiderInfoChange(deliveryOrder, riderCmd, channelOrderId);
			}
		} catch (Exception e) {
			log.error("processRiderInfoChange4NewSupplyTenant error", e);
		}
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public Optional<Failure> takeAway(Long deliveryOrderId, StaffRider rider, Coordination coordination, Long tenantId, Long storeId, Long empowerOrderId) {
		Optional<RiderDeliveryOrder> deliveryOrderOptional = Optional.empty();
		if(Objects.nonNull(deliveryOrderId)){
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		} else if (Objects.nonNull(empowerOrderId)){
			deliveryOrderOptional = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithTenant(empowerOrderId,tenantId,storeId);
		}


		return deliveryOrderOptional
				.map(deliveryOrder -> {
					if (nonSelfDeliveryOrder(deliveryOrder)) {
						return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_ERROR));
					}
					//获取骑手位置
					RiderLocationDetail finalRiderLocationDetail = null;
					if (DeliveryRiderMccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())
							&& DeliveryRiderMccConfigUtils.isFillRiderLocationGrayStore(deliveryOrder.getStoreId())
					&& DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch()) {
						finalRiderLocationDetail = getAcceptOrTakeAwayRiderLocation(rider, deliveryOrder, coordination);
					} else {
						RiderLocationDetail riderLocationDetail = riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
						finalRiderLocationDetail = fillRiderLocation(riderLocationDetail, rider, deliveryOrder, coordination);
					}

					return deliveryOrder.takeAway(rider, finalRiderLocationDetail);
				})
				.orElseGet(() -> Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST)));
	}

	public Optional<Failure> riderChange(Long tenantId, Long storeId, Long orderId, StaffRider newRider, Long[] oldRiderAccountId, String[] olderRiderAccountName) {
		Optional<RiderDeliveryOrder> optRiderDeliveryOrder = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(tenantId, storeId, orderId);
		if (!optRiderDeliveryOrder.isPresent()) {
			log.warn("RiderDeliveryOrder[{}] not exist", orderId);
			return Optional.of(new Failure(false, DELIVERY_ORDER_NOT_EXIST.getCode(), DELIVERY_ORDER_NOT_EXIST.getMessage()));
		}
		RiderDeliveryOrder riderDeliveryOrder = optRiderDeliveryOrder.get();
		StaffRider oldRider = riderDeliveryOrder.getRiderInfo();

		LocalDateTime now = LocalDateTime.now();
		Result<Void> result = riderDeliveryOrder.riderChange(newRider, now);
		if (result.isFail()) {
			return Optional.of(result.getFailure());
		}

		transactionTemplate.execute(new TransactionCallbackWithoutResult() {
			@Override
			protected void doInTransactionWithoutResult(TransactionStatus status) {
				riderDeliveryOrderRepository.save(riderDeliveryOrder);
				riderDeliveryOrderLogClient.logDeliveryChangeSyncly(riderDeliveryOrder.getId(),
						riderDeliveryOrder.getStatus(), DeliveryEventEnum.RIDER_CHANGE,
						new RiderDeliveryOrderRiderChangeInfo(oldRider, newRider, riderDeliveryOrder.getStatus()), now);
			}
		});

		riderDeliveryOrderSyncOutClient.syncToOrderSystem(riderDeliveryOrder, now,riderLocationRepository.getStaffRiderLocation(newRider.getRiderAccountId()));

		try {
			riderPushClient.pushSelfRiderNewTransferOrder(riderDeliveryOrder, newRider.getRiderAccountId());

			riderPushClient.pushFrontEndToGetRiderLocation(riderDeliveryOrder, newRider.getRiderAccountId());

			riderPushClient.pushSelfRiderTransferOutOrder(riderDeliveryOrder, oldRider.getRiderAccountId());
			if (!DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch() || DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)) {
				riderDeliveryOrder.syncOutRiderChange(newRider, oldRider, ChangeRiderEventEnum.TRANSFORM);
			}

		} catch (Exception e) {
			//发送push失败不阻塞主流程
			log.error("发送push失败", e);
		}

		oldRiderAccountId[0] = oldRider.getRiderAccountId();
		olderRiderAccountName[0] = oldRider.getRiderName();
		return Optional.empty();
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public Optional<Failure> complete(Long deliveryOrderId, StaffRider rider, Coordination coordination, Long tenantId, Long storeId) {
		RiderLocationDetail riderLocationDetail=riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
		Optional<RiderDeliveryOrder> deliveryOrderOptional = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		}else {
			deliveryOrderOptional = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(deliveryOrderId);
		}
		return deliveryOrderOptional
				.map(deliveryOrder -> {
					if (nonSelfDeliveryOrder(deliveryOrder)) {
						return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_ERROR));
					}
					RiderLocationDetail finalRiderLocationDetail = fillRiderLocationForComplete(riderLocationDetail, rider, deliveryOrder, coordination);
					return deliveryOrder.complete(rider, finalRiderLocationDetail, false);
				})
				.orElseGet(() -> Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST)));
	}

	@CatTransaction
	public Optional<Failure> completeWithProofPhoto(Long deliveryOrderId, StaffRider rider,
													List<String> proofPhotoUrls, Integer signType,
													Boolean isWeakNetWork,
													Coordination coordination,Long tenantId,Long storeId) {

		//1.扭转配送单状态
		Optional<RiderDeliveryOrder> deliveryOrderOpt = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		}else {
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(deliveryOrderId);
		}

		if (!deliveryOrderOpt.isPresent()) {
			return Optional.of(new Failure(false, DELIVERY_ORDER_NOT_EXIST));
		}

		RiderDeliveryOrder deliveryOrder = deliveryOrderOpt.get();
		if (nonSelfDeliveryOrder(deliveryOrder)) {
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_ERROR));
		}

		RiderLocationDetail riderLocationDetail;
		if (DeliveryRiderMccConfigUtils.isFillRiderLocationGrayStore(deliveryOrder.getStoreId())
				&& DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch()) {
			riderLocationDetail = getCompeteRiderLocation(rider, deliveryOrder, coordination);
		} else {
			riderLocationDetail = riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
			riderLocationDetail = fillRiderLocationForComplete(riderLocationDetail, rider, deliveryOrder, coordination);
		}


		Optional<Failure> failure = deliveryOrder.completeWithProofPhoto(rider, riderLocationDetail, isWeakNetWork, proofPhotoUrls, signType);

		if (failure.isPresent()) {
			return failure;
		}

		if (CollectionUtils.isEmpty(proofPhotoUrls)) {
			return Optional.empty();
		}

		//2.送达图片送审
		List<Pair<Long, String>> photoInfoList = deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList()
				.stream().map(photoInfo -> Pair.of(photoInfo.getPicId(), photoInfo.getPhotoUrl()))
				.collect(Collectors.toList());
		creditAuditClient.sendCreditAudit(photoInfoList, deliveryOrderId, "", rider.getRiderAccountId(),deliveryOrder.getTenantId(),deliveryOrder.getStoreId());


		//3.发送自动审核延迟消息
		creditAuditClient.sendCreditAuditAutomaticPassMessage(deliveryOrderId,deliveryOrder.getTenantId(),deliveryOrder.getStoreId());

		return Optional.empty();
	}

	/**
	 * 如果在redis中查不到骑手经纬度信息，那就将前端传过来的保存在Redis中
	 */
	public RiderLocationDetail fillRiderLocation(RiderLocationDetail riderLocationDetail, StaffRider rider, RiderDeliveryOrder riderDeliveryOrder, Coordination coordination) {
		try {
			//校验开关是否打开
			if (!DeliveryRiderMccConfigUtils.getFillRiderLocationSwitch()) {
				return riderLocationDetail;
			}

			//先过滤歪马
			if (Objects.isNull(riderDeliveryOrder.getTenantId())) {
				log.info("歪马租户不处理骑手位置");
				return riderLocationDetail;
			}

			//先看前端传的经纬度是否为空，不为空就用前端传的位置
			if (Objects.nonNull(coordination) && coordination.checkIsValid()) {
				RiderLocationDetail newRiderLocationDetail = new RiderLocationDetail(rider.getRiderAccountId(),
						rider.getRiderName(), rider.getRiderPhone(), coordination.getLongitude(), coordination.getLatitude(),
						null, null, null, null, LocalDateTime.now().format(formatter), null, null, null);
				riderLocationRepository.save(newRiderLocationDetail);
				return newRiderLocationDetail;
			}

			//如果前端传的位置为空，用门店的位置
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(riderDeliveryOrder.getCustomerOrderKey().getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			Optional<TenantChannelStoreInfo> tenantChannelStoreInfo =
					tenantSystemClient.queryChannelStoreDetailInfoWithAnyChannel(riderDeliveryOrder.getTenantId(), riderDeliveryOrder.getStoreId(),
							orderBizType.getChannelId());
			if (tenantChannelStoreInfo.isPresent() && StringUtils.isNotBlank(tenantChannelStoreInfo.get().getLongitude()) && StringUtils.isNotBlank(tenantChannelStoreInfo.get().getLatitude())) {
				TenantChannelStoreInfo storeInfo = tenantChannelStoreInfo.get();
				RiderLocationDetail newRiderLocationDetail = new RiderLocationDetail(rider.getRiderAccountId(),
						rider.getRiderName(), rider.getRiderPhone(), storeInfo.getLongitude(), storeInfo.getLatitude(),
						null, null, null, null, LocalDateTime.now().format(formatter), null, null, null);
				riderLocationRepository.save(newRiderLocationDetail);
				return newRiderLocationDetail;
			}

			//如果前端位置和门店位置都没有, 返回缓存里的位置
			return riderLocationDetail;
		} catch (Exception e) {
			log.error("获取骑手坐标失败 riderLocationDetail = {}, rider = {}, deliveryOrder:{}, coordination:{}",
					riderLocationDetail, rider, riderDeliveryOrder, coordination, e);
			return riderLocationDetail;
		}
	}

	/**
	 * 取位置的优先级：前端带过来的位置>缓存中的骑手位置>中台门店位置
	 */
	public RiderLocationDetail getAcceptOrTakeAwayRiderLocation(StaffRider rider, RiderDeliveryOrder riderDeliveryOrder, Coordination coordination) {
		try {
			//1.先看前端传的经纬度是否为空，不为空就用前端传的位置
			if (Objects.nonNull(coordination) && coordination.checkIsValid()) {
				return new RiderLocationDetail(rider.getRiderAccountId(),
						rider.getRiderName(), rider.getRiderPhone(), coordination.getLongitude(), coordination.getLatitude(),
						null, null, null, null, LocalDateTime.now().format(formatter), null, null, null);
			}

			//2.取缓存中的骑手位置
			RiderLocationDetail riderLocationDetail = riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
			if (Objects.nonNull(riderLocationDetail)) {
				return riderLocationDetail;
			}

			//3.取中台门店的位置
			Optional<WarehouseDTO> warehouseDTOOpt = oswClient.queryWarehouseById(riderDeliveryOrder.getTenantId(), riderDeliveryOrder.getStoreId());
			if (warehouseDTOOpt.isPresent() && Objects.nonNull(warehouseDTOOpt.get().getRegion())) {
				if(Objects.nonNull(warehouseDTOOpt.get().getRegion().getLongitude()) && Objects.nonNull(warehouseDTOOpt.get().getRegion().getLatitude())) {
					Double storeLongitude = warehouseDTOOpt.get().getRegion().getLongitude();
					Double storeLatitude  = warehouseDTOOpt.get().getRegion().getLatitude();

					return new RiderLocationDetail(rider.getRiderAccountId(),
							rider.getRiderName(), rider.getRiderPhone(), storeLongitude.toString(), storeLatitude.toString(),
							null, null, null, null, LocalDateTime.now().format(formatter), null, null, null);
				}
			}

			return null;
		} catch (Exception e) {
			log.error("获取骑手坐标失败", e);
			Cat.logEvent("GET_RIDER_LOCATION", "ERROR");
			return null;
		}
	}


	/**
	 * 取位置的优先级：前端带过来的位置>缓存中的骑手位置>收货地址
	 */
	public RiderLocationDetail getCompeteRiderLocation(StaffRider rider, RiderDeliveryOrder riderDeliveryOrder, Coordination coordination) {
		try {
			//1.先看前端传的经纬度是否为空，不为空就用前端传的位置
			if (Objects.nonNull(coordination) && coordination.checkIsValid()) {
				return new RiderLocationDetail(rider.getRiderAccountId(),
						rider.getRiderName(), rider.getRiderPhone(), coordination.getLongitude(), coordination.getLatitude(),
						null, null, null, null, LocalDateTime.now().format(formatter), null,null, null);
			}

			//2.取缓存中的骑手位置
			RiderLocationDetail riderLocationDetail = riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
			if (Objects.nonNull(riderLocationDetail)) {
				return riderLocationDetail;
			}

			//3.取收货地址
			if (Objects.nonNull(riderDeliveryOrder.getReceiver())
					&& Objects.nonNull(riderDeliveryOrder.getReceiver().getReceiverAddress())
					&& Objects.nonNull(riderDeliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint())) {
				String longitude = riderDeliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint().getLongitude();
				String latitude  = riderDeliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint().getLatitude();

				if (StringUtils.isNotBlank(longitude) && StringUtils.isNotBlank(latitude)) {
					return new RiderLocationDetail(rider.getRiderAccountId(),
							rider.getRiderName(), rider.getRiderPhone(), longitude, latitude,
							null, null, null, null, LocalDateTime.now().format(formatter), null, null, null);
				}

			}

			return null;
		} catch (Exception e) {
			log.error("获取骑手坐标失败", e);
			Cat.logEvent("GET_RIDER_LOCATION", "ERROR");
			return null;
		}
	}


	public RiderLocationDetail fillRiderLocationForComplete(RiderLocationDetail riderLocationDetail, StaffRider rider, RiderDeliveryOrder riderDeliveryOrder, Coordination coordination) {
		try {
			//校验开关是否打开
			if (!DeliveryRiderMccConfigUtils.getFillRiderLocationSwitch()) {
				return riderLocationDetail;
			}

			//先过滤歪马
			if (Objects.isNull(riderDeliveryOrder.getTenantId())) {
				log.info("歪马租户不处理骑手位置");
				return riderLocationDetail;
			}

			//先看前端传的经纬度是否为空，不为空就用前端传的位置
			if (Objects.nonNull(coordination) && coordination.checkIsValid()) {
				RiderLocationDetail newRiderLocationDetail = new RiderLocationDetail(rider.getRiderAccountId(),
						rider.getRiderName(), rider.getRiderPhone(), coordination.getLongitude(), coordination.getLatitude(),
						null, null, null, null, LocalDateTime.now().format(formatter), null, null, null);
				riderLocationRepository.save(newRiderLocationDetail);
				return newRiderLocationDetail;
			}

			//如果前端传的位置为空，用收件人的地址
			Address receiverAddress = riderDeliveryOrder.getReceiver().getReceiverAddress();
			if (Objects.nonNull(receiverAddress)) {
				CoordinatePoint coordinatePoint = receiverAddress.getCoordinatePoint();
				if (Objects.nonNull(coordination) && StringUtils.isNotBlank(coordinatePoint.getLatitude()) && StringUtils.isNotBlank(coordinatePoint.getLongitude())) {
					RiderLocationDetail newRiderLocationDetail = new RiderLocationDetail(rider.getRiderAccountId(),
							rider.getRiderName(), rider.getRiderPhone(), coordinatePoint.getLongitude(), coordinatePoint.getLatitude(),
							null, null, null, null, LocalDateTime.now().format(formatter), null, null, null);
					riderLocationRepository.save(newRiderLocationDetail);
					return newRiderLocationDetail;
				}
			}

			//如果前端位置和收件人位置都没有, 返回缓存里的位置
			return riderLocationDetail;
		} catch (Exception e) {
			log.error("获取骑手坐标失败 riderLocationDetail = {}, rider = {}, deliveryOrder:{}, coordination:{}",
					riderLocationDetail, rider, riderDeliveryOrder, coordination, e);
			return riderLocationDetail;
		}
	}


	/**
	 * 校验运单门店是否是自营配送门店
	 */
	private boolean nonSelfDeliveryPoi(RiderDeliveryOrder riderDeliveryOrder) {
		Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(riderDeliveryOrder.getTenantId(), riderDeliveryOrder.getStoreId());
		return !opDeliveryPoi.isPresent() || DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY != opDeliveryPoi.get().getDeliveryPlatform();
	}

	private boolean nonSelfDeliveryOrder(RiderDeliveryOrder riderDeliveryOrder) {
		if(riderDeliveryOrder==null){
			return true;
		}
		if(riderDeliveryOrder.getDeliveryChannelEnum() != DeliveryChannelEnum.MERCHANT_DELIVERY){
			return true;
		}
		return false;
	}

	@CatTransaction
	@MethodLog(logResponse = true)
	public Optional<Failure> riderLocation(Long tenantId, Long storeId, RiderLocationDetail riderLocationDetail) {
		if(riderLocationDetail==null){
			return Optional.of(new Failure(false,FailureCodeEnum.INVALID_PARAM));
		}

		List<RiderDeliveryOrder> needSyncDeliveryOrders = Lists.newArrayList();
		if (DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId) || (!DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch())) {
			needSyncDeliveryOrders = riderDeliveryOrderRepository.batchQueryDeliveryOrder(storeId, Lists.newArrayList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS), riderLocationDetail.getRiderAccountId(), tenantId);
			reportMerchantSelfDeliveryRiderPostLocationLog(tenantId, storeId, riderLocationDetail, needSyncDeliveryOrders);
		}
		riderLocationRepository.save(riderLocationDetail);
		riderLocatingExceptionRepository.deleteRiderLocatingException(riderLocationDetail.getRiderAccountId());

		if (DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)) {
			try {
				if (CollectionUtils.isEmpty(needSyncDeliveryOrders)) {
					needSyncDeliveryOrders = riderDeliveryOrderRepository.batchQueryDeliveryOrder(storeId, Lists.newArrayList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS), riderLocationDetail.getRiderAccountId(), tenantId);
				}
				if (CollectionUtils.isNotEmpty(needSyncDeliveryOrders)) {
					if (LionConfigUtils.isLocationUpstreamGrayStore(storeId)) {
						for (RiderDeliveryOrder needSyncDeliveryOrder : needSyncDeliveryOrders) {
							deliveryChangeNotifyService.notifySyncDrunkHorseRiderPositionImmediately(needSyncDeliveryOrder.getId(), needSyncDeliveryOrder.getDeliveryChannelEnum().getDeliveryPlatform(), tenantId, storeId, true);
						}
					}
				}

			} catch (Exception e) {
				log.error("骑手上报位置灰度逻辑异常", e);
			}
		}

		return Optional.of(new Failure(false,FailureCodeEnum.SUCCESS));
	}




	public void reportMerchantSelfDeliveryRiderPostLocationLog(Long tenantId, Long storeId, RiderLocationDetail riderLocationDetail, List<RiderDeliveryOrder> needSyncDeliveryOrders) {
		try {
			RiderLocationDataDO latestStaffRiderLocation =
					riderLocationRepository.getLatestStaffRiderLocation(riderLocationDetail.getRiderAccountId());

			boolean isRepeatPoint = latestStaffRiderLocation != null
					&& Objects.equals(latestStaffRiderLocation.getLatitude(), riderLocationDetail.getLatitude())
					&& Objects.equals(latestStaffRiderLocation.getLongitude(), riderLocationDetail.getLongitude());

			Long locateTime = com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil.toMilliSeconds(LocalDateTime.parse(riderLocationDetail.getTime(), formatter));
			boolean isNotFreshPoint = System.currentTimeMillis() - locateTime > DeliveryRiderMccConfigUtils.getNotFreshThreshold();


			boolean isRealTimePosition = false;
			Long distanceToLastPosition = 0L;
			Long lastPostPositionTime = null;


			List<DeliveryStatusEnum> inDeliveryStatusList = Lists.newArrayList(
					DeliveryStatusEnum.RIDER_TAKEN_GOODS,
					DeliveryStatusEnum.DELIVERY_DONE,
					DeliveryStatusEnum.DELIVERY_CANCELLED);
			// 获取到位置 并且位置是30s以内的 认为取到了实时位置

			if (Objects.nonNull(latestStaffRiderLocation) ) {
				LocalDateTime dateTime = LocalDateTime.parse(latestStaffRiderLocation.getTime(), formatter);
				lastPostPositionTime = dateTime.atZone(ZoneOffset.of("+8")).toInstant().toEpochMilli();
				if (locateTime - lastPostPositionTime < DeliveryRiderMccConfigUtils.getRealTimePositionDuration() * 1000) {
					isRealTimePosition = true;
				}

				//计算跟上一次同步的位置之间的距离
				distanceToLastPosition = CoordinateUtil.calLineDistance(new CoordinatePoint(latestStaffRiderLocation.getLongitude(), latestStaffRiderLocation.getLatitude()),
						new CoordinatePoint(riderLocationDetail.getLongitude(), riderLocationDetail.getLatitude()));
			}


			for (RiderDeliveryOrder order : needSyncDeliveryOrders) {
				String logMsg = XMDLogFormat.build()
						.putTag("rider_account_id", riderLocationDetail.getRiderAccountId() + "")
						.putTag("rider_name", riderLocationDetail.getRiderName())
						.putTag("latitude", riderLocationDetail.getLatitude())
						.putTag("longitude", riderLocationDetail.getLongitude())
						.putTag("os", riderLocationDetail.getOs())
						.putTag("uuid", riderLocationDetail.getUuid())
						.putTag("post_time", LocalDateTime.now().format(formatter))
						.putTag("locate_time", riderLocationDetail.getTime())
						.putTag("accuracy", riderLocationDetail.getAccuracy())
						.putTag("bearing", riderLocationDetail.getBearing())
						.putTag("speed", riderLocationDetail.getSpeed())
						.putTag("provider", riderLocationDetail.getProvider())
						.putTag("is_repeat_point", isRepeatPoint + "")
						.putTag("is_not_fresh_point", isNotFreshPoint + "")
						.putTag("view_order_id", order.getCustomerOrderKey().getChannelOrderId())
						.putTag("is_real_time_position", isRealTimePosition + "")
						.putTag("distance_to_last_position", distanceToLastPosition.toString())
						.putTag("delivery_status", order.getStatus().getCode() + "")
						.putTag("order_biz_type", order.getCustomerOrderKey().getOrderBizType() + "")
						.putTag("store_id", storeId + "")
						.putTag("last_post_position_time", lastPostPositionTime + "")
						.putTag("app_version", riderLocationDetail.getAppVersion())
						.putTag("tenant_id",order.getTenantId()+"")
						//fixme: 这里之前上报就是有问题的，暂时不改
						.putTag("is_in_delivery_point", inDeliveryStatusList.contains(order.getStatus()) + "")
						.toString();
				riderPostLocationLog.info(logMsg);
			}
		} catch (Exception e) {
			log.error("post log error", e);
		}
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> riderArrivalLocation(RiderArrivalLocationTRequest request) {
		try {

			if (riderArrivalLocationRepository.getByDeliveryOrderId(request.getDeliveryOrderId()) != null) {
				return Optional.empty();
			}

			RiderArrivalLocation riderArrivalLocation = new RiderArrivalLocation(request.getDeliveryOrderId(),request.getRiderAccountId());

			//填充到刻位置
			riderArrivalLocation.fillRiderCoordinatePoint(request);
			Optional<RiderDeliveryOrder> optionalRiderDeliveryOrder = Optional.empty();
			if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(request.getTenantId())){
				optionalRiderDeliveryOrder = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(request.getDeliveryOrderId(),request.getTenantId(),request.getStoreId());
			}else {
				optionalRiderDeliveryOrder = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(request.getDeliveryOrderId());
			}



			//查询收货人经纬度
			CoordinatePoint receiverCoordinatePoint = optionalRiderDeliveryOrder
					.map(riderDeliveryOrder -> riderDeliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint())
					.orElse(null);

			//计算直线距离
			riderArrivalLocation.calLineDistanceToReceiver(receiverCoordinatePoint);

			//计算导航距离
			if (DeliveryRiderMccConfigUtils.getCalNavigationDistanceSwitch()) {
				riderArrivalLocation.calNavigationDistanceToReceiver(receiverCoordinatePoint);
			}

			//持久化
			riderArrivalLocationRepository.save(riderArrivalLocation);

		} catch (Exception e) {
			//不阻塞流程
			log.warn("save rider arrival location info error, req: {}", request, e);
		}

		return Optional.empty();
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> riderLocatingException(RiderLocatingExceptionDetail exceptionDetail) {
		if (exceptionDetail == null) {
			return Optional.of(new Failure(false, FailureCodeEnum.INVALID_PARAM));
		}
		riderLocatingExceptionRepository.saveRiderLocatingException(exceptionDetail);

		return Optional.empty();
	}

	@CatTransaction
	//request可能比较大 application层不打印日志
	public void batchPostRiderLocatingLog(BatchPostRiderLocatingLogTRequest request) {
		if (!DeliveryRiderMccConfigUtils.getBatchPostRiderLocatingLogSwitch() || CollectionUtils.isEmpty(request.getLocatingLogList())) {
			return;
		}

		for (RiderLocatingLogTRequest log : request.getLocatingLogList()) {
			if (log != null) {
				riderLocatingLog.info(XMDLogFormat.build()
						.putTag("rider_account_id", log.riderAccountId + "")
						.putTag("log_type", log.getLogType() + "")
						.putTag("exception_type", log.getExceptionType() + "")
						.putTag("latitude", log.getLatitude())
						.putTag("longitude", log.getLongitude())
						.putTag("manufacturer", log.getManufacturer())
						.putTag("phoneOS", log.getPhoneOS())
						.putTag("location_is_used", log.getLocationIsUsed() + "")
						.putTag("uuid", log.getUuid())
						.putTag("utime", TimeUtil.format((int) (log.getUtime() / 1000)))
						.putTag("accuracy",log.getAccuracy())
						.putTag("bearing",log.getBearing())
						.putTag("speed",log.getSpeed())
						.toString());
			}
		}
	}

	@MethodLog(logRequest = false,logResponse = true)
	@CatTransaction
	public Optional<Failure> reportDeliveryException(RiderDeliveryException riderDeliveryException) {
		//校验异常类型
		RiderDeliveryExceptionEnum exceptionEnum = RiderDeliveryExceptionEnum.enumOf(riderDeliveryException.getExceptionType(), riderDeliveryException.getExceptionSubType());
		if (exceptionEnum == null) {
			log.error("异常类型错误, riderDeliveryException:{}", riderDeliveryException);
			return Optional.of(new Failure(false, EXCEPTION_TYPE_ERROR));
		}

		//查运单是否存在
		Optional<RiderDeliveryOrder> deliveryOrderOpt = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(riderDeliveryException.getTenantId())){
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(riderDeliveryException.getDeliveryOrderId(),riderDeliveryException.getTenantId(),riderDeliveryException.getStoreId());
		}else {
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(riderDeliveryException.getDeliveryOrderId());
		}

		if (!deliveryOrderOpt.isPresent()) {
			log.error("上报异常失败，运单不存在, request:{}",riderDeliveryException);
			return Optional.of(new Failure(false, DELIVERY_ORDER_NOT_EXIST));
		}
		RiderDeliveryOrder deliveryOrder = deliveryOrderOpt.get();

		//校验运单状态
		if (deliveryOrder.getStatus() != DeliveryStatusEnum.RIDER_TAKEN_GOODS && deliveryOrder.getStatus() != DeliveryStatusEnum.RIDER_ASSIGNED) {
			log.error("上报异常失败，运单状态不是待取货和配送中, request:{}", riderDeliveryException);
			return Optional.of(new Failure(false, DELIVERY_ORDER_STATUS_ERROR));
		}

		//校验运单锁定状态
		if (deliveryOrder.checkOrderStatusLocked()) {
			log.error("上报异常失败，运单锁定时不能上报异常, request:{}", riderDeliveryException);
			return Optional.of(new Failure(false, DELIVERY_ORDER_STATUS_ERROR));
		}

		//查上报次数
		List<RiderDeliveryException> riderDeliveryExceptions = riderDeliveryExceptionRepository.queryDeliveryExceptionByChannelOrderId(riderDeliveryException.getTenantId(), riderDeliveryException.getStoreId(),
				String.valueOf(riderDeliveryException.getChannelOrderId()), riderDeliveryException.getOrderBizType());

		Integer reportMaxTimes = DeliveryRiderMccConfigUtils.getExceptionReportMaxTimes();
		if (CollectionUtils.isNotEmpty(riderDeliveryExceptions) && riderDeliveryExceptions.size() >= reportMaxTimes) {
			log.error("上报异常失败，上报次数超过{}次, request:{}", reportMaxTimes, riderDeliveryException);
			return Optional.of(new Failure(false, EXCEPTION_REPORT_TIMES_ERROR.getCode(), "上报异常失败，上报次数超过" + reportMaxTimes + "次"));
		}


		riderDeliveryExceptionRepository.save(riderDeliveryException);

		//发MQ通知C端
		syncDeliveryExceptionOut(riderDeliveryException, deliveryOrder);

		return Optional.empty();
	}

	@MethodLog(logRequest = false,logResponse = true)
	@CatTransaction
	public Optional<Failure> processCreditAuditResult(CreditAuditResult creditAuditResult) throws TException {
		//埋点
		if (CollectionUtils.isNotEmpty(creditAuditResult.getViolationPicIdList())) {
			log.info("送达图片审核不通过, creditAuditResult: {}", creditAuditResult);
			Cat.logEvent("DELIVERY_PROOF_PHOTO", "CREDIT_AUDIT_NOT_PASS");
		}

		if (creditAuditResult.getIsAutoPass() != null && creditAuditResult.getIsAutoPass()) {
			log.info("送达图片自动通过审核, deliveryId:{}", creditAuditResult.getDeliveryOrderId());
			Cat.logEvent("DELIVERY_PROOF_PHOTO", "AUTO_AUDIT_PASS");
		}

		//查运单是否存在
		Optional<RiderDeliveryOrder> deliveryOrderOpt = Optional.empty();
		if(DeliveryRiderMccConfigUtils.getDeliveryQueryTenantSwitch(creditAuditResult.getTenantId())){
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(creditAuditResult.getDeliveryOrderId(),creditAuditResult.getTenantId(),creditAuditResult.getStoreId());
		}else {
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMaster(creditAuditResult.getDeliveryOrderId());
		}

		if (!deliveryOrderOpt.isPresent()) {
			log.error("处理送达图片审核失败,运单不存在, creditAuditResult:{}", creditAuditResult);
			return Optional.of(new Failure(false, DELIVERY_ORDER_NOT_EXIST));
		}

		RiderDeliveryOrder deliveryOrder = deliveryOrderOpt.get();

		//幂等
		if(Objects.equals(deliveryOrder.getRiderDeliveryExtInfo().getIsAlreadyAudit(), true)) {
			log.warn("送达图片已审核, 无需处理");
			return Optional.empty();
		}

		//1.更新图片审核结果
		Optional<Failure> failure = deliveryOrder.updateDeliveryProofPhotoAuditStatus(creditAuditResult.getViolationPicIdList(), creditAuditResult.getDatetime());

		if (failure.isPresent()) {
			return failure;
		}

		//2.同步图片到C端
		syncDeliveryProofPhotoToCustomer(deliveryOrder);

		//3.同步送达拍照消息
		syncOutProof(deliveryOrder);

		//4.持久化
		riderDeliveryOrderRepository.save(deliveryOrder);

		return Optional.empty();
	}

	public Optional<Failure> markPickDeliverySplit(Long deliveryOrderId, Long empowerOrderId, Long tenantId, Long storeId) {
		Optional<RiderDeliveryOrder> deliveryOrderOpt = Optional.empty();
		if(Objects.nonNull(deliveryOrderId)){
			deliveryOrderOpt = riderDeliveryOrderRepository.getDeliveryOrderForceMasterWithTenantId(deliveryOrderId,tenantId,storeId);
		} else if (Objects.nonNull(empowerOrderId)){
			deliveryOrderOpt = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithTenant(empowerOrderId,tenantId,storeId);
		}
		if (!deliveryOrderOpt.isPresent()) {
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_ORDER_NOT_EXIST));
		}

		//校验运单状态
		RiderDeliveryOrder riderDeliveryOrder = deliveryOrderOpt.get();
		if (riderDeliveryOrder.checkIsFinalStatus()) {
			return Optional.of(new Failure(false, FailureCodeEnum.DELIVERY_STATUS_ERR, riderDeliveryOrder.getStatus().getDesc()));
		}

		RiderDeliveryExtInfo riderDeliveryExtInfo = riderDeliveryOrder.getRiderDeliveryExtInfo();
		if (Objects.isNull(riderDeliveryExtInfo)) {
			riderDeliveryExtInfo = new RiderDeliveryExtInfo();
		}

		riderDeliveryExtInfo.setPickDeliverySplitTag(true);

		//4.持久化
		riderDeliveryOrderRepository.save(riderDeliveryOrder);

		//发MQ
		riderDeliveryOrderSyncOutClient.asyncOut(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.PICK_DELIVERY_SPLIT.getValue(),
				new DeliveryChangeSyncOutMessage.Head(riderDeliveryOrder.getTenantId(), riderDeliveryOrder.getStoreId(), riderDeliveryOrder.getId(), riderDeliveryOrder.getCustomerOrderKey().getOrderBizType(),
						riderDeliveryOrder.getCustomerOrderKey().getOrderId(), riderDeliveryOrder.getCustomerOrderKey().getChannelOrderId(), riderDeliveryOrder.getStatus().getCode()),
				new DeliveryChangeSyncOutMessage.PickDeliverySplitBody()));

		return Optional.empty();
	}

	private void syncDeliveryProofPhotoToCustomer(RiderDeliveryOrder deliveryOrder) throws TException {
		List<String> passAuditPhotoUrls = deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList()
				.stream()
				.filter(photoInfo -> photoInfo.getAuditStatusEnum() != null && Objects.equals(photoInfo.getAuditStatusEnum(), AuditStatusEnum.PASS_AUDIT))
				.map(RiderDeliveryExtInfo.DeliveryProofPhotoInfo::getPhotoUrl)
				.collect(Collectors.toList());

		//获取骑手位置
		RiderLocationDetail riderLocation = riderLocationRepository.getStaffRiderLocation(deliveryOrder.getRiderInfo().getRiderAccountId());
		CoordinatePoint riderCoordinate = null;
		if (Objects.nonNull(riderLocation)) {
			riderCoordinate = new CoordinatePoint(riderLocation.getLongitude(), riderLocation.getLatitude());
		}

		if (Objects.equals(deliveryOrder.getCustomerOrderKey().getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue()) && CollectionUtils.isNotEmpty(passAuditPhotoUrls)) {
			//同步照片到C端
			riderDeliveryOrderSyncOutClient.updateDeliveryProofPhoto(deliveryOrder, riderCoordinate);
		}

		if (Objects.equals(deliveryOrder.getCustomerOrderKey().getOrderBizType(), DynamicOrderBizType.MEITUAN_WAIMAI.getValue())
				&& CollectionUtils.isNotEmpty(passAuditPhotoUrls)
				&& DeliveryRiderMccConfigUtils.isNewDeliveryCompleteGrayStore(deliveryOrder.getStoreId())) {
			//同步照片到C端, 美团渠道送达照片同步到C端后, TSP自动给用户发短信
			riderDeliveryOrderSyncOutClient.updateDeliveryProofPhoto(deliveryOrder, riderCoordinate);
		}
	}

	private void syncOutProof(RiderDeliveryOrder deliveryOrder) {
		//通知cbiz发短信和微信消息给用户
		if (GrayConfigUtils.judgeIsGrayStore(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
			DeliveryChangeSyncOutMessage.RiderPostDeliveryProofPhotoBody riderPostDeliveryProofPhotoBody = new DeliveryChangeSyncOutMessage.RiderPostDeliveryProofPhotoBody();
			if (Objects.nonNull(deliveryOrder.getRiderDeliveryExtInfo()) && CollectionUtils.isNotEmpty(deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList())) {
				riderPostDeliveryProofPhotoBody.setDeliveryProofPhotoUrls(deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList().stream().map(RiderDeliveryExtInfo.DeliveryProofPhotoInfo::getPhotoUrl).collect(Collectors.toList()));
				List<String> shortUrls = Lists.newArrayList();
				for (RiderDeliveryExtInfo.DeliveryProofPhotoInfo deliveryProofPhotoInfo : deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList()) {
					try {
						shortUrls.add(shortLinkServiceClient.createShortLink(deliveryProofPhotoInfo.getPhotoUrl()));
					} catch (Exception e) {
						log.error("invoke shortLinkServiceClient.createShortLink error", e);
					}
				}
				riderPostDeliveryProofPhotoBody.setDeliveryProofPhotoShortUrls(shortUrls);
			}
			riderDeliveryOrderSyncOutClient.asyncOut(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.RIDER_POST_DELIVERY_PROOF_PHOTO.getValue(),
					new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getCustomerOrderKey().getOrderBizType(),
							deliveryOrder.getCustomerOrderKey().getOrderId(), deliveryOrder.getCustomerOrderKey().getChannelOrderId(), deliveryOrder.getStatus().getCode()),
					riderPostDeliveryProofPhotoBody));
		} else {
			//微商城渠道需要通知cbiz发短信和微信消息给用户
			List<String> passAuditPhotoUrls = deliveryOrder.getRiderDeliveryExtInfo().getDeliveryProofPhotoInfoList()
					.stream()
					.filter(photoInfo -> photoInfo.getAuditStatusEnum() != null && Objects.equals(photoInfo.getAuditStatusEnum(), AuditStatusEnum.PASS_AUDIT))
					.map(RiderDeliveryExtInfo.DeliveryProofPhotoInfo::getPhotoUrl)
					.collect(Collectors.toList());
			if (Objects.equals(deliveryOrder.getCustomerOrderKey().getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue()) && CollectionUtils.isNotEmpty(passAuditPhotoUrls)) {
				riderDeliveryOrderSyncOutClient.asyncOut(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.RIDER_POST_DELIVERY_PROOF_PHOTO.getValue(),
						new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getCustomerOrderKey().getOrderBizType(),
								deliveryOrder.getCustomerOrderKey().getOrderId(), deliveryOrder.getCustomerOrderKey().getChannelOrderId(), deliveryOrder.getStatus().getCode()),
						new DeliveryChangeSyncOutMessage.RiderPostDeliveryProofPhotoBody()));
			}
		}

	}

	private void syncDeliveryExceptionOut(RiderDeliveryException riderDeliveryException, RiderDeliveryOrder riderDeliveryOrder) {

		RiderDeliveryExceptionEnum exceptionEnum = RiderDeliveryExceptionEnum.enumOf(riderDeliveryException.getExceptionType(), riderDeliveryException.getExceptionSubType());
		if (exceptionEnum == null) {
			log.error("异常类型错误, riderDeliveryException:{}", riderDeliveryException);
			return;
		}
		String userRealAddress = "";
		if (riderDeliveryException.getExceptionDescription() != null
				&& StringUtils.isNotBlank(riderDeliveryException.getExceptionDescription().getUserRealAddress())) {
			userRealAddress = riderDeliveryException.getExceptionDescription().getUserRealAddress();
		}

		DeliveryChangeSyncOutMessage message = new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.REPORT_EXCEPTION.getValue(),
				new DeliveryChangeSyncOutMessage.Head(riderDeliveryException.getTenantId(),
						riderDeliveryException.getStoreId(),riderDeliveryOrder.getId(),riderDeliveryException.getOrderBizType(), riderDeliveryOrder.getCustomerOrderKey().getOrderId(),
						riderDeliveryException.getChannelOrderId().toString(), riderDeliveryOrder.getStatus().getCode()),
				new DeliveryChangeSyncOutMessage.RiderReportExceptionBody(exceptionEnum.getExceptionType(), exceptionEnum.getExceptionSubType(),
						exceptionEnum.getExceptionDesc(), exceptionEnum.getExceptionSubDesc(), userRealAddress ,riderDeliveryException.getRiderAccountName()
				));

		SpringContextUtils.getBean(RiderDeliveryOrderSyncOutClient.class).asyncOut(message);

	}


}
