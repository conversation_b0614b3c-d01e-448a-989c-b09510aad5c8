<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>reco_fulfillment_tms</artifactId>
        <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tms-delivery-rider</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>mcc-xframe-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>tms-delivery-poi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>dms-delivery-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>tms-delivery-task</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>store-saas-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.reco.store</groupId>
                    <artifactId>store-saas-infrastructure-shield-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.freemarker</groupId>
                    <artifactId>freemarker</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wmarch.map</groupId>
            <artifactId>maf-client</artifactId>
        </dependency>
        <!-- Excel -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <artifactId>poi-ooxml-schemas</artifactId>
            <groupId>org.apache.poi</groupId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
            <artifactId>shango_dh_labor-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-message-pusher</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mtthrift</artifactId>
                    <groupId>com.meituan.service.mobile</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-common</artifactId>
        </dependency>


        <!-- task -->
<!--        <dependency>-->
<!--            <groupId>com.sankuai.meituan.shangou.empower.sgshopmgmt</groupId>-->
<!--            <artifactId>reco_store_saas_task-framework</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mtthrift</artifactId>
                    <groupId>com.meituan.service.mobile</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
            <artifactId>bizmanagement-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-sdk</artifactId>
            <version>1.0.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>shangou-exception-collector</artifactId>
                    <groupId>com.sankuai.shangou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
            <artifactId>reco_qnh_poi_base-client</artifactId>
        </dependency>


        <dependency>
            <groupId>com.sankuai.shangou.infra</groupId>
            <artifactId>osw-api</artifactId>
        </dependency>
    </dependencies>
</project>
