package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.crane;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderQueryApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PageRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/07/11 14:57
 */
@Slf4j
public class RiderDeliveryOrderStatusCompensateCraneServiceTest extends DbTestBase {

    final static List<RiderDeliveryStatusEnum> nonFinalState = Lists.list(RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
            RiderDeliveryStatusEnum.RIDER_ASSIGNED,
            RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS);
    @Resource
    RiderDeliveryOrderRepository riderDeliveryOrderRepository;
    @Mock
    OrderSystemClient orderSystemClient;
    @Resource
    @InjectMocks
    RiderDeliveryOrderStatusCompensateCraneService riderDeliveryOrderStatusCompensateCraneService;
    LocalDateTime processTime;
    @Resource
    RiderQueryApplicationService riderQueryApplicationService;

    @Before
    public void mockData() {
//        Order:
//        1. Close
//        2. Canceled
//        3. doing
        mockOthers();
        mockComplete();
        mockCanceled();

        String str = "2022-07-17 15:30";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        this.processTime = LocalDateTime.parse(str, formatter);
    }

    @Test
    @Transactional
    @DatabaseSetup(value = "./rider_delivery_order_setup.xml")
    @ExpectedDatabase(value = "./rider_delivery_order_setup_expect.xml", table = "delivery_order",
            assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void doCraneTest() {
        long tenantId = 1000011L;
        riderDeliveryOrderStatusCompensateCraneService.doCrane(tenantId, processTime);
    }

    @Test
    @Transactional
    @DatabaseSetup(value = "./rider_delivery_order_setup.xml")
    @ExpectedDatabase(value = "./rider_delivery_order_setup_expect.xml", table = "delivery_order",
            assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void handleOrderTest() {
        riderDeliveryOrderStatusCompensateCraneService.handleOrder(1000011L, 2L,1L);
        riderDeliveryOrderStatusCompensateCraneService.handleOrder(1000011L, 3L,1L);
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void putWrongTenantId() {
        Assert.assertFalse(riderDeliveryOrderStatusCompensateCraneService.handleOrder(11L, 1L,1L));
    }


    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void pageQueryDeliveryByTimeIntervalAndStatusTest() {
        LocalDateTime end = processTime.minusHours(6);
        LocalDateTime start = processTime.minusHours(19);

        PageRequest pageRequest = new PageRequest(1, 10);
        Assert.assertEquals(3, riderDeliveryOrderRepository
                .queryRiderDeliveryOrderByPage(pageRequest, 1000011L, nonFinalState, start, end, null, null)
                .size());
    }

    @Test
    @Transactional
    @DatabaseSetup("./rider_delivery_order_setup.xml")
    public void getCurrentDeliveryOrderForceMasterTest() {
        Optional<RiderDeliveryOrder> riderDeliveryOrderOptional = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMaster(2L);
        Assert.assertTrue(riderDeliveryOrderOptional.isPresent());
        Assert.assertEquals(RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode(), riderDeliveryOrderOptional.get().getStatus().getCode());
    }

    private void mockOthers() {
        Mockito.when(orderSystemClient.getOrderInfo(1L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(1L, 1L, 1L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.MERCHANT_CONFIRMED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss",
                                1, "ss", false, "sdfa", "2342", 123, null, null, null, 45L, null, null,null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
        Mockito.when(orderSystemClient.getOrderInfo(4L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(4L, 2L, 4L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.PICKING.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1, "ss",
                                false, "sdfa", "2342", 123, null, null, null, 45L, null, null, null,  null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
        Mockito.when(orderSystemClient.getOrderInfo(7L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(7L, 3L, 7L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.APPEAL_APPLIED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1,
                                "ss", false, "sdfa", "2342", 123, null, null, null, 45L, null, null, null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
    }

    private void mockComplete() {
        Mockito.when(orderSystemClient.getOrderInfo(2L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(2L, 1L, 2L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.COMPLETED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1, "ss",
                                false, "123", "12346789765", 123, null, null, null, 45L, null, null, null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
        Mockito.when(orderSystemClient.getOrderInfo(5L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(5L, 2L, 5L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.COMPLETED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1, "ss",
                                false, "sdfa", "2342", 123, null, null, null, 45L, null, null,null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
        Mockito.when(orderSystemClient.getOrderInfo(8L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(8L, 3L, 8L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.COMPLETED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1, "ss",
                                false, "sdfa", "2342", 123, null, null, null, 45L, null, null, null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
    }

    private void mockCanceled() {
        Mockito.when(orderSystemClient.getOrderInfo(3L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(3L, 1L, 3L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.CANCELED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1, "ss",
                                false, "sdfa", "2342", 123, null, null, null, 45L, null, null, null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
        Mockito.when(orderSystemClient.getOrderInfo(6L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(6L, 2L, 6L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.CANCELED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1, "ss",
                                false, "sdfa", "2342", 123, null, null, null, 45L, null, null, null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
        Mockito.when(orderSystemClient.getOrderInfo(9L, false))
                .thenReturn(new Result<>(
                        new OrderInfo(new OrderKey(9L, 3L, 9L), "1", 1L, "1-01",1, 1,
                                OrderStatusEnum.CANCELED.getValue(), true, 1, null, null, null, false, 1, 1, null, "ss", "ss", 1, "ss",
                                false, "sdfa", "2342", 123, null, null, null, 45L, null, null, null, null, null, null,Lists.newArrayList(),null,
                                null,null,null,null,null,null,null,null,null,null,null,null,null,null)
                ));
    }
}