package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.StoreConfigSaveRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TOuterDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryPoiRelationClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import org.junit.Before;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ActiveProfiles("dev")
public class DeliveryConfigurationThriftServiceImplTest extends DbTestBase {

    @Resource
    private DeliveryConfigurationThriftServiceImpl deliveryConfigurationThriftService;

    @MockBean
    private TenantSystemClient tenantSystemClient;
    @MockBean
    private DeliveryPoiRelationClient deliveryPoiRelationClient;

    @Before
    public void setUp() throws Exception {
        when(tenantSystemClient.queryStoreDetailInfo(any(), any())).thenReturn(new TenantStoreInfo(510100, "13812345678", "storeName", 123, "mock address", null, null));
        doNothing().when(deliveryPoiRelationClient).syncStoreChannelInfo(any());
    }

    @Test
    @Transactional
    @DatabaseSetup("./DeliveryConfigurationThriftServiceImpl_saveStoreConfiguration_setup.xml")
    @ExpectedDatabase(value = "./DeliveryConfigurationThriftServiceImpl_saveStoreConfiguration_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void should_saveStoreConfiguration_success() {
        //1001: 有门店记录(聚合运力)，开通渠道，门店记录应该无变化
        deliveryConfigurationThriftService.saveStoreConfiguration(new StoreConfigSaveRequest(
            1000L, 1001L, 1,
                null,
                new TOuterDeliveryConfig(10002, null, 10, "73753", "11047059", 1001L, null, null),
                null,null,null
        ));

        //1002: 无门店记录，开通渠道，新增一条对应门店记录(聚合运力)
        deliveryConfigurationThriftService.saveStoreConfiguration(new StoreConfigSaveRequest(
                1000L, 1002L, 1,
                null,
                new TOuterDeliveryConfig(10002, null, 10, "73753", "11047059", 1002L, null, null),
                null,null,null
        ));


        //1003: 有门店记录(聚合运力)，修改门店配送发起配置，修改记录中对应信息
        deliveryConfigurationThriftService.saveStoreConfiguration(new StoreConfigSaveRequest(
                1000L, 1003L, 2,
                new TStoreConfig(1000L, 1003L, null, 2, 10, 2, null, 2, null, 2, 20, null, null),
                null,
                null,null,null
        ));

        //1004: 无门店记录(聚合运力)，修改门店配送发起配送，新增一条对应门店记录
        deliveryConfigurationThriftService.saveStoreConfiguration(new StoreConfigSaveRequest(
                1000L, 1004L, 2,
                new TStoreConfig(1000L, 1004L, null, 2, 10, 2, null, 2, null, 2, 20, null, null),
                null,
                null,null,null
        ));


        //1005: 有门店记录(聚合运力)，开启麦芽田配送，修改记录中对应信息(麦芽田)
        deliveryConfigurationThriftService.saveStoreConfiguration(new StoreConfigSaveRequest(
                1000L, 1005L, 3,
                new TStoreConfig(null, null, null, 2, 10, 2, null, null, null, null, null, null, null),
                null,
                new TAggDeliveryPlatformConfig(2, 1, null, null, null, null, 1),null,null
        ));

        //1006: 有门店记录(麦芽田)，关闭麦芽田配送，修改记录中对应记录(自建平台)
        deliveryConfigurationThriftService.saveStoreConfiguration(new StoreConfigSaveRequest(
                1000L, 1006L, 3,
                new TStoreConfig(null, null, null, 2, 10, 2, null, null, null, null, null, null, null),
                null,
                new TAggDeliveryPlatformConfig(2, 0, null, null, null, null, 1),null,null
        ));

        //1007: 无门店记录，开启麦芽田配送，新增一条对应门店记录(麦芽田)
        deliveryConfigurationThriftService.saveStoreConfiguration(new StoreConfigSaveRequest(
                1000L, 1007L, 3,
                new TStoreConfig(null, null, null, 2, 10, 2, null, null, null, null, null, null, null),
                null,
                new TAggDeliveryPlatformConfig(2, 1, null, null, null, null, 1),null,null
        ));

    }
}
