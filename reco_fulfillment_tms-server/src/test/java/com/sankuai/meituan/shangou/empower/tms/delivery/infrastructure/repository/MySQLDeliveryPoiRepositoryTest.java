package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.tms.DbTestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr.AggrDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.maltfarm.MaltFarmDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.merchant.MerchantSelfDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelTenantConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.OrderPlatformDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SequentialPollingStrategyConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import org.junit.Before;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/29
 */
@ActiveProfiles("dev")
public class MySQLDeliveryPoiRepositoryTest extends DbTestBase {

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	@MockBean
	private TenantSystemClient tenantSystemClient;

	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	@Before
	public void setUp() throws Exception {
		OBJECT_MAPPER.addMixIn(DeliveryPoi.class, PropertyFilterMixIn.class);
		OBJECT_MAPPER.addMixIn(ChannelTenantConfig.class, PropertyFilterMixIn.class);
		OBJECT_MAPPER.addMixIn(ChannelStoreConfig.class, PropertyFilterMixIn.class);

		when(tenantSystemClient.queryStoreDetailInfo(any(), any())).thenReturn(new TenantStoreInfo(510100, "13812345678", "storeName", 123, "mock address", 1, 1));
	}

	/**
	 * 1. 999: 没有对应记录，查询失败
	 * 2. 1000: 有完整的自建运力的记录，查询成功
	 * 3. 1001: 有聚合运力的记录，查询成功
	 * 4. 1002: 有麦芽田的记录，查询成功
	 * 5. 1003: 有商家自配送的记录，查询成功
	 * 6. 1010: 有自建运力记录，有可用的租户和门店配置，门店设置里面没有排序，触发selfCorrect，查询成功
	 * 7. 1011: 有自建运力的记录，但是没有可用的租户和门店配置，查询失败
	 * 8. 1012: 有自建运力的记录，有可用的租户配置，但是没有可用的门店记录，查询失败
	 * 9. 1013: 没有自建运力的记录，有可用的租户配置，没有可用的门店配置，查询失败
	 * 10. 1014: 没有自建运力的记录，有可用的租户和门店配置，补创建对应自建运力记录，查询成功
	 */
	@Test
	@Transactional
	@DatabaseSetup("./delivery_poi_query_setup.xml")
	@ExpectedDatabase(value = "./delivery_poi_query_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
	public void should_queryDeliveryPoi_success() {
		//999: 没有对应记录，查询失败
		assertFalse(deliveryPoiRepository.queryDeliveryPoi(999L, 999L).isPresent());

		//1000: 有完整的自建运力的记录，查询成功
		Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(1000L, 1000L);
		assertTrue(opDeliveryPoi.isPresent());
		assertDeliveryPoi(opDeliveryPoi.get(), "{\"tenantId\":1000,\"storeId\":1000,\"cityCode\":510100,\"contactPhone\":\"13812345678\",\"deliveryPlatform\":\"SELF_BUILT_DELIVERY_PLATFORM\",\"deliveryLaunchPoint\":{\"immediateOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"delayMinutes\":0},\"bookingOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"configMinutes\":60}},\"deliveryLaunchType\":\"AUTO_LAUNCH_DELIVERY\",\"channelTenantConfigMap\":{\"900\":{\"deliveryChannel\":900,\"appKey\":\"1000_900_key\",\"secretKey\":\"1000_900_sec\",\"enabled\":true}},\"channelStoreConfigMap\":{\"900\":{\"storeId\":1000,\"deliveryChannel\":900,\"deliveryChannelStoreId\":\"1000_900\",\"deliveryChannelStoreName\":\"1000_900测试门店\",\"servicePackageCodes\":[\"4011\",\"4012\"],\"enabled\":true,\"orderedServicePackageCodes\":[\"4011\",\"4012\"]}},\"orderPlatformDeliveryConfigMap\":{\"100\":{\"minOrderPrice\":2000,\"deliveryFee\":null,\"deliveryMinutes\":null}},\"storeAddress\":null,\"deliveryStrategy\":1,\"deliveryStrategyConfig\":{\"orderedDeliveryChannels\":[900],\"timeoutForShiftDeliveryChannelInMinutes\":15}}");

		//1001: 有聚合运力的记录，查询成功
		opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(1001L, 1001L);
		assertTrue(opDeliveryPoi.isPresent());
		assertDeliveryPoi(opDeliveryPoi.get(), "{\"tenantId\":1001,\"storeId\":1001,\"cityCode\":510100,\"contactPhone\":\"13812345678\",\"deliveryPlatform\":\"AGGREGATION_DELIVERY_PLATFORM\",\"deliveryLaunchPoint\":{\"immediateOrderDeliveryLaunchPointConfig\":{\"launchPoint\":2,\"delayMinutes\":10},\"bookingOrderDeliveryLaunchPointConfig\":{\"launchPoint\":2,\"configMinutes\":40}},\"deliveryLaunchType\":\"MANUAL_LAUNCH_DELIVERY\",\"autoLaunchStrategyId\":1}");

		//1002: 有麦芽田的记录，查询成功
		opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(1002L, 1002L);
		assertTrue(opDeliveryPoi.isPresent());
		assertDeliveryPoi(opDeliveryPoi.get(), "{\"tenantId\":1002,\"storeId\":1002,\"cityCode\":510100,\"contactPhone\":\"13812345678\",\"deliveryPlatform\":\"MALT_FARM_DELIVERY_PLATFORM\",\"deliveryLaunchPoint\":{\"immediateOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"delayMinutes\":0},\"bookingOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"configMinutes\":60}},\"deliveryLaunchType\":\"AUTO_LAUNCH_DELIVERY\"}");

		//1003: 有商家自配送的记录，查询成功
		opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(1003L, 1003L);
		assertTrue(opDeliveryPoi.isPresent());
		assertDeliveryPoi(opDeliveryPoi.get(), "{\"tenantId\":1003,\"storeId\":1003,\"cityCode\":510100,\"contactPhone\":\"13812345678\",\"deliveryPlatform\":\"MERCHANT_SELF_DELIVERY\",\"deliveryLaunchPoint\":{\"immediateOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"delayMinutes\":0},\"bookingOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"configMinutes\":40}},\"deliveryLaunchType\":\"AUTO_LAUNCH_DELIVERY\"}");

		//1010: 有自建运力记录，有可用的租户和门店配置，门店设置里面没有排序，触发selfCorrect，查询成功
		opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(1010L, 1010L);
		assertTrue(opDeliveryPoi.isPresent());
		assertDeliveryPoi(opDeliveryPoi.get(), "{\"tenantId\":1010,\"storeId\":1010,\"cityCode\":510100,\"contactPhone\":\"13812345678\",\"deliveryPlatform\":\"SELF_BUILT_DELIVERY_PLATFORM\",\"deliveryLaunchPoint\":{\"immediateOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"delayMinutes\":0},\"bookingOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"configMinutes\":60}},\"deliveryLaunchType\":\"AUTO_LAUNCH_DELIVERY\",\"channelTenantConfigMap\":{\"900\":{\"deliveryChannel\":900,\"appKey\":\"1010_900_key\",\"secretKey\":\"1010_900_sec\",\"enabled\":true},\"800\":{\"deliveryChannel\":800,\"appKey\":\"1010_800_key\",\"secretKey\":\"1010_800_sec\",\"enabled\":true}},\"channelStoreConfigMap\":{\"900\":{\"storeId\":1010,\"deliveryChannel\":900,\"deliveryChannelStoreId\":\"1010_900\",\"deliveryChannelStoreName\":\"1010_900测试门店\",\"servicePackageCodes\":[\"4011\",\"4012\"],\"enabled\":true,\"orderedServicePackageCodes\":[\"4011\",\"4012\"]},\"800\":{\"storeId\":1010,\"deliveryChannel\":800,\"deliveryChannelStoreId\":\"1010_800\",\"deliveryChannelStoreName\":\"1010_800测试门店\",\"servicePackageCodes\":[\"1\"],\"enabled\":true,\"orderedServicePackageCodes\":[\"1\"]}},\"orderPlatformDeliveryConfigMap\":{\"100\":{\"minOrderPrice\":2000,\"deliveryFee\":null,\"deliveryMinutes\":null}},\"storeAddress\":null,\"deliveryStrategy\":1,\"deliveryStrategyConfig\":{\"orderedDeliveryChannels\":[900,800],\"timeoutForShiftDeliveryChannelInMinutes\":15}}");

		//1011: 有自建运力的记录，但是没有可用的租户和门店配置，查询失败
		assertFalse(deliveryPoiRepository.queryDeliveryPoi(1011L, 1011L).isPresent());

		//1012: 有自建运力的记录，有可用的租户配置，但是没有可用的门店记录，查询失败
		assertFalse(deliveryPoiRepository.queryDeliveryPoi(1012L, 1012L).isPresent());

		//1013: 没有自建运力的记录，有可用的租户配置，没有可用的门店配置，查询失败
		assertFalse(deliveryPoiRepository.queryDeliveryPoi(1013L, 1013L).isPresent());

		//1014: 没有自建运力的记录，有可用的租户和门店配置，补创建对应自建运力记录，查询成功
		opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(1014L, 1014L);
		assertTrue(opDeliveryPoi.isPresent());
		assertDeliveryPoi(opDeliveryPoi.get(), "{\"tenantId\":1014,\"storeId\":1014,\"cityCode\":510100,\"contactPhone\":\"13812345678\",\"deliveryPlatform\":\"SELF_BUILT_DELIVERY_PLATFORM\",\"deliveryLaunchPoint\":{\"immediateOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"delayMinutes\":0},\"bookingOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"configMinutes\":60}},\"deliveryLaunchType\":\"AUTO_LAUNCH_DELIVERY\",\"channelTenantConfigMap\":{\"900\":{\"deliveryChannel\":900,\"appKey\":\"1014_900_key\",\"secretKey\":\"1014_900_sec\",\"enabled\":true}},\"channelStoreConfigMap\":{\"900\":{\"storeId\":1014,\"deliveryChannel\":900,\"deliveryChannelStoreId\":\"1014_900\",\"deliveryChannelStoreName\":\"1014_900测试门店\",\"servicePackageCodes\":[\"4011\",\"4012\"],\"enabled\":true,\"orderedServicePackageCodes\":[\"4011\",\"4012\"]}},\"orderPlatformDeliveryConfigMap\":{},\"storeAddress\":null,\"deliveryStrategy\":1,\"deliveryStrategyConfig\":{\"orderedDeliveryChannels\":[900],\"timeoutForShiftDeliveryChannelInMinutes\":15}}");
	}

	@Test
	@Transactional
	@DatabaseSetup("./delivery_poi_query_setup.xml")
	public void should_querySelfBuiltDeliveryPoi_success() {
		assertFalse(deliveryPoiRepository.querySelfBuiltDeliveryPoi(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, "not_exist").isPresent());

		Optional<SelfBuiltDeliveryPoi> opDeliveryPoi = deliveryPoiRepository.querySelfBuiltDeliveryPoi(ThirdDeliveryChannelEnum.HAI_KUI_DELIVERY, "1000_900");
		assertTrue(opDeliveryPoi.isPresent());
		assertDeliveryPoi(opDeliveryPoi.get(), "{\"tenantId\":1000,\"storeId\":1000,\"cityCode\":510100,\"contactPhone\":\"13812345678\",\"deliveryPlatform\":\"SELF_BUILT_DELIVERY_PLATFORM\",\"deliveryLaunchPoint\":{\"immediateOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"delayMinutes\":0},\"bookingOrderDeliveryLaunchPointConfig\":{\"launchPoint\":1,\"configMinutes\":60}},\"deliveryLaunchType\":\"AUTO_LAUNCH_DELIVERY\",\"channelTenantConfigMap\":{\"900\":{\"deliveryChannel\":900,\"appKey\":\"1000_900_key\",\"secretKey\":\"1000_900_sec\",\"enabled\":true}},\"channelStoreConfigMap\":{\"900\":{\"storeId\":1000,\"deliveryChannel\":900,\"deliveryChannelStoreId\":\"1000_900\",\"deliveryChannelStoreName\":\"1000_900测试门店\",\"servicePackageCodes\":[\"4011\",\"4012\"],\"enabled\":true,\"orderedServicePackageCodes\":[\"4011\",\"4012\"]}},\"orderPlatformDeliveryConfigMap\":{\"100\":{\"minOrderPrice\":2000,\"deliveryFee\":null,\"deliveryMinutes\":null}},\"storeAddress\":null,\"deliveryStrategy\":1,\"deliveryStrategyConfig\":{\"orderedDeliveryChannels\":[900],\"timeoutForShiftDeliveryChannelInMinutes\":15}}");
	}

	@Test
	@Transactional
	@DatabaseSetup(value = "./delivery_poi_save_setup.xml")
	@ExpectedDatabase(value = "./delivery_poi_save_expect.xml", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
	public void should_saveDeliveryPoi_success() {
		deliveryPoiRepository.saveDeliveryPoi(new SelfBuiltDeliveryPoi(
				1L, 1000L, 1000L, 510100, "13812345678",
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 10),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.PICK_DONE, 20)
				),
				new Address("mock address", CoordinateTypeEnum.MARS, new CoordinatePoint("101.123456", "31.123456")),
				DeliveryStrategyEnum.SEQUENTIAL_POLLING,
				new SequentialPollingStrategyConfig(ImmutableList.of(ThirdDeliveryChannelEnum.FENG_NIAO_DELIVERY), 20),
				new HashMap<>(),
				new HashMap<>(),
				ImmutableMap.of(100, new OrderPlatformDeliveryConfig(1, 2, 3)),
				DynamicChannelType.MEITUAN.getChannelId(),null
		));

		deliveryPoiRepository.saveDeliveryPoi(new AggrDeliveryPoi(
				3L, 1000L, 1002L, 510100, "13812345678",
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 30),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.PICK_DONE, 40)
				),
				DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY,
				2,DynamicChannelType.MEITUAN.getChannelId(),null
		));

		deliveryPoiRepository.saveDeliveryPoi(new MaltFarmDeliveryPoi(
				2L, 1000L, 1001L, 510100, "13812345678",
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 50),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.PICK_DONE, 60)
				),
				null, DynamicChannelType.MEITUAN.getChannelId(),null
		));

		deliveryPoiRepository.saveDeliveryPoi(new MerchantSelfDeliveryPoi(
				1000L, 1003L, 510100, "13812345678",
				new DeliveryLaunchPoint(
						new ImmediateOrderDeliveryLaunchPointConfig(ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE, 70),
						new BookingOrderDeliveryLaunchPointConfig(BookingOrderDeliveryLaunchPointEnum.PICK_DONE, 80)
				),DynamicChannelType.MEITUAN.getChannelId()
		));
	}

	private void assertDeliveryPoi(DeliveryPoi deliveryPoi, String json) {
		try {
			FilterProvider filters = new SimpleFilterProvider()
					.addFilter("filter properties by name", SimpleBeanPropertyFilter.serializeAllExcept("id"));
			assertEquals(json, OBJECT_MAPPER.writer(filters).writeValueAsString(deliveryPoi));
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	@JsonFilter("filter properties by name")
	static class PropertyFilterMixIn {
	}
}
