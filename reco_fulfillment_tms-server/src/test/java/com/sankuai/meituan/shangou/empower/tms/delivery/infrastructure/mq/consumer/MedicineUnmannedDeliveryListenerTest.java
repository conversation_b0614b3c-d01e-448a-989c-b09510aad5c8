package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.StartAppTest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderGoodsInfoExtInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Date 2023/9/12 10:47 AM
 * Description
 */
//@SpringBootTest
//@Import({OrderPickFinishMessageListener.class,OrderStatusChangeMessageListener.class,OrderSystemClientImpl.class, MySQLDeliveryPoiRepository.class})
@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest(classes={StartAppTest.class})
@Slf4j
public class MedicineUnmannedDeliveryListenerTest {

    @Resource
    private OrderPickFinishMessageListener orderPickFinishMessageListener;

    @Resource
    private OrderStatusChangeMessageListener orderStatusChangeMessageListener;

  //  @MockBean
  //  private OrderSystemClientImpl orderSystemClient;

//    @MockBean
//    private TenantRemoteService tenantRemoteService;

    private static Long TENANT_ID=1006915L;
    private static Long SHOP_ID=49878954L;
    //private static Long ORDER_ID=uuidToLong(UUID.randomUUID());
    private static Long ORDER_ID=1702569689772748849L;
    @Before
    public void setUp() {

        //模拟包含goodsNo 货号的订单
//        when(orderSystemClient.getOrderInfo(any(OrderKey.class), anyBoolean())).thenReturn(
//                new Result<>(
//                       buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue())
//                        //buildOrderInfo(DynamicOrderBizType.ELE_ME.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue())
//                        //buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.SUBMIT.getValue(), OrderSourceEnum.OTO_ONLINE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue())
//
//                )
//        );


    }

    @Test
    public void consumePickFinishMessageTest() {

        OrderPickFinishMessageListener.MessageDTO message = new OrderPickFinishMessageListener.MessageDTO();
        message.setTenantId(TENANT_ID);
        message.setShopId(SHOP_ID);
        message.setOrderId(ORDER_ID);
        message.setWarehouseId(SHOP_ID);

        Map<String, Object> map = new HashMap<>();
        map.put("body", JSON.toJSONString(message));
        MafkaMessage mafkaMessage = new MafkaMessage(
                "", 1, 1, null, JSON.toJSONString(map)
        );
        orderPickFinishMessageListener.consume(mafkaMessage);
    }


    @Test
    public void consumeOrderStatusChangeMessageTest() {
        OrderStatusChangeMessageListener.MessageDTO message = new OrderStatusChangeMessageListener.MessageDTO();
        message.setTenantId(TENANT_ID);
        message.setShopId(SHOP_ID);
        message.setOrderId(ORDER_ID);
        message.setOrderSource(OrderSourceEnum.OTO_ONLINE.getValue());
        message.setStatus(OrderStatusEnum.MERCHANT_CONFIRMED.getValue());
        message.setSourceStatus(1);
        message.setWarehouseId(SHOP_ID);
        message.setCompensate(false);
        message.setOnlineDistributeStatus(30);
        message.setDelayPush(false);

        Map<String, Object> map = new HashMap<>();
        map.put("body", JSON.toJSONString(message));
        MafkaMessage mafkaMessage = new MafkaMessage(
                "", 1, 1, null, JSON.toJSONString(map)
        );
        orderStatusChangeMessageListener.consume(mafkaMessage);
    }



    private static OrderInfo buildOrderInfo(int orderBizType, int orderStatus, int orderSource, boolean isSelfDelivery, int deliveryMethod) {
        return new OrderInfo(
                new OrderKey(TENANT_ID, SHOP_ID, ORDER_ID),
                "channel_order_id",
                4L,
                "4-01",
                orderBizType,
                orderSource,
                orderStatus,
                isSelfDelivery,
                deliveryMethod,
                new Receiver("receiver_name",
                        "receiver_phone",
                        "receiver_privacy_phone",
                        new Address("address_detail", CoordinateTypeEnum.MARS, new CoordinatePoint("1.1", "2.2"))
                ),
                LocalDateTime.now().plusMinutes(30L),
                LocalDateTime.now().plusMinutes(30L),
                false,
                1,
                1000,
                com.google.common.collect.Lists.newArrayList(
                        new OrderInfo.GoodsInfo("sku1", "sku_name1", 2, 300, "份", 0,new OrderGoodsInfoExtInfo("A01"))
                ),
                "comments",
                "invoice_title",
                900,
                "123456789",
                false,
                "riderName",
                "riderPhone",
                0,
                LocalDateTime.now(),
                LocalDateTime.now(),
                LocalDateTime.now(),null,null, null,null, null, null, null,
                Lists.newArrayList( new OrderInfo.GiftGoodsInfo("sku1", "sku_name2", 2, "A02"),new OrderInfo.GiftGoodsInfo("sku3", "sku_name3", 1, "A03")),null,
                null
        );
    }
}
