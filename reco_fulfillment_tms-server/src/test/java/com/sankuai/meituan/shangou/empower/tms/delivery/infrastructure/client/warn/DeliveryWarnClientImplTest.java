package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.warn;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.TestBase;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.DeliveryWarnClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/5/31
 */

@Slf4j
@ActiveProfiles("test")
public class DeliveryWarnClientImplTest extends TestBase {

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private DeliveryWarnClient deliveryWarnClient;

    @Resource
    private String xmKey;
    @Resource
    private String xmToken;
    @Resource
    private Long xmPubId;

    // “歪马履约监控” 公众号相关信息，参见https://km.sankuai.com/page/1301871363
    @Resource
    private String dhOaKey;
    @Resource
    private String dhOaToken;
    @Resource
    private Long dhOaPubId;

    // 配送超时提醒依赖的企业id
    @Resource
    private Long dhCid;
    @Resource
    private Long meituanCid;

    @Resource
    private DeliveryWarnClientImpl deliveryWarnClientImpl;

    @Resource
    private DeliveryRemindConfigRepository deliveryRemindConfigRepository;

    //	@Ignore
    @Test
    public void should_getXmConfig_success() {
        log.debug("DHOaKey:{}", dhOaKey);
        log.info("DHOaToken:{}", dhOaToken);
        log.warn("DHOaId:{}", dhOaPubId);
        log.error("DHCid:{}", dhCid);

        assertEquals("110L1351220073F2", xmKey);
        assertEquals("1e4f1740c0d6b234c1898f5dcd25ac93", xmToken);
        assertEquals(137439029702L, xmPubId.longValue());
    }

    private OrderInfo buildOrderInfo(
            int orderBizType, int orderStatus, int orderSource,
            boolean isSelfDelivery, int deliveryMethod) {
        return new OrderInfo(
                new OrderKey(1001197L, 49867245L, 3L),
                "channel_order_id",
                4L,
                "4-01",
                orderBizType,
                orderSource,
                orderStatus,
                isSelfDelivery,
                deliveryMethod,
                new Receiver("receiver_name",
                        "receiver_phone",
                        "receiver_privacy_phone",
                        new Address("address_detail", CoordinateTypeEnum.MARS, new CoordinatePoint("1.1", "2.2"))
                ),
                LocalDateTime.now().plusMinutes(30L),
                LocalDateTime.now().plusMinutes(30L),
                false,
                1,
                1000,
                Lists.newArrayList(
                        new OrderInfo.GoodsInfo("sku1", "sku_name1", 2, 300, "份", 0)
                ),
                "comments",
                "invoice_title",
                900,
                "123456789",
                false,
                "riderName",
                "riderPhone",
                0,
                LocalDateTime.now(),
                LocalDateTime.now(),
                LocalDateTime.now(),
                null,
                null,
                null,null, null, null, null,Lists.newArrayList(),null,null,null,null,null,null,null,null,null,null,null,null,null,null,null
        );
    }


    private DeliveryOrder buildDeliveryOrder(DeliveryChannelEnum deliveryChannel, DeliveryStatusEnum status) {
        return new DeliveryOrder(
                99L,
                1L,
                2L,
                new OrderKey(1L, 2L, 3L),
                "channel_order_id",
                30,
                101,
                1,
                "1-01",
                false,
                new Receiver(
                        "receiver_name",
                        "receiver_phone",
                        "receiver_privacy_phone",
                        new Address("address_detail", CoordinateTypeEnum.MARS, new CoordinatePoint("1.1", "2.2"))),
                LocalDateTime.of(2020, 12, 12, 12, 12, 12),
                LocalDateTime.of(2020, 12, 12, 12, 12, 12),
                deliveryChannel.getCode(),
                "channel_delivery_id",
                0,
                "1",
                status,
                DeliveryExceptionTypeEnum.NO_EXCEPTION,
                StringUtils.EMPTY,
                TimeUtil.getEpochTime(),
                new Rider("original_rider_name", "original_rider_phone", "original_rider_phone_token"),
                0L,
                TimeUtil.getEpochTime(),
                0,
                BigDecimal.ONE,
                99L,
                LocalDateTime.now(),LocalDateTime.now(), 0, new BigDecimal(1.0), 1, null,10, false,
                null,null,null,null,null,null, null, null, null, null, null, null, null, null, null, null, null, null,null,null,null,null,null,null,null,null
        );
    }

    @Test
    public void pushDeliveryDoneTimeOut_normal() {
        log.info("pushDeliveryDoneTimeOut_normal");
        log.info(System.getProperty("spring.profiles.active"));

        OrderInfo orderInfo = buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue());

        DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_REJECTED);

        Optional<Failure> result = deliveryWarnClientImpl.pushDeliveryDoneTimeOut(orderInfo, deliveryOrder);
        log.info("result {}", result);
        assertEquals(false, result.isPresent());

    }

    //	@Ignore
    @Test
    public void pushDeliveryDoneTimeOutMessage_normal() {
        String messageStr = "配送超时提醒\n" +
                "【门店】歪马测试门店002（双渠道）\n" +
                "【超时订单号】channel_order_id\n" +
                "【订单来源】美团外卖\n" +
                "【用户姓名】receiver_name\n" +
                "【用户手机号】receiver_phone\n" +
                "【骑手姓名】original_rider_name\n" +
                "【骑手电话】original_rider_phone\n" +
                "【当前配送状态】配送拒单\n" +
                "【订单类型】实时单\n" +
                "【订单支付时间】：2022-04-24 15:01:23\n" +
                "【订单预计送达时间】：2022-04-24 15:31:23";

        DeliveryRemindConfig deliveryRemindConfig = new DeliveryRemindConfig(
                3L, 1001197L, 49867245L, null,
                true, null, null
        );

        List<String> recipients = new ArrayList<String>(Arrays.asList("muxiaobo123", "muxiaobo", "linxiaorui", "xuninghan"));
        deliveryRemindConfig.changeRecipients(recipients);

        // receivers的用户部分不存在也是可以发送成功的
        List<String> dfRecipients = new ArrayList<String>(Arrays.asList("muxiaobo123", "muxiaobo", "linxiaorui", "<EMAIL>", "testdx_001"));
        deliveryRemindConfig.changeDhRecipients(dfRecipients);

        Optional<Failure> result = deliveryWarnClientImpl.pushDeliveryDoneTimeOutMessage(messageStr, deliveryRemindConfig);
        log.info("result {}", result);
        assertEquals(false, result.isPresent());
    }
}
