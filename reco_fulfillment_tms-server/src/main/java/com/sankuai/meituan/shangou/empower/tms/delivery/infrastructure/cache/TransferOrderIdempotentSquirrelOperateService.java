package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache;

import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @description 转单幂等缓存
 * @date 2025-07-29
 */
@Service
public class TransferOrderIdempotentSquirrelOperateService extends OFCSquirrelOperateService {
    private static final String CATEGORY_NAME = "transfer_order_idempotent";

    @Override
    public String getCategoryName() {
        return CATEGORY_NAME;
    }

    public boolean setnx(Long tenantId, Long storeId, Long orderId) {
       return setnx(getKey(tenantId, storeId, orderId), "1");
    }

    public boolean delete(Long tenantId, Long storeId, Long orderId) {
       return delete(getKey(tenantId, storeId, orderId));
    }


    private String getKey(Long tenantId, Long storeId, Long orderId) {
        return tenantId + "_" + storeId + "_" + orderId;
    }
}
