package com.sankuai.meituan.shangou.empower.tms.delivery.facade;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.qnh.ofc.ofw.client.thrift.common.base.OfcStatus;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.OrderInfoByOrderIdReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

@Rhino
@Slf4j
public class OFCRemoteService {

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;

    @MethodLog(logRequest = false, logResponse = true)
    @Degrade(
            rhinoKey = "OfcRemoteService.getOfcOrder",
            fallBackMethod = "getOfcOrderFallback", timeoutInMilliseconds = 1000
    )
    public List<FulfillmentOrderDTO> getOfcOrder(Long tenantId,Long offlineStoreId,Long orderId){
        OrderInfoByOrderIdReq req = new OrderInfoByOrderIdReq();
        req.setTenantId(tenantId);
        req.setWarehouseId(offlineStoreId);
        req.setOrderId(orderId);
        try {
            FulfillmentOrderResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderByOrderId(req);
            if(response == null || response.getStatus() == OfcStatus.FAIL){
                throw new DeliveryBaseException("查询ofc订单失败");
            }
            return response.getFulfillmentOrderList();
        }catch (Exception e){
            log.error("getOfcOrder error",e);
            throw new DeliveryBaseException("查询ofc订单失败");
        }
    }

    public List<FulfillmentOrderDTO> getOfcOrderFallback(Long tenantId,Long offlineStoreId,Long orderId){
        throw new DeliveryBaseException("查询ofc订单降级");
    }
}
