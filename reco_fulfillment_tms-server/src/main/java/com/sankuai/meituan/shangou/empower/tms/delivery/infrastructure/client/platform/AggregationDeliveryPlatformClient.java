package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.banma.deliverywaybill.order.ldp.client.thrift.TStatus;
import com.sankuai.banma.deliverywaybill.order.ldp.client.thrift.strategy.DeliveryStrategyThriftService;
import com.sankuai.banma.deliverywaybill.order.ldp.client.thrift.strategy.request.StrategyQueryRequest;
import com.sankuai.banma.deliverywaybill.order.ldp.client.thrift.strategy.response.StrategyInfoResponse;
import com.sankuai.banma.deliverywaybill.order.ldp.client.thrift.strategy.response.StrategyQueryResponse;
import com.sankuai.meituan.banma.thrift.delivery3pl.open.platform.vo.Bm3PLChannel;
import com.sankuai.meituan.banma.thrift.lbs.open.iface.OpenBmLbsCoordinateIface;
import com.sankuai.meituan.banma.thrift.lbs.open.vo.params.OpenGetOrderCoordinateParam;
import com.sankuai.meituan.banma.thrift.lbs.open.vo.result.OpenBmPointResult;
import com.sankuai.meituan.banma.thrift.upstream.open.vo.SelfPkgCancelReasonEnum;
import com.sankuai.meituan.logisticorder.platform.client.dto.AddressDto;
import com.sankuai.meituan.logisticorder.platform.client.dto.CancelAggregationRequest;
import com.sankuai.meituan.logisticorder.platform.client.dto.CancelAggregationResponse;
import com.sankuai.meituan.logisticorder.platform.client.dto.CreateOrderRequest;
import com.sankuai.meituan.logisticorder.platform.client.dto.CreateOrderResponse;
import com.sankuai.meituan.logisticorder.platform.client.dto.ExtentionDto;
import com.sankuai.meituan.logisticorder.platform.client.dto.GoodsItemDto;
import com.sankuai.meituan.logisticorder.platform.client.dto.OrderPreviewResponse;
import com.sankuai.meituan.logisticorder.platform.client.dto.PreviewDto;
import com.sankuai.meituan.logisticorder.platform.client.dto.ResponseStatus;
import com.sankuai.meituan.logisticorder.platform.client.dto.ShippingGoodsDto;
import com.sankuai.meituan.logisticorder.platform.client.dto.ShippingInfoDto;
import com.sankuai.meituan.logisticorder.platform.client.enums.AddressTypeEnum;
import com.sankuai.meituan.logisticorder.platform.client.enums.AggregationOrderSourceEnum;
import com.sankuai.meituan.logisticorder.platform.client.exception.LogisticOrderException;
import com.sankuai.meituan.logisticorder.platform.client.service.LogisticsOrderAggregationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LaunchFailure;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryChannelPreLaunchInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryLaunchInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.LaunchRuleInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.DaySeqNumUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TmsMccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CancelMarkEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.FallbackException;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.aggr.AggrDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.CommentUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by liuyonggao on 2021/1/6.
 */
@Rhino
@Slf4j
@Service
public class AggregationDeliveryPlatformClient extends AbstractDeliveryPlatformClient {

	private static final int PLATFORM_ID = 2;
	private static final double DEFAULT_AMOUNT = 0d;
	private static final int DEFAULT_TIMESTAMP_IN_SECONDS = 0;
	private static final int PRE_BOOK_ORDER = 1;
	private static final int NON_PRE_BOOK_ORDER = 0;

	private static final String OTHER_WAY = "已选择其他配送方式";
	private static final String NO_RIDER = "没有配送员接单";

	private static final String ULAP_EXTENSION_FIELD = "orderExtension";

	@Resource
	private OpenBmLbsCoordinateIface.Iface openBmLbsCoordinateThriftClient;
	@Resource
	private DeliveryStrategyThriftService deliveryStrategyThriftClient;
	@Resource
	private LogisticsOrderAggregationThriftService logisticsOrderAggregationThriftService;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Override
	public DeliveryPlatformEnum getDeliveryPlatform() {
		return DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM;
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	@Degrade(rhinoKey = "DeliveryPlatformClient-preLaunch", fallBackMethod = "preLaunchFallback", timeoutInMilliseconds = 2000)
	public Result<List<DeliveryChannelPreLaunchInfo>> preLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		try {
			CreateOrderRequest request = buildUlapCreateOrderBasicRequest(deliveryPoi, orderInfo);
			log.info("LogisticsOrderAggregationThriftService.preview begin, request={}", request);
			OrderPreviewResponse response = logisticsOrderAggregationThriftService.preview(request);
			log.info("LogisticsOrderAggregationThriftService.preview finish, response={}", response);
			ResponseStatus responseStatus = response.getStatus();

			if (responseStatus.getCode() != TStatus.SUCCESS.getCode()) {
				log.warn("Call LogisticsOrderAggregationThriftService.preview fail. status:{}", responseStatus);
				if (responseStatus.getCode() == LogisticOrderExceptionEnum.LOGISTIC_ORDER_EXISTED.getCode()) {
					Cat.logEvent("LAUNCH_PRE", "LOGISTIC_ORDER_EXISTED");
					return new Result<>(new Failure(false, FailureCodeEnum.LOGISTIC_ORDER_EXISTED));
				}
				Cat.logEvent("LAUNCH_PRE", "FAIL_BY_CODE_" + responseStatus.getCode());
				return new Result<>(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, responseStatus.getMsg()));
			}

			Set<Integer> unsupportedChannels = TmsMccUtils.getUnsupportedChannelsInTms();
			Cat.logEvent("LAUNCH_PRE", "SUCCESS");
			return new Result<>(
					Optional.ofNullable(response.getCarrierPriceList())
							.orElse(new ArrayList<>())
							.stream()
							.filter(it -> StringUtils.isNumeric(it.getCarrierCode()))
							.filter(it -> Objects.nonNull(DeliveryChannelEnum.valueOf(Integer.parseInt(it.getCarrierCode()))))
							.filter(it -> !unsupportedChannels.contains(Integer.parseInt(it.getCarrierCode())))
							.map(it -> {
								if (it.getSuccess()) {
									return new DeliveryChannelPreLaunchInfo(
											DeliveryChannelEnum.valueOf(Integer.parseInt(it.getCarrierCode())),
											String.valueOf(it.getServicePackageId()),
											BigDecimal.valueOf(it.getPoiShippingFee()),
											BigDecimal.valueOf(it.getDiscountFee())
									);
								} else {
									return new DeliveryChannelPreLaunchInfo(
											DeliveryChannelEnum.valueOf(Integer.parseInt(it.getCarrierCode())),
											String.valueOf(it.getServicePackageId()),
											it.getErrorMsg()
									);
								}
							})
							.sorted()
							.collect(Collectors.toList())
			);
		} catch (LogisticOrderException e) {
			Cat.logEvent("LAUNCH_PRE", "FAIL");
			log.warn("Call LogisticsOrderAggregationThriftService.preview fail", e);
			return new Result<>(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, e.getMessage()));
		} catch (Exception e) {
			Cat.logEvent("LAUNCH_PRE", "ERROR");
			log.error("Call LogisticsOrderAggregationThriftService.preview error", e);
			return new Result<>(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	private Result<List<DeliveryChannelPreLaunchInfo>> preLaunchFallback(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		throw new FallbackException("DeliveryPlatformClientImpl-preLaunch 熔断降级");
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	@Degrade(rhinoKey = "DeliveryPlatformClient-launch", fallBackMethod = "launchFallback", timeoutInMilliseconds = 2000)
	public Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {

		if (deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode()) {
			return doAutoLaunch(deliveryPoi, orderInfo);

		} else {
			//已知渠道走手动发单接口
			return doManualLaunch(deliveryPoi, orderInfo, deliveryOrder);
		}
	}


	private Result<DeliveryLaunchInfo> launchFallback(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
		throw new FallbackException("DeliveryPlatformClientImpl-launch 熔断降级");
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	@Degrade(rhinoKey = "DeliveryPlatformClient-cancelDelivery", fallBackMethod = "cancelDeliveryFallback", timeoutInMilliseconds = 2000)
	public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
		log.info("DeliveryPlatformClient.cancelDelivery deliveryOrderPrimaryId:{}", deliveryOrder.getId());
		Optional<Failure> failure = doCancelDelivery(deliveryOrder);

		if (!failure.isPresent()) {
			deliveryOrder.setCancelMark(CancelMarkEnum.CANCELING.getValue());
			deliveryOrderRepository.save(deliveryOrder);
		}

		return failure;
	}

	private Optional<Failure> doCancelDelivery(DeliveryOrder deliveryOrder) {
		if (StringUtils.isEmpty(deliveryOrder.getChannelDeliveryId())) {
			return Optional.of(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, "渠道运单号为空，无法发起取消"));
		}

		String cancelDesc = "订单取消";
		if (deliveryOrder.getStatus().equals(DeliveryStatusEnum.RIDER_ASSIGNED) ||
				deliveryOrder.getStatus().equals(DeliveryStatusEnum.RIDER_ARRIVED_SHOP)) {
			cancelDesc = OTHER_WAY;
		} else if (deliveryOrder.getStatus().equals(DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER) ||
				deliveryOrder.getStatus().equals(DeliveryStatusEnum.DELIVERY_LAUNCHED)) {
			cancelDesc = NO_RIDER;
		}

		try {
			CancelAggregationRequest request = new CancelAggregationRequest();
			request.setLogisticsOrderId(Long.parseLong(deliveryOrder.getChannelDeliveryId()));
			request.setPlatformPoiId(deliveryOrder.getStoreId());
			request.setPlatformId(PLATFORM_ID);
			request.setCancelType(SelfPkgCancelReasonEnum.USER_TYPE.getValue());
			request.setCancelDesc(cancelDesc);
			request.setCancelTime(TimeUtil.toSeconds(LocalDateTime.now()).orElse(0));
			log.info("LogisticsOrderAggregationThriftService.cancelAuto begin, request={}", request);
			CancelAggregationResponse response = logisticsOrderAggregationThriftService.cancelAuto(request);
			log.info("LogisticsOrderAggregationThriftService.cancelAuto finish, response={}", response);
			ResponseStatus responseStatus = response.getStatus();

			if (responseStatus.getCode() != TStatus.SUCCESS.getCode()) {
				log.warn("Call LogisticsOrderAggregationThriftService.cancelAuto fail. status:{}", responseStatus);
				if (responseStatus.getCode() == LogisticOrderExceptionEnum.LOGISTIC_ORDER_CANCELLED.getCode()) {
					Cat.logEvent("LAUNCH_CANCEL_" + deliveryOrder.getDeliveryChannel(), "LOGISTIC_ORDER_CANCELLED");
					return Optional.empty();
				}
				Cat.logEvent("LAUNCH_CANCEL_" + deliveryOrder.getDeliveryChannel(), "FAIL_BY_CODE_" + responseStatus.getCode());
				return Optional.of(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, responseStatus.getMsg()));
			}

			Cat.logEvent("LAUNCH_CANCEL_" + deliveryOrder.getDeliveryChannel(), "SUCCESS");
			return Optional.empty();
		} catch (LogisticOrderException e) {
			Cat.logEvent("LAUNCH_CANCEL_" + deliveryOrder.getDeliveryChannel(), "FAIL");
			log.warn("Call LogisticsOrderAggregationThriftService.cancelAuto fail", e);
			return Optional.of(new Failure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, e.getMessage()));
		} catch (Exception e) {
			Cat.logEvent("LAUNCH_CANCEL_" + deliveryOrder.getDeliveryChannel(), "ERROR");
			log.error("Call LogisticsOrderAggregationThriftService.cancelAuto error", e);
			return Optional.of(new Failure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	private Optional<Failure> cancelDeliveryFallback(DeliveryOrder deliveryOrder) {
		throw new FallbackException("DeliveryPlatformClientImpl-cancelDelivery 熔断降级");
	}


	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi) {
		try {
			OpenGetOrderCoordinateParam param = new OpenGetOrderCoordinateParam(String.valueOf(deliveryOrder.getOrderId()),
					Bm3PLChannel.SHANGOU_FUNENG.getValue(), deliveryPoi.getCityCode());

			OpenBmPointResult result = openBmLbsCoordinateThriftClient.getOrderCoordinate(param);

			if (result.isIsSuccess() && Objects.nonNull(result.getBmPoint())) {
				return Optional.of(CoordinateUtil.translateToCoordinatePoint(String.valueOf(result.getBmPoint().getLng()),
						String.valueOf(result.getBmPoint().getLat())));
			} else {
				log.error("queryRiderPosition fail,{}", result);
			}
		} catch (Exception e) {
			log.error("queryRiderLocation error", e);
		}
		return Optional.empty();
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<LaunchRuleInfo> queryAvailableDeliveryRules(DeliveryPlatformEnum deliveryPlatform) {
		try {
			StrategyQueryRequest strategyQueryRequest = new StrategyQueryRequest();
			log.info("DeliveryStrategyThriftService.queryValidStrategies request:{}", strategyQueryRequest);
			StrategyQueryResponse response = deliveryStrategyThriftClient.queryValidStrategies(strategyQueryRequest);
			log.info("DeliveryStrategyThriftService.queryValidStrategies response:{}", response);
			if (response.getStatus().getCode() == TStatus.SUCCESS.getCode()) {
				List<StrategyInfoResponse> strategyList = response.getStrategyList();
				if (CollectionUtils.isNotEmpty(strategyList)) {
					return strategyList.stream()
							.map(st -> LaunchRuleInfo
									.builder()
									.code(st.getId())
									.name(st.getName())
									.build()
							).collect(Collectors.toList());
				}
			} else {
				log.error("queryAvailableDeliveryRules fail,{}", response);
			}
		} catch (Exception e) {
			log.error("queryAvailableDeliveryRules error", e);
		}
		return Lists.emptyList();
	}

	/**
	 * 执行自动发单
	 */
	private Optional<LaunchFailure> doAutoLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		try {
			CreateOrderRequest request = buildUlapAutoCreateOrderRequest(deliveryPoi, orderInfo);
			log.info("LogisticsOrderAggregationThriftService.createAuto begin, request={}", request);
			CreateOrderResponse response = logisticsOrderAggregationThriftService.createAuto(request);
			log.info("LogisticsOrderAggregationThriftService.createAuto finish, response={}", response);
			ResponseStatus responseStatus = response.getStatus();

			if (responseStatus.getCode() != TStatus.SUCCESS.getCode()) {
				log.warn("Call LogisticsOrderAggregationThriftService.createAuto fail. status:{} ", responseStatus);
				if (responseStatus.getCode() == LogisticOrderExceptionEnum.LOGISTIC_ORDER_EXISTED.getCode()) {
					Cat.logEvent("LAUNCH_AUTO", "LOGISTIC_ORDER_EXISTED");
					return Optional.empty();
				}
				Cat.logEvent("LAUNCH_AUTO", "FAIL_BY_CODE_" + responseStatus.getCode());
				return Optional.of(new LaunchFailure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, responseStatus.getMsg()));
			}

			Cat.logEvent("LAUNCH_AUTO", "SUCCESS");
			return Optional.empty();
		} catch (LogisticOrderException e) {
			Cat.logEvent("LAUNCH_AUTO", "FAIL");
			log.warn("Call LogisticsOrderAggregationThriftService.createAuto fail", e);
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, e.getMessage()));
		} catch (Exception e) {
			Cat.logEvent("LAUNCH_AUTO", "ERROR");
			log.error("Call LogisticsOrderAggregationThriftService.createAuto error", e);
			return Optional.of(new LaunchFailure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	/**
	 * 执行手动发单
	 */
	private Optional<LaunchFailure> doManualLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
		Set<Integer> unsupportedChannels = TmsMccUtils.getUnsupportedChannelsInTms();
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());

		// 验证手动发单的配送商，需要是 TMS 系统支持的配送商
		if (unsupportedChannels.contains(deliveryOrder.getDeliveryChannel())) {
			log.info("TMS do not support channel:[{}] temporarily", deliveryChannel.getCarrierName());
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.UNSUPPORTED_CHANNEL, deliveryChannel.getCarrierName()));
		}
		try {
			CreateOrderRequest request = buildUlapConfirmCreateOrderRequest(deliveryPoi, orderInfo, deliveryOrder, deliveryChannel);
			log.info("LogisticsOrderAggregationThriftService.confirm begin, request={}", request);
			CreateOrderResponse response = logisticsOrderAggregationThriftService.confirm(request);
			log.info("LogisticsOrderAggregationThriftService.confirm finish, response={}", response);
			ResponseStatus responseStatus = response.getStatus();

			if (responseStatus.getCode() != TStatus.SUCCESS.getCode()) {
				log.warn("Call LogisticsOrderAggregationThriftService.confirm fail. status:{} ", responseStatus);
				if (responseStatus.getCode() == LogisticOrderExceptionEnum.LOGISTIC_ORDER_EXISTED.getCode()) {
					Cat.logEvent("LAUNCH_CONFIRM_" + deliveryOrder.getDeliveryChannel(), "LOGISTIC_ORDER_EXISTED");
					return Optional.empty();
				}
				Cat.logEvent("LAUNCH_CONFIRM_" + deliveryOrder.getDeliveryChannel(), "FAIL_BY_CODE_" + responseStatus.getCode());
				return Optional.of(new LaunchFailure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, responseStatus.getMsg()));
			}

			Cat.logEvent("LAUNCH_CONFIRM_" + deliveryOrder.getDeliveryChannel(), "SUCCESS");
			return Optional.empty();
		} catch (LogisticOrderException e) {
			Cat.logEvent("LAUNCH_CONFIRM_" + deliveryOrder.getDeliveryChannel(), "FAIL");
			log.warn("Call LogisticsOrderAggregationThriftService.confirm fail", e);
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.OTHER_SYSTEM_CALL_FAILED, e.getMessage()));
		} catch (Exception e) {
			Cat.logEvent("LAUNCH_CONFIRM_" + deliveryOrder.getDeliveryChannel(), "ERROR");
			log.error("Call LogisticsOrderAggregationThriftService.confirm error", e);
			return Optional.of(new LaunchFailure(true, FailureCodeEnum.SYSTEM_ERROR));
		}
	}

	private int decideIsPrebook(OrderInfo orderInfo) {
		//如果订单预计送达时间已经过了，强制发实时配送单
		if (orderInfo.getEstimatedDeliveryTime() != null && orderInfo.getEstimatedDeliveryTime().isBefore(LocalDateTime.now())) {
			return NON_PRE_BOOK_ORDER;
		}

		return orderInfo.isBookingOrder() ? PRE_BOOK_ORDER : NON_PRE_BOOK_ORDER;
	}

	/**
	 * 构建基本的 ULAP 发单请求.
	 *
	 * @param deliveryPoi 门店配置
	 * @param orderInfo   订单信息
	 * @return 调用 ULAP 的发单请求
	 */
	private CreateOrderRequest buildUlapCreateOrderBasicRequest(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		CreateOrderRequest tRequest = new CreateOrderRequest();
		PreviewDto previewDto = new PreviewDto();
		previewDto.setCityId(deliveryPoi.getCityCode());
		previewDto.setDeliveredTime(TimeUtil.toSeconds(orderInfo.getEstimatedDeliveryTime()).orElse(DEFAULT_TIMESTAMP_IN_SECONDS));
		tRequest.setPreviewDto(previewDto);

		ExtentionDto extentionDto = new ExtentionDto();
		extentionDto.setPayed(true);
		extentionDto.setPkgValue(MoneyUtil.fromCentToYuan(orderInfo.getOriginalTotalAmount()).orElse(DEFAULT_AMOUNT));
		extentionDto.setPkgPrice(MoneyUtil.fromCentToYuan(orderInfo.getActualPayAmt()).orElse(DEFAULT_AMOUNT));
		extentionDto.setPoiSeq(DaySeqNumUtil.getDaySeqNumWithoutDHTenant(orderInfo.getDaySeq(), orderInfo.getDaySeqNum(), orderInfo.getOrderKey().getTenantId()));
		extentionDto.setOriginalOrderSourceId(parse2SourceIdInUlap(orderInfo.getOrderBizType()));
		extentionDto.setPoiConfirmTime(TimeUtil.toSeconds(orderInfo.getMerchantConfirmOrderTime()).orElse(DEFAULT_TIMESTAMP_IN_SECONDS));
		extentionDto.setAggregationOrderSourceId(convert2AggregationOrderSourceEnum(orderInfo.getOrderBizType()).getValue());
		extentionDto.setAggregationOrderViewId(orderInfo.getChannelOrderId());
		String testEnvMockAggregationOrderViewId = MccConfigUtils.getTestEnvMockAggregationOrderViewId();
		if (StringUtils.isNotBlank(testEnvMockAggregationOrderViewId)) {
			extentionDto.setAggregationOrderViewId(testEnvMockAggregationOrderViewId);
		}
		tRequest.getExtendMap().put(ULAP_EXTENSION_FIELD, JsonUtil.toJson(extentionDto));

		tRequest.setPlatformId(PLATFORM_ID);

		AddressDto sender = new AddressDto();
		sender.setAddressType(AddressTypeEnum.Business);
		sender.setPhone(deliveryPoi.getContactPhone());
		tRequest.setSender(sender);

		tRequest.setRecipient(buildRecipient(orderInfo));

		tRequest.setPlatformPoiId(deliveryPoi.getStoreId());

		tRequest.setPlatformOrderId(String.valueOf(orderInfo.getOrderKey().getOrderId()));

		ShippingInfoDto shippingInfoDto = new ShippingInfoDto();
		shippingInfoDto.setRemark(CommentUtils.filterDefaultComment(orderInfo.getComments()));
		shippingInfoDto.setPlatformOrderTime(TimeUtil.toSeconds(orderInfo.getCreateTime()).orElse(DEFAULT_TIMESTAMP_IN_SECONDS));
		shippingInfoDto.setPrebook(decideIsPrebook(orderInfo) == PRE_BOOK_ORDER);
		tRequest.setShippingInfo(shippingInfoDto);

		ShippingGoodsDto shippingGoodsDto = new ShippingGoodsDto();
		shippingGoodsDto.setGoodsItems(orderInfo.getGoodsList().stream()
				.map(it -> {
					GoodsItemDto goodsItemDto = new GoodsItemDto();
					goodsItemDto.setGoodName(it.getName());
					goodsItemDto.setGoodPrice(MoneyUtil.fromCentToYuan(it.getSinglePrice()).orElse(DEFAULT_AMOUNT));
					goodsItemDto.setGoodCount(it.getQuantity());
					goodsItemDto.setGoodUnit(it.getSellUnit());
					return goodsItemDto;
				}).collect(Collectors.toList()));
		shippingGoodsDto.setWeight(orderInfo.getGoodsTotalWeight());
		tRequest.setShippingGoods(shippingGoodsDto);

		return tRequest;
	}

	/**
	 * 构建自动发单的 ULAP 发单请求.
	 *
	 * @param deliveryPoi 门店配置
	 * @param orderInfo   订单信息
	 * @return 调用 ULAP 的发单请求
	 */
	private CreateOrderRequest buildUlapAutoCreateOrderRequest(DeliveryPoi deliveryPoi, OrderInfo orderInfo) {
		CreateOrderRequest tRequest = buildUlapCreateOrderBasicRequest(deliveryPoi, orderInfo);
		// 自动发单，加上策略 ID
		tRequest.getShippingInfo().setStrategyId(((AggrDeliveryPoi) deliveryPoi).getAutoLaunchStrategyId());

		return tRequest;
	}

	/**
	 * 构建手动发单的 ULAP 发单请求.
	 *
	 * @param deliveryPoi   门店配置
	 * @param orderInfo     订单信息
	 * @param deliveryOrder 配送单信息
	 * @return 调用 ULAP 的发单请求
	 */
	private CreateOrderRequest buildUlapConfirmCreateOrderRequest(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannel) {
		CreateOrderRequest tRequest = buildUlapCreateOrderBasicRequest(deliveryPoi, orderInfo);
		// 手动确认发单，加上配送商信息、服务包、配送费
		tRequest.getShippingInfo().setCarrierCode(String.valueOf(deliveryChannel.getCarrierCode()));
		tRequest.getShippingInfo().setCarrierName(deliveryChannel.getCarrierName());
		tRequest.setDeliveryWayType(Integer.parseInt(deliveryOrder.getChannelServicePackageCode()));
		tRequest.getPreviewDto().setPoiShippingFee(deliveryOrder.getDeliveryFee().doubleValue());

		return tRequest;
	}

	private int parse2SourceIdInUlap(Integer orderBizType) {
		Integer channelId = DynamicOrderBizType.orderBizTypeValue2ChannelId(orderBizType);
		return ObjectUtils.defaultIfNull(channelId, 0);
	}

	private AggregationOrderSourceEnum convert2AggregationOrderSourceEnum(Integer orderBizType) {
		DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(orderBizType);
		if (Objects.isNull(orderBizTypeEnum)) {
			return AggregationOrderSourceEnum.OTHER;
		}

		if (DynamicOrderBizType.ELE_ME.equals(orderBizTypeEnum)) {
			return AggregationOrderSourceEnum.ELEM;
		}
		if (DynamicOrderBizType.MEITUAN_WAIMAI.equals(orderBizTypeEnum)) {
			return AggregationOrderSourceEnum.MEITUAN;
		}
		if (DynamicOrderBizType.JING_DONG.equals(orderBizTypeEnum)) {
			return AggregationOrderSourceEnum.JDDJ;
		}
		return AggregationOrderSourceEnum.OTHER;
	}

	/**
	 * 构造接收者参数
	 *
	 * @param orderInfo 订单信息
	 * @return 订单接收者
	 */
	private AddressDto buildRecipient(OrderInfo orderInfo) {
		AddressDto addressDto = new AddressDto();
		addressDto.setAddressType(AddressTypeEnum.Customer);
		String receiverName = orderInfo.getReceiver().getReceiverName();
		addressDto.setName(receiverName);
		String addressDetail = orderInfo.getReceiver().getReceiverAddress().getAddressDetail();
		addressDto.setAddress(addressDetail);
		String receiverPhone = orderInfo.getReceiver().getReceiverPhone();
		addressDto.setPhone(receiverPhone);
		Integer lng = CoordinateUtil.translateToIntStyle(orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint().getLongitude());
		addressDto.setLng(lng);
		Integer lat = CoordinateUtil.translateToIntStyle(orderInfo.getReceiver().getReceiverAddress().getCoordinatePoint().getLatitude());
		addressDto.setLat(lat);
		return addressDto;
	}

	private enum LogisticOrderExceptionEnum {
		LOGISTIC_ORDER_EXISTED(100000013, "已存在正在进行中的单"),
		LOGISTIC_ORDER_CANCELLED(100000011, "物流单已经取消");

		private final int code;
		private final String desc;

		LogisticOrderExceptionEnum(int code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public int getCode() {
			return code;
		}
	}

}
