package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChangeNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.TransferOperateInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.TurnToSelfDeliveryDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformUrlInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.OpenAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.dto.TDeliveryPlatformUrl;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request.OpenTurnToAggregationDeliveryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.request.QueryAggStoreConfigUrlRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.open.response.QueryAggStoreConfigUrlResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.CreateAggregationDeliveryPoiRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.CreateAggregationDeliveryPoiResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.TurnToAggregationDeliveryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferOrderSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.DapDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryConfigApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiFactory;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.ChannelStoreQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.QUERY_POI_FAILED;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @date 2023-08-29
 * @email <EMAIL>
 */
@Slf4j
@Service
public class OpenAggDeliveryThriftServiceImpl implements OpenAggDeliveryThriftService {

    @Resource
    private DapDeliveryPlatformClient dapDeliveryPlatformClient;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private TurnToSelfDeliveryDomainService turnToSelfDeliveryDomainService;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private OrderSystemClient orderSystemClient;
    @Resource
    private DeliveryConfigApplicationService deliveryConfigApplicationService;

    @Resource
    private DeliveryPlatformClient deliveryPlatformClient;

    @Resource
    private DeliveryPoiFactory deliveryPoiFactory;

    @Resource
    private TenantSystemClient tenantSystemClient;

    @Resource
    private DeliveryChangeNotifyService deliveryChangeNotifyService;

    @Resource
    private TransferOrderSquirrelOperateService transferOrderSquirrelOperateService;

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    public QueryAggStoreConfigUrlResponse queryAggStoreConfigUrlInfo(QueryAggStoreConfigUrlRequest request) {
        Optional<DeliveryPoi> deliveryPoiOpt = deliveryPoiRepository.queryDeliveryPoi(request.getTenantId(), request.getPoiId());
        if (!deliveryPoiOpt.isPresent()) {
            return new QueryAggStoreConfigUrlResponse(
                    new Status(QUERY_POI_FAILED.getCode(), "没有对应的配送门店"), null
            );
        }
        Optional<Map<String, DeliveryPlatformUrlInfo>> mapOpt;
        DeliveryPoi deliveryPoi = deliveryPoiOpt.get();
        if (LionConfigUtils.isSwitchToTokenDapUrl()) {
            mapOpt = dapDeliveryPlatformClient.queryLinkInfoOfToken(deliveryPoi, Lists.newArrayList(String.valueOf(request.getPoiId())), LinkTypeEnum.SHOP_LINK_TYPE, request.getToken(), SiteTypeEnum.of(request.getDeviceType()));
        } else {
            mapOpt = dapDeliveryPlatformClient.queryLinkInfo(deliveryPoi, Lists.newArrayList(String.valueOf(request.getPoiId())), LinkTypeEnum.SHOP_LINK_TYPE);
        }

        DeliveryPlatformUrlInfo deliveryPlatformUrlInfo = null;
        if (mapOpt.isPresent()) {
            Map<String, DeliveryPlatformUrlInfo> poiIdUrlMap = mapOpt.get();
            if (poiIdUrlMap.containsKey(String.valueOf(request.getPoiId()))) {
                deliveryPlatformUrlInfo = poiIdUrlMap.get(String.valueOf(request.getPoiId()));
            }
        }
        if (Objects.isNull(deliveryPlatformUrlInfo)) {
            return new QueryAggStoreConfigUrlResponse(
                    new Status(SYSTEM_ERROR.getCode(), "未查询到对应门店设置url"), null
            );
        }

        return new QueryAggStoreConfigUrlResponse(
                Status.SUCCESS, new TDeliveryPlatformUrl(deliveryPlatformUrlInfo.getLinkTitle(), deliveryPlatformUrlInfo.getLinkUrl())
        );
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public TurnToAggregationDeliveryResponse turnToAggregationDelivery(OpenTurnToAggregationDeliveryRequest request) {
        request.validate();

        if (!MccConfigUtils.isDHAggDeliveryGrayStore(request.getPoiId())) {
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "非灰度门店不能转配送"));
        }

        DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(request.getDeliveryPlatformId());
        if (deliveryPlatform == null) {
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "不支持的配送渠道"));
        }

        //查询订单信息
        Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(request.getTenantId(), request.getPoiId(), request.getChannelOrderId(), request.getChannelId(), false);
        if (orderQueryResult.isFail()) {
            return new TurnToAggregationDeliveryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
        }
        //校验订单是否可以转三方配送
        OrderInfo orderInfo = orderQueryResult.getInfo();
        if (!orderInfo.canTurnToSelfDelivery()) {
            log.warn("订单[{}]不满足发起配送条件，转配送失败", orderInfo);
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.ORDER_STATUS_ERROR.getCode(), FailureCodeEnum.ORDER_STATUS_ERROR.getMessage()));
        }

        //最多转两次,即自配 -> 三方 -> 自配，最多有两个自配单
        //NOTE:不能用三方单来看，一次三方单可能有多个
        List<DeliveryOrder> deliveryOrders = deliveryOrderRepository.getDeliveryOrders(new OrderKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getWarehouseId(), orderInfo.getOrderKey().getOrderId()));
        int selfDeliveryCount = (int) deliveryOrders.stream().filter(deliveryOrder -> Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())).count();
        if (selfDeliveryCount >= 2) {
            return new TurnToAggregationDeliveryResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), "最多支持转两次配送方式"));
        }


        //将配送平台保存在缓存中
        String key = transferOrderSquirrelOperateService.getKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
        transferOrderSquirrelOperateService.set(key,new TransferOperateInfo(deliveryPlatform, request.getOperatorId(), request.getOperatorName()));

        //查门店
        Optional<DeliveryPoi> deliveryPoiOpt = deliveryPoiRepository.queryDeliveryPoi(request.getTenantId(), orderInfo.getWarehouseId());
        if (!deliveryPoiOpt.isPresent()) {
            return new TurnToAggregationDeliveryResponse(
                    new Status(QUERY_POI_FAILED.getCode(), "没有对应的配送门店")
            );
        }
        DeliveryPoi deliveryPoi = deliveryPoiOpt.get();

        Optional<Failure> failure = turnToSelfDeliveryDomainService.turnToAggregationDelivery(deliveryPoi, orderInfo, deliveryPlatform, new Operator(request.getOperatorId(), request.getOperatorName()));


        return failure
                .map(value -> new TurnToAggregationDeliveryResponse(new Status(value.getFailureCode(), value.getFailureMessage())))
                .orElseGet(() -> new TurnToAggregationDeliveryResponse(Status.SUCCESS));
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public CreateAggregationDeliveryPoiResponse createAggregationDeliveryPoi(CreateAggregationDeliveryPoiRequest request) {
        //参数校验
        String checkMsg = request.validate();
        if (StringUtils.isNotBlank(checkMsg)) {
            return new CreateAggregationDeliveryPoiResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), checkMsg));
        }

        //生成新的聚合配送门店配置
        DeliveryPoi deliveryPoi = buildDeliveryPoiConfig(request);

        //调用渠道,创建聚合配送门店
        log.info("createShop deliveryPoi:{}", deliveryPoi);
        deliveryPlatformClient.createShop(deliveryPoi);

        //快速授权跑腿
        fastAuth(deliveryPoi);

        return new CreateAggregationDeliveryPoiResponse(Status.SUCCESS);
    }

    private DeliveryPoi buildDeliveryPoiConfig(CreateAggregationDeliveryPoiRequest request) {
        //检查之前是否已有门店配置
        Optional<DeliveryPoi> selfDeliveryDeliveryPoi =
                deliveryPoiRepository.queryDeliveryPoi(request.getTenantId(), request.getStoreId());

        if (!selfDeliveryDeliveryPoi.isPresent()) {
            throw new BizException("门店配送配置不存在");
        }

        //创建新的配送平台门店配置
        return deliveryPoiFactory.createNewPlatformPoiByExisting(selfDeliveryDeliveryPoi.get(),
                        DeliveryPlatformEnum.enumOf(request.getPlatformCode()));
    }

    //快捷授权跑腿，失败会导致不能发跑腿
    private void fastAuth(DeliveryPoi deliveryPoi) {
        try {
            //必须是青云平台
            if (deliveryPoi.getDeliveryPlatform() != DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM) {
                return;
            }

            ChannelStoreQueryResult result = tenantSystemClient
                    .queryChannelStoreDetailInfo(deliveryPoi.getTenantId(), deliveryPoi.getStoreId());
            if (result == null || CollectionUtils.isEmpty(result.getStoreInfos())) {
                return;
            }
            List<TenantChannelStoreInfo> channelStoreList = result.getStoreInfos().stream()
                    .filter(channelStoreInfo -> Objects.equals(channelStoreInfo.getChannelId(), ChannelTypeEnum.MT_MEDICINE.getValue())
                            || Objects.equals(channelStoreInfo.getChannelId(), ChannelTypeEnum.MT_DRUNK_HORSE.getValue()))
                    .collect(Collectors.toList());

            List<Long> wmPoiIdList = channelStoreList.stream()
                    .map(TenantChannelStoreInfo::getChannelPoiId)
                    .map(Long::getLong)
                    .collect(Collectors.toList());

            //重试
            RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(3, 100);
            Optional<Failure> failure = retryTemplate.execute(new RetryCallback<Optional<Failure>, Exception>() {
                @Override
                public Optional<Failure> doWithRetry(RetryContext retryContext) {
                    return deliveryPlatformClient.deliveryFastAuth(deliveryPoi, new ArrayList<>(wmPoiIdList),new ArrayList<>());
                }
            });

            if (failure.isPresent()) {
                throw new BizException(failure.get().getFailureMessage());
            }
        } catch (Exception e) {
            log.warn("青云配送平台快捷授权失败, poiId:{}", deliveryPoi.getStoreId(), e);
            Cat.logEvent("DH_ADAPT_DAP", "AUTH_FAST_FAIL");
        }

    }
}
