package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;


import com.dianping.lion.client.Lion;
import com.dianping.pigeon.util.CollectionUtils;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableSet;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryOperationApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCallbackTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.PickSelectRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryCancelCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.ClearDeliveryExceptionMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.PlatformDeliveryCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryChangeCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.MedicineTenantWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.model.config.TenantBizModeConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.remind.DeliveryRemindConfigRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @date 2019/10/10
 * desc: 订单状态消息（关注商户接单及订单取消状态）Listener
 */
@Slf4j
@Component
@Order()
@SuppressWarnings("rawtypes")
public class OrderStatusChangeMessageListener extends AbstractLaunchDeliveryListener {

	private static final Set<Integer> EMPOWER_ORDER_SOURCE = ImmutableSet.of(
			OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), OrderSourceEnum.GLORY.getValue()
	);

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryProcessApplicationService deliveryProcessApplicationService;
	@Resource
	private MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> deliveryDelayLaunchMessageProducer;
	@Resource
	private MafkaMessageProducer<DeliveryCancelCommandMessage> deliveryCancelMessageProducer;
	@Resource
	private DeliveryOperationApplicationService deliveryOperationApplicationService;
	@Resource
	private MafkaDelayMessageProducer<ClearDeliveryExceptionMsg> clearDeliveryExceptionProducer;
	@Resource
	private MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> deliveryTimeOutCheckProducer;
	@Resource
	private DeliveryMonitorDomainService deliveryMonitorDomainService;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;
	@Resource
	private DeliveryRemindConfigRepository deliveryRemindConfigRepository;

	@Resource
	private TenantRemoteService tenantRemoteService;

	@Resource
	private PickSelectRemoteService pickSelectRemoteService;

	@Resource
	private MedicineTenantWrapper medicineTenantWrapper;

	@Resource
	private MafkaMessageProducer<OrderStatusChangeMessageListener.MessageDTO> deliveryOrderStatusDelayCheckMessageProducer;
	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.ORDER_STATUS_CHANGE;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		MessageDTO message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		if (MccConfigUtils.orderStatusConsumeFilterDrunkHorseSwitch()
				&& com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(message.getTenantId())) {
			//log.info("过滤歪马订单:{}", message.getOrderId());
			return CONSUME_SUCCESS;
		}
		//隔离医药租户
		if (medicineTenantWrapper.isUwmsMedicineTenant(message.getTenantId())) {
			return CONSUME_SUCCESS;
		}

		if(MccConfigUtils.checkUseOFCTenantIdList(message.getTenantId())){
			if(MccConfigUtils.checkDelayCheckTenantIdList(message.getTenantId())){
				deliveryOrderStatusDelayCheckMessageProducer.sendMessage(message,message.getOrderId());
			}
			return CONSUME_SUCCESS;
		}

		log.info("开始消费赋能订单状态变更消息：{}", mafkaMessage);
		return handle(message,mafkaMessage);
	}

	public ConsumeStatus handle(MessageDTO message,MafkaMessage mafkaMessage){
		//校验补单 只针对非歪马
		try {
			if(checkCompensate(message)){
				log.info("补单过滤条件满足，不进行发配送操作:{}",mafkaMessage);
				return CONSUME_SUCCESS;
			}
		}catch (Exception e){
			log.error("check compensate error , mafkaMessage:{}",mafkaMessage,e);
		}
		Long shopId = message.getShopId();
		if(!com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDHTenantIdList().contains(message.getTenantId().toString())) {
			if(message.getWarehouseId() != null){
				shopId = message.getWarehouseId();
			}
		}

		if (message.isOnlineOrder()) {
			try {
				OrderKey orderKey = new OrderKey(message.getTenantId(), shopId, message.getOrderId());
				switch (OrderStatusEnum.enumOf(message.getStatus())) {
					case SUBMIT:
						if(MccConfigUtils.getOrderPaidLaunchDeliverySwitch()!=1){
							return CONSUME_SUCCESS;
						}
						return tryLaunchDelivery(mafkaMessage, orderKey, ImmediateOrderDeliveryLaunchPointEnum.ORDER_PAID,
								BookingOrderDeliveryLaunchPointEnum.ORDER_PAID);
					case MERCHANT_CONFIRMED:
						if (!message.isDelayPush() && EMPOWER_ORDER_SOURCE.contains(message.getOrderSource()) && message.getSourceStatus() != OrderStatusEnum.SUBMIT.getValue()) {
							return CONSUME_SUCCESS;
						}
						triggerExceptionClearMonitor(orderKey);

						return tryLaunchDelivery(mafkaMessage, orderKey, ImmediateOrderDeliveryLaunchPointEnum.MERCHANT_ACCEPT,
								BookingOrderDeliveryLaunchPointEnum.BEFORE_DELIVERY);

					case CANCELED:
						List<DeliveryOrder> openDeliveryOrders = new ArrayList<>();
						if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch()){
							if (com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryCancelFilterSwitch()) {
								// true 过滤
								openDeliveryOrders = deliveryOrderRepository.getOpenDeliveryOrdersWithRoute(orderKey);
							} else {
								openDeliveryOrders = deliveryOrderRepository.getDeliveryOrdersWithRoute(orderKey);
							}
						}else {
							openDeliveryOrders = deliveryOrderRepository.getOpenDeliveryOrders(orderKey);
						}
						if (CollectionUtils.isEmpty(openDeliveryOrders)) {
							return CONSUME_SUCCESS;
						}

						// 按照时间排序，取最新的一条记录
						DeliveryOrder filterActiveDeliveryOrder = DeliveryOrder.filterActiveDeliveryOrder(openDeliveryOrders);
						if (filterActiveDeliveryOrder == null) {
							return CONSUME_SUCCESS;
						}
						deliveryCancelMessageProducer.sendMessage(new DeliveryCancelCommandMessage(filterActiveDeliveryOrder));
						return CONSUME_SUCCESS;

					case COMPLETED:
						List<DeliveryOrder> deliveryOrders = new ArrayList<>();
						if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch()){
							deliveryOrders = deliveryOrderRepository.getOpenDeliveryOrdersWithRoute(orderKey);
						}else {
							deliveryOrders = deliveryOrderRepository.getOpenDeliveryOrders(orderKey);
						}
						for (DeliveryOrder deliveryOrder : deliveryOrders) {
							if (isSelfDelivery(deliveryOrder.getDeliveryChannel())) {
								deliveryUnifiedCallbackMessageProducer.sendMessage(
										new DeliveryUnifiedCallbackMessage(
												DeliveryCallbackTypeEnum.CHANGE_CALLBACK,
												new DeliveryChangeCallbackInfo(
														deliveryOrder.getOrderId(),
														deliveryOrder.getDeliveryChannel(),
														deliveryOrder.getChannelDeliveryId(),
														deliveryOrder.getChannelServicePackageCode(),
														DeliveryEventEnum.FINISH_BY_ORDER_DONE,
														DeliveryExceptionInfo.NO_EXCEPTION,
														LocalDateTime.now(),
														deliveryOrder.getRiderInfo(),
														null,
														null
												)
										),
										deliveryOrder.getOrderId()
								);
							}
						}
						return CONSUME_SUCCESS;

					default:
						log.info("其它状态订单通知，message:{}", message);
				}

			} catch (Exception e) {
				log.warn("处理订单状态变更失败， message={}", message, e);
				return ConsumeStatus.CONSUME_FAILURE;
			}
		}
		return CONSUME_SUCCESS;
	}

	private void triggerExceptionClearMonitor(OrderKey orderKey) {
		if (Lion.getConfigRepository().getBooleanValue("delivery.clear.exception.fallback", false)) {
			return;
		}
		try {
			Result<OrderInfo> orderInfoQueryResult = getOrderSystemClient().getOrderInfo(orderKey, false);
			if (orderInfoQueryResult.isFail()) {
				log.error("查询订单[{}]异常，将放弃清除配送异常. failure:{}", orderKey, orderInfoQueryResult.getFailure());
				return;
			}
			LocalDateTime estimatedDeliveryTime = orderInfoQueryResult.getInfo().getEstimatedDeliveryTime();
			long delayMillis = MccConfigUtils.getDeliveryExceptionClearDelayMillis(estimatedDeliveryTime);
			if (delayMillis > 5000) {
				clearDeliveryExceptionProducer.sendDelayMessageInMillis(new ClearDeliveryExceptionMsg(orderKey, TimeUtil.toMilliSeconds(estimatedDeliveryTime)), delayMillis);
			}
		} catch (Exception e) {
			log.error("triggerExceptionClearMonitor exception", e);
		}
	}

	/**
	 * 校验补单状态
	 * @param messageDTO
	 * @return false 继续走发单流程   true 不继续走发单流程
	 */
	private boolean checkCompensate(MessageDTO messageDTO){

		//非补单 不进行过滤
		if(messageDTO.getCompensate()==null || (!messageDTO.getCompensate())){
			return false;
		}
		//配送状态为空 不进行过滤
		if(messageDTO.getOnlineDistributeStatus()==null){
			return false;
		}

		//业务线为歪马 不进行过滤
		TenantBizModeConfig tenantBizModeConfig = tenantRemoteService.queryTenantBizModeConfig(messageDTO.getTenantId());
		if(tenantBizModeConfig==null){
			return false;
		}
		if(TenantBusinessModeEnum.WAI_MA.getKey().equals(tenantBizModeConfig.getBizMode())){
			return false;
		}
		//配送状态为配置的状态 进行过滤
		return MccConfigUtils.getCompensateNotDeliveryStatusList().contains(messageDTO.getOnlineDistributeStatus()+"");
	}

	@Override
	protected OrderSystemClient getOrderSystemClient() {
		return orderSystemClient;
	}

	@Override
	protected DeliveryProcessApplicationService getDeliveryProcessApplicationService() {
		return deliveryProcessApplicationService;
	}

	@Override
	protected MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> getDeliveryDelayLaunchMessageProducer() {
		return deliveryDelayLaunchMessageProducer;
	}

	@Override
	protected DeliveryOperationApplicationService getDeliveryOperationApplicationService() {
		return deliveryOperationApplicationService;
	}

	@Override
	protected MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> getDeliveryTimeOutCheckProducer() {
		return deliveryTimeOutCheckProducer;
	}

	@Override
	protected DeliveryMonitorDomainService getDeliveryMonitorDomainService() {
		return deliveryMonitorDomainService;
	}

	@Override
	protected DeliveryPoiRepository getDeliveryPoiRepository() {
		return deliveryPoiRepository;
	}

	@Override
	protected DeliveryOrderRepository getDeliveryOrderRepository() {
		return deliveryOrderRepository;
	}

	@Override
	protected DeliveryRemindConfigRepository getDeliveryRemindConfigRepository() {
		return deliveryRemindConfigRepository;
	}

	@Override
	protected PickSelectRemoteService getPickSelectRemoteService() {
		return pickSelectRemoteService;
	}

	@Override
	protected  TenantRemoteService getTenantRemoteService(){
		return tenantRemoteService;
	}

	private MessageDTO translateMessage(MafkaMessage mafkaMessage) {
		try {
			MessageDTO message = translateMessageWithBodyHolder(mafkaMessage, MessageDTO.class);

			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
			Preconditions.checkNotNull(message.getShopId(), "shopId is null");
			Preconditions.checkNotNull(message.getOrderId(), "orderId is null");
			Preconditions.checkNotNull(message.getStatus(), "status is null");
			Preconditions.checkNotNull(message.getOrderSource(), "orderSource is null");
			Preconditions.checkNotNull(message.getSourceStatus(), "sourceStatus is null");
			return message;
		} catch (Exception e) {
			log.error("解析订单状态变更消息失败:{}", mafkaMessage, e);
			return null;
		}
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class MessageDTO {
		private Long tenantId;
		private Long shopId;
		private Long orderId;
		private Integer orderSource;
		private Integer status;
		private Integer sourceStatus;
		private Long warehouseId;
		/**
		 * 补单标识
		 */
		private Boolean compensate;
		/**
		 * 线上最新的配送状态 DistributeStatusEnum
		 */
		private Integer onlineDistributeStatus;

		/**
		 * 标记qnh侧jddj/elm订单延迟下发
		 */
		private boolean delayPush;

		public boolean isOnlineOrder() {
			return Objects.equals(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), orderSource)
					|| Objects.equals(OrderSourceEnum.OTO_ONLINE.getValue(), orderSource)
					|| Objects.equals(OrderSourceEnum.GLORY.getValue(), orderSource);
		}
	}

	/**
	 * 判断配送渠道是否为商家自配送渠道.
	 *
	 * @param deliveryChannel 配送渠道
	 * @return 是否为商家自配送渠道
	 */
	private boolean isSelfDelivery(Integer deliveryChannel) {
		return deliveryChannel == DeliveryChannelEnum.MERCHANT_DELIVERY.getCode() ||
				deliveryChannel == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode();
	}
}
