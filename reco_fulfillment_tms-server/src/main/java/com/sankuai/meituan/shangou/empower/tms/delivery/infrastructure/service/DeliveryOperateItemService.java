package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.BoolTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelConfigEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelDetailDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigContentDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryOperateItemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryOperateItemEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.PlatformDeliveryCheckSquirrelService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository.MySQLDeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.wrapper.TenantChannelThriftServiceWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeliveryOperateItemService implements DeliveryOperateItemClient {

    private static final List<OrderStatusEnum> NOT_SHOW_DELIVERY_ITEM_ORDER_STATUS = Arrays.asList(OrderStatusEnum.CANCELED,OrderStatusEnum.SUBMIT,OrderStatusEnum.PAYING,OrderStatusEnum.CLOSED,OrderStatusEnum.COMPLETED);

    private static final List<Integer> DISTRIBUTE_TYPE_PLATFORM_LIST = Arrays.asList(DistributeTypeEnum.ZONG_BAO.getValue(),DistributeTypeEnum.KUAI_SONG.getValue(),DistributeTypeEnum.ZHUAN_SONG.getValue(),DistributeTypeEnum.COMBO.getValue());

    private static final Integer PRIVATE_DOMAIN_CHANNEL = 0;

    private static final List<DynamicChannelType> SUPPORT_RECALL_DELIVER_CHANNEL_TYPE_LIST = Arrays.asList(DynamicChannelType.ELEM, DynamicChannelType.JD2HOME);

    @Autowired
    private OrderSystemClient orderSystemClient;

    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;

    @Autowired
    private MySQLDeliveryPoiRepository mySQLDeliveryPoiRepository;

    @Autowired
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private TenantChannelThriftServiceWrapper tenantChannelThriftServiceWrapper;

    @Resource
    private PlatformDeliveryCheckSquirrelService platformDeliveryCheckSquirrelService;

    @MethodLog(logRequest = false, logResponse = true)
    public Map<Long, DeliveryOperateItem> queryOperateItem(Long tenantId, Long storeId, List<Long> orderIdList){
        if(CollectionUtils.isEmpty(orderIdList)){
            return Collections.emptyMap();
        }
        List<OrderInfo> orderInfoList = orderSystemClient.batchQueryOrderByEs(tenantId,orderIdList);
        if(CollectionUtils.isEmpty(orderInfoList)){
            return Collections.emptyMap();
        }

        //查询私域渠道配置
        Map<Integer, ChannelDetailDto> channelConfigMap = queryPrivateDomainChannelConfig(orderInfoList);
        List<DeliveryOrder> deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdListWithTenant(orderIdList,tenantId,storeId);
        Map<Long,DeliveryOrder> deliveryOrderMap=getDeliveryOrderActiveMap(deliveryOrderList);
        List<DeliveryPoi> deliveryPoiList = mySQLDeliveryPoiRepository.queryAllDeliveryPoi(tenantId,storeId);
        Set<Integer> platformCodeSet=new HashSet<>();
        if(CollectionUtils.isNotEmpty(deliveryPoiList)){
            deliveryPoiList.forEach(k->{
                if(k.getDeliveryPlatform() != null && k.getDeliveryPlatform() != DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM){
                    platformCodeSet.add(k.getDeliveryPlatform().getCode());
                }
            });
        }

        Map<Long, Integer> deliveryPlatFormMap = getDeliveryOrderDeliveryPlatFormMap(orderInfoList, deliveryOrderMap);
        Map<Long,DeliveryOperateItem> operateItemMap = new HashMap<>();
        for (OrderInfo orderInfo : orderInfoList){
            if(orderInfo.getDeliveryMethod() != null && orderInfo.getDeliveryMethod() == DistributeMethodEnum.STORE_DELIVERY.getValue()){
                continue;
            }
            if(orderInfo.getOrderTransInfo()!=null && !Objects.equals(orderInfo.getOrderTransInfo().getDispatchShopId(),storeId)){
                continue;
            }
            DeliveryOrder deliveryOrder = deliveryOrderMap.get(orderInfo.getOrderKey().getOrderId());
            Set<Integer> operateItemCodeSet = new HashSet<>();
            Integer deliveryPlatFormCode = MapUtils.isNotEmpty(deliveryPlatFormMap) && Objects.nonNull(deliveryOrder) ? deliveryPlatFormMap.get(deliveryOrder.getId()) : null;
            if (Objects.equals(DeliveryPlatformEnum.OTHER_SELF_DELIVERY_PLATFORM.getCode(), deliveryPlatFormCode)) {
                continue;
            }
            checkSelfOperateItem(orderInfo,deliveryOrder,platformCodeSet,operateItemCodeSet, deliveryPlatFormCode, channelConfigMap);
            checkTipOperateItem(orderInfo,deliveryOrder,operateItemCodeSet, deliveryPlatFormCode);
            checkCancelOperateItem(orderInfo,deliveryOrder,operateItemCodeSet, deliveryPlatFormCode);
            checkRecallOperateItem(orderInfo,deliveryOrder,operateItemCodeSet);
            checkExceptionReportOperateItem(orderInfo,deliveryOrder,operateItemCodeSet);
            checkManualLaunchOperateItem(orderInfo, deliveryOrder, deliveryPoiList, operateItemCodeSet);
            checkExceptionRecallOperateItem(orderInfo, deliveryOrder, deliveryPoiList, operateItemCodeSet);
            checkFourWheelDeliveryOperateItem(orderInfo, deliveryOrder, operateItemCodeSet, deliveryPlatFormCode);
            if(CollectionUtils.isNotEmpty(operateItemCodeSet)){
                operateItemMap.put(orderInfo.getOrderKey().getOrderId(),new DeliveryOperateItem(new ArrayList<>(operateItemCodeSet)));
            }
        }
        return operateItemMap;
    }

    private void checkFourWheelDeliveryOperateItem(OrderInfo orderInfo, DeliveryOrder deliveryOrder, Set<Integer> operateItemCodeSet, Integer deliveryPlatFormCode) {

        try {
            //  订单状态
            if(!checkOrderStatus(orderInfo)){
                return;
            }
            if(!MccConfigUtils.isFourWheelDeliveryGrayStore(orderInfo.getWarehouseId())){
                return;
            }
            // 非自提单
            if (orderInfo.getDeliveryMethod() != null && !orderInfo.getDeliveryMethod().equals(DistributeMethodEnum.HOME_DELIVERY.getValue())) {
                return;
            }
            DistributeStatusEnum distributeStatusEnum = DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
            if (Objects.nonNull(orderInfo.getDistributeStatus())) {
                distributeStatusEnum = DistributeStatusEnum.enumOf(orderInfo.getDistributeStatus());
            }
            // 骑手未接单
            if(distributeStatusEnum != DistributeStatusEnum.DISTRIBUTE_UNKNOWN && distributeStatusEnum != DistributeStatusEnum.UN_KNOWN && distributeStatusEnum != DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER){
                return;
            }
            DeliveryPlatformEnum deliveryPlatformEnum = null;
            if(deliveryOrder!=null && deliveryOrder.getDeliveryChannel()!=null && Objects.nonNull(deliveryPlatFormCode)){
                deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatFormCode);
            }
            // 平台配送
            if(deliveryOrder==null || deliveryOrder.getDeliveryChannel()==null || deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() || deliveryPlatformEnum == DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM){
                DynamicChannelType orderChannel = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
                // 美团
                if(orderChannel != DynamicChannelType.MEITUAN){
                    return;
                }

                // 企客
                if(Objects.isNull(orderInfo.getOriginalDistributeType()) || !MccConfigUtils.getFourWheelDistributeCodeList().contains(""+orderInfo.getOriginalDistributeType())){
                    return;
                }

                if(Objects.nonNull(deliveryOrder)){
                    if(deliveryOrder.getStatus().riderHasAccept() || deliveryOrder.getStatus().isFinalStatus()){
                        return;
                    }
                }

                Integer canPushFourWheel = platformDeliveryCheckSquirrelService.get(String.valueOf(orderInfo.getOrderKey().getOrderId()), FourWheelCheckInfo.class)
                        .map(FourWheelCheckInfo::getCanPushFourWheel)
                        .orElse(null);
                // 满足汽车配送条件
                if(Objects.isNull(canPushFourWheel) || !Objects.equals(canPushFourWheel, BoolTypeEnum.YES.getValue())){
                    return;
                }

                // 已经发了汽车配送
                if (Objects.nonNull(deliveryOrder) && Objects.equals(deliveryOrder.getIsFourWheelDelivery(), 2)) {
                    if(!Objects.equals(deliveryOrder.getIsManual(),2)){
                        operateItemCodeSet.add(DeliveryOperateItemEnum.APPEND_FOUR_WHEEL_DELIVERY_TYPE.getCode());
                    }
                } else {
                    operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_FOUR_WHEEL_DELIVERY.getCode());
                }
            }
        }catch (Exception e){
         log.error("checkFourWheelDeliveryOperateItem error",e);
        }

    }

    private Map<Integer, ChannelDetailDto> queryPrivateDomainChannelConfig(List<OrderInfo> orderInfoList){
        //查询私域渠道配置
        Set<DynamicChannelType> dynamicChannelTypes = orderInfoList.stream().map(OrderInfo::getOrderBizType).map(DynamicOrderBizType::orderBizTypeValue2ChannelType).collect(Collectors.toSet());
        List<Integer> channelIds = dynamicChannelTypes.stream().filter(channelType -> channelType.getChannelStandard().equals(PRIVATE_DOMAIN_CHANNEL)).map(DynamicChannelType::getChannelId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(channelIds)) {
            return new HashMap<>();
        }
        return tenantChannelThriftServiceWrapper.batchQueryChannelConfig(channelIds);
    }

    public void checkExceptionReportOperateItem(OrderInfo orderInfo,DeliveryOrder deliveryOrder,Set<Integer> operateItemCodeSet){
        DynamicChannelType orderChannel = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        if (!Objects.equals(orderChannel, DynamicChannelType.JD2HOME)) {
            return;
        }
        if(deliveryOrder == null || deliveryOrder.getDeliveryExceptionCode()==null){
            return;
        }
        if(deliveryOrder.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode()){
            operateItemCodeSet.add(DeliveryOperateItemEnum.REPORT_EXCEPTION.getCode());
        }
        if(deliveryOrder.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode()){
            operateItemCodeSet.add(DeliveryOperateItemEnum.AUDIT_EXCEPTION.getCode());
        }
    }

    public void checkRecallOperateItem(OrderInfo orderInfo,DeliveryOrder deliveryOrder,Set<Integer> operateItemCodeSet){
        DynamicChannelType orderChannel = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        if (!SUPPORT_RECALL_DELIVER_CHANNEL_TYPE_LIST.contains(orderChannel)) {
            return;
        }
        if(!checkOrderStatus(orderInfo)){
            return;
        }
        if(deliveryOrder == null || deliveryOrder.getDeliveryExceptionCode()==null){
            return;
        }

        if (DynamicChannelType.ELEM.equals(orderChannel)) {
            checkRecallOperateItem4Elm(deliveryOrder, operateItemCodeSet);
        } else if (DynamicChannelType.JD2HOME.equals(orderChannel)) {
            checkRecallOperateItem4Jd2Home(deliveryOrder, operateItemCodeSet);
        } else {
            log.error("checkRecallOperateItem error, orderChannel not support, orderChannel is {}", orderChannel);
        }
    }

    private void checkRecallOperateItem4Elm(DeliveryOrder deliveryOrder, Set<Integer> operateItemCodeSet) {
        if(deliveryOrder.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.RECALL_RIDER_FAIL.getCode()
                || deliveryOrder.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.RECALL_SELF_RIDER_FAIL.getCode()
                || deliveryOrder.getDeliveryExceptionCode() == DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode()){
            operateItemCodeSet.add(DeliveryOperateItemEnum.RECALL_DELIVERY.getCode());
        }
    }

    private void checkRecallOperateItem4Jd2Home(DeliveryOrder deliveryOrder, Set<Integer> operateItemCodeSet) {
        Integer isRecallDelivery = deliveryOrder.getIsRecallDelivery();
        if (Objects.isNull(isRecallDelivery)) {
            return;
        }
        if (com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getJddjRecallDeliverySwitch()
                && Objects.equals(RecallDeliveryEnum.NEED_RECALL.getCode(), isRecallDelivery)) {
            operateItemCodeSet.add(DeliveryOperateItemEnum.RECALL_DELIVERY.getCode());
        }
    }

    public void checkCancelOperateItem(OrderInfo orderInfo,DeliveryOrder deliveryOrder,Set<Integer> operateItemCodeSet, Integer deliveryPlatFormCode){
        if(!checkParam(orderInfo,deliveryOrder)){
            return;
        }
        DistributeStatusEnum distributeStatusEnum = DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
        if (Objects.nonNull(orderInfo.getDistributeStatus())) {
            distributeStatusEnum = DistributeStatusEnum.enumOf(orderInfo.getDistributeStatus());
        }
        if(distributeStatusEnum != DistributeStatusEnum.DISTRIBUTE_UNKNOWN && distributeStatusEnum != DistributeStatusEnum.UN_KNOWN && distributeStatusEnum != DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER){
            return;
        }
        //锁单中的订单不展示取消配送按钮 不再限制配送平台
        if (orderInfo.getOrderLockLabel() != null && orderInfo.getOrderLockLabel() &&
                orderInfo.getIsLocked() != null && orderInfo.getIsLocked()) {
            return;
        }
        DeliveryPlatformEnum deliveryPlatformEnum = null;
        if(deliveryOrder!=null && deliveryOrder.getDeliveryChannel()!=null && Objects.nonNull(deliveryPlatFormCode)){
            deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatFormCode);
        }
        if(deliveryOrder==null || deliveryOrder.getDeliveryChannel()==null || deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() || deliveryPlatformEnum == DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM){

            DynamicChannelType orderChannel = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
            if (!Objects.equals(orderChannel, DynamicChannelType.MEITUAN)) {
                return;
            }

            //平台配送
            if(orderInfo.getOriginalDistributeType()!=null && DISTRIBUTE_TYPE_PLATFORM_LIST.contains(orderInfo.getOriginalDistributeType())){

                Long time = null;
                Long now =System.currentTimeMillis();
                if(deliveryOrder!=null){
                    time = TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime());
                }
                if (time == null) {
                    // 拿不到时间,取订单创建时间
                    time = TimeUtil.toMilliSeconds(orderInfo.getCreateTime());
                }

                Long subTime=now - time;
                Long showTime= com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getMtCancelDeliveryLimitTime()*60*1000L;
                if(subTime<showTime){
                    return;
                }

                operateItemCodeSet.add(DeliveryOperateItemEnum.CANCEL_DELIVERY.getCode());
            }
        }
    }

    public void checkTipOperateItem(OrderInfo orderInfo,DeliveryOrder deliveryOrder,Set<Integer> operateItemCodeSet, Integer deliveryPlatFormCode){
        if(!checkParam(orderInfo,deliveryOrder)){
            return;
        }
        DistributeStatusEnum distributeStatusEnum = DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
        if (Objects.nonNull(orderInfo.getDistributeStatus())) {
            distributeStatusEnum = DistributeStatusEnum.enumOf(orderInfo.getDistributeStatus());
        }
        if(distributeStatusEnum != DistributeStatusEnum.DISTRIBUTE_UNKNOWN && distributeStatusEnum != DistributeStatusEnum.UN_KNOWN && distributeStatusEnum != DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER){
            return;
        }
        //锁单中的订单不展示加小费按钮 不再限制配送平台
        if (orderInfo.getOrderLockLabel() != null && orderInfo.getOrderLockLabel() &&
                orderInfo.getIsLocked() != null && orderInfo.getIsLocked()) {
            return;
        }
        DeliveryPlatformEnum deliveryPlatformEnum = null;
        if(deliveryOrder!=null && deliveryOrder.getDeliveryChannel()!=null && Objects.nonNull(deliveryPlatFormCode)){
            deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatFormCode);
        }
        if(deliveryOrder==null || deliveryOrder.getDeliveryChannel()==null || deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() || deliveryPlatformEnum == DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM){
            DynamicChannelType orderChannel = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
            if(orderChannel != DynamicChannelType.MEITUAN){
                return;
            }

           //平台配送
           if(orderInfo.getOriginalDistributeType()!=null && orderInfo.getOriginalDistributeType() == DistributeTypeEnum.ZONG_BAO.getValue()){

               operateItemCodeSet.add(DeliveryOperateItemEnum.ADD_TIP_FEE.getCode());
           }
        }
    }

    /**
     * 目前只有抖音平台配送展示手动呼叫平台配送按钮
    */
    public void checkManualLaunchOperateItem(OrderInfo orderInfo, DeliveryOrder deliveryOrder, List<DeliveryPoi> deliveryPoiList, Set<Integer> operateItemCodeSet) {
        DynamicChannelType orderBizType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        if (Objects.isNull(orderBizType) || !orderBizType.equals(DynamicChannelType.DOU_YIN)) {
            return;
        }
        if(deliveryOrder!=null && deliveryOrder.getTransType()!=null && deliveryOrder.getTransType()!=0){
            return;
        }

        Map<Integer, DeliveryPoi> deliveryPoiMap = deliveryPoiList.stream()
                .collect(Collectors.toMap(DeliveryPoi::getChannelType, deliveryPoi -> deliveryPoi));
        DeliveryPoi deliveryPoi = null;
        Integer deliveryPlatformCode = null;

        if (MapUtils.isNotEmpty(deliveryPoiMap)) {
            deliveryPoi = deliveryPoiMap.get(orderBizType.getChannelId());
        }
        if (Objects.nonNull(deliveryPoi)) {
            deliveryPlatformCode = deliveryPoi.getDeliveryPlatform().getCode();
        }

        // 不是牵牛花管理配送，或者是抖音平台配送，才展示手动呼叫配送按钮
        if (Objects.isNull(deliveryPoi) || ((DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM.getCode() != deliveryPlatformCode) && (DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() != deliveryPlatformCode))) {
            return;
        }

        OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderInfo.getOrderStatus());
        if (Objects.isNull(orderStatusEnum) || OrderStatusEnum.CANCELED.equals(orderStatusEnum) || OrderStatusEnum.COMPLETED.equals(orderStatusEnum)) {
            return;
        }

        if (Objects.nonNull(deliveryOrder)) {
            DeliveryStatusEnum deliveryStatusEnum = deliveryOrder.getStatus();
            if (Objects.nonNull(deliveryStatusEnum) && DeliveryStatusEnum.INIT.equals(deliveryStatusEnum)) {
                operateItemCodeSet.add(DeliveryOperateItemEnum.MANUAL_LAUNCH.getCode());
            }
        } else {
            Integer distributeStatus = orderInfo.getDistributeStatus();
            if (Objects.nonNull(distributeStatus) && (DistributeStatusEnum.DISTRIBUTE_UNKNOWN.getValue() == distributeStatus)) {
                operateItemCodeSet.add(DeliveryOperateItemEnum.MANUAL_LAUNCH.getCode());
            }
        }
    }

    /**
     * 目前只有抖音平台配送展示配送异常重新呼叫配送按钮
     */
    public void checkExceptionRecallOperateItem(OrderInfo orderInfo, DeliveryOrder deliveryOrder, List<DeliveryPoi> deliveryPoiList, Set<Integer> operateItemCodeSet) {
        DynamicChannelType orderBizType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        if (Objects.isNull(orderBizType)) {
            return;
        }
        if(deliveryOrder!=null && deliveryOrder.getTransType()!=null && deliveryOrder.getTransType()!=0){
            return;
        }

        Integer deliveryPlatFormCode = null;
        Optional<DeliveryPoi> deliveryPoiOptional = deliveryPoiList.stream().filter(deliveryPoi -> deliveryPoi.getChannelType().equals(orderBizType.getChannelId())).findFirst();
        if (deliveryPoiOptional.isPresent()) {
            deliveryPlatFormCode = deliveryPoiOptional.get().getDeliveryPlatform().getCode();
        }
        if (Objects.isNull(deliveryPlatFormCode)) {
            return;
        }
        if (DynamicChannelType.DOU_YIN.equals(orderBizType)) {
            // 不是牵牛花管理配送，或者是抖音平台配送，才展示配送异常重新呼叫配送按钮
            if ((DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM.getCode() != deliveryPlatFormCode) && (DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() != deliveryPlatFormCode)) {
                return;
            }
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderInfo.getOrderStatus());
            if (Objects.isNull(orderStatusEnum) || OrderStatusEnum.CANCELED.equals(orderStatusEnum) || OrderStatusEnum.COMPLETED.equals(orderStatusEnum)) {
                return;
            }

            if (Objects.nonNull(deliveryOrder)) {
                DeliveryStatusEnum deliveryStatusEnum = deliveryOrder.getStatus();
                if (Objects.nonNull(deliveryStatusEnum) && DeliveryStatusEnum.DELIVERY_CANCELLED.equals(deliveryStatusEnum)) {
                    operateItemCodeSet.add(DeliveryOperateItemEnum.EXCEPTION_RECALL_LAUNCH.getCode());
                }
            } else {
                Integer distributeStatus = orderInfo.getDistributeStatus();
                if (Objects.nonNull(distributeStatus) && DistributeStatusEnum.DISTRIBUTE_CANCELED.getValue() == distributeStatus) {
                    operateItemCodeSet.add(DeliveryOperateItemEnum.EXCEPTION_RECALL_LAUNCH.getCode());
                }
            }
        } else if (Objects.equals(orderBizType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL)) {
            if (Objects.isNull(deliveryOrder)) {
                return;
            }
            if (!MccConfigUtils.supportRecallRiderStoreIdList(deliveryOrder.getStoreId())){
                return;
            }
            if (!checkOrderStatus(orderInfo)) {
                return;
            }
            if (DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM.getCode() != deliveryPlatFormCode) {
                return;
            }
            if (Objects.equals(deliveryOrder.getDeliveryExceptionCode(),DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getCode())) {
                operateItemCodeSet.add(DeliveryOperateItemEnum.EXCEPTION_RECALL_LAUNCH.getCode());
            }
        } else {
            return;
        }
    }

    private boolean checkOrderStatus(OrderInfo orderInfo){
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderInfo.getOrderStatus());
        if((orderStatusEnum!=null && NOT_SHOW_DELIVERY_ITEM_ORDER_STATUS.contains(orderStatusEnum)) || orderInfo.getOrderBizType()== null ){
            return false;
        }
        return true;
    }

    private boolean checkParam(OrderInfo orderInfo,DeliveryOrder deliveryOrder){

        if(!checkOrderStatus(orderInfo)){
            return false;
        }

        //已经转单的不容许操作
        if(deliveryOrder!=null && deliveryOrder.getTransType()!=null && deliveryOrder.getTransType()!=0){
            return false;
        }

        //二次配送不容许操作
        if(deliveryOrder!=null && deliveryOrder.getDeliveryCount()!=null && deliveryOrder.getDeliveryCount()>1){
            return false;
        }

        //自提单不允许转单
        if (orderInfo.getDeliveryMethod() != null && !orderInfo.getDeliveryMethod().equals(DistributeMethodEnum.HOME_DELIVERY.getValue())) {
            return false;
        }

        // 快专混/企客 预约单不允许转单
        if (orderInfo.isBookingOrder() && MccConfigUtils.isMtBookingPlatformTurnSelfRestrict(orderInfo.getOriginalDistributeType())) {
            return false;
        }
        return true;
    }

    public void checkSelfOperateItem(OrderInfo orderInfo,DeliveryOrder deliveryOrder,Set<Integer> platformCodeSet,Set<Integer> operateItemCodeSet, Integer deliveryPlatFormCode, Map<Integer, ChannelDetailDto> channelConfigMap){
        if(!checkParam(orderInfo,deliveryOrder)){
            return;
        }

        DistributeStatusEnum distributeStatusEnum = DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
        if (Objects.nonNull(orderInfo.getDistributeStatus())) {
            distributeStatusEnum = DistributeStatusEnum.enumOf(orderInfo.getDistributeStatus());
        }
        DynamicChannelType dynamicChannelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
        if (dynamicChannelType == null) {
            return;
        }
        //锁单中的订单不展示转自送按钮 不再限制配送平台
        if (orderInfo.getOrderLockLabel() != null && orderInfo.getOrderLockLabel() &&
                orderInfo.getIsLocked() != null && orderInfo.getIsLocked()) {
            return;
        }
        boolean isPrivateChannel = Objects.equals(dynamicChannelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL);
        DeliveryPlatformEnum deliveryPlatformEnum = null;
        if(deliveryOrder!=null && deliveryOrder.getDeliveryChannel()!=null && Objects.nonNull(deliveryPlatFormCode)){
            deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatFormCode);
        }
        if(deliveryOrder==null || deliveryOrder.getDeliveryChannel()==null || deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() || deliveryPlatformEnum == DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM){
            //平台配送
            fillPlatformDeliveryOperateItem(deliveryOrder,distributeStatusEnum,orderInfo,operateItemCodeSet,platformCodeSet, channelConfigMap, isPrivateChannel);
        }else if(deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()){
            //商家自己送
            if (isPrivateChannel) {
                boolean operateAggDelivery = checkPrivateChannelOperateItem(channelConfigMap, orderInfo, ChannelConfigEnum.AGG_DELIVERY.getCode());
                if (operateAggDelivery) {
                    if(platformCodeSet.contains(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())){
                        operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_MALT_FARM.getCode());
                    }
                    if(platformCodeSet.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())){
                        operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_DAP_DELIVERY.getCode());
                    }
                }
            } else {
                if(platformCodeSet.contains(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())){
                    operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_MALT_FARM.getCode());
                }
                if(platformCodeSet.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())){
                    operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_DAP_DELIVERY.getCode());
                }
            }
        }else {
            //聚合配送
            if(distributeStatusEnum == DistributeStatusEnum.RIDER_DELIVERED){
                return;
            }
            if(distributeStatusEnum == DistributeStatusEnum.RIDER_TAKE_GOODS &&
                    (deliveryOrder.getDeliveryExceptionCode()==null || deliveryOrder.getDeliveryExceptionCode()==0) ){
                return;
            }
            if (isPrivateChannel) {
                boolean operateSelfDelivery = checkPrivateChannelOperateItem(channelConfigMap, orderInfo, ChannelConfigEnum.SELF_DELIVERY.getCode());
                if (operateSelfDelivery) {
                    operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_SELF_DELIVERY.getCode());
                }
            } else {
                operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_SELF_DELIVERY.getCode());
            }
        }
    }

    private void fillPlatformDeliveryOperateItem (DeliveryOrder deliveryOrder, DistributeStatusEnum distributeStatusEnum, OrderInfo orderInfo, Set<Integer> operateItemCodeSet, Set<Integer> platformCodeSet, Map<Integer, ChannelDetailDto> channelConfigMap, Boolean isPrivateChannel) {
        Long time = null;
        Long now =System.currentTimeMillis();
        Map<Integer,Integer> configMap = MccConfigUtils.getPlatformToSelfConfig();
        Integer orderChannel = DynamicOrderBizType.orderBizTypeValue2ChannelId(orderInfo.getOrderBizType());
        if (orderChannel == null) {
            log.info("orderChannel is null: {}", orderInfo.getOrderBizType());
            return;
        }

        if(deliveryOrder!=null && deliveryOrder.getDeliveryChannel()!=DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode()){
            time = TimeUtil.toMilliSeconds(deliveryOrder.getCreateTime());
        }

        if(orderChannel == DynamicChannelType.MEITUAN.getChannelId()){
            if(distributeStatusEnum.getValue() >= DistributeStatusEnum.SELF_DISTRIBUTE.getValue() && distributeStatusEnum.getValue()<=DistributeStatusEnum.RIDER_DELIVERED.getValue()){
                return;
            }
            if (time == null) {
                // 拿不到时间,取订单创建时间
                time = TimeUtil.toMilliSeconds(orderInfo.getCreateTime());
            }

            if(configMap.containsKey(orderChannel)){
                Long subTime=now - time;
                Long showTime=configMap.get(orderChannel)*60*1000L;
                if(subTime<showTime){
                    return;
                }
            }
        }else if(orderChannel == DynamicChannelType.ELEM.getChannelId()){
            Integer deliveryExceptionCode = (deliveryOrder==null || deliveryOrder.getDeliveryExceptionCode() == null) ? 0 : deliveryOrder.getDeliveryExceptionCode();
            DeliveryStatusEnum deliveryStatus = null;
            if(deliveryOrder!=null){
                deliveryStatus = deliveryOrder.getStatus();
            }

            if(Objects.nonNull(deliveryStatus)) {

                if(!deliveryStatus.isExceptionStatus()
                        && deliveryExceptionCode != DeliveryExceptionCodeEnum.SELF_DELIVERY.getCode()
                        && deliveryExceptionCode != DeliveryExceptionCodeEnum.RECALL_SELF_RIDER_FAIL.getCode()
                        && deliveryExceptionCode != DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode()) {
                    return;
                }
            } else {
                return;
            }
        }else if(orderChannel == DynamicChannelType.JD2HOME.getChannelId()){
            if (distributeStatusEnum.getValue() != DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue()) {
                return;
            }
            if (time == null && orderInfo.getPickCompleteTime() != null) {
                //取拣货完成时间
                time = TimeUtil.toMilliSeconds(orderInfo.getPickCompleteTime());
            }
            if (time == null){
                time = now;
            }
            if(configMap.containsKey(orderChannel)){
                Long subTime=now - time;
                Long showTime=configMap.get(orderChannel)*60*1000L;
                if(subTime<showTime){
                    return;
                }
            }
        } else if(orderChannel == DynamicChannelType.YOU_ZAN.getChannelId() || orderChannel == DynamicChannelType.DOU_YIN.getChannelId()){
            if ( notNeedTurnSelf(distributeStatusEnum, orderInfo)) {
                return;
            }
        } else if (isPrivateChannel) {
            boolean operateSelfDelivery = checkPrivateChannelOperateItem(channelConfigMap, orderInfo, ChannelConfigEnum.SELF_DELIVERY.getCode());
            boolean operateAggDelivery = checkPrivateChannelOperateItem(channelConfigMap, orderInfo, ChannelConfigEnum.AGG_DELIVERY.getCode());
            if (operateSelfDelivery) {
                operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_SELF_DELIVERY.getCode());
            }
            if (operateAggDelivery && platformCodeSet.contains(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())) {
                operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_MALT_FARM.getCode());
            }
            if (operateAggDelivery && platformCodeSet.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())) {
                operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_DAP_DELIVERY.getCode());
            }
            return;
        } else {
            return;
        }
        operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_SELF_DELIVERY.getCode());
        if(platformCodeSet.contains(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())){
            operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_MALT_FARM.getCode());
        }
        if(platformCodeSet.contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())){
            operateItemCodeSet.add(DeliveryOperateItemEnum.TRANS_DAP_DELIVERY.getCode());
        }
    }

    private boolean notNeedTurnSelf(DistributeStatusEnum distributeStatusEnum, OrderInfo orderInfo) {
        List<Integer> orderStatusList = Arrays.asList(OrderStatusEnum.SUBMIT.getValue(), OrderStatusEnum.PAYING.getValue(), OrderStatusEnum.PAYED.getValue(),
                OrderStatusEnum.COMPLETED.getValue(), OrderStatusEnum.CANCELED.getValue());

        List<Integer> distributeStatusList = Arrays.asList(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue(), DistributeStatusEnum.RIDER_DELIVERED.getValue());

        return orderStatusList.contains(orderInfo.getOrderStatus()) || distributeStatusList.contains(distributeStatusEnum.getValue());
    }

    private boolean checkPrivateChannelOperateItem(Map<Integer, ChannelDetailDto> channelConfigMap, OrderInfo orderInfo, Integer channelConfigEnum){
        boolean canOperateItem = false;
        if (MapUtils.isNotEmpty(channelConfigMap)) {
            ChannelDetailDto channelDetailDto = channelConfigMap.get(DynamicOrderBizType.orderBizTypeValue2ChannelId(orderInfo.getOrderBizType()));
            if (channelDetailDto != null) {
                Map<Integer, ConfigContentDto> map = channelDetailDto.getConfigs().stream().collect(
                        Collectors.groupingBy(
                                ConfigContentDto::getConfigId,
                                Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
                if (OrderStatusEnum.MERCHANT_CONFIRMED.getValue() == orderInfo.getOrderStatus()){
                    ConfigContentDto configDto = map.get(ConfigItemEnum.WAIT_PICK_BUTTONS.getKey());
                    canOperateItem = checkChannelConfigOps(ConfigItemEnum.WAIT_PICK_BUTTONS,configDto,channelConfigEnum);
                } else if (OrderStatusEnum.PICKING.getValue() == orderInfo.getOrderStatus()){
                    ConfigContentDto configDto = map.get(ConfigItemEnum.DELIVERING_BUTTONS.getKey());
                    canOperateItem = checkChannelConfigOps(ConfigItemEnum.DELIVERING_BUTTONS,configDto,channelConfigEnum);
                } else if (OrderStatusEnum.REFUND_APPLIED.getValue() == orderInfo.getOrderStatus()){
                    ConfigContentDto configDto = map.get(ConfigItemEnum.WAIT_AFTER_SALE_BUTTONS.getKey());
                    canOperateItem = checkChannelConfigOps(ConfigItemEnum.WAIT_AFTER_SALE_BUTTONS,configDto,channelConfigEnum);
                }
            }
        }
        return canOperateItem;
    }

    private boolean checkChannelConfigOps(ConfigItemEnum configItemEnum,ConfigContentDto configDto,Integer channelConfigEnum){
        if (Objects.isNull(configDto)){
            return false;
        }
        if (StringUtils.isBlank(configDto.getConfigContent())) {
            return false;
        }
        List<Number> mainConfigValueAsNumList = configItemEnum.getMainConfigValueAsNumList(configDto.getConfigContent());
        if (CollectionUtils.isEmpty(mainConfigValueAsNumList)) {
            return false;
        }
        return mainConfigValueAsNumList.stream().map(Number::intValue).anyMatch(i -> i.equals(channelConfigEnum));
    }

    private Map<Long,DeliveryOrder> getDeliveryOrderActiveMap(List<DeliveryOrder> deliveryOrderList){
        if(CollectionUtils.isEmpty(deliveryOrderList)){
            return Collections.emptyMap();
        }
        ArrayListMultimap<Long,DeliveryOrder> multimap=ArrayListMultimap.create();
        for (DeliveryOrder deliveryOrder : deliveryOrderList){
            multimap.put(deliveryOrder.getOrderId(),deliveryOrder);
        }
        Map<Long,DeliveryOrder> deliveryOrderMap = new HashMap<>();
        for (Long key : multimap.keySet()){
            List<DeliveryOrder> deliveryOrders = multimap.get(key);
            if(CollectionUtils.isEmpty(deliveryOrders)){
                continue;
            }
            Optional<DeliveryOrder> deliveryOrderOptional = deliveryOrders.stream().max(new Comparator<DeliveryOrder>() {
                @Override
                public int compare(DeliveryOrder o1, DeliveryOrder o2) {
                    return o1.getCreateTime().isAfter(o2.getCreateTime()) ? 1 : -1;
                }
            });
            deliveryOrderOptional.ifPresent(deliveryOrder -> deliveryOrderMap.put(key, deliveryOrder));
        }

        return deliveryOrderMap;
    }

    private Map<Long, Integer> getDeliveryOrderDeliveryPlatFormMap(List<OrderInfo> orderInfoList, Map<Long,DeliveryOrder> deliveryOrderMap) {
        if (MapUtils.isEmpty(deliveryOrderMap)) {
            log.warn("getDeliveryOrderDeliveryPlatFormMap, deliveryOrderMap is empty");
            return Maps.newHashMap();
        }

        // key是运单ID，value是运单对应的配送平台code
        Map<Long, Integer> deliveryPlatFormMap = Maps.newHashMap();

        Map<Long, Integer> deliveryChannelMap = orderInfoList.stream().map(orderInfo -> deliveryOrderMap.get(orderInfo.getOrderKey().getOrderId()))
                .filter(Objects::nonNull).collect(Collectors.toMap(DeliveryOrder::getId, DeliveryOrder::getDeliveryChannel));
        Set<Integer> carrierCodeSet = Sets.newHashSet(deliveryChannelMap.values());
        List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
        Map<Integer, DeliveryChannel> deliveryChannelMapFromRpc = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, Function.identity()));

        deliveryChannelMap.forEach((deliveryOrderId, deliveryChannelCode) -> {
            DeliveryChannel deliveryChannel = deliveryChannelMapFromRpc.get(deliveryChannelCode);
            deliveryPlatFormMap.put(deliveryOrderId, Objects.nonNull(deliveryChannel) ? deliveryChannel.getDeliveryPlatFormCode() : NumberUtils.INTEGER_ZERO);
        });
        return deliveryPlatFormMap;
    }

}
