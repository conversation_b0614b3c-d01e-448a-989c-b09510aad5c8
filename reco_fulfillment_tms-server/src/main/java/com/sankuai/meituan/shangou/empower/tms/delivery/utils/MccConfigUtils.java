package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.dianping.lion.client.Lion.getConfigRepository;

/**
 * MCC配置
 */
@Slf4j
public class MccConfigUtils {

    private static final String ALL = "all";

    /**
     * 开启麦芽田开关时，同步门店开关
     * 1 开 0关
     * @return
     */
    public static int saveMaltFarmShopSwitch(){
        return Lion.getConfigRepository().getIntValue("delivery.save.malt.farm.shop.switch", 1);
    }

    /**
     * 用户下单发配送开关
     * 1 开 0关
     * @return
     */
    public static int getOrderPaidLaunchDeliverySwitch(){
        return Lion.getConfigRepository().getIntValue("order.paid.launch.delivery.switch", 1);
    }

    /**
     * 测试环境配置 mock 聚合运力平台发配送参数
     */
    public static String getTestEnvMockAggregationOrderViewId(){
        return Lion.getConfigRepository().get("test.env.mock.aggregation.order.view.id", "");
    }

    /**
     * 骑手坐标数据失效时间 秒
     * @return
     */
    public static int getRiderLocationDataInvalidTime(){
        return Lion.getConfigRepository().getIntValue("rider.location.data.invalid.time", 120);
    }

    /**
     * 骑手坐标数据同步时间 毫秒
     * @return
     */
    public static int getRiderLocationSyncTime(){
        return Lion.getConfigRepository().getIntValue("rider.location.sync.time", 30000);
    }

    /**
     * 骑手坐标数据同步时间 毫秒
     * @return
     */
    public static int getDrunkHorseRiderLocationSyncTime(){
        return Lion.getConfigRepository().getIntValue("dh.rider.location.sync.time", 30000);
    }

    /**
     * 骑手坐标上报灰度开关 -1 全量
     * @param storeId
     * @return
     */
    public static boolean checkRiderLocationSyncGreySwitch(Long storeId){
        if(storeId==null){
            return false;
        }
        String greyStr=Lion.getConfigRepository().get("rider.location.sync.grey.switch", "-1");
        if(StringUtils.isEmpty(greyStr)){
            return false;
        }
        List<String> greyList= Splitter.on(",").splitToList(greyStr);
        if(greyList.contains("-1") || greyList.contains(""+storeId)){
            return true;
        }
        return false;
    }

    /**
     * 缓存操作重试次数
     * @return
     */
    public static int getSquirrelOperateRetryCount(){
        return Lion.getConfigRepository().getIntValue("squirrel.operate.retry.count", 3);
    }

    /**
     * 缓存操作重试间隔 毫秒
     * @return
     */
    public static int getSquirrelOperateRetryPeriod(){
        return Lion.getConfigRepository().getIntValue("squirrel.operate.retry.period", 100);
    }

    /**
     * 补单不发配送的状态列表
     * @return
     */
    public static List<String> getCompensateNotDeliveryStatusList(){
        String statusStr=Lion.getConfigRepository().get("compensate.not.delivery.status.list","");
        if(StringUtils.isEmpty(statusStr)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(statusStr);
    }

    public static String getMapReqKey() {
        return Lion.getConfigRepository().get("map.request.key","38f65472-afa7-477b-bae5-42de46cc9b10");
    }

    public static String getDrunkHorseMapReqKey() {
        return Lion.getConfigRepository().get("drunk.horse.map.request.key","mc310eafd3284592be0c21f93ab8207t");
    }

    public static String getNewSupplyMapReqKey() {
        return Lion.getConfigRepository().get("new.supply.map.request.key","m30812967afc484089fc4eb4716c3ebt");
    }

    public static String getMapGeoQueryKey(){
        return Lion.getConfigRepository().get("map.geo.query.key", "56d1b8c9-9d43-4cd5-b69f-9743867afff6");
    }

    public static Boolean elemStoreConfigSwitch(){
        return Lion.getConfigRepository().getBooleanValue("elem.store.config.switch", true);
    }

    public static DeliveryChannelEnum dapChannelMapping(String dapChannelCode){
        if(StringUtils.isEmpty(dapChannelCode)){
            return DeliveryChannelEnum.DAP_DELIVERY;
        }
        //格式 dapChannelCode=DeliveryChannelEnumCode|....
        String codeMappingStr = Lion.getConfigRepository().get("dap.channel.code.mapping", "");
        if(StringUtils.isEmpty(codeMappingStr)){
            return DeliveryChannelEnum.DAP_DELIVERY_UNKNOW;
        }
        try {
            List<String> mappingCodeList=Splitter.on("|").splitToList(codeMappingStr);
            for (String codeStr : mappingCodeList){
                String[] code = codeStr.split("=");
                if(code[0].equals(dapChannelCode)){
                    return DeliveryChannelEnum.valueOf(Integer.parseInt(code[1]));
                }
            }
        }catch (Exception e){
            log.error("dapChannelMapping error codeMappingStr:{}，dapChannelCode:{}",codeMappingStr,dapChannelCode,e);
        }
        return DeliveryChannelEnum.DAP_DELIVERY_UNKNOW;
    }


    public static boolean drunkHorseOrderStatusConsumeSwitch() {
        return Lion.getConfigRepository().getBooleanValue("drunk.horse.order.status.consume.switch", false);
    }

    public static boolean orderStatusConsumeFilterDrunkHorseSwitch() {
        return Lion.getConfigRepository().getBooleanValue("order.status.consume.filter.drunk.horse.switch", false);
    }

    public static List<Integer> getChannelType(String tenantId) {

        try {
            //牵牛花相关租户暂时用mcc配置来获取渠道
            final String qnhChannelString = Lion.getConfigRepository().get("qnh.tenant.channel");
            Map<String, List<Integer>> configMap = new HashMap<>();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(qnhChannelString)) {
                List<String> channelList = Splitter.on("|").splitToList(qnhChannelString);
                channelList.stream().filter(str -> org.apache.commons.lang3.StringUtils.isNotEmpty(str)).forEach(str -> {
                    List<String> strList = Splitter.on("=").splitToList(str);
                    configMap.put(strList.get(0), JsonUtil.fromJson(strList.get(1), new TypeReference<List<Integer>>() {
                    }));
                });
            }

            if (MapUtils.isNotEmpty(configMap) && configMap.containsKey(tenantId)) {
                final List<Integer> channelIdList = configMap.get(tenantId);
                if (CollectionUtils.isNotEmpty(channelIdList)) {
                    return channelIdList;
                }
            }
        } catch (Exception e) {
            log.error("get channel type from tenant error", e);
        }

        return Collections.emptyList();
    }

    /**
     * 查询聚合配送渠道展示顺序
    */
    public static List<Integer> queryChannelOrder() {
        String channelOrderStr = Lion.getConfigRepository().get("agg.delivery.channel.order.list", "100,200,300,500,800,900");
        List<String> channelOrderList = Splitter.on(",").splitToList(channelOrderStr);
        return channelOrderList.stream().map(Integer::valueOf).collect(Collectors.toList());
    }
    public static long getDeliveryExceptionClearDelayMillis(LocalDateTime estimatedDeliveryTime) {
        Integer minus = Lion.getConfigRepository().getIntValue("delivery.clear.exception.minus", 4 * 60);
        Long clearTime = TimeUtil.toMilliSeconds(estimatedDeliveryTime.plusMinutes(minus));
        Long now = TimeUtil.toMilliSeconds(LocalDateTime.now());
        return clearTime - now;
    }

    public static Integer dapUrlMarkIdPartNum(){
        return Lion.getConfigRepository().getIntValue("dap.url.mark.id.part.num", 20);
    }

    public static List<Integer> queryDisplayDeliveryPlatformCodeList() {
        String deliveryPlatformCodeStr = Lion.getConfigRepository().get("display.delivery.platform.list", "2,3,4,5");
        List<String> deliveryPlatformCodeList = Splitter.on(",").splitToList(deliveryPlatformCodeStr);
        return deliveryPlatformCodeList.stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    public static List<Integer> queryOrderBizType4OrderChannelDelivery() {
        String orderBizTypeCodeStr = Lion.getConfigRepository().get("orderBizType.for.OrderChannelDelivery.list", "501");
        List<String> orderBizTypeCodeCodeList = Splitter.on(",").splitToList(orderBizTypeCodeStr);
        return orderBizTypeCodeCodeList.stream().map(Integer::valueOf).collect(Collectors.toList());
    }

    public static String queryYzOrderChannelDeliveryStatusMapping() {
        String yzDeliveryStatusMappingStr =  Lion.getConfigRepository().get("yz.delivery.status.mapping", "");
        return yzDeliveryStatusMappingStr;
    }

    /**
     * 获取抖音平台配送状态和牵牛花配送状态映射关系
    */
    public static String queryDyOrderChannelDeliveryStatusMapping() {
        return Lion.getConfigRepository().get("dy.platform.delivery.status.mapping", StringUtils.EMPTY);
    }

    /**
     * 获取私域渠道平台配送状态和牵牛花配送状态映射关系
     */
    public static String queryOpenApiOrderChannelDeliveryStatusMapping() {
        return Lion.getConfigRepository().get("openapi.platform.delivery.status.mapping", StringUtils.EMPTY);
    }

    public static Map<Integer,Integer> getPlatformToSelfConfig(){
        String content = ConfigUtilAdapter.getString("delivery_platform_to_self_config", "100=15|300=10");
        if(org.apache.commons.lang.StringUtils.isEmpty(content)){
            return Collections.emptyMap();
        }
        List<String> strList=Splitter.on("|").splitToList(content);
        if(CollectionUtils.isEmpty(strList)){
            return Collections.emptyMap();
        }
        Map<Integer,Integer> configMap = new HashMap<>();
        strList.forEach(str->{
            List<String> dataList = Splitter.on("=").splitToList(str);
            if(CollectionUtils.isEmpty(dataList) || dataList.size()!=2){
                return;
            }
            configMap.put(Integer.parseInt(dataList.get(0)),Integer.parseInt(dataList.get(1)));
        });
        return configMap;
    }

    public static Integer getDeliveryOrderEsCompensateMaxNum() {
        return Lion.getConfigRepository().getIntValue("delivery.order.es.compensate.max.num", 50);
    }

    public static Boolean isManagementSnCodeGrayStore(Long storeId) {
        String grayStoreIdStr = Lion.getConfigRepository("com.sankuai.waimai.sc.pickselectservice")
                .get("management.sn.code.gray.store.ids", "");
        if(StringUtils.isBlank(grayStoreIdStr)) {
            return false;
        }

        if (Objects.equals(grayStoreIdStr, "-1")) {
            return true;
        }


        return Splitter.on(",").splitToList(grayStoreIdStr).contains(String.valueOf(storeId));
    }

    /**
     * 歪马送酒租户id
     */
    @SuppressWarnings({"UnstableApiUsage"})
    public static List<String> getDHTenantIdList(){
        String tenantIdStr = Lion.getConfigRepository().get("drunk.horse.tenant.id.list", "1000395");
        if(org.apache.commons.lang.StringUtils.isBlank(tenantIdStr)){
            return Collections.emptyList();
        }
        return Splitter.on(",").splitToList(tenantIdStr);
    }

    /**
     * 是否是歪马租户
     */
    public static Boolean checkIsDHTenant(Long tenantId) {
        if (Objects.isNull(tenantId)) {
            return false;
        }
        List<String> dhTenantIdList = getDHTenantIdList();
        return dhTenantIdList.contains(tenantId.toString());
    }

    public static boolean getRiderLocationRedisMigrateSwitch() {
        return Lion.getConfigRepository().getBooleanValue("rider.location.redis.migrate.switch", false);
    }


    public static boolean checkGraySnCity(Integer cityId) {
        // 灰度城市id， 第一个数字是 -1 表示全量
        String grayCityIds =  Lion.getConfigRepository("com.sankuai.drunkhorsemgmt.wms")
                .get("sn.collect.grayCityIds", "[]");

        List<Integer> cityIdList = JacksonUtils.fromJson(grayCityIds, new org.codehaus.jackson.type.TypeReference<List<Integer>>() {
        });

        if (CollectionUtils.isNotEmpty(cityIdList) && cityIdList.get(0) == -1) {
            return true;
        }

        if(cityId == null) {
            return false;
        }


        return cityIdList.contains(cityId);
    }

    public static boolean getOfcRemoteSwitch(){
        return Lion.getConfigRepository().getBooleanValue("tms.ofc.remote.switch",true);
    }

    public static String getReportLocationUrl() {
        return Lion.getConfigRepository().get("report.location.url","https://pieapi-empower.meituan.com/pieapi/rider/delivery/location");
    }

    /**
     * 新供给聚合配送骑手坐标缓存过期时间
     */
    public static Integer getAggDeliveryRiderLocationExpireDays() {
        return Lion.getConfigRepository().getIntValue("agg.delivery.rider.location.expire.days", 5);
    }

    /**
     * 新供给商家自己配送骑手坐标缓存过期时间
     */
    public static Integer getSelfMerchantDeliveryRiderLocationExpireDays() {
        return Lion.getConfigRepository().getIntValue("self.merchant.delivery.rider.location.expire.days", 5);
    }

    public static boolean getOrderPositionSyncSwitch(){
        return Lion.getConfigRepository().getBooleanValue("order.position.sync.switch",true);
    }

    public static Integer getReportLocationInterval() {
        return Lion.getConfigRepository().getIntValue("report.location.interval", 30);
    }

    public static Integer getShorterReportLocationInterval() {
        return Lion.getConfigRepository().getIntValue("shorter.report.location.interval", 10);
    }

    public static boolean isLocationIntervalGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("location.interval.gray.store.list", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static boolean isLocationShorterIntervalGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("location.shorter.interval.gray.store.list", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    /**
     * 新供给骑手信息缓存过期时间
     */
    public static Integer getDeliveryRiderInfoExpireDays() {
        return Lion.getConfigRepository().getIntValue("delivery.rider.info.expire.days", 2);
    }

    /**
     * 是否是新合规路线灰度门店
     */
    public static boolean isNewRouteStore(Long storeId) {
        try {
            List<Long> grayStores = Lion.getConfigRepository().getList("new.route.gray.stores", Long.class, Lists.newArrayList());
            //全量逻辑
            if (grayStores.size() == 1 && grayStores.get(0).equals(-1L)) {
                return true;
            }
            return grayStores.contains(storeId);
        } catch (Exception e) {
            log.error("get isNewRouteStore error", e);
            return false;
        }
    }

    /**
     * 是否是新合规路线灰度门店
     */
    public static boolean isNewComplianceGrayStore(Long storeId) {
        try {
            List<Long> grayStores = Lion.getConfigRepository().getList("new.compliance.gray.stores", Long.class, Lists.newArrayList());
            //全量逻辑
            if (grayStores.size() == 1 && grayStores.get(0).equals(-1L)) {
                return true;
            }
            return grayStores.contains(storeId);
        } catch (Exception e) {
            log.error("get compliance gray store error", e);
            return false;
        }
    }

    /**
     * 是否是新合规路线灰度门店
     */
    public static String getNewComplianceStrategy() {
        return Lion.getConfigRepository().get("dh.new.compliance.strategy", "S");
    }

    /**
     * 是否是新合规路线灰度门店
     */
    public static String getOldRouteStrategy() {
        return Lion.getConfigRepository().get("dh.new.route.strategy", "A");
    }

    public static boolean checkUseOFCTenantIdList(Long tenantId) {
        List<Long> tenantIdList = getConfigRepository().getList("use.ofc.tenant.id.list", Long.class, Collections.emptyList());
        if(CollectionUtils.isEmpty(tenantIdList)){
            return false;
        }
        if(tenantIdList.contains(-1L)){
            return true;
        }
        return tenantIdList.contains(tenantId);
    }

    public static boolean checkDelayCheckTenantIdList(Long tenantId){
        List<Long> tenantIdList = getConfigRepository().getList("delay.check.tenant.id.list", Long.class, Collections.emptyList());
        if(CollectionUtils.isEmpty(tenantIdList)){
            return false;
        }
        if(tenantIdList.contains(-1L)){
            return true;
        }
        return tenantIdList.contains(tenantId);
    }

    public static boolean isMigrateInterfaceGrayStore(Long storeId){
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("migrate.interface.gray.store.id.list", Long.class, Collections.singletonList(-1L));
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static boolean supportRecallRiderStoreIdList(long storeId) {
        String whitelist = ConfigUtilAdapter.getString("support.recall.rider.store.list");
        if (org.apache.commons.lang.StringUtils.isBlank(whitelist)) {
            return false;
        }
        //所有租户打开，不需要灰度
        if (ALL.equals(whitelist)) {
            return true;
        } else {
            String[] whitelistArray = StringUtils.split(whitelist, ",");
            return Arrays.stream(whitelistArray).anyMatch(whitelistStoreId -> String.valueOf(storeId).equals(whitelistStoreId.trim()));
        }
    }

    public static boolean isSwitchRiderPositionSyncGrayStore(Long storeId){
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("switch.rider.position.sync.gray.storeIds", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    /**
     * 获取自配送同步骑手坐标频率mapping
     */
    public static String getSelfDeliverySyncRiderLocationPeriodMapping() {
        return Lion.getConfigRepository().get("self.delivery.sync.rider.location.period.mapping", StringUtils.EMPTY);
    }

    public static int getDeliveryLeafIdRetryCount() {
        return Lion.getConfigRepository().getIntValue("delivery.leaf.id.retry.count",5);
    }
    public static int getDeliveryLeafIdRetryTime() {
        return Lion.getConfigRepository().getIntValue("delivery.leaf.id.retry.time",50);
    }

    public static boolean getDeliveryLeafSwitch(){
        return Lion.getConfigRepository().getBooleanValue("delivery.leaf.switch",false);
    }

    /**
     * 新供给检查整单配送超时缓存过期时间
     */
    public static Integer getDeliveryTimeOutCheckExpireDays() {
        return Lion.getConfigRepository().getIntValue("delivery.timeout.check.expire.days", 1);
    }
    public static boolean getReplaceSwitch(){
        return Lion.getConfigRepository().getBooleanValue("delivery.replace.switch",true);
    }

    public static Integer getDapLinkSquirrelExpireMinutes() {
        return Lion.getConfigRepository().getIntValue("dap.link.squirrel.expire.minutes", 360);
    }

    public static boolean getQueryTenantChannelSwitch() {
        return Lion.getConfigRepository().getBooleanValue("query.tenant.channel.switch",true);
    }

    /**
     * 容具归还超时时间
     */
    public static Integer sealContainerReturnTimeOutConfig() {
        return Lion.getConfigRepository().getIntValue("seal.container.return.timeout.config", 60);
    }

    public static boolean isSealContainerReturnTimeoutNotifyGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("seal.container.return.timeout.notify.gray.storeIds", Long.class, Collections.singletonList(-1L));
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static Integer esQueryRoutingStoreListMaxCount() {
        return Lion.getConfigRepository().getIntValue("es.query.routing.store.list.max.count", 200);
    }

    public static boolean transAllDegreeSwitch(){
        return Lion.getConfigRepository().getBooleanValue("trans.all.degree.switch", false);
    }
    
    /**
     * 标记容具归对应订单已完成或已取消灰度
     * @param storeId 门店id
     * @return true-开启灰度 false-未开启灰度
     */
    public static boolean isSignOrderCompletedOrCanceledGrayStore(Long storeId) {
        if (storeId == null) {
            return false;
        }

        List<Long> grayStoreList = Lion.getConfigRepository()
                .getList("sign.order.completed.or.canceled.gray.store.ids", Long.class, Lists.newArrayList());

        if (grayStoreList.size() == 1 && Objects.equals(grayStoreList.get(0), -1L)) {
            return true;
        }

        return grayStoreList.contains(storeId);
    }


    public static boolean isOrderInteractionOptimizeGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository("com.sankuai.shangou.logistics.oio").getList("order.interaction.optimize.gray.storeIds", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    /**
     * 美团配送平台预约单转自配送是否限制
     */
    public static boolean isMtBookingPlatformTurnSelfRestrict(Integer originalDistributeType) {
        if (Objects.isNull(originalDistributeType)) {
            return false;
        }
        String types = getConfigRepository().get("mt.booking.platform.turn.self.restrict", "5,10,15,30");
        if (StringUtils.isBlank(types)) {
            return false;
        }
        return Arrays.stream(types.split(",")).anyMatch(type -> type.equals(String.valueOf(originalDistributeType)));
    }

    /**
     * 青云无授权链接开关
     */
    public static boolean dapLinkNoAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("dap.link.no.auth.switch", true);
    }

    /**
     * 青云无授权门店配置链接开关
     */
    public static boolean dapConfigLinkNoAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("dap.config.link.no.auth.switch", true);
    }

    public static List<String> getFourWheelDistributeCodeList(){
        return Lion.getConfigRepository().getList("four.wheel.distribute.code.list");
    }

    public static boolean isFourWheelDeliveryGrayStore(Long storeId) {
        List<Long> grayStoreIds = Lion.getConfigRepository().getList("four.wheel.delivery.gray.storeIds", Long.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(grayStoreIds)) {
            return false;
        }

        if (grayStoreIds.size() == 1 && Objects.equals(grayStoreIds.get(0), -1L)) {
            return true;
        }

        return grayStoreIds.contains(storeId);
    }

    public static boolean platformDeliveryActiveSwitch() {
        return Lion.getConfigRepository().getBooleanValue("platform.delivery.active.switch", true);
    }

    /**
     * 麦芽田无授权门店配置链接开关
     */
    public static boolean maltConfigLinkNoAuthSwitch() {
        return Lion.getConfigRepository().getBooleanValue("malt.config.link.no.auth.switch", true);
    }

    public static boolean unlockOrderIdempotentSwitch() {
        return Lion.getConfigRepository().getBooleanValue("unlock.order.idempotent.switch", true);
    }

    public static boolean transferOrderIdempotentSwitch() {
        return Lion.getConfigRepository().getBooleanValue("transfer.order.idempotent.switch", true);
    }
}
