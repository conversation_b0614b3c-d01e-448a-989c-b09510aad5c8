package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiShippingModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.Coordination;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.poishipping.PoiShippingInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.AggDeliveryPlatformAppConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.ChannelStoreRelation;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.ShopAuthInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.auth.AuthClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DapShopAuthResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformUrlInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.LaunchRuleInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.AggDeliveryPlatformAppConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.UrlUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.BookingOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.exception.ChannelNotExistException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.DapShopAuthNotifyRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.DapShopAuthNotifyResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.MaltFarmSignUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryPlatformException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.ShopAuthSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.DapDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryConfigApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryRangeSyncCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.DeliveryStoreApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.SaveConfigCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.TenantDeliveryConfigUpdateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.application.TenantDeliveryConfigUpdateCmd.TenantChannelDeliveryConfigUpdateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryChannelInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.relation.DeliveryPoiRelationClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.ChannelStoreQueryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ChannelTenantConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.DeliveryTenantRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.OrderPlatformDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.PoiFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.task.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.GeoUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.StoreConfigTranslate;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.AGGREGATION_DELIVERY_PLATFORM;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYSTEM_ERROR;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.TENANT_NOT_CONFIG_DELIVERY_CHANNEL;
import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/20
 */
@Slf4j
@Service
public class DeliveryConfigurationThriftServiceImpl implements DeliveryConfigurationThriftService {

	@Resource
	private DeliveryStoreApplicationService deliveryStoreApplicationService;
	@Resource
	private DeliveryConfigApplicationService deliveryConfigApplicationService;
	@Resource
	private AuthClient authClient;
	@Resource
	private DeliveryTenantRepository deliveryTenantRepository;
	@Resource
	private DeliveryPlatformClient deliveryPlatformClient;
	@Resource
	private TenantSystemClient tenantSystemClient;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private DeliveryPoiRelationClient deliveryPoiRelationClient;
	@Resource
	private DapDeliveryPlatformClient dapDeliveryPlatformClient;
	@Resource
	private OcmsChannelClient ocmsChannelClient;

	@Resource
	private ShopAuthSquirrelOperationService shopAuthSquirrelOperationService;

	@Autowired
	private PoiFacade poiFacade;

	public static final List<Integer> channelTypeList = Arrays.asList(DynamicChannelType.DOU_YIN.getChannelId(), DynamicChannelType.YOU_ZAN.getChannelId(), DynamicChannelType.TAO_XIAN_DA.getChannelId());

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public StoreConfigQueryResponse queryStoreConfiguration(StoreConfigQueryRequest request) {
		String check = request.validate();
		if (check != null) {
			return new StoreConfigQueryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
		}

		StoreConfigQueryResponse response = new StoreConfigQueryResponse();
		response.setStatus(Status.SUCCESS);
		try {

			TStoreConfig tStoreConfig = new TStoreConfig();
			TenantStoreInfo storeInfo = tenantSystemClient.queryStoreDetailInfo(request.getTenantId(), request.getStoreId());
			if (Objects.nonNull(storeInfo)) {
				tStoreConfig.setStoreName(storeInfo.getStoreName());
			}
			List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(request.getTenantId(), request.getStoreId());
			Optional<DeliveryPoi> opDeliveryPoi=deliveryPoiList.stream().filter(w ->w.getChannelType() == DynamicChannelType.MEITUAN.getChannelId()).findFirst();
			opDeliveryPoi.ifPresent(deliveryPoi -> StoreConfigTranslate.fillConfigAttr(deliveryPoi, tStoreConfig));
			if (CollectionUtils.isNotEmpty(request.getAggPlatformCodes())) {

				StoreConfigTranslate.fillAggPlatformConfig(
						opDeliveryPoi.map(DeliveryPoi::getDeliveryPlatform).orElse(null), tStoreConfig, request.getAggPlatformCodes()
				);
				if(MccConfigUtils.elemStoreConfigSwitch()){
					tStoreConfig.setAggPlatformConfigs(StoreConfigTranslate.fillAggPlatformConfig(deliveryPoiList, request.getAggPlatformCodes(), request.getTenantId()));
				}

				tStoreConfig.setLastPlatformConfigs(StoreConfigTranslate.fillLastAggPlatformConfig(deliveryPoiList, request.getTenantId()));

				if((CollectionUtils.isNotEmpty(tStoreConfig.getAggPlatformConfigs()) || CollectionUtils.isNotEmpty(tStoreConfig.getLastPlatformConfigs()))
						&& request.getAggPlatformCodes().contains(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())
						&& CollectionUtils.isNotEmpty(deliveryPoiList)){
					Optional<Map<String, DeliveryPlatformUrlInfo>> optionalMap = dapDeliveryPlatformClient.queryLinkInfo(deliveryPoiList.get(0),Arrays.asList(request.getStoreId()+""), LinkTypeEnum.SHOP_LINK_TYPE);
					if(optionalMap.isPresent()){
						Map<String, DeliveryPlatformUrlInfo> infoMap=optionalMap.get();
						if(MapUtils.isNotEmpty(infoMap) && infoMap.containsKey(request.getStoreId()+"")){
							DeliveryPlatformUrlInfo urlInfo=infoMap.get(request.getStoreId()+"");
							if(CollectionUtils.isNotEmpty(tStoreConfig.getAggPlatformConfigs())){
								for (TAggDeliveryPlatformConfig config : tStoreConfig.getAggPlatformConfigs()){
									if(config.getPlatformCode() != DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()){
										continue;
									}
									if(urlInfo!=null){
										config.setConfigUrl(urlInfo.getLinkUrl());
									}
								}
							}
							if(CollectionUtils.isNotEmpty(tStoreConfig.getLastPlatformConfigs())){
								for (TAggDeliveryPlatformConfig config : tStoreConfig.getLastPlatformConfigs()){
									if(config.getPlatformCode() != DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()){
										continue;
									}
									if(urlInfo!=null){
										config.setConfigUrl(urlInfo.getLinkUrl());
									}
								}
							}

						}
					}

				}
			}

			List<DeliveryChannelInfo> channels = deliveryPoiRelationClient.queryStoreChannelInfo(request.getStoreId());
			List<TOuterDeliveryConfig> outerDeliveryConfigs = StoreConfigTranslate.filterSupportChannel(channels);
			// 不存在麦芽田等新增三方聚合运力平台
			if (CollectionUtils.isEmpty(outerDeliveryConfigs) && CollectionUtils.isEmpty(tStoreConfig.getAggPlatformConfigs())) {
				return new StoreConfigQueryResponse(new Status(FailureCodeEnum.NO_CHANNEL_SUPPORT.getCode(), FailureCodeEnum.NO_CHANNEL_SUPPORT.getMessage()));
			}
			response.setOuterDeliveryConfigs(outerDeliveryConfigs);
			// 查询可用配送规则，并填充给 tStoreConfig
			List<LaunchRuleInfo> availableRules = deliveryPlatformClient.queryAvailableDeliveryRules(AGGREGATION_DELIVERY_PLATFORM);
			List<TLaunchRule> tLaunchRules = StoreConfigTranslate.translateAvailableRule(tStoreConfig, availableRules);
			tStoreConfig.setAvailableLaunchRules(tLaunchRules);
			response.setTStoreConfig(tStoreConfig);
		} catch (Exception e) {
			log.error("queryStoreConfiguration error", e);
			response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}

		return response;
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public BatchStoreConfigQueryResponse batchQueryStoreConfiguration(BatchStoreConfigQueryRequest request) {
		try {
			String check = request.validate();
			if (check != null) {
				return new BatchStoreConfigQueryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
			}

			List<Long> storeIds = request.getStoreIdList().stream().distinct().collect(Collectors.toList());
			if (storeIds.size() > 1) {
				log.warn("灰度观察：批量查询多个门店[{}]", storeIds);
				Cat.logEvent("REFACTOR_OBSERVE", "BATCH_QUERY_MULTI_STORE");
			}

			if(!MccConfigUtils.elemStoreConfigSwitch()){
				List<TStoreConfig> tStoreConfigs = storeIds.stream()
						.map(it -> deliveryPoiRepository.queryDeliveryPoi(request.getTenantId(), it))
						.filter(Optional::isPresent)
						.map(Optional::get)
						.map(it -> {
							TStoreConfig tStoreConfig = new TStoreConfig();
							StoreConfigTranslate.fillConfigAttr(it, tStoreConfig);
							if (CollectionUtils.isNotEmpty(request.getAggPlatformCodes())) {
								StoreConfigTranslate.fillAggPlatformConfig(it.getDeliveryPlatform(), tStoreConfig, request.getAggPlatformCodes());
							}
							return tStoreConfig;
						})
						.collect(Collectors.toList());

				return new BatchStoreConfigQueryResponse(Status.SUCCESS, tStoreConfigs);
			}

			List<TStoreConfig> storeConfigList=new ArrayList<>();
			for (Long storeId : storeIds){
				TStoreConfig tStoreConfig = new TStoreConfig();
				List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(request.getTenantId(), storeId);
				if(CollectionUtils.isEmpty(deliveryPoiList)){
					continue;
				}
				Optional<DeliveryPoi> opDeliveryPoi=deliveryPoiList.stream().filter(w ->w.getChannelType() == DynamicChannelType.MEITUAN.getChannelId()).findFirst();
				opDeliveryPoi.ifPresent(deliveryPoi -> StoreConfigTranslate.fillConfigAttr(deliveryPoi, tStoreConfig));
				if (CollectionUtils.isNotEmpty(request.getAggPlatformCodes())) {
					tStoreConfig.setAggPlatformConfigs(StoreConfigTranslate.fillAggPlatformConfig(deliveryPoiList,request.getAggPlatformCodes(),request.getTenantId()));
				}
				if(!opDeliveryPoi.isPresent()){
					deliveryPoiList.stream().findFirst().ifPresent(deliveryPoi -> StoreConfigTranslate.fillConfigAttr(deliveryPoi, tStoreConfig));
				}
				storeConfigList.add(tStoreConfig);
			}
			return new BatchStoreConfigQueryResponse(Status.SUCCESS, storeConfigList);
		} catch (Exception e) {
			log.error("batchQueryStoreConfiguration error", e);
			return new BatchStoreConfigQueryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public ConfigCommonResponse saveStoreConfiguration(StoreConfigSaveRequest req) {
		ConfigCommonResponse response = new ConfigCommonResponse();
		response.setStatus(Status.SUCCESS);
		ShopAuthConfig shopAuthConfig = new ShopAuthConfig();

		String check = req.validate();
		if (check != null) {
			return new ConfigCommonResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check),null);
		}

		TAggDeliveryPlatformConfig config = req.getTAggDeliveryPlatformConfig();
		// 有赞&抖音-平台配送 不做基础校验
		boolean validFlag = isOrderChannelDeliveryPlatform(config.getChannelType(), config.getPlatformCode());

		boolean opening = Objects.equals(req.getTAggDeliveryPlatformConfig().getOpenFlag(), 1);
		if (!validFlag && isPoiBasicSettingsNotLegalExcludeLatitudeAndLongitude(req.getTenantId(), req.getStoreId(), opening)) {
			return new ConfigCommonResponse(Status.from(FailureCodeEnum.POI_BASIC_SETTINGS_INCOMPLETE),null);
		}

		try {

			SaveConfigCmd cmd=SaveConfigCmd.builder()
					.storeId(req.getStoreId())
					.tenantId(req.getTenantId())
					.immediateOrderDeliveryLaunchPointConfig(buildImmediateOrderDeliveryLaunchPointConfig(req))
					.bookingOrderDeliveryLaunchPointConfig(buildBookingOrderDeliveryLaunchPointConfig(req))
					.deliveryLaunchType(Objects.nonNull(req.getTStoreConfig()) ?
							DeliveryLaunchTypeEnum.enumOf(req.getTStoreConfig().getLaunchPattern()) : null)
					.autoLaunchStrategyId(Objects.nonNull(req.getTStoreConfig()) ? req.getTStoreConfig().getLaunchRule() : null)
					.module(req.getModule())
					.channelInfo(buildSyncDelivery(req))
					.deliveryPlatformInfo(buildDeliveryPlatform(req))
					.build();
			DeliveryPoi deliveryPoi = deliveryConfigApplicationService.buildDeliveryPoiConfigV2(cmd);

			if (isDeliveryPlatformOpen(cmd) && isMaltDeliveryPlatformOpen(cmd)) {
				deliveryPlatformClient.createShop(deliveryPoi);
			}
			DeliveryPlatformUrlInfo dapUrlInfo = null;
			if (isDeliveryPlatformOpen(cmd) && isDapPlatformOpen(cmd)) {
				Map<Integer, DeliveryStoreConfigDto> deliveryStoreConfigDtoMap = new HashMap<>();
				deliveryStoreConfigDtoMap.put(deliveryPoi.getChannelType(), new DeliveryStoreConfigDto(cmd.getDeliveryPlatformInfo().getPlatformCode(),
						cmd.getDeliveryPlatformInfo().getOpenFlag(), cmd.getDeliveryPlatformInfo().getChannelType(),
						deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode(),
						deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes(),
						deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes()));
				DapShopAuthResult dapShopAuthResult = authDapShop(req.getTenantId(), req.getStoreId(), req.getOperatorId(), req.getOperatorName(), Arrays.asList(deliveryPoi), deliveryStoreConfigDtoMap);
				if (! dapShopAuthResult.getCode().equals(ResponseCodeEnum.SUCCESS.getValue())) {
					response.setStatus(new Status(dapShopAuthResult.getCode(),dapShopAuthResult.getMessage()));
					shopAuthConfig.setCode(dapShopAuthResult.getCode());
					response.setShopAuthConfig(shopAuthConfig);
					return response;
				} else {
					shopAuthConfig.setCode(ResponseCodeEnum.SUCCESS.getValue());
					shopAuthConfig.setIsAuthed(dapShopAuthResult.getIsAuthed() ? 1 : 0);
					if (dapShopAuthResult.getIsAuthed()) {
						fastAuth(deliveryPoi);
					} else {
						Optional<Map<String, DeliveryPlatformUrlInfo>> optionalMap = deliveryPlatformClient.queryLinkInfo(deliveryPoi, Arrays.asList(String.valueOf(req.getStoreId())), LinkTypeEnum.SHOP_LINK_TYPE);
						if (optionalMap.isPresent()) {
							Map<String, DeliveryPlatformUrlInfo> infoMap = optionalMap.get();
							if (MapUtils.isNotEmpty(infoMap) && infoMap.containsKey(String.valueOf(req.getStoreId()))) {
								dapUrlInfo = infoMap.get(String.valueOf(req.getStoreId()));
							}
						}
						shopAuthConfig.setDapLinkUrl(null);
						if (Objects.nonNull(dapUrlInfo)) {
							shopAuthConfig.setDapLinkUrl(dapUrlInfo.getLinkUrl());
						}
						response.setShopAuthConfig(shopAuthConfig);
						return response;
					}
				}
			}
			deliveryConfigApplicationService.saveStoreConfiguration(cmd, deliveryPoi);
			Map<String,StoreConfigOpInfo> opInfoMap =new HashMap<>();
			opInfoMap.put(deliveryPoi.getStoreId()+"_"+deliveryPoi.getChannelType(),
					StoreConfigOpInfo.builder()
							.channelType(deliveryPoi.getChannelType())
							.deliveryPoint(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint().getCode())
							.deliveryPlatform(deliveryPoi.getDeliveryPlatform().getCode())
							.deliveryLaunchDelayMinutes(deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getDelayMinutes())
							.bookingDeliveryLaunchDelayMinutes(deliveryPoi.getDeliveryLaunchPoint().getBookingOrderDeliveryLaunchPointConfig().getConfigMinutes())
							.build());
			List<StoreConfigOpLog> storeConfigOpLogList = transfer2OpLog(Arrays.asList(deliveryPoi), opInfoMap,
					req.getOperatorId(), req.getOperatorName());
			deliveryPoiRepository.batchSaveStoreConfigOpLog(storeConfigOpLogList);

		} catch (DeliveryPlatformException e) {
			log.error("delivery platform fail", e);
			response.setStatus(new Status(e.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("saveStoreConfiguration error", e);
			response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
		response.setShopAuthConfig(shopAuthConfig);
		return response;
	}

	private boolean isOrderChannelDeliveryPlatform(Integer channelType, Integer platformCode) {
		return (channelTypeList.contains(channelType)&&
				Objects.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(), platformCode));
	}

	private List<StoreConfigOpLog> transfer2OpLog(List<DeliveryPoi> deliveryPoiList,
												  Map<String,StoreConfigOpInfo> opInfoMap,
												  Long operatorId, String operatorName) {
		return deliveryPoiList.stream().map(deliveryPoi -> {
			Integer channelType = deliveryPoi.getChannelType();
			StoreConfigOpInfo storeConfigOpInfo =opInfoMap.get(deliveryPoi.getStoreId()+"_"+channelType);

			return StoreConfigOpLog.builder()
					.storeConfigId(deliveryPoi.getId())
					.tenantId(deliveryPoi.getTenantId())
					.storeId(deliveryPoi.getStoreId())
					.opType(StoreConfigOpTypeEnum.MODIFY.getValue())
					.opInfo(storeConfigOpInfo== null ? null: JacksonUtils.toJson(storeConfigOpInfo))
					.operatorId(operatorId == null ? 0L:operatorId)
					.operatorName(operatorName == null ? "":operatorName)
					.build();
		}).collect(Collectors.toList());
	}

	@MethodLog(logRequest = true, logResponse = true)
	@Override
	public ConfigCommonResponse saveDeliveryStoreConfiguration(DeliveryStoreConfigSaveRequest request) {
		ConfigCommonResponse response = new ConfigCommonResponse();
		ShopAuthConfig shopAuthConfig = new ShopAuthConfig();
		response.setStatus(Status.SUCCESS);

		try {
			String check = request.validate();
			if (check != null) {
				return new ConfigCommonResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
			}
			Long storeId = request.getStoreId();
			Long tenantId = request.getTenantId();
			Integer module = request.getModule();
			Long operatorId = request.getOperatorId();
			String operatorName = request.getOperatorName();
			List<DeliveryStoreConfigDto> deliveryPlatformConfigList = request.getDeliveryStoreConfigDto();

			boolean anyOpening = request.getDeliveryStoreConfigDto().stream().anyMatch(conf -> Objects.equals(conf.getOpenFlag(), 1));
			if (isValidPoiBasicSettings(tenantId, storeId, deliveryPlatformConfigList, anyOpening)
					&& isPoiBasicSettingsNotLegalExcludeLatitudeAndLongitude(tenantId, storeId)) {
				return new ConfigCommonResponse(Status.from(FailureCodeEnum.POI_BASIC_SETTINGS_INCOMPLETE));
			}

			Map<Integer, DeliveryStoreConfigDto> deliveryPoiMap = deliveryPlatformConfigList.stream()
					.collect(Collectors.toMap(DeliveryStoreConfigDto::getChannelType, tAggDeliveryPlatformConfig -> tAggDeliveryPlatformConfig));
			StoreConfigInfo storeConfigInfo= deliveryConfigApplicationService.buildDeliveryPoiConfigList(storeId, tenantId, deliveryPlatformConfigList);
			List<DeliveryPoi> dapDeliveryPoiList = storeConfigInfo.getDeliveryPoiList();
			List<DeliveryPoi> saveDeliveryPoi = storeConfigInfo.getSaveDeliveryPoiList();
			//青云门店授权信息
			if (CollectionUtils.isNotEmpty(dapDeliveryPoiList)) {
				DapShopAuthResult dapShopAuthResult = authDapShop(tenantId, storeId, operatorId, operatorName, dapDeliveryPoiList, deliveryPoiMap);
				if (! dapShopAuthResult.getCode().equals(ResponseCodeEnum.SUCCESS.getValue())) {
					shopAuthConfig.setCode(dapShopAuthResult.getCode());
				} else {
					DeliveryPlatformUrlInfo dapUrlInfo = null;
					if(!dapShopAuthResult.getIsAuthed()) {
						Optional<Map<String, DeliveryPlatformUrlInfo>> optionalMap = deliveryPlatformClient.queryLinkInfo(dapDeliveryPoiList.get(0), Arrays.asList(String.valueOf(storeId)), LinkTypeEnum.SHOP_LINK_TYPE);
						if (optionalMap.isPresent()) {
							Map<String, DeliveryPlatformUrlInfo> infoMap = optionalMap.get();
							if (MapUtils.isNotEmpty(infoMap) && infoMap.containsKey(String.valueOf(storeId))) {
								dapUrlInfo = infoMap.get(String.valueOf(storeId));
							}
						}
					} else {
						saveDeliveryPoi.addAll(dapDeliveryPoiList);
						fastAuth(dapDeliveryPoiList.get(0));
					}
					shopAuthConfig.setCode(ResponseCodeEnum.SUCCESS.getValue());
					shopAuthConfig.setIsAuthed(dapShopAuthResult.getIsAuthed() ? 1:0);
					shopAuthConfig.setDapLinkUrl(null);
					if (Objects.nonNull(dapUrlInfo)) {
						shopAuthConfig.setDapLinkUrl(dapUrlInfo.getLinkUrl());
					}
				}
			}
			//开通麦芽田平台门店
			Set<Integer> platformCodeList = Sets.newHashSet();
			for(DeliveryPoi deliveryPoi: saveDeliveryPoi) {
				DeliveryStoreConfigDto platformConfig = deliveryPoiMap.get(deliveryPoi.getChannelType());
				SaveConfigCmd cmd = buildSaveConfigCmd(storeId, tenantId, module, platformConfig);
				if (isDeliveryPlatformOpen(cmd) && isMaltDeliveryPlatformOpen(cmd)) {
					if(platformCodeList.contains(cmd.getDeliveryPlatformInfo().getPlatformCode())) {
						continue;
					}
					platformCodeList.add(cmd.getDeliveryPlatformInfo().getPlatformCode());
					log.info("createShop deliveryPoi:{}", deliveryPoi);
					deliveryPlatformClient.createShop(deliveryPoi);
				}
			}
			//保存门店配置修改
			deliveryPoiRepository.batchSaveDeliveryPoi(saveDeliveryPoi);
			deliveryConfigApplicationService.batchSaveStoreConfigLog(saveDeliveryPoi, storeConfigInfo.getOpInfoMap(),
					request.getOperatorId(), request.getOperatorName());
		} catch (DeliveryPlatformException e) {
			log.error("delivery platform fail", e);
			response.setStatus(new Status(e.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("saveStoreConfiguration error", e);
			response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
		response.setShopAuthConfig(shopAuthConfig);
		return response;
	}

	private DapShopAuthResult authDapShop(Long tenantId, Long storeId, Long operatorId, String operatorName, List<DeliveryPoi> dapDeliveryPoiList, Map<Integer, DeliveryStoreConfigDto> deliveryPoiMap) {
		List<DeliveryStoreConfigDto> deliveryDto = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(dapDeliveryPoiList) && MapUtils.isNotEmpty(deliveryPoiMap)) {
			for(DeliveryPoi deliveryPoi: dapDeliveryPoiList) {
				DeliveryStoreConfigDto platformConfig = deliveryPoiMap.get(deliveryPoi.getChannelType());
				SaveConfigCmd cmd = buildSaveConfigCmd(storeId, tenantId, 3, platformConfig);
				if (isDeliveryPlatformOpen(cmd) &&  isDapPlatformOpen(cmd)) {
					deliveryDto.add(platformConfig);
				}
			}
		}
		if (CollectionUtils.isNotEmpty(deliveryDto)) {
			shopAuthSquirrelOperationService.set(String.valueOf(storeId), new ShopAuthInfo(storeId, tenantId, operatorId, operatorName, deliveryDto));
			Result<DapShopAuthResult> dapShopAuthResultResult = deliveryPlatformClient.deliveryShopAuth(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM, storeId);
			return dapShopAuthResultResult.getInfo();
		}
		return DapShopAuthResult.isSuccess();
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public ConfigCommonResponse unbindDeliveryCompanyPoiOnAggr(UnbindDeliveryPoiOnAggrPlatformRequest request) {
		ConfigCommonResponse response = new ConfigCommonResponse();
		response.setStatus(Status.SUCCESS);

		String check = request.validate();
		if (check != null) {
			return new ConfigCommonResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
		}
		try {
			deliveryPoiRelationClient.unbindDeliveryCompanyPoi(request.getStoreId(), request.getDeliveryChannelId(),
					request.getDeliveryChannelPoiId());
		} catch (DeliveryBaseException e) {
			log.error("unbindDeliveryCompanyPoiOnAggr fail", e);
			response.setStatus(new Status(SYSTEM_ERROR.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("unbindDeliveryCompanyPoiOnAggr error", e);
			response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
		return response;
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryRangeSyncResponse syncDeliveryRange(DeliveryRangeSyncRequest request) {
		try {
			String requestErrorMsg = request.validate();
			if (requestErrorMsg != null) {
				return new DeliveryRangeSyncResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
			}

			Optional<Failure> storeAuthFailure =
					authClient.checkStorePermission(request.getTenantId(), request.getOperatorId(), ImmutableList.of(request.getStoreId()));
			if (storeAuthFailure.isPresent()) {
				return new DeliveryRangeSyncResponse(new Status(storeAuthFailure.get().getFailureCode(), storeAuthFailure.get().getFailureMessage()));
			}

			Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(request.getTenantId(), request.getStoreId());
			if (!opDeliveryPoi.isPresent()) {
				return new DeliveryRangeSyncResponse(new Status(TENANT_NOT_CONFIG_DELIVERY_CHANNEL.getCode(), TENANT_NOT_CONFIG_DELIVERY_CHANNEL.getMessage()));
			}

			Map<Integer, OrderPlatformDeliveryConfig> orderPlatformDeliveryConfigMap = request.getChannelDeliveryRangeSyncRequests()
					.stream()
					.collect(Collectors.toMap(
							ChannelDeliveryRangeSyncRequest::getChannelId,
							it -> new OrderPlatformDeliveryConfig(it.getMinOrderPrice(), it.getDeliveryFee(), it.getDeliveryTime()),
							(o1, o2) -> o2)
					);
			Map<Integer, Failure> failureMap =
					deliveryStoreApplicationService.syncDeliveryRange(new DeliveryRangeSyncCmd(opDeliveryPoi.get(), orderPlatformDeliveryConfigMap));

			List<DeliveryRangeSyncFailReason> failReasons = failureMap.entrySet()
					.stream()
					.map(it -> new DeliveryRangeSyncFailReason(it.getKey(), it.getValue().getFailureMessage()))
					.collect(Collectors.toList());
			return new DeliveryRangeSyncResponse(Status.SUCCESS, failReasons);

		} catch (Exception e) {
			log.error("DeliveryConfigurationThriftService.syncDeliveryRange error", e);
			return new DeliveryRangeSyncResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public QueryDeliveryShippingTagResponse queryReceiverRelatedDeliveryRangeTagList(QueryDeliveryShippingTagRequest request) throws TException {
		String errMsg = request.validate();
		if (StringUtils.isNotBlank(errMsg)) {
			return new QueryDeliveryShippingTagResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), errMsg), null);
		}

		List<PoiShippingInfoDTO> poiShippingInfoDTOS = ocmsChannelClient.queryPoiShippingInfo(request.getTenantId(), request.getStoreId(), request.getChannelId());

		List<ShippingTagEnum> shippingTagEnums = parseShippingTagList(poiShippingInfoDTOS, GeoUtils.createCoordinate(request.getLatitude(), request.getLongitude()));

		return new QueryDeliveryShippingTagResponse(Status.SUCCESS, shippingTagEnums.stream().map(ShippingTagEnum::getCode).collect(Collectors.toList()));

	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public TenantDeliveryConfigQueryResponse queryTenantDeliveryConfig(TenantDeliveryConfigQueryRequest request) {
		try {
			String requestErrorMsg = request.validate();
			if (requestErrorMsg != null) {
				return new TenantDeliveryConfigQueryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
			}

			Map<ThirdDeliveryChannelEnum, ChannelTenantConfig> channelTenantMap =
					deliveryTenantRepository.getChannelTenants(request.getTenantId(), false);

			return new TenantDeliveryConfigQueryResponse(
					Arrays.stream(ThirdDeliveryChannelEnum.values())
							.map(it -> channelTenantMap.getOrDefault(it, new ChannelTenantConfig(null, it, EMPTY, EMPTY, false)))
							.map(it -> new TChannelDeliveryConfig(
									it.getDeliveryChannel().getCode(),
									it.getDeliveryChannel().getName(),
									it.getAppKey(),
									it.getSecretKey(),
									it.isEnabled() ? 1 : 0)
							)
							.collect(Collectors.toList())
			);
		} catch (Exception e) {
			log.error("DeliveryConfigurationThriftService.queryTenantDeliveryConfig error", e);
			return new TenantDeliveryConfigQueryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public TenantDeliveryConfigUpdateResponse updateTenantDeliveryConfig(TenantDeliveryConfigUpdateRequest request) {
		try {
			String requestErrorMsg = request.validate();
			if (requestErrorMsg != null) {
				return new TenantDeliveryConfigUpdateResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestErrorMsg));
			}

			for (ChannelDeliveryConfigUpdateRequest each : request.getChannelDeliveryConfigs()) {
				if (ThirdDeliveryChannelEnum.valueOf(each.getDeliveryChannelId()) == null) {
					return new TenantDeliveryConfigUpdateResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), "配送渠道ID不合法"));
				}
			}

			return deliveryStoreApplicationService.updateTenantDeliveryConfig(
					new TenantDeliveryConfigUpdateCmd(
							request.getTenantId(),
							request.getChannelDeliveryConfigs()
									.stream()
									.map(it -> new TenantChannelDeliveryConfigUpdateCmd(
											ThirdDeliveryChannelEnum.valueOf(it.getDeliveryChannelId()),
											it.getAppKey(),
											it.getSecret(),
											it.getStatus() == ChannelDeliveryConfigUpdateRequest.ENABLED
									))
									.collect(Collectors.toList()),
							new Operator(request.getOperatorId(), request.getOperatorName())
					))
					.map(it -> new TenantDeliveryConfigUpdateResponse(new Status(it.getFailureCode(), it.getFailureMessage())))
					.orElseGet(() -> new TenantDeliveryConfigUpdateResponse(Status.SUCCESS));

		} catch (Exception e) {
			log.error("DeliveryConfigurationThriftService.updateTenantDeliveryConfig error", e);
			return new TenantDeliveryConfigUpdateResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public StoreAggDeliveryConfigQueryResponse queryStoreAggDeliveryConfig(StoreAggDeliveryConfigQueryRequest request) {
		String requestParamCheck = request.validate();
		if (Objects.nonNull(requestParamCheck)) {
			return new StoreAggDeliveryConfigQueryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestParamCheck));
		}

		Long tenantId = request.getTenantId();
		Long storeId = request.getStoreId();
		StoreAggDeliveryConfigDto storeAggDeliveryConfigDto = new StoreAggDeliveryConfigDto();
		try {
			List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAggDeliveryPoi(tenantId, storeId);
			List<Integer> channelOrder = deliveryPoiRepository.queryChannelOrder();
			// 对门店渠道按照配置中的顺序进行排序，如果门店渠道在配置中不存在，则排在最后
			deliveryPoiList.sort(((o1, o2) -> {
				int io1 = channelOrder.indexOf(o1.getChannelType());
				io1 = io1 == -1? Integer.MAX_VALUE: io1;
				int io2 = channelOrder.indexOf(o2.getChannelType());
				io2 = io2 == -1? Integer.MAX_VALUE: io2;
				return io1 - io2;
			}));
			List<DeliveryChannelLaunchPointDto> deliveryChannelLaunchPoints = deliveryPoiList.stream().map(this::transfer2DeliveryLaunchPoint).collect(Collectors.toList());
			storeAggDeliveryConfigDto.setDeliveryChannelLaunchPoints(deliveryChannelLaunchPoints);
			return new StoreAggDeliveryConfigQueryResponse(Status.SUCCESS, storeAggDeliveryConfigDto);
		} catch (Exception e) {
			log.error("queryStoreAggDeliveryConfig error", e);
			return new StoreAggDeliveryConfigQueryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public StoreDeliveryConfigQueryResponse queryStoreDeliveryConfig(StoreDeliveryConfigQueryRequest request) {
		String requestParamCheck = request.validate();
		if (Objects.nonNull(requestParamCheck)) {
			return new StoreDeliveryConfigQueryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), requestParamCheck));
		}

		Long tenantId = request.getTenantId();
		Long storeId = request.getStoreId();
		StoreDeliveryConfigDto storeDeliveryConfigDto = new StoreDeliveryConfigDto();
		try {
			List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAggDeliveryPoi(tenantId, storeId);
			List<Integer> channelOrder = deliveryPoiRepository.queryChannelOrder();
			// 对门店渠道按照mcc配置中的顺序进行排序，如果门店渠道在配置中不存在，则排在最后
			deliveryPoiList.sort(((o1, o2) -> {
				int io1 = channelOrder.indexOf(o1.getChannelType());
				io1 = io1 == -1? Integer.MAX_VALUE: io1;
				int io2 = channelOrder.indexOf(o2.getChannelType());
				io2 = io2 == -1? Integer.MAX_VALUE: io2;
				return io1 - io2;
			}));
			List<StoreChannelDeliveryConfigDto> storeChannelDeliveryConfigList = transfer2DeliveryConfig(deliveryPoiList, request);
			storeDeliveryConfigDto.setStoreChannelDeliveryConfigList(storeChannelDeliveryConfigList);
			return new StoreDeliveryConfigQueryResponse(Status.SUCCESS, storeDeliveryConfigDto);
		} catch (Exception e) {
			log.error("queryStoreDeliveryConfig error", e);
			return new StoreDeliveryConfigQueryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public ConfigCommonResponse modifyStoreAggDeliveryConfig(StoreAggDeliveryConfigModifyRequest request) {
		String check = request.validate();
		if (check != null) {
			return new ConfigCommonResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), check));
		}
		// 这个修改方法是不改动开关，只修改配置项，修改配置项则开关肯定是打开的
		if (isValidPoiBasicSettings(request) && isPoiBasicSettingsNotLegal(request.getTenantId(), request.getStoreId(), true)) {
			return new ConfigCommonResponse(Status.from(FailureCodeEnum.POI_BASIC_SETTINGS_INCOMPLETE));
		}

		Long tenantId = request.getTenantId();
		Long storeId = request.getStoreId();
		Long operatorId = request.getOperatorId();
		String operatorName = request.getOperatorName();
		StoreAggDeliveryConfigDto storeAggDeliveryConfigDto = request.getStoreAggDeliveryConfigDto();
		if (CollectionUtils.isEmpty(storeAggDeliveryConfigDto.getDeliveryChannelLaunchPoints())) {
			log.info("修改聚合配送设置，配送规则无改动，deliveryChannelLaunchPoints为空列表, tenantId: {}, storeId: {}", tenantId, storeId);
			return new ConfigCommonResponse(Status.SUCCESS);
		}

		try {
			deliveryConfigApplicationService.modifyStoreAggDeliveryConfig(tenantId, storeId, operatorId, operatorName
					, storeAggDeliveryConfigDto);
		} catch (ChannelNotExistException e) {
			log.error("修改聚合配送设置失败, 下发参数中的渠道与store_config表的渠道不一致, tenantId: {}, storeId: {}", tenantId, storeId);
			return new ConfigCommonResponse(new Status(FailureCodeEnum.CHANNEL_NOT_EXIST_EXCEPTION.getCode(), e.getMessage()));
		} catch (BizException e) {
			log.error("修改聚合配送设置业务失败, tenantId: {}, storeId: {}", tenantId, storeId);
			return new ConfigCommonResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("修改聚合配送设置失败, tenantId: {}, storeId: {}", tenantId, storeId);
			return new ConfigCommonResponse(new Status(FailureCodeEnum.SYSTEM_ERROR.getCode(), e.getMessage()));
		}
		return new ConfigCommonResponse(Status.SUCCESS);
	}

	@Override
	@CatTransaction
	public StoreDeliveryConfigBatchQueryResponse batchQueryStoreDeliveryConfig(BatchStoreDeliveryConfigQueryRequest request) {
		String validateResult = request.validate();
		if (Objects.nonNull(validateResult)) {
			return new StoreDeliveryConfigBatchQueryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), validateResult), Collections.emptyList());
		}

		Long tenantId = request.getTenantId();
		List<Long> storeIdList = request.getStoreIdList();

		try {
			List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.batchQueryDeliveryPoi(tenantId, storeIdList);
			if (CollectionUtils.isEmpty(deliveryPoiList)) {
				log.warn("batchQueryStoreDeliveryConfig, deliveryPoiList is empty");
				return new StoreDeliveryConfigBatchQueryResponse(Status.SUCCESS, Collections.emptyList());
			}

			List<BatchStoreDeliveryConfigDto> batchStoreDeliveryConfigDtoList = transfer2BatchStoreDeliveryConfigList(deliveryPoiList);
			StoreDeliveryConfigBatchQueryResponse response = new StoreDeliveryConfigBatchQueryResponse();
			response.setStatus(Status.SUCCESS);
			response.setBatchStoreDeliveryConfigDtoList(batchStoreDeliveryConfigDtoList);
			return response;
		} catch (Exception e) {
			log.error("batchQueryStoreDeliveryConfig error", e);
			return new StoreDeliveryConfigBatchQueryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()), Collections.emptyList());
		}
	}

	@Override
	public ConfigCommonResponse saveBatchImportDeliveryStoreConfiguration(BatchImportDeliveryStoreConfigSaveRequest request) {
		ConfigCommonResponse response = new ConfigCommonResponse();
		response.setStatus(Status.SUCCESS);

		String validateResult = request.validate();
		if (Objects.nonNull(validateResult)) {
			return new ConfigCommonResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), validateResult));
		}

		try {
			Long storeId = request.getStoreId();
			Long tenantId = request.getTenantId();
			Integer module = request.getModule();
			Long operatorId = request.getOperatorId();
			String operatorName = request.getOperatorName();
			List<DeliveryStoreConfigDto> deliveryPlatformConfigList = request.getDeliveryStoreConfigDtoList();

			Map<Integer, DeliveryStoreConfigDto> deliveryPoiMap = deliveryPlatformConfigList.stream()
					.collect(Collectors.toMap(DeliveryStoreConfigDto::getChannelType, tAggDeliveryPlatformConfig -> tAggDeliveryPlatformConfig));

			StoreConfigInfo storeConfigInfo = deliveryConfigApplicationService.buildBatchImportDeliveryPoiConfigList(storeId, tenantId, deliveryPlatformConfigList, operatorId, operatorName);
			List<DeliveryPoi> deliveryPoiList = storeConfigInfo.getDeliveryPoiList();
			List<DeliveryPoi> saveDeliveryPoi = storeConfigInfo.getSaveDeliveryPoiList();

			//开通麦芽田/青云聚信平台门店
			Set<Integer> platformCodeList = Sets.newHashSet();
			for(DeliveryPoi deliveryPoi: deliveryPoiList) {
				DeliveryStoreConfigDto platformConfig = deliveryPoiMap.get(deliveryPoi.getChannelType());
				SaveConfigCmd cmd = buildSaveConfigCmd(storeId, tenantId, module, platformConfig);
				if (isDeliveryPlatformOpen(cmd) && (isMaltDeliveryPlatformOpen(cmd) || isDapPlatformOpen(cmd))) {
					if(platformCodeList.contains(cmd.getDeliveryPlatformInfo().getPlatformCode())) {
						continue;
					}
					platformCodeList.add(cmd.getDeliveryPlatformInfo().getPlatformCode());
					log.info("createShop deliveryPoi:{}", deliveryPoi);
					deliveryPlatformClient.createShop(deliveryPoi);
					fastAuth(deliveryPoi);
				}
			}

			//保存门店配置修改
			deliveryPoiRepository.batchSaveDeliveryPoi(saveDeliveryPoi);
		} catch (DeliveryPlatformException e) {
			log.error("delivery platform fail", e);
			response.setStatus(new Status(e.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("saveStoreConfiguration error", e);
			response.setStatus(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
		return response;
	}

	@Override
	public DapShopAuthNotifyResp dapShopAuthNotify(DapShopAuthNotifyRequest req) {
		DapShopAuthNotifyResp response = new DapShopAuthNotifyResp();
		response.setStatus(Status.SUCCESS);
		if(req==null ||  StringUtils.isEmpty(req.getStoreId())){
			response.setStatus(new Status(ResponseCodeEnum.REQUEST_PARAMS_ERROR.getValue(), ResponseCodeEnum.REQUEST_PARAMS_ERROR.name()));
			return response;
		}
		Optional<ShopAuthInfo> shopAuthInfo = shopAuthSquirrelOperationService.get(req.getStoreId(), ShopAuthInfo.class);
		if (shopAuthInfo.isPresent()) {
			ShopAuthInfo shopAuth = shopAuthInfo.get();
			DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM;
			StoreConfigInfo storeConfigInfo = deliveryConfigApplicationService.buildBatchOpenDapShop(shopAuth.getDeliveryConfigs(), shopAuth.getTenantId(), shopAuth.getStoreId(), deliveryPlatform);
			List<DeliveryPoi> deliveryPoiList = storeConfigInfo.getDeliveryPoiList();
			fastAuth(deliveryPoiList.get(0));
			//保存门店配置
			deliveryPoiRepository.batchSaveDeliveryPoi(deliveryPoiList);
			deliveryConfigApplicationService.batchSaveStoreConfigLog(deliveryPoiList, storeConfigInfo.getOpInfoMap(),
					shopAuth.getOperatorId(), shopAuth.getOperatorName());
			return response;
		}
		response.setStatus(new Status(ResponseCodeEnum.UNKNOWN_ERROR.getValue(), ResponseCodeEnum.UNKNOWN_ERROR.name()));
		return response;

	}

	@Override
	public DeliveryConfigSimpleQueryResponse simpleQueryDeliveryConfig(StoreDeliveryConfigSimpleQueryRequest request) {
		String validateResult = request.validate();
		if (Objects.nonNull(validateResult)) {
			return new DeliveryConfigSimpleQueryResponse(new Status(FailureCodeEnum.INVALID_PARAM.getCode(), validateResult), Collections.emptyList());
		}

		Long tenantId = request.getTenantId();
		List<Long> storeIdList = request.getStoreIdList();

		try {
			List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.batchQueryDeliveryPoiList(tenantId, storeIdList);
			if (CollectionUtils.isEmpty(deliveryPoiList)) {
				log.warn("simpleQueryDeliveryConfig, deliveryPoiList is empty");
				return new DeliveryConfigSimpleQueryResponse(Status.SUCCESS, Collections.emptyList());
			}

			List<BatchStoreDeliveryConfigDto> batchStoreDeliveryConfigDtoList = transfer2BatchStoreDeliveryConfigList(deliveryPoiList);
			DeliveryConfigSimpleQueryResponse response = new DeliveryConfigSimpleQueryResponse();
			response.setStatus(Status.SUCCESS);
			response.setBatchStoreDeliveryConfigDtoList(batchStoreDeliveryConfigDtoList);
			return response;
		} catch (Exception e) {
			log.error("simpleQueryDeliveryConfig error", e);
			return new DeliveryConfigSimpleQueryResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()), Collections.emptyList());
		}
	}

	//平台开通修改判断
	private boolean isDeliveryPlatformOpen(SaveConfigCmd cmd) {
		return (cmd.getModule().equals(ConfigModuleEnum.PLATFORM.getValue())) && cmd.getDeliveryPlatformInfo() != null;
	}

	//打开麦芽田开关
	private boolean isMaltDeliveryPlatformOpen(SaveConfigCmd cmd) {
		Integer openFlag = cmd.getDeliveryPlatformInfo().getOpenFlag();
		DeliveryPlatformEnum platformEnum = DeliveryPlatformEnum.enumOf(cmd.getDeliveryPlatformInfo().getPlatformCode());
		return platformEnum == DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM
				&& openFlag != null && openFlag == BigInteger.ONE.intValue();
	}

	//打开青云聚信平台开关
	private boolean isDapPlatformOpen(SaveConfigCmd cmd) {
		Integer openFlag = cmd.getDeliveryPlatformInfo().getOpenFlag();
		DeliveryPlatformEnum platformEnum = DeliveryPlatformEnum.enumOf(cmd.getDeliveryPlatformInfo().getPlatformCode());
		return platformEnum == DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM
				&& openFlag != null && openFlag == BigInteger.ONE.intValue();
	}

	private SaveConfigCmd buildSaveConfigCmd (Long storeId, Long tenantId, Integer module, DeliveryStoreConfigDto platformConfig) {
		SaveConfigCmd cmd=SaveConfigCmd.builder()
				.storeId(storeId)
				.tenantId(tenantId)
				.module(module)
				.deliveryPlatformInfo(AggDeliveryPlatformInfo.builder()
						.platformCode(platformConfig.getPlatformCode())
						.channelType(platformConfig.getChannelType())
						.openFlag(platformConfig.getOpenFlag())
						.build())
				.build();
		return cmd;
	}

	private DeliveryChannelInfo buildSyncDelivery(StoreConfigSaveRequest req) {
		if (Objects.isNull(req.getTOuterDeliveryConfig())) {
			return null;
		}
		return DeliveryChannelInfo.builder()
				.deliveryChannelId(req.getTOuterDeliveryConfig().getDeliveryChannelId())
				.deliveryChannelMerchantId(req.getTOuterDeliveryConfig().getDeliveryChannelMerchantId())
				.deliveryChannelName(req.getTOuterDeliveryConfig().getDeliveryChannelName())
				.deliveryChannelPoiId(req.getTOuterDeliveryConfig().getDeliveryChannelPoiId())
				.deliveryChannelPoiExt(req.getTOuterDeliveryConfig().getDeliveryChannelPoiExt())
				.syncType(req.getTOuterDeliveryConfig().getOperateType())
				.storeId(req.getStoreId())
				.build();


	}

	private ImmediateOrderDeliveryLaunchPointConfig buildImmediateOrderDeliveryLaunchPointConfig(StoreConfigSaveRequest req) {
		return Optional.ofNullable(req.getTStoreConfig())
				.filter(it -> Objects.nonNull(req.getTStoreConfig().getAutoLaunchPoint()))
				.filter(it -> Objects.nonNull(ImmediateOrderDeliveryLaunchPointEnum.enumOf(req.getTStoreConfig().getAutoLaunchPoint())))
				.filter(it -> Objects.nonNull(req.getTStoreConfig().getAutoLaunchDelayMinutes()))
				.map(it -> new ImmediateOrderDeliveryLaunchPointConfig(
						ImmediateOrderDeliveryLaunchPointEnum.enumOf(req.getTStoreConfig().getAutoLaunchPoint()),
						req.getTStoreConfig().getAutoLaunchDelayMinutes()
				))
				.orElse(null);
	}

	private BookingOrderDeliveryLaunchPointConfig buildBookingOrderDeliveryLaunchPointConfig(StoreConfigSaveRequest req) {
		return Optional.ofNullable(req.getTStoreConfig())
				.filter(it -> Objects.nonNull(req.getTStoreConfig().getBookingOrderAutoLaunchPoint()))
				.filter(it -> Objects.nonNull(BookingOrderDeliveryLaunchPointEnum.enumOf(req.getTStoreConfig().getBookingOrderAutoLaunchPoint())))
				.filter(it -> Objects.nonNull(req.getTStoreConfig().getBookingOrderAutoLaunchMinutes()))
				.map(it -> new BookingOrderDeliveryLaunchPointConfig(
						BookingOrderDeliveryLaunchPointEnum.enumOf(req.getTStoreConfig().getBookingOrderAutoLaunchPoint()),
						req.getTStoreConfig().getBookingOrderAutoLaunchMinutes()
				))
				.orElse(null);
	}

	private AggDeliveryPlatformInfo buildDeliveryPlatform(StoreConfigSaveRequest req) {
		if (Objects.isNull(req.getTAggDeliveryPlatformConfig())) {
			return null;
		}
		TAggDeliveryPlatformConfig platformConfig = req.getTAggDeliveryPlatformConfig();
		DeliveryPlatformEnum deliveryPlatform = DeliveryPlatformEnum.enumOf(platformConfig.getPlatformCode());
		if (deliveryPlatform == null) {
			return null;
		}


		return new AggDeliveryPlatformInfo(deliveryPlatform.getCode(), platformConfig.getOpenFlag(),platformConfig.getChannelType()==null ? DynamicChannelType.MEITUAN.getChannelId() : platformConfig.getChannelType());
	}

	private List<StoreChannelDeliveryConfigDto> transfer2DeliveryConfig(List<DeliveryPoi> deliveryPoiList, StoreDeliveryConfigQueryRequest request) {
		Long storeId = request.getStoreId();
		if (CollectionUtils.isEmpty(deliveryPoiList)) {
			log.warn("fillDeliveryPlatformConfig, deliveryPoiList is empty");
			return Collections.emptyList();
		}

		// 查询青云聚信平台动态配置
		DeliveryPlatformUrlInfo dapUrlInfo = null;
		String storeIdStr = String.valueOf(storeId);
		if (deliveryPoiList.stream().anyMatch(deliveryPoi -> DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.equals(deliveryPoi.getDeliveryPlatform()))) {
			String token = request.getToken();
			SiteTypeEnum siteType = SiteTypeEnum.of(request.getDeviceType());
			Optional<Map<String, DeliveryPlatformUrlInfo>> optionalMap =
					dapDeliveryPlatformClient.queryLinkInfoOfToken(deliveryPoiList.get(NumberUtils.INTEGER_ZERO),
							Collections.singletonList(storeIdStr), LinkTypeEnum.SHOP_LINK_TYPE, token, siteType);
			if (optionalMap.isPresent()) {
				Map<String, DeliveryPlatformUrlInfo> infoMap = optionalMap.get();
				if (MapUtils.isNotEmpty(infoMap) && infoMap.containsKey(storeIdStr)) {
					dapUrlInfo = infoMap.get(storeIdStr);
				}
			}
		}

		List<StoreChannelDeliveryConfigDto> deliveryConfigList = Lists.newArrayList();
		for (DeliveryPoi deliveryPoi: deliveryPoiList) {
			StoreChannelDeliveryConfigDto deliveryConfig = new StoreChannelDeliveryConfigDto();
			deliveryConfig.setChannelType(deliveryPoi.getChannelType());
			// 填充配送发单节点和自动呼叫时间
			transfer2LaunchPointAndTimeDto(deliveryPoi, deliveryConfig);
			// 填充配送平台设置
			deliveryConfig.setDeliveryPlatformConfig(transfer2DeliveryPlatformConfigDto(deliveryPoi, dapUrlInfo, storeIdStr));
			deliveryConfigList.add(deliveryConfig);
		}
		return deliveryConfigList;
	}


	private Integer transfer2LaunchPointDto(DeliveryPoi deliveryPoi) {
		DeliveryLaunchPoint deliveryLaunchPoint = Optional.ofNullable(deliveryPoi.getDeliveryLaunchPoint())
				.orElseThrow(() -> new CommonRuntimeException("deliveryLaunchPoint is null"));
		DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getImmediateOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new CommonRuntimeException("immediateOrderDeliveryLaunchPointConfig is null"));
		ImmediateOrderDeliveryLaunchPointEnum launchPoint =
				Optional.ofNullable(immediateOrderDeliveryLaunchPointConfig.getLaunchPoint())
						.orElseThrow(() -> new CommonRuntimeException("launchPoint is null"));
		return launchPoint.getCode();
	}

	private void transfer2LaunchPointAndTimeDto(DeliveryPoi deliveryPoi, StoreChannelDeliveryConfigDto deliveryConfig) {
		DeliveryLaunchPoint deliveryLaunchPoint = Optional.ofNullable(deliveryPoi.getDeliveryLaunchPoint())
				.orElseThrow(() -> new CommonRuntimeException("deliveryLaunchPoint is null"));
		DeliveryLaunchPoint.ImmediateOrderDeliveryLaunchPointConfig immediateOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getImmediateOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new CommonRuntimeException("immediateOrderDeliveryLaunchPointConfig is null"));
		DeliveryLaunchPoint.BookingOrderDeliveryLaunchPointConfig bookingOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getBookingOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new CommonRuntimeException("bookingOrderDeliveryLaunchPointConfig is null"));
		ImmediateOrderDeliveryLaunchPointEnum launchPoint =
				Optional.ofNullable(immediateOrderDeliveryLaunchPointConfig.getLaunchPoint())
						.orElseThrow(() -> new CommonRuntimeException("launchPoint is null"));
		deliveryConfig.setDeliveryLaunchPoint(launchPoint.getCode());
		deliveryConfig.setDeliveryDelayLaunchMinutes(Optional.ofNullable(immediateOrderDeliveryLaunchPointConfig.getDelayMinutes())
				.orElse(0));
		deliveryConfig.setBookingOrderDeliveryLaunchMinutes(Optional.ofNullable(bookingOrderDeliveryLaunchPointConfig.getConfigMinutes())
				.orElse(60));
	}

	private DeliveryPlatformConfigDto transfer2DeliveryPlatformConfigDto(DeliveryPoi deliveryPoi,
																		 DeliveryPlatformUrlInfo dapUrlInfo,
																		 String storeIdStr) {
		DeliveryPlatformConfigDto deliveryPlatformConfig = new DeliveryPlatformConfigDto();

		//如果平台code不在mcc配置里，配送开关关闭
		List<Integer> displayDeliveryPlatformCodeList = MccConfigUtils.queryDisplayDeliveryPlatformCodeList();
		Integer openFlag = displayDeliveryPlatformCodeList.contains(deliveryPoi.getDeliveryPlatform().getCode())?
				NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO;
		deliveryPlatformConfig.setOpenFlag(openFlag);

		// 如果配送开关关闭，返回渠道上一次的配送平台code，前端用于打开开关后展示最后一次的配送平台配置
		Integer platformCode = DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode();
		if(NumberUtils.INTEGER_ONE.equals(openFlag)) {
			platformCode = deliveryPoi.getDeliveryPlatform().getCode();
		} else if ((deliveryPoi.getLastDeliveryPlatform()!= null)  && (displayDeliveryPlatformCodeList.contains(deliveryPoi.getLastDeliveryPlatform().getCode()))) {
			platformCode = deliveryPoi.getLastDeliveryPlatform().getCode();
		}
		deliveryPlatformConfig.setPlatformCode(platformCode);

		AggDeliveryPlatformAppConfig appConfig = transfer2AppConfig(platformCode, dapUrlInfo, storeIdStr);
		deliveryPlatformConfig.setAppKey(appConfig.getAppKey());
		deliveryPlatformConfig.setAppSecretKey(appConfig.getAppSecret());
		deliveryPlatformConfig.setRedirectUrl(appConfig.getRedirectUrl());

		return deliveryPlatformConfig;
	}

	private AggDeliveryPlatformAppConfig transfer2AppConfig(Integer platformCode, DeliveryPlatformUrlInfo dapUrlInfo, String storeIdStr) {
		AggDeliveryPlatformAppConfig appConfig =
				Optional.ofNullable(AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(platformCode))
						.orElse(new AggDeliveryPlatformAppConfig());

		if (DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM == DeliveryPlatformEnum.enumOf(platformCode)) {
			if (MccConfigUtils.maltConfigLinkNoAuthSwitch()) {
				String url = MessageFormat.format(appConfig.getConfigUrl(), appConfig.getAppKey(), storeIdStr);
				UrlUtils.UrlInfo urlInfo = getUrlInfo(appConfig.getConfigUrl(), url);
				appConfig.setRedirectUrl(UrlUtils.buildUrl4UrlInfo(urlInfo));
			}else {
				appConfig.setRedirectUrl(null);
			}
		} else if (DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM == DeliveryPlatformEnum.enumOf(platformCode) && Objects.nonNull(dapUrlInfo)) {
			appConfig.setRedirectUrl(dapUrlInfo.getLinkUrl());
		}

		return appConfig;
	}

	private UrlUtils.UrlInfo getUrlInfo(String configUrl, String url) {
		UrlUtils.UrlInfo urlInfo = UrlUtils.buildUrlInfo4Url(url);
		Map<String, String> searchParams = urlInfo.getSearchParams();
		Map<String, Object> signMap = new HashMap<>(searchParams);
		String sign = MaltFarmSignUtils.generateSignature(signMap, configUrl);
		searchParams.put("sign", sign);
		return urlInfo;
	}

	private DeliveryChannelLaunchPointDto transfer2DeliveryLaunchPoint(DeliveryPoi deliveryPoi) {
		DeliveryLaunchPoint deliveryLaunchPoint = Optional.ofNullable(deliveryPoi.getDeliveryLaunchPoint())
				.orElseThrow(() -> new CommonRuntimeException("deliveryLaunchPoint is null"));
		ImmediateOrderDeliveryLaunchPointConfig immediateOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getImmediateOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new CommonRuntimeException("immediateOrderDeliveryLaunchPointConfig is null"));
		BookingOrderDeliveryLaunchPointConfig bookingOrderDeliveryLaunchPointConfig =
				Optional.ofNullable(deliveryLaunchPoint.getBookingOrderDeliveryLaunchPointConfig())
						.orElseThrow(() -> new CommonRuntimeException("immediateOrderDeliveryLaunchPointConfig is null"));
		ImmediateOrderDeliveryLaunchPointEnum launchPoint =
				Optional.ofNullable(immediateOrderDeliveryLaunchPointConfig.getLaunchPoint())
						.orElseThrow(() -> new CommonRuntimeException("launchPoint is null"));
		return DeliveryChannelLaunchPointDto.builder().channelType(deliveryPoi.getChannelType())
				.deliveryLaunchPoint(launchPoint.getCode())
				.deliveryLaunchDelayMinutes(Optional.ofNullable(immediateOrderDeliveryLaunchPointConfig.getDelayMinutes()).orElse(0))
				.bookingOrderDeliveryLaunchMinutes(Optional.ofNullable(bookingOrderDeliveryLaunchPointConfig.getConfigMinutes()).orElse(60)).build();
	}

	private List<ShippingTagEnum> parseShippingTagList(List<PoiShippingInfoDTO> poiShippingInfoDTOS, Coordinate coordinate) {
		Point point = GeoUtils.createPoint(coordinate);

		//先获取包含收货地址的所有范围
		List<PoiShippingInfoDTO> includeReceiverPointShippingList = poiShippingInfoDTOS.stream()
				.filter(poiShippingInfoDTO -> {
					Polygon deliveryArea = this.parseDeliveryArea(poiShippingInfoDTO);
					return point.within(deliveryArea);
				}).collect(Collectors.toList());

		//如果落在正常范围内,之间返回空List
		if (judgeIsWithinNormalRange(includeReceiverPointShippingList)) {
			return Collections.emptyList();
		}

		//否则返回所有包含收货地址的特殊范围标签
		return includeReceiverPointShippingList.stream()
				.filter(dto -> StringUtils.isNotBlank(dto.getAppShippingCode()))
				.map(dto -> ShippingTagEnum.parseTagFromShippingCode(dto.getAppShippingCode()))
				.flatMap(Collection::stream)
				.distinct()
				.collect(Collectors.toList());
	}

	private Polygon parseDeliveryArea(PoiShippingInfoDTO poiShippingInfoDTO) {
		List<Coordination> area = poiShippingInfoDTO.getArea();
		return GeoUtils.createPolygon(area.stream()
				.map(coordinatePoint -> GeoUtils.createCoordinate(coordinatePoint.getX(), coordinatePoint.getY()))
				.collect(Collectors.toList()));
	}

	private Boolean judgeIsWithinNormalRange(List<PoiShippingInfoDTO> includeReceiverPointShippingList) {
		return includeReceiverPointShippingList.stream()
				.anyMatch(poiShippingInfoDTO -> CollectionUtils.isEmpty(ShippingTagEnum.parseTagFromShippingCode(poiShippingInfoDTO.getAppShippingCode())));
	}

    private void fastAuth(DeliveryPoi deliveryPoi) {
        if (!com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils
                .getTenantPoiFastAuthSwitch()) {
            log.info("绑定信息开关关闭");
            return;
        }
        if (deliveryPoi.getDeliveryPlatform() != DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM) {
            return;
        }
        try {

            TenantStoreInfo tenantStoreInfo = tenantSystemClient.queryStoreDetailInfo(deliveryPoi.getTenantId(),
                    deliveryPoi.getStoreId());
            if (tenantStoreInfo == null) {
                log.info("门店详情为空");
                return;
            }

            if (tenantStoreInfo.getEntityType() == null || tenantStoreInfo.getShippingMode() == null) {
                log.info("门店类型为空");
                return;
            }
            Set<Long> wmPoiIdSet = new HashSet<>();
			List<ChannelStoreRelation> relationList = new ArrayList<>();
            if (tenantStoreInfo.getEntityType() == PoiEntityTypeEnum.STORE.code()
                    && tenantStoreInfo.getShippingMode() == PoiShippingModeEnum.SHIP_BY_STORE.code()) {
                ChannelStoreQueryResult result = tenantSystemClient
                        .queryChannelStoreDetailInfo(deliveryPoi.getTenantId(), deliveryPoi.getStoreId());
                if (result == null || CollectionUtils.isEmpty(result.getStoreInfos())) {
                    return;
                }
                Optional<TenantChannelStoreInfo> channelStore = Optional.ofNullable(result.getStoreInfos())
                        .orElse(Lists.newArrayList()).stream().filter(tenantChannelStoreInfo -> tenantChannelStoreInfo
                                .getChannelId().equals(ChannelType.MEITUAN.getValue()))
                        .findFirst();

                channelStore.ifPresent(tenantChannelStoreInfo -> wmPoiIdSet
                        .add(NumberUtils.toLong(tenantChannelStoreInfo.getChannelPoiId())));

				if(!com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(deliveryPoi.getTenantId())){
					if(channelStore.isPresent()){
						String channelPoiId = channelStore.get().getChannelInnerPoiId();
						if(StringUtils.isEmpty(channelPoiId)){
							channelPoiId = channelStore.get().getChannelPoiId();
						}
						relationList.add(new ChannelStoreRelation(deliveryPoi.getStoreId(),NumberUtils.toLong(channelPoiId)));
					}else {
						relationList.add(new ChannelStoreRelation(deliveryPoi.getStoreId(),0L));
					}
				}
            } else {
                Map<Long, List<Long>> relationPoiMap = tenantSystemClient.batchQueryRelationMapByPoiIds(
                        deliveryPoi.getTenantId(), Arrays.asList(deliveryPoi.getStoreId()), true);
                if (MapUtils.isEmpty(relationPoiMap)
                        || CollectionUtils.isEmpty(relationPoiMap.get(deliveryPoi.getStoreId()))) {
                    log.info("正向关系为空");
                    return;
                }
                List<Long> relationPoiList = relationPoiMap.get(deliveryPoi.getStoreId());
                if (CollectionUtils.isNotEmpty(relationPoiList)) {
                    Map<Long, List<TenantChannelStoreInfo>> channelStoreMap = tenantSystemClient
                            .queryChannelStoreDetailInfoList(deliveryPoi.getTenantId(),
                                    new ArrayList<>(relationPoiList));
                    if (MapUtils.isNotEmpty(channelStoreMap)) {
                        for (Long poiId : channelStoreMap.keySet()) {
                            List<TenantChannelStoreInfo> channelStoreInfoList = channelStoreMap.get(poiId);
                            if (CollectionUtils.isEmpty(channelStoreInfoList)) {
								if(!com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(deliveryPoi.getTenantId())){
									relationList.add(new ChannelStoreRelation(poiId,0L));
								}
                                continue;
                            }
							Optional<TenantChannelStoreInfo> mtStoreInfo = channelStoreInfoList.stream()
                                    .filter(tenantChannelStoreInfo -> tenantChannelStoreInfo.getChannelId()
                                            .equals(ChannelType.MEITUAN.getValue()))
                                    .findFirst();
							mtStoreInfo.ifPresent(tenantChannelStoreInfo -> wmPoiIdSet
									.add(NumberUtils
											.toLong(tenantChannelStoreInfo.getChannelPoiId())));

							if(!com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(deliveryPoi.getTenantId())){
								if(mtStoreInfo.isPresent()){
									String channelPoiId = mtStoreInfo.get().getChannelInnerPoiId();
									if(StringUtils.isEmpty(channelPoiId)){
										channelPoiId = mtStoreInfo.get().getChannelPoiId();
									}
									relationList.add(new ChannelStoreRelation(poiId,NumberUtils.toLong(channelPoiId)));
								}else {
									relationList.add(new ChannelStoreRelation(poiId,0L));
								}
							}
                        }
                    }
                }
            }

            deliveryPlatformClient.deliveryFastAuth(deliveryPoi, new ArrayList<>(wmPoiIdSet),relationList);
        } catch (Exception e) {
            log.error("fastAuth error deliveryPoi:{}", deliveryPoi, e);
        }
    }

	/**
	 * 批量导入门店配送配置使用
	 */
	private List<BatchStoreDeliveryConfigDto> transfer2BatchStoreDeliveryConfigList(List<DeliveryPoi> deliveryPoiList) {
		List<BatchStoreDeliveryConfigDto> result = Lists.newArrayList();
		Map<Long, List<DeliveryPoi>> deliveryPoiMap = deliveryPoiList.stream().collect(Collectors.groupingBy(DeliveryPoi::getStoreId));
		deliveryPoiMap.forEach((storeId, deliveryPoiValueList) -> {
			BatchStoreDeliveryConfigDto deliveryConfigDto = new BatchStoreDeliveryConfigDto();
			deliveryConfigDto.setStoreId(storeId);
			deliveryConfigDto.setStoreChannelDeliveryConfigList(transfer2SubDeliveryConfig(deliveryPoiValueList));
			result.add(deliveryConfigDto);
		});
		return result;
	}

	/**
	 * 批量导入门店配送配置使用
	 */
	private List<StoreSubDeliveryConfigDto> transfer2SubDeliveryConfig(List<DeliveryPoi> deliveryPoiList) {
		if (CollectionUtils.isEmpty(deliveryPoiList)) {
			log.warn("fillDeliveryPlatformConfig, deliveryPoiList is empty");
			return Collections.emptyList();
		}

		List<Integer> displayDeliveryPlatformCodeList = MccConfigUtils.queryDisplayDeliveryPlatformCodeList();

		List<StoreSubDeliveryConfigDto> deliveryConfigList = Lists.newArrayList();
		for (DeliveryPoi deliveryPoi: deliveryPoiList) {
			StoreSubDeliveryConfigDto deliveryConfig = new StoreSubDeliveryConfigDto();
			deliveryConfig.setChannelType(deliveryPoi.getChannelType());
			Integer openFlag = displayDeliveryPlatformCodeList.contains(deliveryPoi.getDeliveryPlatform().getCode())?
					NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO;
			deliveryConfig.setOpenFlag(openFlag);
			// 导入模板里的"当前配送方式参考"页签,当不是牵牛花管理配送时,不展示配送发单节点和配送平台
			if (NumberUtils.INTEGER_ONE.equals(openFlag)) {
				deliveryConfig.setDeliveryLaunchPoint(transfer2LaunchPointDto(deliveryPoi));
				deliveryConfig.setDeliveryPlatformCode(deliveryPoi.getDeliveryPlatform().getCode());
			}
			deliveryConfigList.add(deliveryConfig);
		}
		return deliveryConfigList;
	}

	private boolean isPoiBasicSettingsNotLegal(long tenantId, long poiId, boolean opening) {
		if (DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)) {
			log.info("DrunkHorse tenant: {}", tenantId);
			return false;
		}
		if (!opening) {
			// 只有开关打开时才校验门店基本设置
			log.info("Not opening delivery config, ignore this, poi: {}", poiId);
			return false;
		}
		PoiInfoDto poi = poiFacade.queryPoiManagerInfo(poiId);
		log.info("Checking poi: {}", poi);
		return StringUtils.isBlank(poi.getManager()) || StringUtils.isBlank(poi.getMobile())
				|| StringUtils.isBlank(poi.getPoiAddress())
				|| poi.getLatitude() == null || poi.getLongitude() == null;
	}

	private boolean isPoiBasicSettingsNotLegalExcludeLatitudeAndLongitude(long tenantId, long poiId, boolean opening) {
		if (DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)) {
			log.info("DrunkHorse tenant: {}", tenantId);
			return false;
		}
		if (!opening) {
			// 只有开关打开时才校验门店基本设置
			log.info("Not opening delivery config, ignore this, poi: {}", poiId);
			return false;
		}
		PoiInfoDto poi = poiFacade.queryPoiManagerInfo(poiId);
		log.info("Checking poi: {}", poi);
		return StringUtils.isBlank(poi.getManager()) || StringUtils.isBlank(poi.getMobile())
				|| StringUtils.isBlank(poi.getPoiAddress());
	}

	private boolean isPoiBasicSettingsNotLegalExcludeLatitudeAndLongitude(long tenantId, long poiId) {
		if (DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)) {
			log.info("DrunkHorse tenant: {}", tenantId);
			return false;
		}

		PoiInfoDto poi = poiFacade.queryPoiManagerInfo(poiId);
		return StringUtils.isBlank(poi.getManager()) || StringUtils.isBlank(poi.getMobile())
				|| StringUtils.isBlank(poi.getPoiAddress());
	}

	/**
	 * 是否需要校验门店基础信息
	 *
	 * @param request 需要保存的配置请求
	 * @return boolean
	 */
	private boolean isValidPoiBasicSettings(DeliveryStoreConfigSaveRequest request, boolean opening) {
		if (!opening) {
			return false;
		}
		List<DeliveryStoreConfigDto> deliveryStoreConfigList = request.getDeliveryStoreConfigDto();
		if (CollectionUtils.isEmpty(deliveryStoreConfigList) ) {
			return false;
		}
		// 有赞&抖音-平台配送 不做基础校验
		Optional<DeliveryStoreConfigDto> storeConfigOptional = deliveryStoreConfigList.stream()
				.filter(configDto -> {return isOrderChannelDeliveryPlatform(configDto.getChannelType(), configDto.getPlatformCode());})
				.findFirst();
		if (!storeConfigOptional.isPresent()) {
			return true;
		}
		StoreDeliveryConfigQueryRequest queryRequest = new StoreDeliveryConfigQueryRequest();
		queryRequest.setTenantId(request.getTenantId());
		queryRequest.setStoreId(request.getStoreId());
		StoreDeliveryConfigQueryResponse response = this.queryStoreDeliveryConfig(queryRequest);
		if (!Status.SUCCESS.equals(response.getStatus())) {
			return true;
		}
		StoreDeliveryConfigDto storeDeliveryConfigDto = response.getStoreDeliveryConfigDto();
		List<StoreChannelDeliveryConfigDto> storeChannelDeliveryConfigList = storeDeliveryConfigDto.getStoreChannelDeliveryConfigList();
		// 变更的渠道列表
		List<DeliveryStoreConfigDto> list = deliveryStoreConfigList.stream()
				.filter(item -> !storeChannelDeliveryConfigList.stream()
						// 字段为null不会更新DB字段，可以理解为未变更
						.filter(storeConfig -> item.getDeliveryLaunchDelayMinutes() == null
								|| Objects.equals(item.getDeliveryLaunchDelayMinutes(), storeConfig.getDeliveryDelayLaunchMinutes()))
						.filter(storeConfig -> item.getBookingOrderDeliveryLaunchMinutes() == null
								|| Objects.equals(item.getBookingOrderDeliveryLaunchMinutes(), storeConfig.getBookingOrderDeliveryLaunchMinutes()))
						.filter(storeConfig -> Objects.equals(item.getChannelType(), storeConfig.getChannelType()))
						.filter(storeConfig -> Objects.equals(item.getOpenFlag(), storeConfig.getDeliveryPlatformConfig().getOpenFlag()))
						.filter(storeConfig -> Objects.equals(item.getPlatformCode(), storeConfig.getDeliveryPlatformConfig().getPlatformCode()))
						.filter(storeConfig -> Objects.equals(item.getDeliveryLaunchPoint(), storeConfig.getDeliveryLaunchPoint()))
						.findFirst().isPresent()
				).collect(Collectors.toList());
		log.info("变更的渠道列表 list:{}", list);
		if (CollectionUtils.isEmpty(list)) {
			return true;
		}
		Set<Integer> channelTypeSet = list.stream().map(DeliveryStoreConfigDto::getChannelType).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(channelTypeSet)) {
			return true;
		}
		//比较修改配送配置的渠道是否是抖音&有赞，如果是则removeAll之后channelTypeSet为空，channelTypeSet参数别的地方不要调用！！
		channelTypeSet.removeAll(channelTypeList);
		if(CollectionUtils.isEmpty(channelTypeSet)) {
			return false;
		}
		return true;
	}

	private boolean isValidPoiBasicSettings(StoreAggDeliveryConfigModifyRequest request) {
		try {
			// 有赞-平台配送 不做基础校验
			List<DeliveryChannelLaunchPointDto> deliveryChannelLaunchPoints = request.getStoreAggDeliveryConfigDto().getDeliveryChannelLaunchPoints();
			Optional<DeliveryChannelLaunchPointDto> yzStoreConfigOptional = deliveryChannelLaunchPoints.stream()
					.filter(configDto -> Objects.equals(DynamicChannelType.YOU_ZAN.getChannelId(), configDto.getChannelType()))
					.findFirst();
			if (!yzStoreConfigOptional.isPresent()) {
				return true;
			}
			StoreDeliveryConfigQueryRequest queryRequest = new StoreDeliveryConfigQueryRequest();
			queryRequest.setTenantId(request.getTenantId());
			queryRequest.setStoreId(request.getStoreId());
			StoreDeliveryConfigQueryResponse response = this.queryStoreDeliveryConfig(queryRequest);
			if (!Status.SUCCESS.equals(response.getStatus())) {
				return true;
			}
			StoreDeliveryConfigDto storeDeliveryConfigDto = response.getStoreDeliveryConfigDto();
			List<StoreChannelDeliveryConfigDto> storeChannelDeliveryConfigList = storeDeliveryConfigDto.getStoreChannelDeliveryConfigList();
			Optional<StoreChannelDeliveryConfigDto> configDtoOptional = storeChannelDeliveryConfigList.stream()
					.filter(item -> Objects.equals(item.getChannelType(), DynamicChannelType.YOU_ZAN.getChannelId()))
					.findFirst();
			if (!configDtoOptional.isPresent()) {
				return true;
			}
			StoreChannelDeliveryConfigDto deliveryConfigDto = configDtoOptional.get();
			if (!Objects.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(), deliveryConfigDto.getDeliveryPlatformConfig().getPlatformCode())) {
				return true;
			}
			// 变更的渠道列表
			List<DeliveryChannelLaunchPointDto> list = deliveryChannelLaunchPoints.stream()
					.filter(item -> !storeChannelDeliveryConfigList.stream()
							.filter(storeConfig -> Objects.equals(item.getDeliveryLaunchDelayMinutes(), storeConfig.getDeliveryDelayLaunchMinutes()))
							.filter(storeConfig -> Objects.equals(item.getBookingOrderDeliveryLaunchMinutes(), storeConfig.getBookingOrderDeliveryLaunchMinutes()))
							.filter(storeConfig -> Objects.equals(item.getChannelType(), storeConfig.getChannelType()))
							.filter(storeConfig -> Objects.equals(item.getDeliveryLaunchPoint(), storeConfig.getDeliveryLaunchPoint()))
							.findFirst().isPresent()
					).collect(Collectors.toList());
			if (list.size() == 1 && Objects.equals(DynamicChannelType.YOU_ZAN.getChannelId(), list.get(0).getChannelType())) {
				return false;
			}
		} catch (Exception e) {
			log.error("isValidPoiBasicSettings error: ", e);
		}
		return true;
	}

	/**
	 * 是否需要校验门店基础信息，返回true代表需要校验
	 */
	private boolean isValidPoiBasicSettings(Long tenantId, Long storeId, List<DeliveryStoreConfigDto> cmdDeliveryStoreConfigList, boolean opening) {
		if (!opening) {
			// 如果门店配送配置都是非牵牛花管理配送，不需要校验门店基础信息
			return false;
		}
		if (CollectionUtils.isEmpty(cmdDeliveryStoreConfigList) ) {
			return false;
		}

		// 有赞&抖音&淘鲜达渠道的平台配送不需要做基础校验
		boolean isAllMatchOrderChannelDelivery = cmdDeliveryStoreConfigList.stream()
				.allMatch(configDto -> isOrderChannelDeliveryPlatform(configDto.getChannelType(), configDto.getPlatformCode()));
		if (isAllMatchOrderChannelDelivery) {
			return false;
		}

		// 查询门店配送配置
		StoreDeliveryConfigQueryRequest queryRequest = new StoreDeliveryConfigQueryRequest();
		queryRequest.setTenantId(tenantId);
		queryRequest.setStoreId(storeId);
		StoreDeliveryConfigQueryResponse response = queryStoreDeliveryConfig(queryRequest);
		if (!Status.SUCCESS.equals(response.getStatus())) {
			return true;
		}

		StoreDeliveryConfigDto storeDeliveryConfigDto = response.getStoreDeliveryConfigDto();
		List<StoreChannelDeliveryConfigDto> currentStoreDeliveryConfigList = storeDeliveryConfigDto.getStoreChannelDeliveryConfigList();
		if (CollectionUtils.isEmpty(currentStoreDeliveryConfigList)) {
			// 如果当前DB里没有对应的门店配送配置，需要做基础校验
			return true;
		}

		// 获取需要更新的渠道列表
		List<DeliveryStoreConfigDto> needUpdateDeliveryStoreConfigList = cmdDeliveryStoreConfigList.stream()
				.filter(cmdDeliveryStoreConfig -> isNeedUpdate4CmdDeliveryStoreConfig(cmdDeliveryStoreConfig, currentStoreDeliveryConfigList))
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(needUpdateDeliveryStoreConfigList)) {
			// 即使没有需要更新的渠道列表，也要校验已经保存在DB里的历史门店配置
			return true;
		}

		return needUpdateDeliveryStoreConfigList.stream().anyMatch(configDto -> !isOrderChannelDeliveryPlatform(configDto.getChannelType(), configDto.getPlatformCode()));
	}

	private boolean isNeedUpdate4CmdDeliveryStoreConfig(DeliveryStoreConfigDto cmdDeliveryStoreConfig,
														List<StoreChannelDeliveryConfigDto> currentStoreDeliveryConfigList) {
		return currentStoreDeliveryConfigList.stream().noneMatch(currentStoreDeliveryConfig -> isEqual4DeliveryStoreConfig(cmdDeliveryStoreConfig, currentStoreDeliveryConfig));
	}

	private boolean isEqual4DeliveryStoreConfig(DeliveryStoreConfigDto cmdDeliveryStoreConfig, StoreChannelDeliveryConfigDto currentStoreDeliveryConfig) {
		if (Objects.isNull(cmdDeliveryStoreConfig) || Objects.isNull(currentStoreDeliveryConfig)) {
			return false;
		}

		return Objects.equals(cmdDeliveryStoreConfig.getDeliveryLaunchDelayMinutes(), currentStoreDeliveryConfig.getDeliveryDelayLaunchMinutes())
				&& Objects.equals(cmdDeliveryStoreConfig.getBookingOrderDeliveryLaunchMinutes(), currentStoreDeliveryConfig.getBookingOrderDeliveryLaunchMinutes())
				&& Objects.equals(cmdDeliveryStoreConfig.getChannelType(), currentStoreDeliveryConfig.getChannelType())
				&& Objects.equals(cmdDeliveryStoreConfig.getOpenFlag(), currentStoreDeliveryConfig.getDeliveryPlatformConfig().getOpenFlag())
				&& Objects.equals(cmdDeliveryStoreConfig.getPlatformCode(), currentStoreDeliveryConfig.getDeliveryPlatformConfig().getPlatformCode())
				&& Objects.equals(cmdDeliveryStoreConfig.getDeliveryLaunchPoint(), currentStoreDeliveryConfig.getDeliveryLaunchPoint());
	}

}
