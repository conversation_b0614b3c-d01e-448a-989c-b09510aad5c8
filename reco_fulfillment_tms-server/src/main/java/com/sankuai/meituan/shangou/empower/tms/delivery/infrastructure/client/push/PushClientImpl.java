package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryRiderInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.RiderAccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountListByPoiAndAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountListByPoiAndRoleIdsRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.aop.LoadTestAop;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.configuration.TMSConstant;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PositionPushConfigEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryBaseException;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PushInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.push.RiderPushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.PositionPushConfigEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.PositionPushHelper;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.meituan.shangou.saas.message.dto.push.pike.pcclient.action.alert.AlertAction;
import com.sankuai.meituan.shangou.saas.message.dto.push.pike.pcclient.action.alert.AlertActionData;
import com.sankuai.meituan.shangou.saas.message.dto.push.pike.pcclient.action.alert.AlertTypeEnum;
import com.sankuai.meituan.shangou.saas.message.enums.push.PikeMsgModeEnum;
import com.sankuai.meituan.shangou.saas.message.request.push.FusionPushSendRequest;
import com.sankuai.meituan.shangou.saas.message.request.push.NotificationPushSendRequest;
import com.sankuai.meituan.shangou.saas.message.request.push.PushPikeMsgToPcClientReq;
import com.sankuai.meituan.shangou.saas.message.request.push.SharkPushSendRequest;
import com.sankuai.meituan.shangou.saas.message.response.BaseResponse;
import com.sankuai.meituan.shangou.saas.message.response.MessageCommonResponse;
import com.sankuai.meituan.shangou.saas.message.service.PushThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.push.DeliveryExceptionPushMessage.AppEnum.*;

/**
 * 面向用户的消息推送客户端实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/11
 */
@Slf4j
@Service
public class PushClientImpl implements PushClient {

	@Resource
	private AuthThriftService.Iface authThriftServiceClient;
	@Resource
	private PushThriftService pushThriftServiceClient;
	@Resource
	private BasePushClient basePushClient;
	@Resource
	private RiderPushClient riderPushClient;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;
	@Resource
	private PoiThriftService poiThriftServiceClient;

	@Resource
	@Lazy
	private OrderSystemClient orderSystemClient;

	// 牵牛花 push appId
	private final Integer SHU_GUO_PAI_PUSH_APP_ID = 5;

	// 自营配送骑手待领取任务 tab 权限码
	private final String SELF_RIDER_WAIT_TO_GET = "SELF_RIDER_WAIT_TO_GET";
	// 歪马三方配送异常TAB权限码
	private final String THIRD_ORDER_EXCEPTION_AUTH_CODE = "THIRD_ORDER_EXCEPTION_QUERYABLE";

	// 自营骑手新任务 tab 页的 key
	private final String RIDER_NEW_TASK_TAB_KEY = "RIDER_NEW_TASK";

	//自营配送骑手新任务-拣配任务 tab 权限码
	private final String SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE = "NEW_TASK_PICK_DELIVERY";

	private final static Long PUSH_CONFIG_ID_RIDER_DELIVERY_NEW = 1043L;
	private final static Long RESTAURANT_PUSH_CONFIG_ID_RIDER_DELIVERY_NEW = 1089L;
	private final static Long PUSH_CONFIG_ID_RIDER_ASSIGN_TIMEOUT = 1045L;
	private final static Long PUSH_CONFIG_ID_RIDER_TAKEN_GOODS_TIMEOUT = 1046L;
	private final static Long PUSH_CONFIG_ID_RIDER_DELIVERY_TIMEOUT_WARNING = 1047L;

	private final static Long PUSH_CONFIG_ID_DELIVERY_PAUSE_WARNING = 1048L;

	private final static Long PUSH_CONFIG_ID_DELIVERY_STATUS_ROLL_BACK_WARNING = 1049L;

	private final static Long PUSH_THIRD_ORDER_EXCEPTION_WARNING = 1061L;

	private static final int SUCCESS = 0;

	private final static Long PUSH_CONFIG_ID_FAST_ORDER_DELIVERY_TIMEOUT = 1120L;

	private final static Long PUSH_SEAL_CONTAINER_RETURN_TIMEOUT = 1130L;

	@Value("#{'${dh.storeManager.roleIds}'.split(',')}")
	private List<Long> dhStoreManagerRoleIds;

	@Resource
	private GrayManagementThriftService grayManagementThriftService;

	@Override
	@CatTransaction
	@LoadTestAop
	public void pushDeliveryException(DeliveryOrder deliveryOrder) {
		if(MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())) {
			//歪马异常push
			dhExceptionPush(deliveryOrder);
			return;
		}
		if (MccConfigUtils.pushDeliveryExceptionAuthCodeSwitch()) {
			DeliveryExceptionPushMessage message = new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(),
					SHU_GUO_PAI_MALT_FARM);

			doSendFusionPush(message,
					1041L,
					ImmutableMap
							.of("tenantId", String.valueOf(message.getTenantId()),
									"storeId", String.valueOf(message.getPoiId()))
			);
		} else {
			log.info("pushDeliveryExceptionAuthCodeSwitch is false");
			DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
			if (deliveryOrder.isAggregationDeliveryDeliveryOrder(deliveryChannel)
					|| deliveryOrder.isMaltFarmDeliveryOrder(deliveryChannel)
					|| deliveryOrder.isDapDeliveryOrder(deliveryChannel)) {
				DeliveryExceptionPushMessage message = new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(),
						SHU_GUO_PAI_MALT_FARM);

				doSendFusionPush(message,
						1041L,
						ImmutableMap
								.of("tenantId", String.valueOf(message.getTenantId()),
										"storeId", String.valueOf(message.getPoiId()))
				);
			}
			else {
				doSendPush(new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(), LV_YUE_ZHU_SHOU));
				doSendPush(new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(), SHU_GUO_PAI));
				doSendPush(new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(), NEW_SHU_GUO_PAI));
			}
		}

		// 推送给pc客户端
		pushDeliveryExceptionToPcClient(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getChannelOrderId());
	}

	private void dhExceptionPush(DeliveryOrder deliveryOrder) {
		Long tenantId = deliveryOrder.getTenantId();
		Long storeId = deliveryOrder.getStoreId();

		// 获取门店下具有"新任务 tab"权限的所有账号 ID
		List<Long> accountIds;
		try {
			accountIds = queryAccountListByPoiAndAuth(tenantId, storeId, THIRD_ORDER_EXCEPTION_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);
		} catch (Exception e) {
			log.error("pushMerchantSelfRiderAssignTimeout queryAccountListByPoiAndAuth fail orderId={}", deliveryOrder.getOrderId(), e);
			return;
		}
		if (CollectionUtils.isEmpty(accountIds)) {
			log.info("pushMerchantSelfRiderAssignTimeout accountIds is empty orderId={}", deliveryOrder.getOrderId());
			return;
		}

		ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of("tenantId", String.valueOf(tenantId), "storeId", String.valueOf(storeId));
		basePushClient.unifiedSendFusionPush(tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(),
				PUSH_THIRD_ORDER_EXCEPTION_WARNING, msgPropertyMap);
	}

	@Override
	@CatTransaction
	@Deprecated
	public void pushAggDeliveryException(DeliveryOrder deliveryOrder, String url) {
		DeliveryExceptionPushMessage message = new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(),
				SHU_GUO_PAI_MALT_FARM);
		doSendFusionPush(message, url);
	}

	@Override
	@CatTransaction
	@LoadTestAop
	public void pushMerchantSelfDeliveryOrderCancelled(DeliveryOrder deliveryOrder, DeliveryStatusEnum beforeStatus) {
		try {
			// 当前门店未开启取消配送提醒
			if (!MccConfigUtils.checkTenantStoreOpenCancelDeliveryPush(deliveryOrder.getStoreId())) {
				return;
			}
			//当前只对歪马做提醒
			if (!MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())) {
				return;
			}
			Rider riderInfo = deliveryOrder.getRiderInfo();
			if (Objects.isNull(riderInfo)) {
				log.info("No rider assigned, give up notifying");
				return;
			}

			if (!(riderInfo instanceof StaffRider)) {
				log.warn("Invalid Rider, give up notifying");
				return;
			}
			//获取订单信息
			Result<OrderInfo> orderInfo = orderSystemClient.getOrderInfo(deliveryOrder.getOrderKey(), false);
			Boolean orderIsCancel = Optional.ofNullable(orderInfo).map(Result::getInfo)
					.map(OrderInfo::getOrderStatus)
					.map(item -> Objects.equals(item, OrderStatusEnum.CANCELED.getValue()))
					.orElse(false);

			//订单未取消
			if (!orderIsCancel) {
				log.info("订单未取消 Order is not cancelled, give up notifying");
				return;
			}
			FusionPushSendRequest request = new FusionPushSendRequest();
			request.setTenantId(deliveryOrder.getTenantId());
			request.setPoiId(deliveryOrder.getStoreId());
			request.setAccountIds(Lists.newArrayList(((StaffRider)riderInfo).getRiderAccountId()));
			//牵牛花
			request.setAppCode("5");
			if (Objects.equals(beforeStatus, DeliveryStatusEnum.RIDER_ASSIGNED)
					|| Objects.equals(beforeStatus, DeliveryStatusEnum.RIDER_ARRIVED_SHOP)) {
				request.setEventConfigId(1171L);
			} else {
				request.setEventConfigId(1170L);
			}
			request.setMsgPropertyMap(ImmutableMap.of(
					"tenantId", String.valueOf(deliveryOrder.getTenantId()),
					"storeId", String.valueOf(deliveryOrder.getStoreId())
			));

			MessageCommonResponse response = pushThriftServiceClient.sendFusionPush(request);
			log.info("Send merchant self delivery order cancelled push. request={}, response={}", JsonUtil.toJson(request), JsonUtil.toJson(response));
		} catch (Exception e) {
			log.error("Send merchant self delivery order cancelled push failed, deliveryOrderId={}", deliveryOrder.getId(), e);
		}
	}

	@Override
	@CatTransaction
	public void pushMerchantSelfDeliveryNewDeliveryTask(DeliveryOrder deliveryOrder, OrderInfo orderInfo) {
		Long tenantId = deliveryOrder.getTenantId();
		Long storeId = deliveryOrder.getStoreId();

		// 1. 获取门店下具有"新任务 tab"权限的所有账号 ID
		List<Long> accountIds;
		try {
			if (MccConfigUtils.checkIsDHTenant(tenantId) && grayManagementThriftService.checkIsGrayStore(GrayKeyEnum.PICK_DELIVERY_SPLIT.getGrayKey(), tenantId, storeId)) {
				accountIds = queryAccountListByPoiAndAuth(tenantId, storeId, SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);
			} else {
				accountIds = queryAccountListByPoiAndAuth(tenantId, storeId, SELF_RIDER_WAIT_TO_GET, SHU_GUO_PAI_PUSH_APP_ID);
			}

		} catch (DeliveryBaseException e) {
			log.warn("PushClient.pushMerchantSelfDeliveryNewDeliveryTask queryAccountListByPoiAndAuth fail", e);
			return;
		}
		if (CollectionUtils.isEmpty(accountIds)) {
			log.info("PushClient.pushMerchantSelfDeliveryNewDeliveryTask - Push target accountIds is empty, give up notifying");
			return;
		}

		boolean isRestaurant = false;
		try {
			if (MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())
					&& MccConfigUtils.isDhScenePoi(deliveryOrder.getStoreId())
					&& Objects.nonNull(orderInfo)
					&& StringUtils.isNotBlank(orderInfo.getCategory())) {
				isRestaurant = isRestaurantScene(orderInfo.getCategory());
			}
		} catch (Exception e) {
			log.error("check isRestaurant error", e);
		}

		if (isRestaurant) {
			// 2. 发送通知自营骑手有"餐馆"新任务的消息 PUSH
			ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of("tenantId", String.valueOf(tenantId),
					"storeId", String.valueOf(storeId));
			log.info("餐馆PushClient.pushMerchantSelfDeliveryNewDeliveryTask 消息 push. tenantId:{}, storeId:{}, " +
							"accountIds:{}, appId:{}, pushConfigId:{}, pushMsg:{}", tenantId, storeId, accountIds,
					SHU_GUO_PAI_PUSH_APP_ID.toString(), RESTAURANT_PUSH_CONFIG_ID_RIDER_DELIVERY_NEW, msgPropertyMap);
			basePushClient.unifiedSendFusionPush(tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(),
					RESTAURANT_PUSH_CONFIG_ID_RIDER_DELIVERY_NEW, msgPropertyMap);
		} else {
			// 2. 发送通知自营骑手有新任务的消息 PUSH
			ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of("tenantId", String.valueOf(tenantId),
					"storeId", String.valueOf(storeId));
			log.info("PushClient.pushMerchantSelfDeliveryNewDeliveryTask 消息 push. tenantId:{}, storeId:{}, " +
							"accountIds:{}, appId:{}, pushConfigId:{}, pushMsg:{}", tenantId, storeId, accountIds,
					SHU_GUO_PAI_PUSH_APP_ID.toString(), PUSH_CONFIG_ID_RIDER_DELIVERY_NEW, msgPropertyMap);
			basePushClient.unifiedSendFusionPush(tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(),
					PUSH_CONFIG_ID_RIDER_DELIVERY_NEW, msgPropertyMap);
		}

		// 3. 发送刷新自营骑手新任务 tab 页的 PUSH
		String sharkContent = getRedDotRefreshSharkPushContent(tenantId, storeId, RIDER_NEW_TASK_TAB_KEY);
		log.info("PushClient.pushMerchantSelfDeliveryNewDeliveryTask 新任务 tab 页刷新 push. tenantId:{}, accountIds:{}, appId:{}, " +
				"sharkPushContent:{}", tenantId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(), sharkContent);
		basePushClient.unifiedSendSharkPush(tenantId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(), sharkContent);
	}

	@Override
	// @MethodLog(logRequest = true)
	public void pushMerchantSelfRiderAssignTimeout(RiderDeliveryOrder deliveryOrder) {
		Long tenantId = deliveryOrder.getTenantId();
		Long storeId = deliveryOrder.getStoreId();

		// 获取门店下具有"新任务 tab"权限的所有账号 ID
		List<Long> accountIds;
		try {
			if (MccConfigUtils.checkIsDHTenant(tenantId) && grayManagementThriftService.checkIsGrayStore(GrayKeyEnum.PICK_DELIVERY_SPLIT.getGrayKey(), tenantId, storeId)) {
				accountIds = queryAccountListByPoiAndAuth(tenantId, storeId, SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);
			} else {
				accountIds = queryAccountListByPoiAndAuth(tenantId, storeId, SELF_RIDER_WAIT_TO_GET, SHU_GUO_PAI_PUSH_APP_ID);
			}
		} catch (Exception e) {
			log.error("pushMerchantSelfRiderAssignTimeout queryAccountListByPoiAndAuth fail orderId={}", deliveryOrder.getCustomerOrderKey().getOrderId(), e);
			return;
		}
		if (CollectionUtils.isEmpty(accountIds)) {
			log.info("pushMerchantSelfRiderAssignTimeout accountIds is empty orderId={}", deliveryOrder.getCustomerOrderKey().getOrderId());
			return;
		}

		ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of("tenantId", String.valueOf(tenantId), "storeId", String.valueOf(storeId));
		basePushClient.unifiedSendFusionPush(tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(),
				PUSH_CONFIG_ID_RIDER_ASSIGN_TIMEOUT, msgPropertyMap);
		log.info("pushMerchantSelfRiderAssignTimeout success deliveryOrderId={}", deliveryOrder.getId());
	}

	@Override
	// @MethodLog(logRequest = true)
	public void pushMerchantSelfRiderTakenGoodsTimeout(RiderDeliveryOrder deliveryOrder) {
		if (!GrayConfigUtils.judgeIsGrayStore(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), GrayKeyEnum.PICK_DELIVERY_SPLIT.getGrayKey(), false)) {
			Long tenantId = deliveryOrder.getTenantId();
			Long storeId = deliveryOrder.getStoreId();

			if (deliveryOrder.getRiderInfo() == null || deliveryOrder.getRiderInfo().getRiderAccountId() == null) {
				log.warn("骑手信息为空,deliveryOrder:{}", deliveryOrder);
				return;
			}
			ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of("tenantId", String.valueOf(tenantId), "storeId", String.valueOf(storeId));
			basePushClient.unifiedSendFusionPush(tenantId, storeId, Collections.singletonList(deliveryOrder.getRiderInfo().getRiderAccountId()), SHU_GUO_PAI_PUSH_APP_ID.toString(),
					PUSH_CONFIG_ID_RIDER_TAKEN_GOODS_TIMEOUT, msgPropertyMap);
			log.info("pushMerchantSelfRiderTakenGoodsTimeout success deliveryOrderId={}", deliveryOrder.getId());
		}
	}

	@Override
	// @MethodLog(logRequest = true)
	public void pushMerchantSelfRiderDeliveryTimeoutWarning(RiderDeliveryOrder deliveryOrder) {
		if(!Objects.equals(deliveryOrder.getDeliveryChannelEnum(), DeliveryChannelEnum.MERCHANT_DELIVERY)) {
			log.info("非自配运单，不发超时提醒, deliveryOrderId = {}", deliveryOrder.getId());
			return;
		}
		Long tenantId = deliveryOrder.getTenantId();
		Long storeId = deliveryOrder.getStoreId();
		List<Long> accountIds = null;

		if(deliveryOrder.getStatus() == DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER) {
			// 获取门店下具有"新任务 tab"权限的所有账号 ID
			if (MccConfigUtils.checkIsDHTenant(tenantId) && grayManagementThriftService.checkIsGrayStore(GrayKeyEnum.PICK_DELIVERY_SPLIT.getGrayKey(), tenantId, storeId)) {
				accountIds = queryAccountListByPoiAndAuth(tenantId, storeId, SELF_RIDER_NEW_PICK_DELIVERY_TASK_TAB_AUTH_CODE, SHU_GUO_PAI_PUSH_APP_ID);
			} else {
				accountIds = queryAccountListByPoiAndAuth(tenantId, storeId, SELF_RIDER_WAIT_TO_GET, SHU_GUO_PAI_PUSH_APP_ID);
			}
		} else if (deliveryOrder.getStatus() == DeliveryStatusEnum.RIDER_ASSIGNED || deliveryOrder.getStatus() == DeliveryStatusEnum.RIDER_TAKEN_GOODS) {
			accountIds = Collections.singletonList(deliveryOrder.getRiderInfo().getRiderAccountId());
		}

		if (CollectionUtils.isEmpty(accountIds)) {
			return;
		}
		ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of("tenantId", String.valueOf(tenantId), "storeId", String.valueOf(storeId));
		basePushClient.unifiedSendFusionPush(tenantId, storeId, accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(),
				PUSH_CONFIG_ID_RIDER_DELIVERY_TIMEOUT_WARNING, msgPropertyMap);
		log.info("pushMerchantSelfRiderDeliveryTimeoutWarning success deliveryOrderId={}", deliveryOrder.getId());
	}

	@Override
	public void pushMerchantSelfManagerDeliveryPauseWarning(DeliveryChangeSyncOutMessage syncOutMessage) {
		//只有暂停配送才需要通知
		if (DeliveryAsyncOutTypeEnum.LOCK_DELIVERY_STATUS.getValue() == syncOutMessage.getChangeType()) {

			List<Long> accountIds;
			if (PositionPushHelper.getDHByPositionSwitch(syncOutMessage.getHead().getTenantId(), syncOutMessage.getHead().getShopId())) {

				PoiInfoDto poiDto = getPoiDto(syncOutMessage);

				PushInfo pushInfo = riderPushClient.queryPushInfoByPositions(syncOutMessage.getHead().getTenantId(), syncOutMessage.getHead().getShopId(), PositionPushHelper.getPositionIds(poiDto, PositionPushConfigEnum.DELIVERY_PAUSE));
				accountIds = pushInfo.getAccountIdList();
			} else {
				QueryAccountListByPoiAndRoleIdsRequest request = new QueryAccountListByPoiAndRoleIdsRequest(
						syncOutMessage.getHead().getTenantId(),
						syncOutMessage.getHead().getShopId(), dhStoreManagerRoleIds);
				accountIds = queryRiderByPoiAndRoleIds(request);
			}
			if (CollectionUtils.isEmpty(accountIds)) {
				log.info("pushMerchantSelfManagerDeliveryPauseWarning, accountIds are empty");
				return;
			}

			ImmutableMap<String, String> msgPropertyMap = ImmutableMap.of(
					"tenantId", String.valueOf(syncOutMessage.getHead().getTenantId()),
					"storeId", String.valueOf(syncOutMessage.getHead().getShopId()),
					"channelId", String.valueOf(DynamicOrderBizType.orderBizTypeValue2ChannelId(
							(syncOutMessage.getHead().getOrderBizType()))),
					"channelOrderId", syncOutMessage.getHead().getChannelOrderId());
			log.info("pushMerchantSelfManagerDeliveryPauseWarning, tenantId:{}, storeId:{}, accountIds:{}",
					syncOutMessage.getHead().getTenantId(),
					syncOutMessage.getHead().getShopId(),
					accountIds);
			basePushClient.unifiedSendFusionPush(syncOutMessage.getHead().getTenantId(),
					syncOutMessage.getHead().getShopId(), accountIds, SHU_GUO_PAI_PUSH_APP_ID.toString(),
					PUSH_CONFIG_ID_DELIVERY_PAUSE_WARNING, msgPropertyMap);
		}
	}

	private PoiInfoDto getPoiDto(DeliveryChangeSyncOutMessage syncOutMessage) {
		PoiMapResponse poiMapResponse = poiThriftServiceClient.queryTenantPoiInfoMapByPoiIds(
				Lists.newArrayList(syncOutMessage.getHead().getShopId()), syncOutMessage.getHead().getTenantId()
		);
		if (SUCCESS != poiMapResponse.getStatus().getCode() || !poiMapResponse.getPoiInfoMap().containsKey(syncOutMessage.getHead().getShopId())) {
			log.warn("Query poi[{}] info failed. ", syncOutMessage.getHead().getShopId());
			throw new SystemException("Query poi info failed");
		}
		return poiMapResponse.getPoiInfoMap().get(syncOutMessage.getHead().getShopId());
	}

	private List<Long> queryRiderByPoiAndRoleIds(QueryAccountListByPoiAndRoleIdsRequest request) {
		try {
			QueryRiderInfoListResponse response = authThriftServiceClient.queryRiderByPoiAndRoleIds(request);
			if (response == null || response.getResult() == null
					|| response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
				throw new RuntimeException("authThriftServiceClient.queryRiderByPoiAndRoleIds error");
			}
			return Optional.ofNullable(response.getRiderAccountInfoList()).orElse(Collections.emptyList()).stream()
					.map(RiderAccountInfoVo::getId).collect(Collectors.toList());
		} catch (TException e) {
			log.error("authThriftServiceClient.queryRiderByPoiAndRoleIds error, request:{}", request);
			throw new RuntimeException(e);
		}

	}

	@Deprecated
	private void doSendPush(DeliveryExceptionPushMessage message) {
		log.info("start push DeliveryExceptionPushMessage:{}", message);
		try {
			//1.查询账号列表
			QueryAccountListByPoiAndAuthRequest accountRequest = build(message);
			QueryAccountInfoListResponse response = authThriftServiceClient.queryAccountListByPoiAndAuth(accountRequest);
			if (response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
				log.error("authThriftService.queryAccountListByPoiAndAuth error response:" + response);
				return;
			}
			if (CollectionUtils.isEmpty(response.getAccountInfoList())) {
				log.info("accountIds is empty");
				return;
			}
			List<Long> accountIds = response.getAccountInfoList().stream().map(AccountInfoVo::getAccountId).collect(Collectors.toList());

			//2.发送shark push
			SharkPushSendRequest sharkRequest = buildSharkPush(message, accountIds);
			log.info("PushThriftService.sendSharkPush request:" + sharkRequest);
			MessageCommonResponse sharkPushResponse = pushThriftServiceClient.sendSharkPush(sharkRequest);
			log.info("PushThriftService.sendSharkPush response:" + sharkPushResponse);
			if (sharkPushResponse.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
				log.error("PushThriftService.sendSharkPush error!, message:{}", sharkPushResponse.getMsg());
			}

			//3.发送notification push
			NotificationPushSendRequest notifyRequest = buildNotifyPush(message, accountIds);
			log.info("PushThriftService.sendNotificationPush request:" + notifyRequest);
			MessageCommonResponse notificationPushResponse = pushThriftServiceClient.sendNotificationPush(notifyRequest);
			log.info("PushThriftService.sendNotificationPush response:" + notificationPushResponse);
			if (notificationPushResponse.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
				log.error("pushThriftService.sendNotificationPush error!, message:{}", notificationPushResponse.getMsg());
			}
		} catch (Exception e) {
			log.error("Push DeliveryExceptionPushMessage failed!", e);
		}
	}

	/**
	 * 落运单过后可以废弃掉方法
	 *
	 * @param message
	 * @param argsMap
	 */
	private void doSendFusionPush(DeliveryExceptionPushMessage message, long eventConfigId, Map<String, String> argsMap) {
		log.info("start fusion push DeliveryExceptionPushMessage:{}, argsMap:{}", message, argsMap);
		try {
			// 1.查询账号列表
			QueryAccountListByPoiAndAuthRequest accountRequest = build(message);
			// 麦芽田配送异常code
			QueryAccountInfoListResponse response = authThriftServiceClient.queryAccountListByPoiAndAuth(accountRequest);
			if (response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
				log.error("authThriftService.queryAccountListByPoiAndAuth error response:" + response);
				return;
			}
			if (CollectionUtils.isEmpty(response.getAccountInfoList())) {
				log.info("accountIds is empty");
				return;
			}
			List<Long> accountIds = response.getAccountInfoList().stream().map(AccountInfoVo::getAccountId).collect(Collectors.toList());

			FusionPushSendRequest requestReq = new FusionPushSendRequest();
			requestReq.setAppCode(String.valueOf(accountRequest.getAppId()));
			requestReq.setAccountIds(accountIds);
			requestReq.setTenantId(message.getTenantId());
			requestReq.setPoiId(message.getPoiId());
			// 麦芽田配送异常push模板id
			requestReq.setEventConfigId(eventConfigId);
			requestReq.setMsgPropertyMap(argsMap);
			log.info("PushThriftService.sendFusionPush request:{}", JsonUtil.toJson(requestReq));
			MessageCommonResponse notificationPushResponse = pushThriftServiceClient.sendFusionPush(requestReq);
			log.info("PushThriftService.sendFusionPush response:{}", JsonUtil.toJson(notificationPushResponse));
		} catch (Exception e) {
			log.error("fusion Push DeliveryExceptionPushMessage failed!", e);
		}
	}

	/**
	 * 落运单过后可以废弃掉方法
	 *
	 * @param message
	 * @param url
	 */
	@Deprecated
	private void doSendFusionPush(DeliveryExceptionPushMessage message, String url) {
		log.info("start fusion push DeliveryExceptionPushMessage:{}, url:{}", message, url);
		try {
			// 1.查询账号列表
			QueryAccountListByPoiAndAuthRequest accountRequest = build(message);
			// 麦芽田配送异常code
			QueryAccountInfoListResponse response = authThriftServiceClient.queryAccountListByPoiAndAuth(accountRequest);
			if (response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
				log.error("authThriftService.queryAccountListByPoiAndAuth error response:" + response);
				return;
			}
			if (CollectionUtils.isEmpty(response.getAccountInfoList())) {
				log.info("accountIds is empty");
				return;
			}
			List<Long> accountIds = response.getAccountInfoList().stream().map(AccountInfoVo::getAccountId).collect(Collectors.toList());

			FusionPushSendRequest requestReq = new FusionPushSendRequest();
			requestReq.setAppCode(String.valueOf(accountRequest.getAppId()));
			requestReq.setAccountIds(accountIds);
			requestReq.setTenantId(message.getTenantId());
			requestReq.setPoiId(message.getPoiId());
			// 麦芽田配送异常push模板id
			requestReq.setEventConfigId(1040L);
			requestReq.setMsgPropertyMap(ImmutableMap
					.of("tenantId", String.valueOf(message.getTenantId()),
							"storeId", String.valueOf(message.getPoiId()),
							"url", URLEncoder.encode(url, TMSConstant.CHAR_SET)));
			log.info("PushThriftService.sendFusionPush request:{}", JsonUtil.toJson(requestReq));
			MessageCommonResponse notificationPushResponse = pushThriftServiceClient.sendFusionPush(requestReq);
			log.info("PushThriftService.sendFusionPush response:{}", JsonUtil.toJson(notificationPushResponse));
		} catch (Exception e) {
			log.error("fusion Push DeliveryExceptionPushMessage failed!", e);
		}
	}

	@Override
	@CatTransaction
	public void pushDeliveryStatusRollbackException(DeliveryOrder deliveryOrder, String orderSerialNumber) {
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		boolean aggregationDelivery = deliveryOrder.isMaltFarmDeliveryOrder(deliveryChannel)
				|| deliveryOrder.isDapDeliveryOrder(deliveryChannel);
		if(!aggregationDelivery){
			return;
		}
		DeliveryExceptionPushMessage message = new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(),
				SHU_GUO_PAI_MALT_FARM);
		doSendFusionPush(message,
				PUSH_CONFIG_ID_DELIVERY_STATUS_ROLL_BACK_WARNING,
				ImmutableMap
						.of("tenantId", String.valueOf(message.getTenantId()),
								"storeId", String.valueOf(message.getPoiId()),
								"orderSerialNumber", Optional.ofNullable(orderSerialNumber).orElse("0"))
		);
	}

	@Override
	@CatTransaction
	public void pushFastOrderDeliveryTimeoutException(DeliveryOrder deliveryOrder) {
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		boolean platformDelivery = deliveryOrder.isPlatformDeliveryOrder(deliveryChannel);
		if(!platformDelivery){
			return;
		}
		DeliveryExceptionPushMessage message = new DeliveryExceptionPushMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId(),
				SHU_GUO_PAI_MALT_FARM);
		doSendFusionPush(message,
				PUSH_CONFIG_ID_FAST_ORDER_DELIVERY_TIMEOUT,
				ImmutableMap
						.of("tenantId", String.valueOf(message.getTenantId()),
								"storeId", String.valueOf(message.getPoiId()))
		);
		// 推送给pc客户端
		pushDeliveryExceptionToPcClient(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getChannelOrderId());
	}

	@Override
	@MethodLog(logResponse = true)
	public void pushSealContainerReturnTimeout(Long tenantId, Long storeId, Long riderAccountId) {
		basePushClient.unifiedSendFusionPush(tenantId,
				storeId, Collections.singletonList(riderAccountId), SHU_GUO_PAI_PUSH_APP_ID.toString(),
				PUSH_SEAL_CONTAINER_RETURN_TIMEOUT,
				ImmutableMap.of("tenantId", String.valueOf(tenantId), "storeId", String.valueOf(storeId)));
	}

	private QueryAccountListByPoiAndAuthRequest build(DeliveryExceptionPushMessage message) {
		QueryAccountListByPoiAndAuthRequest request = new QueryAccountListByPoiAndAuthRequest();
		HashMap<Integer, List<String>> dataAuthMap = new HashMap<>();
		dataAuthMap.put(PermissionGroupTypeEnum.POI.getValue(), Collections.singletonList(message.getPoiId().toString()));
		dataAuthMap.put(PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue(), Collections.singletonList(message.getPoiId().toString()));

		request.setDataAuthMap(dataAuthMap);
		request.setTenantId(message.getTenantId());
		request.setAppId(message.getAppId());
		request.setPermissionCodeList(Lists.newArrayList(message.getAuthCode()));
		request.setPageNum(1);
		request.setPageSize(199);
		return request;
	}

	/**
	 * 查询门店下，具有某个权限的所有账号 ID 列表
	 * 会返回和当前门店相关的全量账号数据，上层使用时需要主要用户数量问题
	 *
	 * @param tenantId 租户 ID
	 * @param poiId    门店 ID
	 * @param authCode 权限码
	 * @param appId       APP ID
	 * @return 账号 ID 列表
	 */
	public List<Long> queryAccountListByPoiAndAuth(long tenantId, long poiId, String authCode, int appId) {
		// 构造请求
		int pageSize = 200;
		QueryAccountListByPoiAndAuthRequest accountRequest = new QueryAccountListByPoiAndAuthRequest();
		accountRequest.setTenantId(tenantId);
		accountRequest.setCodes(Lists.newArrayList(String.valueOf(poiId)));
		accountRequest.setPermissionGroupType(PermissionGroupTypeEnum.POI.getValue());
		accountRequest.setAppId(appId);
		accountRequest.setPermissionCodeList(Lists.newArrayList(authCode));
		accountRequest.setValid(1);
		accountRequest.setPageSize(pageSize);

		Set<Long> accountIds = new HashSet<>();
		int pageNum = 0;

		// 分页查全量数据，预估和一个门店相关的人员也不会超过1000人
		while (++pageNum <= 5) {
			accountRequest.setPageNum(pageNum);
			try {
				log.info("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth start. request:{}",
						JsonUtil.toJson(accountRequest));
				QueryAccountInfoListResponse response = authThriftServiceClient.queryAccountListByPoiAndAuth(accountRequest);
				log.info("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth end. request:{}, response:{}",
						JsonUtil.toJson(accountRequest), JsonUtil.toJson(response));
				if (response == null || response.getResult() == null || response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
					log.error("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth fail. response:{}",
							JsonUtil.toJson(response));
					throw new DeliveryBaseException("PushClientImpl.queryAccountListByPoiAndAuth fail");
				}
				List<AccountInfoVo> accountInfoList = response.getAccountInfoList();
				if (CollectionUtils.isNotEmpty(accountInfoList)) {
					accountInfoList.stream().forEach(account -> accountIds.add(account.getAccountId()));
				}
				if (CollectionUtils.isEmpty(accountInfoList) || accountInfoList.size() < pageSize) {
					// 无需查找更多的数据
					break;
				}
			} catch (DeliveryBaseException e) {
				throw e;
			} catch (Exception e) {
				log.error("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth error.", e);
				throw new DeliveryBaseException("PushClientImpl.queryAccountListByPoiAndAuth error");
			}
		}

		return new ArrayList<>(accountIds);
	}

	/**
	 * 查询门店下，具有某个权限的所有账号 ID 列表
	 * 会返回和当前门店相关的全量账号数据，上层使用时需要主要用户数量问题
	 *
	 * @param tenantId 租户 ID
	 * @param poiId    门店 ID
	 * @param authCodeList 权限码列表
	 * @param appId       APP ID
	 * @return 账号 ID 列表
	 */
	public List<Long> queryAccountListByPoiAndAuthList(long tenantId, long poiId, List<String> authCodeList, int appId) {
		// 构造请求
		int pageSize = 200;
		QueryAccountListByPoiAndAuthRequest accountRequest = new QueryAccountListByPoiAndAuthRequest();
		accountRequest.setTenantId(tenantId);
		accountRequest.setCodes(Lists.newArrayList(String.valueOf(poiId)));
		accountRequest.setPermissionGroupType(PermissionGroupTypeEnum.POI.getValue());
		accountRequest.setAppId(appId);
		accountRequest.setPermissionCodeList(authCodeList);
		accountRequest.setValid(1);
		accountRequest.setPageSize(pageSize);

		Set<Long> accountIds = new HashSet<>();
		int pageNum = 0;

		// 分页查全量数据，预估和一个门店相关的人员也不会超过1000人
		while (++pageNum <= DeliveryRiderMccConfigUtils.getSelfDeliveryQueryAuthCount()) {
			accountRequest.setPageNum(pageNum);
			try {
				log.info("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth start. request:{}",
						JsonUtil.toJson(accountRequest));
				QueryAccountInfoListResponse response = authThriftServiceClient.queryAccountListByPoiAndAuth(accountRequest);
				log.info("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth end. request:{}, response:{}",
						JsonUtil.toJson(accountRequest), JsonUtil.toJson(response));
				if (response == null || response.getResult() == null || response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
					log.error("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth fail. response:{}",
							JsonUtil.toJson(response));
					throw new DeliveryBaseException("PushClientImpl.queryAccountListByPoiAndAuth fail");
				}
				List<AccountInfoVo> accountInfoList = response.getAccountInfoList();
				if (CollectionUtils.isNotEmpty(accountInfoList)) {
					accountInfoList.stream().forEach(account -> accountIds.add(account.getAccountId()));
				}
				if (CollectionUtils.isEmpty(accountInfoList) || accountInfoList.size() < pageSize) {
					// 无需查找更多的数据
					break;
				}
			} catch (DeliveryBaseException e) {
				throw e;
			} catch (Exception e) {
				log.error("PushClientImpl call AuthThriftService.queryAccountListByPoiAndAuth error.", e);
				throw new DeliveryBaseException("PushClientImpl.queryAccountListByPoiAndAuth error");
			}
		}

		return new ArrayList<>(accountIds);
	}


	private SharkPushSendRequest buildSharkPush(DeliveryExceptionPushMessage message, List<Long> accountIds) {
		SharkPushSendRequest request = new SharkPushSendRequest();
		request.setTenantId(message.getTenantId());
		request.setAppCode(message.getAppId().toString());
		request.setAccountIds(accountIds);
		request.setContent(message.getSharkPushContent());
		return request;
	}

	private NotificationPushSendRequest buildNotifyPush(DeliveryExceptionPushMessage message, List<Long> accountIds) {
		NotificationPushSendRequest request = new NotificationPushSendRequest();
		request.setTenantId(message.getTenantId());
		request.setAppCode(message.getAppId().toString());
		request.setAccountIds(accountIds);
		request.setTitle(message.getTitle());
		request.setJumpUrl(message.getJumpUrl());
		request.setPassThrough(true);
		request.setSoundFileName(message.getSoundFileName());
		request.setContent(message.getContent());
		return request;
	}

	/**
	 * 获取订单 tab 页刷新的 SharkPush 内容.
	 *
	 * @param tabKey tab key
	 * @return SharkPush 内容
	 */
	private String getRedDotRefreshSharkPushContent(Long tenantId, Long storeId, String tabKey) {
		ObjectNode contentJson = JsonUtil.generateObjectNode();
		contentJson.put("operation", "combo");
		contentJson.put("combo", "true");
		contentJson.put("serverTS", System.currentTimeMillis());
		contentJson.put("storeId", storeId);
		contentJson.put("tenantId", tenantId);
		ArrayNode actions = contentJson.putArray("actions");
		ObjectNode redDotAction = JsonUtil.generateObjectNode();
		redDotAction.put("operation", "redDot");
		redDotAction.put("code", tabKey);
		actions.add(redDotAction);
		return contentJson.toString();
	}

	private void pushDeliveryExceptionToPcClient(long tenantId, long storeId, String channelOrderId) {
		try {
			doPushDeliveryExceptionToPcClient(tenantId, storeId, channelOrderId);
		} catch (Exception e) {
			log.error("Pushing shipping error message to pc client error: {}", e.getMessage(), e);
		}
	}

	private void doPushDeliveryExceptionToPcClient(long tenantId, long storeId, String channelOrderId) throws TException {
		PushPikeMsgToPcClientReq req = new PushPikeMsgToPcClientReq();
		req.setTenantId(tenantId);
		req.setPoiId(storeId);
		req.setMessageMode(PikeMsgModeEnum.GROUP);
		req.setDestinationId(String.valueOf(storeId));
		req.setContent(new AlertAction(new AlertActionData(channelOrderId, AlertTypeEnum.SHIPPING_ERROR)).toJson());
		BaseResponse<String> resp = pushThriftServiceClient.pushPikeMsgToPcClient(req);
		log.info("Pushing shipping error message to pc client req: {}, response: {}", req, resp);
		if (!resp.isSuccess()) {
			log.error("Pushing shipping error message to pc client is failed: {}", resp.getMsg());
		}
	}

	private Boolean isRestaurantScene(String category) {
		try {
			if (StringUtils.isBlank(category)) {
				return false;
			}
			String scene = AddressSceneConvertUtils.convertCategory2Scene(category);
			if (Objects.isNull(scene)) {
				return false;
			}
			return MccConfigUtils.getDhRestaurantSceneList().contains(scene);
		} catch (Exception e) {
			log.error("isRestaurantScene error", e);
			return false;
		}
	}
}
