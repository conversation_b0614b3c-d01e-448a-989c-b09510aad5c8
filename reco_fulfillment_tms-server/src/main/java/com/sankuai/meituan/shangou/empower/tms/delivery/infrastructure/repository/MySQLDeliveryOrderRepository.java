package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.dianping.cat.Cat;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.dms.base.model.value.CustomerOrderKey;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryTimeline;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.DaySeqNumUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TmsMccUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.constants.DeliveryOrderConstants;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOComplexMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryOrderDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryOrderDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.OrderReserveTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.leaf.DeliveryOrderIdLeafService;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.ConvertUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 配送运单仓储服务的MySQL实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/9
 */
@Slf4j
@Repository
@Order(Ordered.HIGHEST_PRECEDENCE)
public class MySQLDeliveryOrderRepository implements DeliveryOrderRepository {

	private static final int MAX_LIMIT = 1000;

	private static final int DELIVERY_NO_EXCEPTION_CODE = 0;

	@Resource
	private DeliveryOrderDOMapper deliveryOrderDOMapper;

    @Resource
    private DeliveryOrderDOComplexMapper deliveryOrderDOComplexMapper;

	@Resource
	private DeliveryOrderDOExMapper deliveryOrderDOExMapper;

	@Resource
	private DeliveryOrderIdLeafService deliveryOrderIdLeafService;

	@Resource
	private DeliveryOrderRouteRepository deliveryOrderRouteRepository;

	@Override
	@CatTransaction
	public void save(DeliveryOrder deliveryOrder) {
		if (!saveDeliveryOrder(deliveryOrder)) {
			log.error("运单保存失败: {}", deliveryOrder);
			throw new MySQLOperateFailedException("运单保存失败");
		}
	}

	@Override
	@CatTransaction
	public boolean saveDeliveryOrder(DeliveryOrder deliveryOrder) {
		Preconditions.checkNotNull(deliveryOrder, "deliveryOrder is null");

		if (deliveryOrder.getId() == null) {
			deliveryOrder.setVersion(0);
			if(MccConfigUtils.getDeliveryLeafSwitch()){
				List<Long> deliveryIdList = deliveryOrderIdLeafService.batchGetLeafId(1);
				if(CollectionUtils.isNotEmpty(deliveryIdList)){
					deliveryOrder.setId(deliveryIdList.get(0));
				}
			}
			DeliveryOrderDO record = translate(deliveryOrder);
			log.info("Inserting deliveryOrder: {}", record);
			if(record.getId()!=null){
				deliveryOrderDOMapper.insertSelective(record);
			}else {
				deliveryOrderDOComplexMapper.insertSelective(record);
			}

			deliveryOrderRouteRepository.saveDeliveryOrderRoute(deliveryOrder);
			deliveryOrder.setId(record.getId());
			return true;

		} else {
			Integer oldVersion = deliveryOrder.getVersion();
			deliveryOrder.setVersion(oldVersion + 1);

			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
			criteria.andIdEqualTo(deliveryOrder.getId())
					.andVersionEqualTo(oldVersion);
			if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryOrder.getTenantId())){
				criteria.andTenantIdEqualTo(deliveryOrder.getTenantId())
						.andStoreIdEqualTo(deliveryOrder.getStoreId());
			}

			DeliveryOrderDO updatingRecord = translate(deliveryOrder);
			log.info("Updating deliveryOrder: {}", updatingRecord);
			return 1 == deliveryOrderDOMapper.updateByExampleSelective(updatingRecord, example);
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void saveDeliveryOrderActiveStatus(DeliveryOrder deliveryOrder) {
		DeliveryOrderDO record = new DeliveryOrderDO();
		record.setId(deliveryOrder.getId());
		record.setActiveStatus(deliveryOrder.getActiveStatus());

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryOrder.getTenantId())){
			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			example.createCriteria().andIdEqualTo(deliveryOrder.getId())
					.andActiveStatusEqualTo(deliveryOrder.getActiveStatus())
					.andTenantIdEqualTo(deliveryOrder.getTenantId())
					.andStoreIdEqualTo(deliveryOrder.getStoreId());
			deliveryOrderDOMapper.updateByExampleSelective(record,example);
		}else {
			deliveryOrderDOMapper.updateByPrimaryKeySelective(record);
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getOpenDeliveryOrders(OrderKey orderKey) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderKey.getOrderId())
				.andDeliveryStatusIn(DeliveryStatusEnum.getOpenStatusCodes());

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(orderKey.getTenantId())){
			criteria.andTenantIdEqualTo(orderKey.getTenantId()).andStoreIdEqualTo(orderKey.getStoreId());
		}

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getOpenDeliveryOrdersWithRoute(OrderKey orderKey) {

		List<DeliveryOrderRoute> routeList = deliveryOrderRouteRepository.queryOrderRouteByOrderId(orderKey.getOrderId());
		if(CollectionUtils.isEmpty(routeList)){
			return Collections.emptyList();
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderKey.getOrderId())
				.andDeliveryStatusIn(DeliveryStatusEnum.getOpenStatusCodes())
				.andTenantIdEqualTo(orderKey.getTenantId()).andStoreIdIn(routeList.stream().map(DeliveryOrderRoute::getOfflineStoreId).distinct().collect(Collectors.toList()));

		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleWithStoreList(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrders(OrderKey orderKey) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderKey.getOrderId());
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(orderKey.getTenantId())){
			criteria.andTenantIdEqualTo(orderKey.getTenantId()).andStoreIdEqualTo(orderKey.getStoreId());
		}
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrdersWithRoute(OrderKey orderKey) {

		List<DeliveryOrderRoute> routeList = deliveryOrderRouteRepository.queryOrderRouteByOrderId(orderKey.getOrderId());
		if(CollectionUtils.isEmpty(routeList)){
			return Collections.emptyList();
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderKey.getOrderId())
				.andTenantIdEqualTo(orderKey.getTenantId()).andStoreIdIn(routeList.stream().map(DeliveryOrderRoute::getOfflineStoreId).distinct().collect(Collectors.toList()));

		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleWithStoreList(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<DeliveryOrder> getActiveDeliveryOrderForceMaster(Long orderId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andFulfillmentOrderIdEqualTo(orderId)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);

		DeliveryOrderRoute deliveryOrderRoute = deliveryOrderRouteRepository.queryOrderRouteByFulfillOrderId(orderId);
		if(deliveryOrderRoute==null) {
			return Optional.empty();
		}
		if (com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryOrderRoute.getTenantId())) {
			criteria.andTenantIdEqualTo(deliveryOrderRoute.getTenantId()).andStoreIdEqualTo(deliveryOrderRoute.getOfflineStoreId());
		}

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(records.get(0)));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<DeliveryOrder> getActiveDeliveryOrderWithTenant(Long orderId,Long tenantId,Long storeId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andFulfillmentOrderIdEqualTo(orderId)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE)
				.andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(records.get(0)));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	public Optional<DeliveryOrder> getActiveDeliveryOrderForceMaster(Long orderId,Long fulfillmentOrderId,Long tenantId,Long storeId){
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria
				.andOrderIdEqualTo(orderId)
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if(fulfillmentOrderId!=null && fulfillmentOrderId>0){
			criteria.andFulfillmentOrderIdEqualTo(fulfillmentOrderId);
		}

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		}

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(records.get(0)));
		}
	}

	@Override
	public Optional<DeliveryOrder> getCurrentDeliveryOrderForceMaster(Long orderId,Long tenantId,Long storeId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		}

		example.setOrderByClause("id desc");

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(
					records.stream()
							.filter(it -> it.getActiveStatus() == DeliveryOrder.DELIVERY_ORDER_ACTIVE)
							.findAny()
							.orElse(records.get(0))
			));
		}
	}

	@Override
	public Optional<DeliveryOrder> getDeliveryOrderFromMaster(Long orderId,Long tenantId,Long storeId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderId);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		}

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(records.get(0)));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrdersForceMaster(Long orderId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdEqualTo(orderId);
		example.or().andFulfillmentOrderIdEqualTo(orderId);
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrdersMaxWithOrderId(Long orderId){
		DeliveryOrderRoute deliveryOrderRoute = deliveryOrderRouteRepository.queryOrderRouteByOrderIdWithMax(orderId);

		if(deliveryOrderRoute == null){
			return Collections.emptyList();
		}
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(deliveryOrderRoute.getOrderId());
		criteria.andFulfillmentOrderIdEqualTo(deliveryOrderRoute.getFulfillmentOrderId()).andTenantIdEqualTo(deliveryOrderRoute.getTenantId()).andStoreIdEqualTo(deliveryOrderRoute.getOfflineStoreId());


		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrdersAllWithOrderId(Long orderId){
		List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteRepository.queryOrderRouteByOrderIdOrFulfill(orderId);
		if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
			return Collections.emptyList();
		}

		Map<Pair</** tenantId */Long,/** storeId */Long>,/** orderId */Set<Long>> routeMap = new HashMap<>();
		deliveryOrderRouteList.forEach(route->{
			Pair<Long,Long> tenantPair = new Pair<>(route.getTenantId(),route.getOfflineStoreId());
			if(routeMap.containsKey(tenantPair)){
				routeMap.get(tenantPair).add(route.getOrderId());
			}else {
				Set<Long> orderSet = new HashSet<>();
				orderSet.add(route.getOrderId());
				routeMap.put(tenantPair,orderSet);
			}
		});
		if(MapUtils.isEmpty(routeMap)){
			return Collections.emptyList();
		}
		List<DeliveryOrderDO> records = new ArrayList<>();
		for (Pair<Long,Long> pair:routeMap.keySet()){
			Set<Long> orderSet = routeMap.get(pair);
			if(CollectionUtils.isEmpty(orderSet)){
				continue;
			}
			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
			criteria.andOrderIdIn(new ArrayList<>(orderSet))
					.andTenantIdEqualTo(pair.getKey()).andStoreIdEqualTo(pair.getValue());
			List<DeliveryOrderDO> recordList = deliveryOrderDOMapper.selectByExample(example);
			if(CollectionUtils.isEmpty(recordList)){
				continue;
			}
			records.addAll(recordList);
		}

		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrdersAllWithOrderIdSlave(Long orderId){
		List<DeliveryOrderRoute> deliveryOrderRouteList = deliveryOrderRouteRepository.queryOrderRouteByOrderIdOrFulfillSlave(orderId);
		if(CollectionUtils.isEmpty(deliveryOrderRouteList)){
			return Collections.emptyList();
		}

		Map<Pair</** tenantId */Long,/** storeId */Long>,/** orderId */Set<Long>> routeMap = new HashMap<>();
		deliveryOrderRouteList.forEach(route->{
			Pair<Long,Long> tenantPair = new Pair<>(route.getTenantId(),route.getOfflineStoreId());
			if(routeMap.containsKey(tenantPair)){
				routeMap.get(tenantPair).add(route.getOrderId());
			}else {
				Set<Long> orderSet = new HashSet<>();
				orderSet.add(route.getOrderId());
				routeMap.put(tenantPair,orderSet);
			}
		});
		if(MapUtils.isEmpty(routeMap)){
			return Collections.emptyList();
		}
		List<DeliveryOrderDO> records = new ArrayList<>();
		for (Pair<Long,Long> pair:routeMap.keySet()){
			Set<Long> orderSet = routeMap.get(pair);
			if(CollectionUtils.isEmpty(orderSet)){
				continue;
			}
			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
			criteria.andOrderIdIn(new ArrayList<>(orderSet))
					.andTenantIdEqualTo(pair.getKey()).andStoreIdEqualTo(pair.getValue());
			List<DeliveryOrderDO> recordList = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
			if(CollectionUtils.isEmpty(recordList)){
				continue;
			}
			records.addAll(recordList);
		}

		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrdersForceMasterWithTenant(Long orderId,Long tenantId,Long storeId){


		Set<DeliveryOrderDO> deliveryOrderSet = new HashSet<>();
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderId);
		criteria.andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(records)){
			deliveryOrderSet.addAll(records);
		}

		DeliveryOrderDOExample example1 = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria1 = example1.createCriteria();
		criteria1.andFulfillmentOrderIdEqualTo(orderId).andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		List<DeliveryOrderDO> records1 = deliveryOrderDOMapper.selectByExample(example1);
		if(CollectionUtils.isNotEmpty(records1)){
			deliveryOrderSet.addAll(records1);
		}
		if(CollectionUtils.isEmpty(deliveryOrderSet)){
			return new ArrayList<>();
		}
		return translate(new ArrayList<>(deliveryOrderSet));
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrdersWithTenantOrderId(Long orderId,Long tenantId,Long storeId){
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andOrderIdEqualTo(orderId);
		criteria.andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}


	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrderSlave(Long orderId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdEqualTo(orderId);
		example.or().andFulfillmentOrderIdEqualTo(orderId);
		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDeliveryOrderSlave(Long orderId, Long tenantId, Long storeId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdEqualTo(orderId)
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId);
		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		return translate(records);
	}

	@Override
	public Optional<DeliveryOrder> getLastDeliveryOrderSlave(Long orderId, Long tenantId, Long storeId) {
		List<DeliveryOrder> deliveryOrderList = this.getDeliveryOrderSlave(orderId, tenantId, storeId);
		if (CollectionUtils.isEmpty(deliveryOrderList)) {
			return Optional.empty();
		}
		// 过滤激活的运单
		Optional<DeliveryOrder> deliveryOrderOptional = deliveryOrderList.stream().filter(DeliveryOrder::isActive).findFirst();
		if (deliveryOrderOptional.isPresent()) {
			return deliveryOrderOptional;
		}else {
			// 最后创建的运单，id排序不准确
			return deliveryOrderList.stream().max(Comparator.comparingLong(item -> TimeUtil.toMilliSeconds(item.getCreateTime())));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryOrder getDeliveryOrder(Long id) {
		return translate(deliveryOrderDOMapper.selectByPrimaryKey(id));
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryOrder getDeliveryOrderWithTenant(Long id,Long tenantId,Long storeId){
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andIdEqualTo(id).andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		List<DeliveryOrderDO> deliveryOrderDOList = deliveryOrderDOMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(deliveryOrderDOList)){
			return null;
		}
		return translate(deliveryOrderDOList.get(0));
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<TOrderIdentifier> getDeliveryOrderList(List<Long> idList) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andIdIn(idList);
		example.setOrderByClause("id");
		final List<DeliveryOrderDO> deliveryOrderDOS = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(deliveryOrderDOS)) {
			return new ArrayList<>();
		}

		return deliveryOrderDOS.stream().map(this::translateIdentifier).collect(Collectors.toList());

	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<DeliveryOrder> getDeliveryOrderForceMaster(Long id) {
		try {
			ZebraForceMasterHelper.forceMasterInLocalContext();

			return Optional.ofNullable(getDeliveryOrder(id));
		} finally {
			ZebraForceMasterHelper.clearLocalContext();
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryOrder getDeliveryOrder(ThirdDeliveryChannelEnum deliveryChannel, String channelDeliveryId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andDeliveryChannelEqualTo(deliveryChannel.getCode())
				.andChannelDeliveryIdEqualTo(channelDeliveryId);
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(
				Optional.ofNullable(records)
						.filter(CollectionUtils::isNotEmpty)
						.map(it -> it.get(0))
						.orElse(null)
		);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public PageResult<DeliveryOrder> pageQueryThirdDeliveryOrder(Long tenantId, Long storeId, List<Integer> statusList, Boolean filterException,
																 PageRequest pageRequest) {

		if (CollectionUtils.isEmpty(statusList)) {
			return new PageResult<>(translate(Collections.emptyList()), pageRequest, 0);
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusIn(statusList)
				.andDeliveryChannelNotEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andActiveStatusEqualTo(DeliveryOrder.DELIVERY_ORDER_ACTIVE);
		if (Objects.nonNull(filterException) && filterException) {
			criteria.andDeliveryExceptionTypeNotEqualTo(DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode());
			criteria.andDeliveryExceptionCodeNotEqualTo(DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode());
		}
		example.setOrderByClause("estimated_delivery_end_time asc");

		long total = deliveryOrderDOMapper.countByExample(example);

		example.setLimit(pageRequest.getPageSize());
		example.setOffset((pageRequest.getPage() - 1) * pageRequest.getPageSize());

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return new PageResult<>(translate(records), pageRequest, total);
	}

	@Override
	public Map<DeliveryStatusEnum, Integer> queryDeliveryOrderCount(Long tenantId, Long storeId, List<Integer> statusList) {
		if (CollectionUtils.isEmpty(statusList)) {
			return Collections.emptyMap();
		}

		List<CountGroupExDO> countGroupExDOS = deliveryOrderDOExMapper.countThirdDeliveryOrderGroupByStatus(tenantId, storeId, statusList);

		return countGroupExDOS
				.stream()
				.collect(Collectors.toMap(countGroupExDO -> DeliveryStatusEnum.valueOf(countGroupExDO.getGroupColumn().intValue()), CountGroupExDO::getGroupCount));
	}

	@Override
	public Integer queryThirdDeliveryOnExceptionOrderCount(Long tenantId, Long storeId, List<Integer> statusList) {
		if (CollectionUtils.isEmpty(statusList)) {
			return 0;
		}

		DeliveryOrderDOExample example =new DeliveryOrderDOExample();
		example
				.createCriteria()
				.andTenantIdEqualTo(tenantId)
				.andStoreIdEqualTo(storeId)
				.andDeliveryStatusIn(statusList)
				.andActiveStatusEqualTo(0L)
				.andDeliveryChannelNotEqualTo(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
				.andDeliveryExceptionCodeNotEqualTo(DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode())
				.andDeliveryExceptionTypeNotEqualTo(DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode());
		long num = deliveryOrderDOMapper.countByExample(example);

		return Long.valueOf(num).intValue();
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Map<OrderKey, List<DeliveryOrder>> getDeliveryOrdersByOrderKeys(List<OrderKey> orderKeys) {
		List<DeliveryOrderDO> records = new ArrayList<>();
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch()){
			ArrayListMultimap<Pair<Long,Long>,Long> multimap = ArrayListMultimap.create();
			for (OrderKey orderKey : orderKeys){
				multimap.put(new Pair<>(orderKey.getTenantId(), orderKey.getStoreId()),orderKey.getOrderId());
			}
			for (Pair<Long,Long> pair : multimap.keySet()){
				DeliveryOrderDOExample example = new DeliveryOrderDOExample();
				example.createCriteria().andOrderIdIn(multimap.get(pair)).andTenantIdEqualTo(pair.getKey()).andStoreIdEqualTo(pair.getValue());
				example.setLimit(ConfigUtilAdapter.getInt("delivery.order.query.maxSize", MAX_LIMIT));
				List<DeliveryOrderDO> partRecords = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
				if(CollectionUtils.isNotEmpty(partRecords)){
					records.addAll(partRecords);
				}
			}
		}else {
			DeliveryOrderDOExample example = new DeliveryOrderDOExample();
			example.createCriteria().andOrderIdIn(orderKeys.stream().map(OrderKey::getOrderId).collect(Collectors.toList()));
			example.setLimit(ConfigUtilAdapter.getInt("delivery.order.query.maxSize", MAX_LIMIT));
			records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		}

		if (CollectionUtils.isEmpty(records)) {
			return Maps.newHashMap();
		}
		return records.stream()
				.collect(Collectors
						.toMap(record -> new OrderKey(record.getTenantId(), record.getStoreId(), record.getOrderId()),
								record -> Lists.newArrayList(translate(record)),
								(newList, oldList) -> {
									oldList.addAll(newList);
									return oldList;
								}));

	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getNotTimeoutDeliveryOrder(Long storeId,Long tenantId) {
		Integer hours = TmsMccUtils.getHours4JudgeDeliveryOrderTimeout();
		// 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时
		LocalDateTime timeoutPoint = LocalDateTime.now().minusHours(hours);
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andStoreIdEqualTo(storeId)
				.andEstimatedDeliveryTimeGreaterThanOrEqualTo(timeoutPoint);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}
		example.setLimit(ConfigUtilAdapter.getInt("delivery.order.query.maxSize", MAX_LIMIT));
		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryOrder> getDHNotTimeoutDeliveryOrder(Long storeId,Long tenantId) {
		Integer hours = TmsMccUtils.getDHHours4JudgeDeliveryOrderTimeout();
		// 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时
		LocalDateTime timeoutPoint = LocalDateTime.now().minusHours(hours);
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andStoreIdEqualTo(storeId)
				.andEstimatedDeliveryTimeGreaterThanOrEqualTo(timeoutPoint);
		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			criteria.andTenantIdEqualTo(tenantId);
		}
		example.setLimit(ConfigUtilAdapter.getInt("delivery.order.query.maxSize", MAX_LIMIT));
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<DeliveryOrder> getDeliveryOrdersByOrderIdList(List<Long> orderList) {

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch()){
			List<DeliveryOrderRoute> routeList = deliveryOrderRouteRepository.queryOrderRouteByOrderIdList(orderList);
			if(CollectionUtils.isEmpty(routeList)){
				return Collections.emptyList();
			}
			ArrayListMultimap<Pair<Long,Long>,Long> multimap = ArrayListMultimap.create();
			for (DeliveryOrderRoute route: routeList){
				multimap.put(new Pair<>(route.getTenantId(), route.getOfflineStoreId()), route.getOrderId());
			}
			List<DeliveryOrderDO> records = new ArrayList<>();
			for (Pair<Long,Long> pair : multimap.keySet()){
				DeliveryOrderDOExample example = new DeliveryOrderDOExample();
				example.createCriteria().andOrderIdIn(multimap.get(pair)).andTenantIdEqualTo(pair.getKey()).andStoreIdEqualTo(pair.getValue());
				List<DeliveryOrderDO> recordList = deliveryOrderDOMapper.selectByExample(example);
				if(CollectionUtils.isNotEmpty(recordList)){
					records.addAll(recordList);
				}
			}
			return translate(records);
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdIn(orderList);
		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		return translate(records);
	}

	@Override
	public List<DeliveryOrder> getDeliveryOrdersByOrderIdListSlave(List<Long> orderList) {

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch()){
			List<DeliveryOrderRoute> routeList = deliveryOrderRouteRepository.queryOrderRouteByOrderIdListSlave(orderList);
			if(CollectionUtils.isEmpty(routeList)){
				return Collections.emptyList();
			}
			ArrayListMultimap<Pair<Long,Long>,Long> multimap = ArrayListMultimap.create();
			for (DeliveryOrderRoute route: routeList){
				multimap.put(new Pair<>(route.getTenantId(), route.getOfflineStoreId()), route.getOrderId());
			}
			List<DeliveryOrderDO> records = new ArrayList<>();
			for (Pair<Long,Long> pair : multimap.keySet()){
				DeliveryOrderDOExample example = new DeliveryOrderDOExample();
				example.createCriteria().andOrderIdIn(multimap.get(pair)).andTenantIdEqualTo(pair.getKey()).andStoreIdEqualTo(pair.getValue());
				List<DeliveryOrderDO> recordList = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
				if(CollectionUtils.isNotEmpty(recordList)){
					records.addAll(recordList);
				}
			}
			return translate(records);
		}

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdIn(orderList);
		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		return translate(records);
	}

	@Override
	public List<DeliveryOrder> getDeliveryOrdersByOrderIdListWithTenant(List<Long> orderList,Long tenantId,Long storeId) {

		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria().andOrderIdIn(orderList).andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId);
		List<DeliveryOrderDO> records = deliveryOrderDOComplexMapper.selectByExampleSlave(example);
		return translate(records);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<Long> getNotTimeoutDeliveryOrderId(List<Long> storeIdList) {
		if (CollectionUtils.isEmpty(storeIdList)) {
			return Collections.emptyList();
		}
		Integer hours = TmsMccUtils.getHours4JudgeDeliveryOrderTimeout();
		// 判断是否配送过时的时间点，若预计送达时间大于等于该时间点，则认为未超时
		// 骑手已送达会清异常，所以过滤骑手已送达的运单
		LocalDateTime timeoutPoint = LocalDateTime.now().minusHours(hours);
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andStoreIdIn(storeIdList)
				.andEstimatedDeliveryTimeGreaterThanOrEqualTo(timeoutPoint)
				.andDeliveryStatusNotEqualTo(DeliveryStatusEnum.DELIVERY_DONE.getCode())
				.andDeliveryExceptionTypeNotEqualTo(DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode());
		example.setDistinct(true);
        List<DeliveryOrderIdDO> records = deliveryOrderDOComplexMapper.selectOrderIdList(example);
		if (CollectionUtils.isEmpty(records)) {
			return new ArrayList<>();
		} else {
			return records.stream().map(DeliveryOrderIdDO::getOrderId).collect(Collectors.toList());
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public List<DeliveryIdDO> getDeliveryIdList(List<Long> orderIdList) {
		if (CollectionUtils.isEmpty(orderIdList)) {
			return new ArrayList<>();
		}
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		example.createCriteria()
				.andOrderIdIn(orderIdList);
		example.setOrderByClause("id");
		example.setDistinct(true);
        List<DeliveryIdDO> records = deliveryOrderDOComplexMapper.selectIdList(example);
		if (CollectionUtils.isEmpty(records)) {
			return new ArrayList<>();
		} else {
			return records;
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void updateRiderInfo(Long deliveryOrderId, StaffRider rider, Long tenantId, Long storeId) {
		if (Objects.isNull(rider)) {
			return;
		}

		DeliveryOrderDO deliveryOrderDO = new DeliveryOrderDO();
		deliveryOrderDO.setId(deliveryOrderId);
		deliveryOrderDO.setRiderAccountId(rider.getRiderAccountId());
		deliveryOrderDO.setRiderName(rider.getRiderName());
		deliveryOrderDO.setRiderPhone(rider.getRiderPhone());

		if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			DeliveryOrderDOExample example=new DeliveryOrderDOExample();
			example.createCriteria().andTenantIdEqualTo(tenantId).andStoreIdEqualTo(storeId).andIdEqualTo(deliveryOrderId);
			deliveryOrderDOMapper.updateByExampleSelective(deliveryOrderDO,example);
		}else {
			deliveryOrderDOMapper.updateByPrimaryKeySelective(deliveryOrderDO);
		}
	}

	@Override
	public List<DeliveryOrderDO> batchQueryOriginWaybillNo(Long tenantId, Collection<Long> storeIds, List<Long> orderIds, List<Integer> statusCodes) {
		if (CollectionUtils.isEmpty(orderIds) || CollectionUtils.isEmpty(storeIds) || Objects.isNull(tenantId) || CollectionUtils.isEmpty(statusCodes)) {
			return new ArrayList<>();
		}
		return deliveryOrderDOMapper.batchQueryOriginWaybillNo(tenantId, storeIds, orderIds, statusCodes);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<DeliveryOrder> getLatestDeliveryOrderForceMaster(Long orderId) {
		DeliveryOrderDOExample example = new DeliveryOrderDOExample();
		DeliveryOrderDOExample.Criteria criteria = example.createCriteria();
		criteria.andFulfillmentOrderIdEqualTo(orderId);

		example.setOrderByClause("create_time desc");

		DeliveryOrderRoute deliveryOrderRoute = deliveryOrderRouteRepository.queryOrderRouteByFulfillOrderId(orderId);
		if(deliveryOrderRoute==null) {
			return Optional.empty();
		}
		if (com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryOrderRoute.getTenantId())) {
			criteria.andTenantIdEqualTo(deliveryOrderRoute.getTenantId()).andStoreIdEqualTo(deliveryOrderRoute.getOfflineStoreId());
		}

		List<DeliveryOrderDO> records = deliveryOrderDOMapper.selectByExample(example);
		if (CollectionUtils.isEmpty(records)) {
			return Optional.empty();
		} else {
			return Optional.ofNullable(translate(records.get(0)));
		}
	}

	private DeliveryOrderDO translate(DeliveryOrder deliveryOrder) {
		if (deliveryOrder == null) {
			return null;
		}

		DeliveryOrderDO record = new DeliveryOrderDO();
		record.setId(deliveryOrder.getId());
		record.setTenantId(deliveryOrder.getTenantId());
		record.setStoreId(deliveryOrder.getStoreId());
		record.setOrderId(deliveryOrder.getOrderKey().getOrderId());
		record.setChannelOrderId(deliveryOrder.getChannelOrderId());
		record.setOrderBizType(deliveryOrder.getOrderBizType());
		record.setDaySeq(deliveryOrder.getDaySeq());
		record.setReserved(OrderReserveTypeEnum.convert(deliveryOrder.getReserved()).getCode());
		record.setOrderSource(deliveryOrder.getOrderSource());
		record.setReceiverName(deliveryOrder.getReceiver().getReceiverName());
		record.setReceiverPhone(deliveryOrder.getReceiver().getReceiverPhone());
		record.setReceiverPrivacyPhone(deliveryOrder.getReceiver().getReceiverPrivacyPhone());
		record.setReceiverAddress(JsonTranslator.toJson(deliveryOrder.getReceiver().getReceiverAddress()));
		record.setEstimatedDeliveryTime(deliveryOrder.getEstimatedDeliveryTime());
		record.setEstimatedDeliveryEndTime(deliveryOrder.getEstimatedDeliveryEndTime());
		record.setDeliveryChannel(deliveryOrder.getDeliveryChannel());
		record.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
		record.setChannelServicePackageCode(deliveryOrder.getChannelServicePackageCode());
		record.setDeliveryStatus(deliveryOrder.getStatus().getCode());
		record.setDeliveryExceptionType(deliveryOrder.getExceptionType().getCode());
		record.setDeliveryExceptionDescription(deliveryOrder.getExceptionDescription());
		record.setLastEventTime(deliveryOrder.getLastEventTime());
		if (deliveryOrder.getRiderInfo() != null) {
			record.setRiderName(deliveryOrder.getRiderInfo().getRiderName());
			record.setRiderPhone(deliveryOrder.getRiderInfo().getRiderPhone());
			record.setRiderPhoneToken(deliveryOrder.getRiderInfo().getRiderPhoneToken());
			if(deliveryOrder.getRiderInfo() instanceof StaffRider){
				record.setRiderAccountId(((StaffRider)deliveryOrder.getRiderInfo()).getRiderAccountId());
			}
		}
		record.setActiveStatus(deliveryOrder.getActiveStatus());
		record.setDeliveryDoneTime(deliveryOrder.getDeliveryDoneTime());
		record.setVersion(deliveryOrder.getVersion());
		record.setDeliveryFee(Optional.ofNullable(deliveryOrder.getDeliveryFee()).orElse(BigDecimal.ZERO));
		record.setDistance(Optional.ofNullable(deliveryOrder.getDistance()).orElse(0L));
		record.setCancelMark(Optional.ofNullable(deliveryOrder.getCancelMark()).orElse(0));
		record.setDeliveryExceptionCode(deliveryOrder.getDeliveryExceptionCode());
		record.setTipAmount(Optional.ofNullable(deliveryOrder.getTipAmount()).orElse(BigDecimal.ZERO));
		record.setDeliveryCount(Optional.ofNullable(deliveryOrder.getDeliveryCount()).orElse(1));
		record.setPlatformFee(Optional.ofNullable(deliveryOrder.getPlatformFee()).orElse(BigDecimal.ZERO));
		//设置扩展字段
		record.setExtInfo(JacksonUtils.toJson(this.buildExtMap(deliveryOrder)));
		record.setFulfillmentOrderId(deliveryOrder.getFulfillmentOrderId());
		if(deliveryOrder.getFulfillmentOrderId()==null || deliveryOrder.getFulfillmentOrderId()<=0){
			record.setFulfillmentOrderId(deliveryOrder.getOrderId());
		}
		record.setPlatformSource(deliveryOrder.getPlatformSourceEnum().getCode());
		return record;
	}

	//统一放到扩展字段中
	private Map<String, Object> buildExtMap(DeliveryOrder deliveryOrder) {
		Map<String, Object> extMap = new HashMap<>();
		if (deliveryOrder.getActualPayAmt() != null) {
			extMap.put("actualPayAmt", deliveryOrder.getActualPayAmt());
		}
		extMap.put("businessToAggregationDelivery", deliveryOrder.isBusinessToAggregationDelivery());
		if (deliveryOrder.getLastExceptionEventTime() != null) {
			extMap.put("lastExceptionEventTime", TimeUtil.toSeconds(deliveryOrder.getLastExceptionEventTime()).orElse(0));
		}
		if(deliveryOrder.getTransType() != null){
			extMap.put("transType",deliveryOrder.getTransType());
		}

		if(StringUtils.isNotEmpty(deliveryOrder.getFailType())){
			extMap.put("failType",deliveryOrder.getFailType());
		}
		if(StringUtils.isNotEmpty(deliveryOrder.getDealDeadline())){
			extMap.put("dealDeadline",deliveryOrder.getDealDeadline());
		}
		if (deliveryOrder.getUserId() != null) {
			extMap.put("userId", deliveryOrder.getUserId());
		}

		if (deliveryOrder.getDeliveryProofPhotoInfoList() != null) {
			extMap.put("deliveryProofPhotoInfoList", deliveryOrder.getDeliveryProofPhotoInfoList());
		}

		if (deliveryOrder.getIsAlreadyAudit() != null) {
			extMap.put("isAlreadyAudit", deliveryOrder.getIsAlreadyAudit());
		}

		if (deliveryOrder.getOrderGoodsCount() != null) {
			extMap.put("orderGoodsCount", deliveryOrder.getOrderGoodsCount());
		}

		if(StringUtils.isNotBlank(deliveryOrder.getSignPosition())) {
			extMap.put("signPosition", deliveryOrder.getSignPosition());
		}

		if (deliveryOrder.getSignType() != null) {
			extMap.put("signType", deliveryOrder.getSignType());
		}

		if (deliveryOrder.getProofPhotoCount() != null) {
			extMap.put("proofPhotoCount", deliveryOrder.getProofPhotoCount());
		}

		if (deliveryOrder.getIsWeakNetwork() != null) {
			extMap.put("isWeakNetwork", deliveryOrder.getIsWeakNetwork());
		}

		if (StringUtils.isNotEmpty(deliveryOrder.getBaseFee())) {
			extMap.put("baseFee", deliveryOrder.getBaseFee());
		}
		if (StringUtils.isNotEmpty(deliveryOrder.getDiscountFee())) {
			extMap.put("discountFee", deliveryOrder.getDiscountFee());
		}
		if (StringUtils.isNotEmpty(deliveryOrder.getInsuredFee())) {
			extMap.put("insuredFee", deliveryOrder.getInsuredFee());
		}

		if (deliveryOrder.getIsQhnManagement() != null) {
			extMap.put("isQnhManagement", deliveryOrder.getIsQhnManagement());
		}

		if (Objects.nonNull(deliveryOrder.getDeliveryCancelCode())) {
			extMap.put("deliveryCancelCode", deliveryOrder.getDeliveryCancelCode());
		}


		if (deliveryOrder.getIsOneYuanOrder() != null) {
			extMap.put("isOneYuanOrder", deliveryOrder.getIsOneYuanOrder());
		}

		if (deliveryOrder.getIsRecallDelivery() != null) {
			extMap.put("isRecallDelivery", deliveryOrder.getIsRecallDelivery());
		}

		if (StringUtils.isNotEmpty(deliveryOrder.getDaySeqNum())) {
			extMap.put(DaySeqNumUtil.DAY_SEQ_NUM, deliveryOrder.getDaySeqNum());
		}

		if (Objects.nonNull(deliveryOrder.getAssessDeliveryTime())) {
			extMap.put("assessDeliveryTime", deliveryOrder.getAssessDeliveryTime());
		}

		if (StringUtils.isNotBlank(deliveryOrder.getOriginWaybillNo())) {
			extMap.put(DeliveryOrderConstants.ORIGIN_WAYBILL_NO, deliveryOrder.getOriginWaybillNo());
		}

		if (Objects.nonNull(deliveryOrder.getPickDeliverySplitTag())) {
			extMap.put("pickDeliverySplitTag", deliveryOrder.getPickDeliverySplitTag());
		}

		if (Objects.nonNull(deliveryOrder.getIsFourWheelDelivery())) {
			extMap.put("isFourWheelDelivery", deliveryOrder.getIsFourWheelDelivery());
		}

		if (Objects.nonNull(deliveryOrder.getIsManual())) {
			extMap.put("isManual", deliveryOrder.getIsManual());
		}

		if (Objects.nonNull(deliveryOrder.getRewardType())) {
			extMap.put("rewardType", deliveryOrder.getRewardType());
		}
		return extMap;
	}

	private TOrderIdentifier translateIdentifier(DeliveryOrderDO record) {
		if (record == null) {
			return null;
		}
		HashMap<String, Object> map = JacksonUtils.fromJsonToMap(record.getExtInfo());

		return new TOrderIdentifier(record.getOrderBizType(), record.getChannelOrderId(), DeliveryStatusEnum.valueOf(record.getDeliveryStatus()),
				record.getDeliveryExceptionType(), record.getDeliveryExceptionCode(),map.containsKey("dealDeadline") ? (String)map.get("dealDeadline") : null);

	}

	private DeliveryOrder translate(DeliveryOrderDO record) {
		if (record == null) {
			return null;
		}

		HashMap<String, Object> map = JacksonUtils.fromJsonToMap(record.getExtInfo());

		CustomerOrderKey customerOrderKey = new CustomerOrderKey(record.getOrderId(),
				record.getChannelOrderId(),
				record.getOrderBizType(),
				record.getDaySeq(),
				OrderReserveTypeEnum.convert(record.getReserved()),
				record.getFulfillmentOrderId(),record.getOrderSource(),(String) map.get(DaySeqNumUtil.DAY_SEQ_NUM),PlatformSourceEnum.platformCodeToEnum(record.getPlatformSource()));

		DeliveryTimeline timeline = new DeliveryTimeline(record.getLastEventTime(),
				record.getEstimatedDeliveryTime(),
				record.getEstimatedDeliveryEndTime(),
				record.getDeliveryDoneTime(),
				record.getCreateTime());

		Receiver receiver = new Receiver(
				record.getReceiverName(),
				record.getReceiverPhone(),
				record.getReceiverPrivacyPhone(),
				JsonTranslator.fromAddressJson(record.getReceiverAddress())
		);

		return DeliveryOrder.builder().id(record.getId())
				.tenantId(record.getTenantId())
				.storeId(record.getStoreId())
				.deliveryChannel(record.getDeliveryChannel())
				.customerOrderKey(customerOrderKey)
				.status(DeliveryStatusEnum.valueOf(record.getDeliveryStatus()))
				.timeline(timeline)
				.receiver(receiver)
				.distance(record.getDistance())
				.version(record.getVersion())
				.channelDeliveryId(record.getChannelDeliveryId())
				.deliveryExceptionCode(record.getDeliveryExceptionCode())
				.channelServicePackageCode(record.getChannelServicePackageCode())
				.exceptionType(DeliveryExceptionTypeEnum.valueOf(record.getDeliveryExceptionType()))
				.exceptionDescription(record.getDeliveryExceptionDescription())
				.riderInfo(translateRider(record))
				.activeStatus(record.getActiveStatus())
				.deliveryFee(record.getDeliveryFee())
				.updateTime(record.getUpdateTime())
				.cancelMark(record.getCancelMark())
				.tipAmount(record.getTipAmount())
				.deliveryCount(record.getDeliveryCount())
				.platformFee(record.getPlatformFee())
				.actualPayAmt((Integer) map.get("actualPayAmt"))
				.businessToAggregationDelivery((Boolean) map.getOrDefault("businessToAggregationDelivery", false))
				.lastExceptionEventTime(TimeUtil.fromSeconds(ConvertUtil.toLong(map.get("lastExceptionEventTime"))))
				.transType(map.containsKey("transType") ? (Integer) map.get("transType") : null)
				.failType(map.containsKey("failType") ? (String)map.get("failType") : null)
				.dealDeadline(map.containsKey("dealDeadline") ? (String)map.get("dealDeadline") : null)
				.userId(map.containsKey("userId") ? ConvertUtil.toLong(map.get("userId")) : null)
				.deliveryProofPhotoInfoList(parseDeliveryProofPhotoInfoList(map))
				.isAlreadyAudit((Boolean) map.getOrDefault("isAlreadyAudit", false))
				.orderGoodsCount((Integer) map.getOrDefault("orderGoodsCount", 0))
				.signPosition(map.containsKey("signPosition") ?(String) map.get("signPosition") : null)
				.signType(map.containsKey("signType") ? (Integer) map.get("signType") : null)
				.isWeakNetwork(map.containsKey("isWeakNetwork") ? (Boolean) map.get("isWeakNetwork") : null)
				.proofPhotoCount(map.containsKey("proofPhotoCount") ? (Integer) map.get("proofPhotoCount") : null)
				.baseFee(map.containsKey("baseFee") ? (String)map.get("baseFee") : null)
				.discountFee(map.containsKey("discountFee") ? (String)map.get("discountFee") : null)
				.insuredFee(map.containsKey("insuredFee") ? (String)map.get("insuredFee") : null)
				.isQhnManagement(map.containsKey("isQnhManagement") ? (Integer)map.get("isQnhManagement") : null)
				.deliveryCancelCode(map.containsKey("deliveryCancelCode") ? (Integer) map.get("deliveryCancelCode") : null)
				.isOneYuanOrder(map.containsKey("isOneYuanOrder") ? (Boolean) map.get("isOneYuanOrder") : null)
				.isRecallDelivery(map.containsKey("isRecallDelivery") ? (Integer) map.get("isRecallDelivery") : null)
				.assessDeliveryTime(map.containsKey("assessDeliveryTime") ? (Long) map.get("assessDeliveryTime") : null)
				.originWaybillNo(map.containsKey(DeliveryOrderConstants.ORIGIN_WAYBILL_NO) ? (String) map.get(DeliveryOrderConstants.ORIGIN_WAYBILL_NO) : null)
				.pickDeliverySplitTag(map.containsKey("pickDeliverySplitTag") ? (Boolean) map.get("pickDeliverySplitTag") : false)
				.isFourWheelDelivery(map.containsKey("isFourWheelDelivery") ? (Integer)map.get("isFourWheelDelivery") : null)
				.isManual(map.containsKey("isManual") ? (Integer)map.get("isManual") : null)
				.build();

	}

	private List<DeliveryOrder> translate(List<DeliveryOrderDO> records) {
		if (CollectionUtils.isEmpty(records)) {
			return new ArrayList<>();
		}

		return records.stream()
				.map(this::translate)
				.collect(Collectors.toList());
	}

	private List<RiderDeliveryExtInfo.DeliveryProofPhotoInfo> parseDeliveryProofPhotoInfoList(HashMap<String, Object> extInfoMap) {
		try {
			if (extInfoMap.containsKey("deliveryProofPhotoInfoList")) {
				return (List<RiderDeliveryExtInfo.DeliveryProofPhotoInfo>) extInfoMap.get("deliveryProofPhotoInfoList");
			}

			Cat.logEvent("DELIVERY_PROOF_PHOTO", "PARSE_EXT_INFO_ERROR");
			return null;
		} catch (Exception e) {
			log.error("解析送达照片信息失败");
			return null;
		}
	}


	private Rider translateRider(DeliveryOrderDO record) {
		if (DeliveryChannelEnum.MERCHANT_DELIVERY.getCode() == record.getDeliveryChannel()) {
			return Optional.of(record)
					.filter(it -> StaffRider.DEFAULT_ACCOUNT_ID != it.getRiderAccountId())
					.map(it -> new StaffRider(it.getRiderName(), it.getRiderPhone(), it.getRiderPhoneToken(), it.getRiderAccountId()))
					.orElse(null);

		} else {
			return Optional.of(record)
					.filter(it -> StringUtils.isNotEmpty(it.getRiderName()) || StringUtils.isNotEmpty(it.getRiderPhone()))
					.map(it -> new Rider(it.getRiderName(), it.getRiderPhone(), it.getRiderPhoneToken()))
					.orElse(null);
		}
	}

}
