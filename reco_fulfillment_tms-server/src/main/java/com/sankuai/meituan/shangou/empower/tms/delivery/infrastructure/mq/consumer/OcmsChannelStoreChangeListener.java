package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiShippingModeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.ChannelStoreRelation;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.StoreChangeInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantChannelStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantStoreInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;

@Slf4j
@Component
public class OcmsChannelStoreChangeListener extends AbstractDeadLetterConsumer {

    private final static List<Integer> INFO_TYPE = Arrays.asList(2, 3, 4);

    @Resource
    private TenantSystemClient tenantSystemClient;
    @Resource
    private DeliveryPlatformClient deliveryPlatformClient;
    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.OCMS_STORE_CHANGE_TMS_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费渠道门店变更消息：{}", message);
        StoreChangeInfo storeChangeInfo = translateMessage(message);
        if (storeChangeInfo == null || !INFO_TYPE.contains(storeChangeInfo.getInfoType())) {
            return CONSUME_SUCCESS;
        }
        if (storeChangeInfo.getChannelId() != ChannelType.MEITUAN.getValue()) {
            log.info("非美团渠道门店变更");
            return CONSUME_SUCCESS;
        }
        TenantStoreInfo tenantStoreInfo = tenantSystemClient.queryStoreDetailInfo(storeChangeInfo.getTenantId(),
                storeChangeInfo.getStoreId());
        if (tenantStoreInfo == null) {
            log.info("门店详情为空");
            return CONSUME_SUCCESS;
        }

        if (tenantStoreInfo.getEntityType() == null || tenantStoreInfo.getShippingMode() == null) {
            log.info("门店类型为空");
            return CONSUME_SUCCESS;
        }
        try {
            return fastAuthWmStoreIdList(storeChangeInfo, tenantStoreInfo);
        } catch (Exception e) {
            log.error("消费渠道门店变更消息失败", e);
            return CONSUME_FAILURE;
        }
    }

    private DeliveryPoi getDapStoreDelivery(Long tenantId, Long storeId) {
        List<DeliveryPoi> deliveryPoiList = deliveryPoiRepository.queryAllDeliveryPoi(tenantId, storeId);
        if (CollectionUtils.isEmpty(deliveryPoiList)) {
            log.info("无配送门店");
            return null;
        }
        Optional<DeliveryPoi> optionalDeliveryPoi = deliveryPoiList.stream()
                .filter(deliveryPoi -> deliveryPoi.getDeliveryPlatform() == DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)
                .findAny();
        if (!optionalDeliveryPoi.isPresent()) {
            log.info("无开通青云门店");
            return null;
        }
        return optionalDeliveryPoi.get();
    }

    @MethodLog(logRequest = false, logResponse = true)
    private ConsumeStatus fastAuthWmStoreIdList(StoreChangeInfo storeChangeInfo, TenantStoreInfo tenantStoreInfo) {
        if (!MccConfigUtils.getTenantPoiFastAuthSwitch()) {
            log.info("绑定信息开关关闭");
            return CONSUME_SUCCESS;
        }
        if (tenantStoreInfo.getEntityType() == PoiEntityTypeEnum.STORE.code()
                && tenantStoreInfo.getShippingMode() == PoiShippingModeEnum.SHIP_BY_STORE.code()) {
            // 普通门店
            DeliveryPoi deliveryPoi = getDapStoreDelivery(storeChangeInfo.getTenantId(), storeChangeInfo.getStoreId());
            if (deliveryPoi == null) {
                log.info("Dap门店为空");
                return CONSUME_SUCCESS;
            }
            List<Long> wmStoreIdList = null;
            List<ChannelStoreRelation> relationList = new ArrayList<>();
            if (storeChangeInfo.getInfoType() == 2 || storeChangeInfo.getInfoType() == 3) {
                wmStoreIdList = Arrays.asList(NumberUtils.toLong(storeChangeInfo.getChannelOnLinePoiCode()));
                if(!MccConfigUtils.checkIsDHTenant(storeChangeInfo.getTenantId())){
                    relationList.add(new ChannelStoreRelation(storeChangeInfo.getStoreId(),NumberUtils.toLong(storeChangeInfo.getChannelOnLinePoiCode())));
                }
            } else if (storeChangeInfo.getInfoType() == 4) {
                wmStoreIdList = Collections.emptyList();
                if(!MccConfigUtils.checkIsDHTenant(storeChangeInfo.getTenantId())){
                    relationList.add(new ChannelStoreRelation(storeChangeInfo.getStoreId(),0L));
                }
            } else {
                log.info("操作类型不正确");
                return CONSUME_SUCCESS;
            }
            Optional<Failure> failure = deliveryPlatformClient.deliveryFastAuth(deliveryPoi, wmStoreIdList,relationList);
            if (failure.isPresent() && failure.get().isNeedRetry()) {
                return RECONSUME_LATER;
            }
            return CONSUME_SUCCESS;
        }

        Map<Long, List<Long>> relationMap = tenantSystemClient.batchQueryRelationMapByPoiIds(
                storeChangeInfo.getTenantId(), Arrays.asList(storeChangeInfo.getStoreId()), false);
        if (MapUtils.isEmpty(relationMap) || CollectionUtils.isEmpty(relationMap.get(storeChangeInfo.getStoreId()))) {
            log.info("逆向关系为空");
            return CONSUME_SUCCESS;
        }
        Long wareHouseId = relationMap.get(storeChangeInfo.getStoreId()).get(0);

        DeliveryPoi deliveryPoi = getDapStoreDelivery(storeChangeInfo.getTenantId(), wareHouseId);
        if (deliveryPoi == null) {
            log.info("Dap门店为空");
            return CONSUME_SUCCESS;
        }

        Map<Long, List<Long>> relationPoiMap = tenantSystemClient
                .batchQueryRelationMapByPoiIds(storeChangeInfo.getTenantId(), Arrays.asList(wareHouseId), true);
        if (MapUtils.isEmpty(relationPoiMap) || CollectionUtils.isEmpty(relationPoiMap.get(wareHouseId))) {
            log.info("正向关系为空");
            return CONSUME_SUCCESS;
        }
        List<Long> relationPoiList = relationPoiMap.get(wareHouseId);
        relationPoiList.remove(storeChangeInfo.getStoreId());
        Set<Long> wmStoreIdSet = new HashSet<>();
        List<ChannelStoreRelation> relationList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relationPoiList)) {
            Map<Long, List<TenantChannelStoreInfo>> channelStoreMap = tenantSystemClient
                    .queryChannelStoreDetailInfoList(storeChangeInfo.getTenantId(), new ArrayList<>(relationPoiList));
            if (MapUtils.isNotEmpty(channelStoreMap)) {
                for (Long poiId : channelStoreMap.keySet()) {
                    List<TenantChannelStoreInfo> channelStoreInfoList = channelStoreMap.get(poiId);
                    if (CollectionUtils.isEmpty(channelStoreInfoList)) {
                        if(!MccConfigUtils.checkIsDHTenant(storeChangeInfo.getTenantId())){
                            relationList.add(new ChannelStoreRelation(poiId,0L));
                        }
                        continue;
                    }
                    Optional<TenantChannelStoreInfo> mtStoreInfo = channelStoreInfoList.stream()
                            .filter(tenantChannelStoreInfo -> tenantChannelStoreInfo.getChannelId()
                                    .equals(ChannelType.MEITUAN.getValue()))
                            .findFirst();
                    mtStoreInfo.ifPresent(tenantChannelStoreInfo -> wmStoreIdSet
                            .add(NumberUtils.toLong(tenantChannelStoreInfo.getChannelPoiId())));
                    if(!MccConfigUtils.checkIsDHTenant(storeChangeInfo.getTenantId())){
                        if(mtStoreInfo.isPresent()){
                            String channelPoiId = mtStoreInfo.get().getChannelInnerPoiId();
                            if(StringUtils.isEmpty(channelPoiId)){
                                channelPoiId = mtStoreInfo.get().getChannelPoiId();
                            }

                            relationList.add(new ChannelStoreRelation(poiId,NumberUtils.toLong(channelPoiId)));
                        }else {
                            relationList.add(new ChannelStoreRelation(poiId,0L));
                        }
                    }
                }
            }
        }
        if (storeChangeInfo.getInfoType() == 2 || storeChangeInfo.getInfoType() == 3) {
            wmStoreIdSet.add(NumberUtils.toLong(storeChangeInfo.getChannelOnLinePoiCode()));
            if(!MccConfigUtils.checkIsDHTenant(storeChangeInfo.getTenantId())){
                relationList.add(new ChannelStoreRelation(storeChangeInfo.getStoreId(),NumberUtils.toLong(storeChangeInfo.getChannelOnLinePoiCode())));
            }
        }else if(storeChangeInfo.getInfoType() == 4){
            if(!MccConfigUtils.checkIsDHTenant(storeChangeInfo.getTenantId())){
                relationList.add(new ChannelStoreRelation(storeChangeInfo.getStoreId(),0L));
            }
        }
        Optional<Failure> failure = deliveryPlatformClient.deliveryFastAuth(deliveryPoi, new ArrayList<>(wmStoreIdSet),relationList);
        if (failure.isPresent() && failure.get().isNeedRetry()) {
            return RECONSUME_LATER;
        }
        return CONSUME_SUCCESS;
    }

    private StoreChangeInfo translateMessage(MafkaMessage message) {
        try {
            StoreChangeInfo storeChangeInfo = translateMessage(message, StoreChangeInfo.class);
            Preconditions.checkNotNull(storeChangeInfo, "渠道门店通知消息不能为空");
            Preconditions.checkNotNull(storeChangeInfo.getInfoType(), "门店通知类型不能为空");
            Preconditions.checkNotNull(storeChangeInfo.getTenantId(), "门店通知中的租户信息不能为空");
            Preconditions.checkNotNull(storeChangeInfo.getStoreId(), "门店通知中的门店信息不能为空");
            return storeChangeInfo;
        } catch (Exception e) {
            log.error("解析门店创建消息失败. message:{}", message, e);
            return null;
        }
    }

    protected <T> T translateMessage(MafkaMessage mafkaMessage, Class<T> clazz) {
        return Optional.ofNullable(mafkaMessage).map(MafkaMessage::getBody).map(Object::toString)
                .map(it -> JsonUtil.fromJson(it, clazz)).orElse(null);
    }

}
