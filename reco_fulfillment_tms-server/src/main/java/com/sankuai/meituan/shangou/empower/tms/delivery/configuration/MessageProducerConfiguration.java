package com.sankuai.meituan.shangou.empower.tms.delivery.configuration;

import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.sankuai.meituan.shangou.empower.rider.client.message.SelfDeliveryLaunchMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer.OrderStatusChangeMessageListener;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.qnh.ofc.ofw.common.mq.message.FulfillmentUpstreamMessage;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MQProducerEnum.*;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/10
 */
@Configuration
public class MessageProducerConfiguration {

	/**
	 * 用于异步记录配送流水
	 *
	 * @since 1.4
	 */
	@Bean
	public MafkaMessageProducer<DeliveryChangeLogMessage> deliveryChangeLogMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_LOG_COMMAND);
	}

	/**
	 * 用于对订单同步配送进度
	 */
	@Bean
	public MafkaMessageProducer<DeliveryChangeNotifyMessage> deliveryChangeNotifyMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_CHANGE_NOTIFY);
	}

	@Bean
	public MafkaMessageProducer<DeliveryExceptionNotifyMessage> deliveryExceptionNotifyMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_EXCEPTION_NOTIFY_TOPIC);
	}

	@Bean
	public MafkaMessageProducer<OrderStatusChangeMessageListener.MessageDTO> deliveryOrderStatusDelayCheckMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_ORDER_STATUS_DELAY_CHECK_TOPIC);
	}

	@Bean
	public MafkaMessageProducer<DeliveryTransNotifyMessage> deliveryTransNotifyMessageMafkaMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_TRANS_NOTIFY);
	}

	@Bean
	public MafkaMessageProducer<DeliveryChangeSyncOutMessage> deliveryChangeSyncOutMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_CHANGE_ASYNC_OUT);
	}

	@Bean
	public MafkaMessageProducer<SelfDeliveryLaunchMsg> selfDeliveryLaunchMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(QNH_DELIVERY_LAUNCH_TOPIC);
	}

	/**
	 * 用于延迟发起配送
	 */
	@Bean
	public MafkaDelayMessageProducer<DeliveryLaunchCommandMessage> deliveryDelayLaunchMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(DELIVERY_LAUNCH_COMMAND);
	}

	/**
	 * 用于新供给延迟发起配送
	 */
	@Bean
	public MafkaDelayMessageProducer<DeliveryLaunchCmdMessage> newSupplyDeliveryDelayLaunchMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(NEW_SUPPLY_DELIVERY_LAUNCH_COMMAND);
	}

	/**
	 * 用于取消配送
	 */
	@Bean
	public MafkaMessageProducer<DeliveryCancelCommandMessage> deliveryCancelMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_CANCEL_COMMAND);
	}

	/**
	 * 用于骑手接单超时检测
	 */
	@Bean
	public MafkaDelayMessageProducer<RiderAssignTimeOutCheckMessageHolder> riderAssignTimeOutCheckMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(RIDER_ASSIGN_TIME_OUT);
	}

	/**
	 * 用于骑手位置同步
	 */
	@Bean
	public MafkaDelayMessageProducer<SyncRiderPositionMessage> riderPositionSyncProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(RIDER_POSITION_SYNC);
	}

	/**
	 * 用于骑手位置立即同步
	 */
	@Bean
	public MafkaMessageProducer<SyncRiderPositionMessage> riderPositionImmediatelySyncProducer() throws Exception {
		return new MafkaMessageProducer<>(RIDER_POSITION_SYNC);
	}

	/**
	 * 用于配送异常检测兜底
	 */
	@Bean("clearDeliveryExceptionProducer")
	public MafkaDelayMessageProducer<ClearDeliveryExceptionMsg> clearDeliveryExceptionProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(CLEAR_DELIVERY_EXCEPTION);
	}

	/**
	 * 用于配送超时检查
	 */
	@Bean
	public MafkaDelayMessageProducer<DeliveryTimeOutCheckMessage> deliveryTimeOutCheckProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(DELIVERY_TIMEOUT_CHECK);
	}

	/**
	 * 配送超时，用于通知外部服务
	 */
	@Bean
	public MafkaMessageProducer<DeliveryTimeoutNotifyMessage> deliveryTimeOutNotifyProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_TIMEOUT_NOTIFY);
	}

	/**
	 * 用于统一处理回调
	 */
	@Bean
	public MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_UNIFIED_CALLBACK);
	}

	/**
	 * 用于预计送达时间超时检查
	 */
	@Bean
	public MafkaDelayMessageProducer<NewSupplyDeliveryTimeOutCheckMessage> deliveryTimeOutMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(NEW_SUPPLY_DELIVERY_TIMEOUT_CHECK);
	}

	/**
	 * 用于兜底检查拣货完成后歪马配送的运单状态
	 */
	@Bean
	public MafkaDelayMessageProducer<DHDeliveryStatusCheckMessage> dhDeliveryStatusCheckMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(DH_DELIVERY_STATUS_CHECK);
	}

	@Bean
	public MafkaMessageProducer<DeliveryTransSelfMsg> deliveryOrderTansMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DELIVERY_ORDER_TRANSFORM_TOPIC);
	}

	/**
	 * 用于同步骑手位置到渠道
	 */
	@Bean
	public MafkaMessageProducer<RiderLocationSyncToChannelMessage> riderLocationSyncToChannelMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(RIDER_LOCATION_SYNC_TO_CHANNEL_TOPIC);
	}

	@Bean
	public MafkaMessageProducer<OrderTrackEvent> orderTrackLogProducer() throws Exception {
		return new MafkaMessageProducer<>(ORDER_TRACK_LOG);
	}

	/**
	 * 用于保时洁图片送审
	 */
	@Bean
	public MafkaMessageProducer<CreditAuditPicMessage> creditAuditPicMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(CREDIT_AUDIT_PIC_BATCH_LISTENER);
	}

	/**
	 * 用于保时洁送审结果长时间未返回时自动通过审核
	 */
	@Bean
	public MafkaDelayMessageProducer<CreditAuditAutomaticPassMessage> creditAuditAutomaticPassMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(CREDIT_AUDIT_AUTOMATIC_PASS_TOPIC);
	}

	/**
	 * 线下推广自提的拣货下发消息，用于推订单侧至“骑手已接单”
	 */
	@Bean
	public MafkaDelayMessageProducer<OfflinePromotePickPushDownMessage> offlinePromotePickPushDownMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(OFFLINE_PROMOTE_PICK_PUSH_DOWN_TOPIC);
	}

	@Bean
	public MafkaMessageProducer<FulfillmentUpstreamMessage> ofcFulfillmentUpstreamMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(OFC_OFW_FULFILLMENT_UPSTREAM_TOPIC);
	}

	/**
	 * 自配送订单配送状态对外推送
	 */
	@Bean
	public MafkaMessageProducer<SelfDeliveryChangeOuterNotifyMessage> selfDeliveryChangeOuterNotifyMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(SELF_DELIVERY_CHANGE_OUTER_NOTIFY);
	}

	/**
	 * 歪马运单转换配送方式通知消息, 用于通知拣货服务,对拣货单打标
	 */
	@Bean
	public MafkaMessageProducer<DrunkHorseTransDeliveryTypeMessage> drunkHorseTransDeliveryTypeMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(DRUNK_HORSE_TRANS_DELIVERY_TYPE);
	}



	/**
	 * 延迟通知配送状态变更消息, 在歪马三方转自配的场景, 自配送状态可能会被三方配送取消的消息覆盖，所以这里延迟发自配状态变更消息
	 * @return
	 * @throws Exception
	 */
	@Bean
	public MafkaDelayMessageProducer<DelayNotifyDeliveryStatusMessage> DelayNotifyDeliveryStatusMessageProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(DELAY_NOTIFY_DELIVERY_STATUS_TOPIC);
	}

	/**
	 * 订单履约看板，配送消息
	 * @return
	 * @throws Exception
	 */
	@Bean
	public MafkaMessageProducer<FulfillMsg> fulfillMsgProducer() throws Exception {
		return new MafkaMessageProducer<>(FULL_FILL_MESSAGE_TOPIC);
	}

	/**
	 * 订单锁定结束通知消息
	 * @return
	 * @throws Exception
	 */
	@Bean
	public MafkaMessageProducer<OrderLockEndNotifyMessage> orderLockEndNotifyMessageProducer() throws Exception {
		return new MafkaMessageProducer<>(ORDER_LOCK_END_NOTIFY);
	}

	@Bean
	public MafkaDelayMessageProducer<SealContainerReturnTimeoutMessage> sealContainerReturnTimeoutMessageDelayProducer() throws Exception {
		return new MafkaDelayMessageProducer<>(SEAL_CONTAINER_RETURN_TIMEOUT_TOPIC);
	}

	/**
	 * 平台配送校验
	 */
	@Bean
	public MafkaMessageProducer<PlatformDeliveryCheckMessage> platformDeliveryCheckProducer() throws Exception {
		return new MafkaMessageProducer<>(PLATFORM_DELIVERY_CHECK_TOPIC);
	}


}
