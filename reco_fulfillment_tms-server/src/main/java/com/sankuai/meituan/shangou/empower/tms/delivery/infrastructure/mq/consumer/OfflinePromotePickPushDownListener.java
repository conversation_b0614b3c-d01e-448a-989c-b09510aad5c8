package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryChangeNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryLaunchCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.OfflinePromotePickPushDownMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.AccountInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.auth.RiderAuthClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * <AUTHOR>
 * @date 2023-08-04
 * @email <EMAIL>
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class OfflinePromotePickPushDownListener extends AbstractDeadLetterConsumer{

    @Resource
    private OrderSystemClient orderSystemClient;
    @Resource
    private RiderAuthClient riderAuthClient;
    @Resource
    private MafkaMessageProducer<DeliveryChangeNotifyMessage> deliveryChangeNotifyMessageProducer;
    @Resource
    private MafkaDelayMessageProducer<OfflinePromotePickPushDownMessage> offlinePromotePickPushDownMessageProducer;

    private static final List<OrderStatusEnum> NEED_PUSH_ORDER_STATUS = Lists.newArrayList(
            OrderStatusEnum.MERCHANT_CONFIRMED, OrderStatusEnum.PICKING, OrderStatusEnum.REFUND_APPLIED,
            OrderStatusEnum.APPEAL_APPLIED
    );
    private static final List<OrderStatusEnum> NEED_RETRY_STATUS = Lists.newArrayList(
            OrderStatusEnum.SUBMIT, OrderStatusEnum.PAYING, OrderStatusEnum.PAYED
    );

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.OFFLINE_PROMOTE_PICK_PUSH_DOWN_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始消费线下推广自提订单下发消息 message = {}", mafkaMessage);
        try {
            OfflinePromotePickPushDownMessage message = translateMessage(mafkaMessage);
            if (message == null) {
                return CONSUME_SUCCESS;
            }
            //歪马租户
            if (!MccConfigUtils.checkIsDHTenant(message.getTenantId())) {
                return CONSUME_SUCCESS;
            }
            //灰度门店
            if (!MccConfigUtils.isDHOfflinePromoteGrayStore(message.getStoreId())) {
                return CONSUME_SUCCESS;
            }

            Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(new OrderKey(message.getTenantId(), message.getStoreId(), message.getOrderId()), false);
            if (orderInfoResult.isFail()) {
                return CONSUME_FAILURE;
            }
            OrderInfo orderInfo = orderInfoResult.getInfo();
            //非地推消息，忽略
            if (!orderInfo.isPullNewSelfPickGoodsOrder()) {
                return CONSUME_SUCCESS;
            }
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderInfo.getOrderStatus());
            //非要响应状态
            if(Objects.isNull(orderStatusEnum) || !NEED_PUSH_ORDER_STATUS.contains(orderStatusEnum)) {
                //商家接单前的需要重试
                if (NEED_RETRY_STATUS.contains(orderStatusEnum) && message.getTimes() <= 20) {
                    message.setTimes(message.getTimes() + 1);
                    offlinePromotePickPushDownMessageProducer.sendDelayMessageInMillis(message, 10000L);
                }
                return CONSUME_SUCCESS;
            }
            //接单消息才推动订单状态至已取货
            DeliveryChangeNotifyMessage deliveryChangeNotifyMessage = buildDeliveryChangeNotifyMessage(orderInfo, message.getUpdateTime());
            deliveryChangeNotifyMessageProducer.sendMessage(deliveryChangeNotifyMessage);

            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("OfflinePromotePickPushDownListener.consume error,msg = {}", mafkaMessage, e);
            return CONSUME_FAILURE;
        }

    }

    private DeliveryChangeNotifyMessage buildDeliveryChangeNotifyMessage(OrderInfo orderInfo, Long updateTime) {
        DeliveryChangeNotifyMessage message = new DeliveryChangeNotifyMessage();
        message.setTenantId(orderInfo.getOrderKey().getTenantId());
        message.setShopId(orderInfo.getOrderKey().getStoreId());
        message.setViewOrderId(orderInfo.getChannelOrderId());
        message.setOrderId(0L);
        message.setOrderSource(orderInfo.getOrderSource());
        message.setOrderBizType(orderInfo.getOrderBizType());
        message.setChannelDeliveryId("0");
        message.setDeliveryChannelId(DeliveryChannelEnum.MERCHANT_DELIVERY.getCode());
        message.setDeliveryOrderId(0L);
        AccountInfo accountInfo = riderAuthClient.queryRiderByAccountId(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderExtInfo().getPullNewAccountId());
        message.setRiderName(accountInfo.getUserName());
        message.setRiderPhone(accountInfo.getPhone());
        message.setDeliveryStatus(RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode());
        message.setUpdateTime(updateTime);
        return message;
    }

    private OfflinePromotePickPushDownMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            OfflinePromotePickPushDownMessage message = translateMessage(mafkaMessage, OfflinePromotePickPushDownMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getTenantId(), "发起配送消息tenantId为空");
            Preconditions.checkNotNull(message.getStoreId(), "发起配送消息shopId为空");
            Preconditions.checkNotNull(message.getOrderId(), "发起配送消息orderId为空");
            Preconditions.checkNotNull(message.getUpdateTime(), "发起配送消息orderId为空");
            return message;

        } catch (Exception e) {
            log.error("解析线下推广自提订单下发消息失败:{}", mafkaMessage, e);
            return null;
        }
    }
}
