package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.crane;

import com.cip.crane.client.ITaskHandler;
import com.cip.crane.netty.protocol.ScheduleTask;
import com.google.common.base.Splitter;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.DeliveryOrderEsPatternDao;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryChannelCraneTaskException;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.DeliveryOrderEsCompensateCraneTaskException;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 运单ES补偿任务，用于处理ES里运单数据不是最新的场景
 * crane任务里传入需要更新的赋能订单ID列表，通过查delivery_order主库和反查订单将ES里的运单数据更新至最新
 * <AUTHOR>
 */
@Component
@Slf4j
public class DeliveryOrderEsCompensateCraneTask implements ITaskHandler  {

    private static final String SEPARATOR = ",";

    @Resource
    private DeliveryOrderEsPatternDao deliveryOrderEsPatternDao;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Override
    public void handleTask(ScheduleTask scheduleTask) throws DeliveryChannelCraneTaskException {
        log.info("DeliveryOrderEsCompensateCraneTask start");

        String orderIdListStr = scheduleTask.getTaskItems().get(0);
        List<Long> orderIdList = Splitter.on(SEPARATOR).splitToList(orderIdListStr).stream().map(Long::valueOf).collect(Collectors.toList());
        Integer maxNum = MccConfigUtils.getDeliveryOrderEsCompensateMaxNum();
        if (orderIdList.size() > maxNum) {
            throw new DeliveryOrderEsCompensateCraneTaskException("DeliveryChannelRefreshCraneTask failed, orderId num is more than limit");
        }

        try {
            orderIdList.forEach(this::compensateSingleOrder);
        } catch (Exception e) {
            throw new DeliveryOrderEsCompensateCraneTaskException("DeliveryChannelRefreshCraneTask handleTask failed");
        }
        log.info("DeliveryOrderEsCompensateCraneTask end");
    }

    private void compensateSingleOrder(Long orderId) {
        // 查询运单主库获取运单列表
        List<DeliveryOrder> deliveryOrders = new ArrayList<>();
        if(com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDeliveryQueryTenantSwitch()){
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersMaxWithOrderId(orderId);
        }else {
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderId);
        }

        if (CollectionUtils.isEmpty(deliveryOrders)) {
            log.warn("compensateSingleOrder, deliveryOrders is empty");
            return;
        }

        // 查询承运商信息
        Set<Integer> carrierCodeSet = deliveryOrders.stream().map(DeliveryOrder::getDeliveryChannel).collect(Collectors.toSet());
        List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
        Map<Integer, DeliveryChannel> deliveryChannelMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, Function.identity()));

        // 获取当前DB里的有效运单
        DeliveryOrder availableDeliveryOrder = filterAvailableDeliveryOrder(deliveryOrders);

        // 获取门店ID
        Long storeId = deliveryOrders.stream().findAny().map(DeliveryOrder::getStoreId).orElseThrow(() -> new DeliveryOrderEsCompensateCraneTaskException("storeId is null"));

        // 查询订单状态
        Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(orderId, false);
        if (orderInfoResult.isFail() || Objects.isNull(orderInfoResult.getInfo())) {
            throw new DeliveryOrderEsCompensateCraneTaskException("compensateSingleOrder, query orderInfo failed");
        }
        Integer orderStatus = orderInfoResult.getInfo().getOrderStatus();

        // 填充deliveryOrderEsPo
        DeliveryOrderEsPo deliveryOrderEsPo = new DeliveryOrderEsPo();
        DeliveryOrderEsPo currentEsPo = deliveryOrderEsPatternDao.getDeliveryOrderEsPoByOrderId(orderId, storeId);
        if (Objects.isNull(currentEsPo)) {
            log.info("compensateSingleOrder, deliveryOrderEsPoList is empty, orderId is {}", orderId);
            DeliveryOrder earliestCreateTimeDeliveryOrder = getEarliestCreateTimeDeliveryOrder(deliveryOrders);
            fillDeliveryOrderDoc4Base(deliveryOrderEsPo, earliestCreateTimeDeliveryOrder);
        } else {
            log.info("compensateSingleOrder, deliveryOrderEsPoList is not empty, orderId is {}", orderId);
            fillDeliveryOrderDoc4Base(deliveryOrderEsPo, currentEsPo);
        }
        fillDeliveryOrderDoc4Available(deliveryOrderEsPo, availableDeliveryOrder, deliveryChannelMap, orderStatus);
        fillDeliveryOrderDoc4Trace(deliveryOrderEsPo, deliveryOrders, deliveryChannelMap);

        // 写入运单ES
        deliveryOrderEsPatternDao.update(deliveryOrderEsPo);
    }

    /**
     * 找出最近、有效的运单
     * 优先找出激活的运单，如果没有激活的，则按照id取最大的一个
     */
    private DeliveryOrder filterAvailableDeliveryOrder(List<DeliveryOrder> deliveryOrders) {

//        deliveryOrders.sort(Comparator.comparingLong(deliveryOrder -> {
//            if (deliveryOrder.isActive()) {
//                return Long.MIN_VALUE;
//            } else {
//                return -deliveryOrder.getId();
//            }
//        }));
        return DeliveryOrder.filterActiveDeliveryOrder(deliveryOrders);
    }

    private DeliveryOrder getEarliestCreateTimeDeliveryOrder(List<DeliveryOrder> deliveryOrders) {
        return deliveryOrders.stream().min(Comparator.comparing(DeliveryOrder::getCreateTime)).orElseThrow(() -> new DeliveryOrderEsCompensateCraneTaskException("getEarliestCreateTimeDeliveryOrder, deliveryOrder is null"));
    }

    private void fillDeliveryOrderDoc4Base(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrder deliveryOrder) {
        deliveryOrderEsPo.setId(String.valueOf(deliveryOrder.getOrderId()));
        deliveryOrderEsPo.setIndexKey(deliveryOrder.getCreateTime());
        deliveryOrderEsPo.setRoutingValue(String.valueOf(deliveryOrder.getStoreId()));
    }

    private void fillDeliveryOrderDoc4Base(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrderEsPo currentEsPo) {
        deliveryOrderEsPo.setId(currentEsPo.getId());
        deliveryOrderEsPo.setIndexKey(currentEsPo.getIndexKey());
        deliveryOrderEsPo.setRoutingValue(currentEsPo.getRoutingValue());
    }

    private void fillDeliveryOrderDoc4Available(DeliveryOrderEsPo deliveryOrderEsPo, DeliveryOrder deliveryOrder,
                                                Map<Integer, DeliveryChannel> deliveryChannelMap, Integer orderStatus) {
        deliveryOrderEsPo.setDeliveryOrderId(deliveryOrder.getId());
        deliveryOrderEsPo.setTenantId(deliveryOrder.getTenantId());
        deliveryOrderEsPo.setStoreId(deliveryOrder.getStoreId());
        deliveryOrderEsPo.setOrderId(deliveryOrder.getOrderId());
        deliveryOrderEsPo.setChannelOrderId(deliveryOrder.getChannelOrderId());
        deliveryOrderEsPo.setOrderBizType(deliveryOrder.getOrderBizType());
        deliveryOrderEsPo.setDaySeq(deliveryOrder.getDaySeq());
        deliveryOrderEsPo.setReserved(deliveryOrder.getReserved() ? NumberUtils.BYTE_ONE : NumberUtils.BYTE_ZERO);
        deliveryOrderEsPo.setOrderSource(deliveryOrder.getOrderSource());
        deliveryOrderEsPo.setReceiverName(Objects.nonNull(deliveryOrder.getReceiver()) ? deliveryOrder.getReceiver().getReceiverName() : StringUtils.EMPTY);
        deliveryOrderEsPo.setReceiverPhone(Objects.nonNull(deliveryOrder.getReceiver()) ? deliveryOrder.getReceiver().getReceiverPhone() : StringUtils.EMPTY);
        deliveryOrderEsPo.setReceiverPrivacyPhone(Objects.nonNull(deliveryOrder.getReceiver()) ? deliveryOrder.getReceiver().getReceiverPrivacyPhone() : StringUtils.EMPTY);
        deliveryOrderEsPo.setReceiverAddress(Objects.nonNull(deliveryOrder.getReceiver()) && Objects.nonNull(deliveryOrder.getReceiver().getReceiverAddress()) ? deliveryOrder.getReceiver().getReceiverAddress().getAddressDetail() : StringUtils.EMPTY);
        deliveryOrderEsPo.setEstimatedDeliveryTime(deliveryOrder.getEstimatedDeliveryTime());
        deliveryOrderEsPo.setEstimatedDeliveryEndTime(deliveryOrder.getEstimatedDeliveryEndTime());
        deliveryOrderEsPo.setDeliveryChannelCode(deliveryOrder.getDeliveryChannel());
        deliveryOrderEsPo.setDeliveryChannelName(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getCarrierName() : StringUtils.EMPTY);
        deliveryOrderEsPo.setDeliveryPlatformCode(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getDeliveryPlatFormCode() : NumberUtils.INTEGER_ZERO);
        deliveryOrderEsPo.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
        deliveryOrderEsPo.setDeliveryStatus(deliveryOrder.getStatus().getCode());
        deliveryOrderEsPo.setDeliveryExceptionType(deliveryOrder.getExceptionType().getCode());
        deliveryOrderEsPo.setDeliveryExceptionDescription(deliveryOrder.getExceptionDescription());
        deliveryOrderEsPo.setRiderName(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderName() : StringUtils.EMPTY);
        deliveryOrderEsPo.setRiderPhone(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderPhone() : StringUtils.EMPTY);
        deliveryOrderEsPo.setActiveStatus(deliveryOrder.getActiveStatus());
        deliveryOrderEsPo.setCreateTime(deliveryOrder.getCreateTime());
        deliveryOrderEsPo.setUpdateTime(deliveryOrder.getUpdateTime());
        deliveryOrderEsPo.setLastEventTime(deliveryOrder.getLastEventTime());
        deliveryOrderEsPo.setDeliveryDoneTime(deliveryOrder.getDeliveryDoneTime());
        deliveryOrderEsPo.setDeliveryFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getDeliveryFee()).intValue());
        deliveryOrderEsPo.setDistance(deliveryOrder.getDistance());
        deliveryOrderEsPo.setCancelMark(deliveryOrder.getCancelMark());
        deliveryOrderEsPo.setDeliveryExceptionCode(deliveryOrder.getDeliveryExceptionCode());
        deliveryOrderEsPo.setTipAmount(MoneyUtil.fromYuanToCenter(deliveryOrder.getTipAmount()).intValue());
        deliveryOrderEsPo.setDeliveryCount(deliveryOrder.getDeliveryCount());
        deliveryOrderEsPo.setPlatformFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getPlatformFee()).intValue());
        deliveryOrderEsPo.setOrderStatus(orderStatus);
        deliveryOrderEsPo.setAllowLatestAuditTime(deliveryOrder.getDealDeadline());
        deliveryOrderEsPo.setOriginWaybillNo(deliveryOrder.getOriginWaybillNo());
    }

    private void fillDeliveryOrderDoc4Trace(DeliveryOrderEsPo deliveryOrderEsPo, List<DeliveryOrder> deliveryOrders, Map<Integer, DeliveryChannel> deliveryChannelMap) {
        List<DeliveryOrderEsPo.DeliveryOrderSubPo> deliveryOrderSubPoList = deliveryOrders.stream()
                .map(deliveryOrder -> buildDeliveryOrderSubPo(deliveryOrder, deliveryChannelMap)).collect(Collectors.toList());
        deliveryOrderEsPo.setDeliveryTraceList(JsonUtil.toJson(deliveryOrderSubPoList));
    }

    private DeliveryOrderEsPo.DeliveryOrderSubPo buildDeliveryOrderSubPo(DeliveryOrder deliveryOrder, Map<Integer, DeliveryChannel> deliveryChannelMap) {
        DeliveryOrderEsPo.DeliveryOrderSubPo deliveryOrderSubPo = new DeliveryOrderEsPo.DeliveryOrderSubPo();

        deliveryOrderSubPo.setDeliveryOrderId(deliveryOrder.getId());
        deliveryOrderSubPo.setDeliveryChannelCode(deliveryOrder.getDeliveryChannel());
        deliveryOrderSubPo.setDeliveryChannelName(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getCarrierName() : StringUtils.EMPTY);
        deliveryOrderSubPo.setDeliveryPlatformCode(Objects.nonNull(deliveryChannelMap.get(deliveryOrder.getDeliveryChannel())) ? deliveryChannelMap.get(deliveryOrder.getDeliveryChannel()).getDeliveryPlatFormCode() : NumberUtils.INTEGER_ZERO);
        deliveryOrderSubPo.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
        deliveryOrderSubPo.setDeliveryStatus(deliveryOrder.getStatus().getCode());
        deliveryOrderSubPo.setDeliveryExceptionType(deliveryOrder.getExceptionType().getCode());
        deliveryOrderSubPo.setDeliveryExceptionDescription(deliveryOrder.getExceptionDescription());
        deliveryOrderSubPo.setRiderName(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderName() : StringUtils.EMPTY);
        deliveryOrderSubPo.setRiderPhone(Objects.nonNull(deliveryOrder.getRiderInfo()) ? deliveryOrder.getRiderInfo().getRiderPhone() : StringUtils.EMPTY);
        deliveryOrderSubPo.setActiveStatus(deliveryOrder.getActiveStatus());
        deliveryOrderSubPo.setCreateTime(deliveryOrder.getCreateTime());
        deliveryOrderSubPo.setUpdateTime(deliveryOrder.getUpdateTime());
        deliveryOrderSubPo.setLastEventTime(deliveryOrder.getLastEventTime());
        deliveryOrderSubPo.setDeliveryDoneTime(deliveryOrder.getDeliveryDoneTime());
        deliveryOrderSubPo.setDeliveryFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getDeliveryFee()).intValue());
        deliveryOrderSubPo.setDistance(deliveryOrder.getDistance());
        deliveryOrderSubPo.setCancelMark(deliveryOrder.getCancelMark());
        deliveryOrderSubPo.setDeliveryExceptionCode(deliveryOrder.getDeliveryExceptionCode());
        deliveryOrderSubPo.setTipAmount(MoneyUtil.fromYuanToCenter(deliveryOrder.getTipAmount()).intValue());
        deliveryOrderSubPo.setDeliveryCount(deliveryOrder.getDeliveryCount());
        deliveryOrderSubPo.setPlatformFee(MoneyUtil.fromYuanToCenter(deliveryOrder.getPlatformFee()).intValue());
        deliveryOrderSubPo.setAllowLatestAuditTime(deliveryOrder.getDealDeadline());
        deliveryOrderSubPo.setOriginWaybillNo(deliveryOrder.getOriginWaybillNo());

        return deliveryOrderSubPo;
    }

}
