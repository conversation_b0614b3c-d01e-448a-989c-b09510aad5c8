package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerConfig;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerRetryException;
import com.meituan.reco.pickselect.common.mq.consumer.RetryConfig;
import com.meituan.reco.pickselect.common.mq.producer.ProducerConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLogRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryCancelReasonEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.CoordinateDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.RiderInfoDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonLogicException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryChangeNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.SelfDeliveryChangeOuterNotifyMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaTopicEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;


/**
 * 新供给tms内部监听配送状态变更消费者
 * 这个topic当前回传的是自配送 + 有赞/抖音平台配送状态变更，不包括老的平台配送（美团 + 饿了么 + 京东）
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeliveryChangeNotifyTmsListener extends AbstractMqListener {

	@Resource
	DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private MafkaMessageProducer<SelfDeliveryChangeOuterNotifyMessage> selfDeliveryChangeOuterNotifyMessageProducer;

	@Resource
	private DeliveryOrderLogRepository deliveryOrderLogRepository;

	private static final List<Integer> DELIVERY_EVENT_CODES =
			Arrays.asList(DeliveryEventEnum.RIDER_ARRIVE_SHOP.getCode(), DeliveryEventEnum.RIDER_START_DELIVERY.getCode());

	@Override
	protected ConsumerConfig config() {
		return MafkaConsumerEnum.DELIVERY_CHANGE_NOTIFY_TMS_CONSUMER;
	}

	@Override
	protected RetryConfig retryConfig() {
		return new RetryConfig() {
			@Override
			public ProducerConfig producerConfig() {
				return MafkaTopicEnum.DELIVERY_CHANGE_NOTIFY_TMS_TOPIC;
			}

			@Override
			public int maxRetry() {
				return MccConfigUtils.getDeliveryChangeNotifyMqMaxRetryTimes();
			}

			@Override
			public int delayInSeconds() {
				return MccConfigUtils.getDeliveryChangeNotifyMqDelaySeconds();
			}
		};
	}

	@Override
	protected void consume(MafkaMessage mafkaMessage) throws ConsumerRetryException {
		try {
			if (!MccConfigUtils.getDeliveryChangeConsumeSwitch()) {
				log.info("DeliveryChangeConsumeSwitch is off, msg is {}", mafkaMessage);
				return;
			}

			DeliveryChangeNotifyMessage msg = translateMessage(mafkaMessage);
			if (Objects.isNull(msg)) {
				log.error("DeliveryChangeNotifyTmsListener, message is null");
				throw new CommonLogicException("DeliveryChangeNotifyTmsListener, message is null");
			}

			Long tenantId = msg.getTenantId();
			if (MccConfigUtils.checkIsDHTenant(tenantId)) {
				return;
			}

			log.info("DeliveryChangeNotifyTmsListener consume message: {}", mafkaMessage);
			Integer deliveryChannelId = msg.getDeliveryChannelId();
			DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannelId);
			if (needSyncOut(deliveryChannel)) {
				// 查询配送关键节点时间
				Map<Long, List<DeliveryOrderLog>> deliveryOrderLogMap = deliveryOrderLogRepository.getLogsByDeliveryOrderIdsAndDeliveryEventCodes(Collections.singletonList(msg.getDeliveryOrderId()), DELIVERY_EVENT_CODES);

				SelfDeliveryChangeOuterNotifyMessage message = buildSelfDeliveryChangeOuterNotifyMessage(msg, deliveryChannel, deliveryOrderLogMap);
				selfDeliveryChangeOuterNotifyMessageProducer.sendMessage(message, msg.getOrderId());
			}
		} catch (CommonLogicException e) {
			log.error("DeliveryChangeNotifyTmsListener failed, message={}", mafkaMessage, e);
		} catch (Exception e) {
			log.error("DeliveryChangeNotifyTmsListener failed, will retry, message={}", mafkaMessage, e);
			throw new ConsumerRetryException(e);
		}
	}

	private DeliveryChangeNotifyMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			return Optional.ofNullable(mafkaMessage)
					.map(MafkaMessage::getBody)
					.map(Object::toString)
					.map(it -> JsonUtil.fromJson(it, DeliveryChangeNotifyMessage.class))
					.orElse(null);
		} catch (Exception e) {
			log.error("DeliveryChangeNotifyTmsListener translateMessage error", e);
			Cat.logEvent("DELIVERY_CHANGE_NOTIFY_TMS_CONSUMER_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}

	private boolean needSyncOut(DeliveryChannel deliveryChannelDto) {
		if (Objects.isNull(deliveryChannelDto)) {
			return false;
		}

		Integer deliveryPlatformCode = deliveryChannelDto.getDeliveryPlatFormCode();
		if (Objects.isNull(deliveryPlatformCode)) {
			return false;
		}

		DeliveryPlatformEnum deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatformCode);
		if (Objects.isNull(deliveryPlatformEnum)) {
			return false;
		}

		return deliveryPlatformEnum == DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM
				|| deliveryPlatformEnum == DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM
				|| deliveryPlatformEnum == DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;
	}

	private SelfDeliveryChangeOuterNotifyMessage buildSelfDeliveryChangeOuterNotifyMessage(DeliveryChangeNotifyMessage msg,
																						   DeliveryChannel deliveryChannel,
																						   Map<Long, List<DeliveryOrderLog>> deliveryOrderLogMap) {
		SelfDeliveryChangeOuterNotifyMessage message = new SelfDeliveryChangeOuterNotifyMessage();
		message.setTenantId(msg.getTenantId());
		message.setOrderId(msg.getOrderId());
		message.setDeliveryDistance(msg.getDistance());
		message.setLastEventTime(msg.getUpdateTime());
		message.setChannel_sheetno(msg.getViewOrderId());

		if (Objects.nonNull(msg.getDeliveryStatus())) {
			message.setDeliveryStatusCode(msg.getDeliveryStatus());
		}

		if (Objects.nonNull(deliveryChannel)) {
			message.setDeliveryChannelCode(deliveryChannel.getCarrierCode());
			message.setDeliveryChannelDesc(deliveryChannel.getCarrierName());
		}

		if (Objects.nonNull(msg.getDeliveryFee())) {
			message.setDeliveryFee(MoneyUtil.fromYuanToCenter(msg.getDeliveryFee()).intValue());
		}

		// 组装配送关键节点的时间
		if (MapUtils.isNotEmpty(deliveryOrderLogMap)) {
			List<DeliveryOrderLog> deliveryOrderLogList = deliveryOrderLogMap.get(msg.getDeliveryOrderId());
			if (CollectionUtils.isNotEmpty(deliveryOrderLogList)) {
				if (Objects.equals(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode(), deliveryChannel.getDeliveryPlatFormCode())) {
					// 商家自己送没有记骑手到店时间，骑手到店时间和骑手取货时间一致
					deliveryOrderLogList.stream().filter(deliveryOrderLog -> filterDeliveryOpLog(deliveryOrderLog, DeliveryEventEnum.RIDER_START_DELIVERY))
							.findAny().ifPresent(deliveryOrderLog -> message.setRiderArrivedStoreTime(TimeUtil.toMilliSeconds(deliveryOrderLog.getChangeTime())));
				} else {
					deliveryOrderLogList.stream().filter(deliveryOrderLog -> filterDeliveryOpLog(deliveryOrderLog, DeliveryEventEnum.RIDER_ARRIVE_SHOP))
							.findAny().ifPresent(deliveryOrderLog -> message.setRiderArrivedStoreTime(TimeUtil.toMilliSeconds(deliveryOrderLog.getChangeTime())));
				}
				deliveryOrderLogList.stream().filter(deliveryOrderLog -> filterDeliveryOpLog(deliveryOrderLog, DeliveryEventEnum.RIDER_START_DELIVERY))
						.findAny().ifPresent(deliveryOrderLog -> message.setRiderTakeGoodsTime(TimeUtil.toMilliSeconds(deliveryOrderLog.getChangeTime())));
			}
		}

		// 组装配送取消原因
		if (Objects.equals(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode(), deliveryChannel.getDeliveryPlatFormCode())) {
			// 对于商家自己送的配送取消原因用固定值填充
			if (Objects.equals(DeliveryStatusEnum.DELIVERY_CANCELLED.getCode(), msg.getDeliveryStatus())) {
				message.setCancelReasonCode(DeliveryCancelReasonEnum.OTHER_CANCEL.getCancelCode());
				message.setCancelReasonDesc(DeliveryCancelReasonEnum.OTHER_CANCEL.getCancelReason());
			}
		} else {
			Integer deliveryCancelCode = msg.getCancelReasonCode();
			DeliveryCancelReasonEnum deliveryCancelReasonEnum = DeliveryCancelReasonEnum.enumOf(deliveryCancelCode);
			if (Objects.nonNull(deliveryCancelCode) && Objects.nonNull(deliveryCancelReasonEnum)) {
				message.setCancelReasonCode(deliveryCancelCode);
				message.setCancelReasonDesc(deliveryCancelReasonEnum.getCancelReason());
			}
		}

		RiderInfoDto riderInfoDto = new RiderInfoDto();
		riderInfoDto.setRiderName(msg.getRiderName());
		riderInfoDto.setRiderPhone(msg.getRiderPhone());
		riderInfoDto.setRiderCoordinate(CoordinateDto.builder()
				.longitude(msg.getRiderCurrentLongitude())
				.latitude(msg.getRiderCurrentLatitude()).build());
		message.setRiderInfoDto(riderInfoDto);

		return message;
	}

	private boolean filterDeliveryOpLog(DeliveryOrderLog deliveryOrderLog, DeliveryEventEnum deliveryEventEnum) {
		if (Objects.isNull(deliveryOrderLog) || Objects.isNull(deliveryEventEnum) || Objects.isNull(deliveryOrderLog.getChangeTime())) {
			return false;
		}

		return Objects.equals(deliveryOrderLog.getChangeEvent(), deliveryEventEnum);
	}

}
