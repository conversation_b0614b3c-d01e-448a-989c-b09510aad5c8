package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.order;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.NewSupplyRiderLocationRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OFCSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.DeliveryCancelTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.FulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.qnh.ofc.ofw.common.consts.FulfillmentUpstreamMessageTypeEnum;
import com.sankuai.qnh.ofc.ofw.common.mq.message.FulfillmentUpstreamMessage;
import com.sankuai.qnh.ofc.ofw.common.mq.message.delivery.OfcDeliveryChangeNotifyMessage;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Rhino
public class OFCSystemClientImpl implements OFCSystemClient {

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private DeliveryPlatformClient deliveryPlatformClient;
    @Resource
    private MafkaMessageProducer<FulfillmentUpstreamMessage> ofcFulfillmentUpstreamMessageProducer;

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;

    @Resource
    private NewSupplyRiderLocationRepository newSupplyRiderLocationRepository;

    @Override
    public void syncDeliveryChangeToSystem(DeliveryOrder deliveryOrder, LocalDateTime updateTime) {
        DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
        CoordinatePoint riderLocation = null;
        FulfillmentUpstreamMessage message = toDeliveryChangeNotifyMessage(deliveryOrder, riderLocation, updateTime);
        ofcFulfillmentUpstreamMessageProducer.sendMessage(message,message.getFulfillmentOrderId());
    }

    @Override
    public void syncDeliveryChangeToSystem(Long tenantId, Long storeId, Long orderId, Long fulfillOrderId, LocalDateTime updateTime) {
        FulfillmentUpstreamMessage message = toDeliveryChangeNotifyMessage(tenantId,storeId,orderId,fulfillOrderId, updateTime);
        ofcFulfillmentUpstreamMessageProducer.sendMessage(message,message.getFulfillmentOrderId());
    }


    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "OFCSystemClientImpl.queryOrderIsContainSnOrSealProduct", fallBackMethod = "queryOrderIsContainSnOrSealProductFallback", timeoutInMilliseconds = 2000)
    public boolean queryOrderIsContainSnOrSealProduct(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        try {
            FulfillmentOrderIdKeyReq request = new FulfillmentOrderIdKeyReq();
            request.setTenantId(tenantId);
            request.setWarehouseId(storeId);
            request.setChannelOrderIdKey(new ChannelOrderIdKeyReq(orderBizType, channelOrderId));
            log.info("start invoke fulfillmentOrderSearchThriftService.searchFulfillmentOrderByFulfillmentOrderId, request: {}", request);
            FulfillmentOrderDetailResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderByFulfillmentOrderId(request);
            log.info("end invoke fulfillmentOrderSearchThriftService.searchFulfillmentOrderByFulfillmentOrderId, response: {}", response);

            if (response == null || response.getStatus() == null) {
                throw new ThirdPartyException("查询履约单失败");
            }

            if (response.getStatus().getCode() != 0) {
                throw new BizException("查询履约单业务失败");
            }

            if (CollectionUtils.isEmpty(response.getFulfillmentOrderList())) {
                throw new BizException("未查询到履约单");
            }

            FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO = response.getFulfillmentOrderList().get(0);
            return Objects.equals(fulfillmentOrderDetailDTO.getIsContainSnProduct(), true) || Objects.equals(fulfillmentOrderDetailDTO.getIsContainSealProduct(), true);

        } catch (Exception e) {
            log.error("查询订单是否包含sn或封签商品失败", e);
            Cat.logEvent("SEAL", "QUERY_OFC_ERROR");
            return false;
        }
    }

    public boolean queryOrderIsContainSnOrSealProductFallback(Long tenantId, Long storeId, String channelOrderId, Integer orderBizType) {
        log.error("OFCSystemClientImpl.queryOrderIsContainSnOrSealProduct 发生降级");
        return false;
    }

    private FulfillmentUpstreamMessage toDeliveryChangeNotifyMessage(Long tenantId, Long storeId, Long orderId, Long fulfillOrderId, LocalDateTime updateTime) {
        OfcDeliveryChangeNotifyMessage ofcDeliveryChangeNotifyMessage = new OfcDeliveryChangeNotifyMessage();
        ofcDeliveryChangeNotifyMessage.setTenantId(tenantId);
        ofcDeliveryChangeNotifyMessage.setShopId(storeId);
        ofcDeliveryChangeNotifyMessage.setOrderId(orderId);
        ofcDeliveryChangeNotifyMessage.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_CANCELLED.getCode());
        ofcDeliveryChangeNotifyMessage.setPlatformSource(PlatformSourceEnum.OMS.getCode());
        ofcDeliveryChangeNotifyMessage.setUpdateTime(TimeUtil.toMilliSeconds(updateTime));
        Long fulfillmentOrderId = fulfillOrderId;
        if(fulfillmentOrderId == null){
            fulfillmentOrderId = orderId;
        }
        ofcDeliveryChangeNotifyMessage.setFulfillmentOrderId(fulfillmentOrderId);

        FulfillmentUpstreamMessage fulfillmentUpstreamMessage = new FulfillmentUpstreamMessage();
        fulfillmentUpstreamMessage.setFulfillmentOrderId(fulfillmentOrderId);
        fulfillmentUpstreamMessage.setMessageType(FulfillmentUpstreamMessageTypeEnum.DELIVERY_STATUS_CHANGE.getType());
        fulfillmentUpstreamMessage.setTenantId(tenantId);
        fulfillmentUpstreamMessage.setWarehouseId(storeId);
        fulfillmentUpstreamMessage.setData(JsonUtil.toJson(ofcDeliveryChangeNotifyMessage));
        return fulfillmentUpstreamMessage;
    }

    private FulfillmentUpstreamMessage toDeliveryChangeNotifyMessage(DeliveryOrder deliveryOrder, CoordinatePoint riderLocation, LocalDateTime updateTime) {
        OfcDeliveryChangeNotifyMessage ofcDeliveryChangeNotifyMessage = new OfcDeliveryChangeNotifyMessage();
        ofcDeliveryChangeNotifyMessage.setTenantId(deliveryOrder.getTenantId());
        ofcDeliveryChangeNotifyMessage.setShopId(deliveryOrder.getStoreId());
        ofcDeliveryChangeNotifyMessage.setViewOrderId(deliveryOrder.getChannelOrderId());
        ofcDeliveryChangeNotifyMessage.setOrderId(deliveryOrder.getOrderKey().getOrderId());
        ofcDeliveryChangeNotifyMessage.setOrderSource(deliveryOrder.getOrderSource());
        ofcDeliveryChangeNotifyMessage.setOrderBizType(deliveryOrder.getOrderBizType());
        ofcDeliveryChangeNotifyMessage.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
        ofcDeliveryChangeNotifyMessage.setDeliveryChannelId(deliveryOrder.getDeliveryChannel());
        ofcDeliveryChangeNotifyMessage.setDeliveryOrderId(deliveryOrder.getId());
        if (deliveryOrder.getRiderInfo() != null) {
            ofcDeliveryChangeNotifyMessage.setRiderName(deliveryOrder.getRiderInfo().getRiderName());
            ofcDeliveryChangeNotifyMessage.setRiderPhone(deliveryOrder.getRiderInfo().getRiderPhone());
            if (riderLocation != null) {
                ofcDeliveryChangeNotifyMessage.setRiderCurrentLongitude(riderLocation.getLongitude());
                ofcDeliveryChangeNotifyMessage.setRiderCurrentLatitude(riderLocation.getLatitude());
                ofcDeliveryChangeNotifyMessage.setCoordinateType(CoordinateTypeEnum.MARS.getCode());
            }
        }
        //状态映射，兼容原老逻辑，待迁移配送查询后可直接删除
        switch (deliveryOrder.getStatus()) {
            case DELIVERY_FAILED:
                if (deliveryOrder.getExceptionType() == DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER ||
                        deliveryOrder.getExceptionType() == DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM ||
                        deliveryOrder.getExceptionType() == DeliveryExceptionTypeEnum.UNKNOWN) {
                    ofcDeliveryChangeNotifyMessage.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_CANCELLED.getCode());
                    ofcDeliveryChangeNotifyMessage.setCancelReasonType(DeliveryCancelTypeEnum.SYSTEM_CANCEL.getValue());
                } else {
                    ofcDeliveryChangeNotifyMessage.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_FAILED.getCode());
                    ofcDeliveryChangeNotifyMessage.setCancelReasonType(DeliveryCancelTypeEnum.SYSTEM_CANCEL_FOR_OTHERS.getValue());
                }
                ofcDeliveryChangeNotifyMessage.setCancelReasonDescription(deliveryOrder.getExceptionDescription());
                break;
            case DELIVERY_CANCELLED:
                ofcDeliveryChangeNotifyMessage.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_CANCELLED.getCode());
                ofcDeliveryChangeNotifyMessage.setCancelReasonDescription(deliveryOrder.getExceptionDescription());
                if (deliveryOrder.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION) {
                    ofcDeliveryChangeNotifyMessage.setCancelReasonType(DeliveryCancelTypeEnum.SYSTEM_CANCEL.getValue());
                } else {
                    ofcDeliveryChangeNotifyMessage.setCancelReasonType(DeliveryCancelTypeEnum.CLIENT_CANCEL.getValue());
                }
                break;
            case DELIVERY_REJECTED:
                ofcDeliveryChangeNotifyMessage.setDeliveryStatus(DeliveryStatusEnum.DELIVERY_REJECTED.getCode());
                ofcDeliveryChangeNotifyMessage.setRejectReasonDescription(deliveryOrder.getExceptionDescription());
                break;
            default:
                ofcDeliveryChangeNotifyMessage.setDeliveryStatus(deliveryOrder.getStatus().getCode());
        }
        ofcDeliveryChangeNotifyMessage.setUpdateTime(TimeUtil.toMilliSeconds(updateTime));
        ofcDeliveryChangeNotifyMessage.setDistance(deliveryOrder.getDistance());
        ofcDeliveryChangeNotifyMessage.setEstimatedDeliveryEndTime(TimeUtil.toMilliSeconds(deliveryOrder.getEstimatedDeliveryEndTime()));
        ofcDeliveryChangeNotifyMessage.setDeliveryFee(deliveryOrder.getDeliveryFee());
        ofcDeliveryChangeNotifyMessage.setDeliveryCount(deliveryOrder.getDeliveryCount());
        Long fulfillmentOrderId = deliveryOrder.getFulfillmentOrderId();
        if(fulfillmentOrderId == null || fulfillmentOrderId<=0){
            fulfillmentOrderId = deliveryOrder.getOrderId();
        }
        ofcDeliveryChangeNotifyMessage.setFulfillmentOrderId(fulfillmentOrderId);
        ofcDeliveryChangeNotifyMessage.setPlatformSource(PlatformSourceEnum.OMS.getCode());
        if(deliveryOrder.getPlatformSourceEnum()!=null){
            ofcDeliveryChangeNotifyMessage.setPlatformSource(deliveryOrder.getPlatformSourceEnum().getCode());
        }

        FulfillmentUpstreamMessage fulfillmentUpstreamMessage = new FulfillmentUpstreamMessage();
        fulfillmentUpstreamMessage.setFulfillmentOrderId(fulfillmentOrderId);
        fulfillmentUpstreamMessage.setMessageType(FulfillmentUpstreamMessageTypeEnum.DELIVERY_STATUS_CHANGE.getType());
        fulfillmentUpstreamMessage.setTenantId(deliveryOrder.getTenantId());
        fulfillmentUpstreamMessage.setWarehouseId(deliveryOrder.getStoreId());
        fulfillmentUpstreamMessage.setData(JsonUtil.toJson(ofcDeliveryChangeNotifyMessage));
        return fulfillmentUpstreamMessage;
    }

    private CoordinatePoint queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto) {
        if (deliveryOrder.getStatus().needQueryRiderLocation()) {
            if(MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())){
                Optional<DeliveryPoi> deliveryPoiOptional = getDeliveryPoi(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),deliveryOrder.getOrderBizType());
                return deliveryPoiOptional
                        .flatMap(deliveryPoi -> deliveryPlatformClient.queryRiderLocation(deliveryOrder, deliveryChannelDto, deliveryPoi))
                        .orElse(null);
            }else {
                if(MccConfigUtils.getPaoTuiCode().contains(deliveryChannelDto.getCarrierCode()+"")){
                    return null;
                }
                if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.getOrderPositionSyncSwitch()){
                    Optional<CoordinatePoint> coordinatePointOptional = newSupplyRiderLocationRepository.queryRiderLocationFromSquirrel(deliveryOrder.getOrderId(),deliveryChannelDto);
                    if(coordinatePointOptional.isPresent()){
                        return coordinatePointOptional.get();
                    }
                }
                Optional<DeliveryPoi> deliveryPoiOptional = getDeliveryPoi(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),deliveryOrder.getOrderBizType());
                return deliveryPoiOptional
                        .flatMap(deliveryPoi -> deliveryPlatformClient.queryRiderLocation(deliveryOrder, deliveryChannelDto, deliveryPoi))
                        .orElse(null);
            }

        }
        return null;
    }

    private Optional<DeliveryPoi> getDeliveryPoi(Long tenantId,Long storeId,Integer orderBizType){
        Optional<DeliveryPoi> opDeliveryPoi ;
        if(MccConfigUtils.getDHTenantIdList().contains(tenantId+"")){
            opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(tenantId, storeId);
        }else {
            DynamicOrderBizType dynamicOrderBizType = ObjectUtils.defaultIfNull(
                    DynamicOrderBizType.findOf(orderBizType), DynamicOrderBizType.MEITUAN_WAIMAI);
            opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(tenantId, storeId,
                    dynamicOrderBizType.getChannelId());
        }
        return opDeliveryPoi;
    }
}
