package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.ocms.channel.mq.YzDeliveryTakeOutOrderUpdateMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.QueryDeliveryInfoApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.DeliveryCallbackThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.request.FarmDeliveryChangeNotifyReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.callback.response.FarmDeliveryChangeNotifyResp;
import com.sankuai.meituan.shangou.empower.tms.delivery.enums.YzOrderChannelDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;


@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class YzDeliveryChangeMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private QueryDeliveryInfoApplicationService queryDeliveryInfoApplicationService;

	@Resource
	private DeliveryCallbackThriftService deliveryCallbackThriftService;

	@Resource
	DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;

	@Resource
	private OrderSystemClient orderSystemClient;

	private static final double DEFAULT_AMOUNT = 0;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.YZ_DELIVERY_ORDER_CHANGE_CONSUMER;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		if (!MccConfigUtils.getYzDeliveryChangeMessageListenerSwitch()) {
			log.info("有赞同城配送状态变更消息监听开关关闭，不处理消息");
			return CONSUME_SUCCESS;
		}

		log.info("开始消费有赞同城配送包裹状态变更事件消息: {}", mafkaMessage);
		YzDeliveryTakeOutOrderUpdateMsg msg = translateMessage(mafkaMessage);
		if (Objects.isNull(msg)) {
			log.error("消费有赞同城配送包裹状态变更事件, msg is null");
			return CONSUME_SUCCESS;
		}

		try {
			String viewOrderId = msg.getTid();
			if (StringUtils.isEmpty(viewOrderId)) {
				log.error("消费有赞运单变更消息失败, viewOrderId is null");
				return CONSUME_FAILURE;
			}
			Result<OrderInfo> orderInfoResult = orderSystemClient.queryByViewOrderId(DynamicOrderBizType.YOU_ZAN_MIDDLE.getValue(), viewOrderId,false);
			OrderInfo orderInfo = orderInfoResult.getInfo();
			if (orderInfo == null) {
				log.error("消费有赞运单变更消息失败，orderId is null");
				return CONSUME_FAILURE;
			}

			DeliveryOrder deliveryOrder = null;
			Long orderId = orderInfo.getOrderKey().getOrderId();
			Optional<DeliveryOrder> deliveryOrderOptional = queryDeliveryInfoApplicationService.queryDeliveryOrderByOrderId(orderId,orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
			if (deliveryOrderOptional.isPresent()) {
				deliveryOrder = deliveryOrderOptional.get();
				// 只有当有赞渠道订单发的是平台配送才会走后续逻辑
				if (!isOrderChannelDeliveryPlatFormDeliveryOrder(deliveryOrder)) {
					log.info("消费有赞运单变更消息成功，有赞订单不是发的平台配送，后续逻辑终止");
					return CONSUME_SUCCESS;
				}
			} else if (MccConfigUtils.yzPlatformDeliveryUpdateSwitch()) {
				// 非牵牛花管理配送的运单（商家端发的），DB里初始化一个运单，用于记录后续运单配送状态变更
				deliveryOrder = initDeliverOrder(msg, orderInfo);
			}
			if (Objects.isNull(deliveryOrder)) {
				log.error("消费有赞运单变更消息失败，deliveryOrder is null");
				return CONSUME_FAILURE;
			}

			FarmDeliveryChangeNotifyReq req = buildFarmDeliveryChangeNotifyReq(msg, deliveryOrder);
			log.info("invoke farmNotifyDeliveryChange request = {}", req);
			FarmDeliveryChangeNotifyResp farmDeliveryChangeNotifyResp = deliveryCallbackThriftService.farmNotifyDeliveryChange(req);
			log.info("invoke farmNotifyDeliveryChange response = {}", farmDeliveryChangeNotifyResp);
			return CONSUME_SUCCESS;
		} catch (Exception e) {
			log.error("消费有赞运单变更消息失败，将会进行重试消费", e);
			return CONSUME_FAILURE;
		}
	}

	private YzDeliveryTakeOutOrderUpdateMsg translateMessage(MafkaMessage mafkaMessage) {
		try {
			YzDeliveryTakeOutOrderUpdateMsg message = translateMessage(mafkaMessage, YzDeliveryTakeOutOrderUpdateMsg.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			// kdtId为有赞侧店铺编号
			Preconditions.checkNotNull(message.getKdtId(), "kdtId is null");
			// distId为有赞侧运单ID
			Preconditions.checkNotNull(message.getDistId(), "distId is null");
			// tid为有赞侧渠道订单ID
			Preconditions.checkNotNull(message.getTid(), "tid is null");
			return message;
		} catch (Exception e) {
			log.error("解析有赞运单变更消息失败:{}", mafkaMessage, e);
			Cat.logEvent("YZ_DELIVERY_ORDER_CHANGE_CONSUMER_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}

	private FarmDeliveryChangeNotifyReq buildFarmDeliveryChangeNotifyReq(YzDeliveryTakeOutOrderUpdateMsg msg, DeliveryOrder deliveryOrder) {
		FarmDeliveryChangeNotifyReq req = new FarmDeliveryChangeNotifyReq();

		req.setOriginId(String.valueOf(deliveryOrder.getOrderId()));
		if (Objects.nonNull(msg.getUpdateTime())) {
			req.setTimestamp(Long.valueOf(msg.getUpdateTime()));
		}
		if (Objects.nonNull(msg.getDeliveryType())) {
			req.setLogistic(String.valueOf(msg.getDeliveryType()));
		}
		req.setLogisticNo(msg.getDistId());
		req.setStatus(msg.getStatus());
		if (StringUtils.isNotEmpty(msg.getDmName())) {
			req.setRiderName(msg.getDmName());
		}
		if (StringUtils.isNotEmpty(msg.getDmMobile())) {
			req.setRiderPhone(msg.getDmMobile());
		}
		if (Objects.nonNull(msg.getDeliveryFee())) {
			req.setAmount(String.valueOf(MoneyUtil.fromCentToYuan(msg.getDeliveryFee()).orElse(DEFAULT_AMOUNT)));
		}
		req.setCancelReason(msg.getCancelReason());
		req.setCancelReasonCode(msg.getCancelFrom());
		req.setPlatformCode(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
		req.setOrderChannelType(DynamicChannelType.YOU_ZAN.getChannelId());

		return req;
	}

	private boolean isOrderChannelDeliveryPlatFormDeliveryOrder(DeliveryOrder deliveryOrder) {
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannelDto.getDeliveryPlatFormCode();
	}

	/**
	 * 无运单时，运单初始化
	 */
	private DeliveryOrder initDeliverOrder(YzDeliveryTakeOutOrderUpdateMsg msg, OrderInfo orderInfo) {
		log.info("有赞渠道牵牛花配送管理关闭，无运单信息时接收有赞包裹变更消息初始化 channelOrderId：{}", orderInfo.getChannelOrderId());
		Integer deliveryChannelCode;
		if (msg.getDeliveryType() == null) {
			// 兜底
			deliveryChannelCode = DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY.getCode();
		} else {
			if (MccConfigUtils.yzDeliveryNoCarrierTypeList().contains(msg.getDeliveryType())) {
				// 无具体承运商是赋默认值(如 同时呼叫多个承运商)
				deliveryChannelCode = DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY.getCode();
			} else {
				DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByLogisticMark(
						DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode(),
						DynamicChannelType.YOU_ZAN.getChannelId(),
						String.valueOf(msg.getDeliveryType())
				);
				deliveryChannelCode = deliveryChannel.getCarrierCode();
			}
		}
		Double deliveryFee = MoneyUtil.fromCentToYuan(msg.getDeliveryFee()).orElse(DEFAULT_AMOUNT);
		Optional<DeliveryStatusEnum> deliveryStatusEnumOptional = YzOrderChannelDeliveryStatusEnum.mapToDeliveryStatus(msg.getStatus());
		if (!deliveryStatusEnumOptional.isPresent()) {
			log.error("yz deliveryStatus 不存在");
			return null;
		}
		DeliveryStatusEnum deliveryStatusEnum = deliveryStatusEnumOptional.get();
		LocalDateTime changeTime = TimeUtil.fromSeconds(Long.valueOf(msg.getUpdateTime()));
		// 初始化
		DeliveryOrder deliveryOrder = new DeliveryOrder(orderInfo, deliveryChannelCode);
		deliveryOrder.setChannelDeliveryId(msg.getDistId());
		deliveryOrder.activate();
		deliveryOrder.setDeliveryCount(1);
		deliveryOrder.setDeliveryFee(BigDecimal.valueOf(deliveryFee));
		if (!StringUtils.isAllBlank(msg.getDmName(), msg.getDmMobile())) {
			Rider rider = new Rider(msg.getDmName(), msg.getDmMobile(), null);
			deliveryOrder.setRiderInfo(rider);
		}
		deliveryOrder.onStatusChangeWithOutLog(deliveryStatusEnum, changeTime);
		deliveryOrderRepository.save(deliveryOrder);
		return deliveryOrder;
	}
	
}
