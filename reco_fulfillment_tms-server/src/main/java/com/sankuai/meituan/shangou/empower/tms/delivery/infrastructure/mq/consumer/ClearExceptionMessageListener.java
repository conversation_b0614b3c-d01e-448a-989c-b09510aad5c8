package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.ClearDeliveryExceptionMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.MedicineTenantWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;

/**
 * <AUTHOR>
 * @date 2021/4/16
 * @email jianglilin02@meituan
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class ClearExceptionMessageListener extends AbstractDeadLetterConsumer {

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private MedicineTenantWrapper medicineTenantWrapper;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.CLEAR_DELIVERY_EXCEPTION_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始清除异常消息: {}", message);
        ClearDeliveryExceptionMsg clearDeliveryExceptionMsg = translateMessage(message);
        if (Objects.isNull(clearDeliveryExceptionMsg)) {
            return CONSUME_FAILURE;
        }
        try {
            OrderKey orderKey = clearDeliveryExceptionMsg.getOrderKey();
            List<DeliveryOrder> deliveryOrders = new ArrayList<>();
            if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
                deliveryOrders = deliveryOrderRepository.getDeliveryOrdersMaxWithOrderId(orderKey.getOrderId());
            }else {
                deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderKey.getOrderId());
            }

            List<DeliveryOrder> activeDeliveryOrders = Optional.ofNullable(deliveryOrders).orElse(Lists.newArrayList()).stream().filter(DeliveryOrder::isActive).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(activeDeliveryOrders)) {
                return CONSUME_SUCCESS;
            }
            DeliveryOrder deliveryOrder = activeDeliveryOrders.iterator().next();
            if (isCompareEstimatedDeliveryTime(clearDeliveryExceptionMsg)) {
                LocalDateTime estimatedDeliveryTime = TimeUtil.fromMilliSeconds(clearDeliveryExceptionMsg.getEstimatedDeliveryTime());
                // 下单后支持修改预计送达时间，这里比较消息体和运单快照里的预计送达时间，确保仅处理最新的预计送达时间对应的消息
                if (!Objects.equals(estimatedDeliveryTime, Optional.ofNullable(deliveryOrder.getEstimatedDeliveryEndTime())
                        .orElse(deliveryOrder.getEstimatedDeliveryTime()))) {
                    log.info("ClearExceptionMessageListener estimatedDeliveryTime not equal, orderId is {}", orderKey.getOrderId());
                    return CONSUME_SUCCESS;
                }
            }
            deliveryOrder.clearException();
            boolean result = deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
            // 并发导致的保存失败、进行重试
            if (!result) {
                return RECONSUME_LATER;
            }
            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("consume ClearDeliveryExceptionMsg error", e);
            return CONSUME_FAILURE;
        }
    }


    private ClearDeliveryExceptionMsg translateMessage(MafkaMessage mafkaMessage) {
        try {
            ClearDeliveryExceptionMsg message = translateMessage(mafkaMessage, ClearDeliveryExceptionMsg.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getOrderKey(), "deliveryId is null");
            return message;

        } catch (Exception e) {
            log.error("解析配送流水消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    /**
     * 返回true代表需要比较预计送达时间
     */
    private boolean isCompareEstimatedDeliveryTime(ClearDeliveryExceptionMsg message) {
        Long tenantId = message.getOrderKey().getTenantId();
        if (MccConfigUtils.checkIsDHTenant(tenantId)) {
            return false;
        }
        if (medicineTenantWrapper.isUwmsMedicineTenant(tenantId)) {
            return false;
        }

        if (Objects.isNull(message.getEstimatedDeliveryTime())) {
            return false;
        }

        Long storeId = message.getOrderKey().getStoreId();
        if (Objects.isNull(storeId)) {
            return false;
        }

        // 门店白名单校验
        return MccConfigUtils.isOrderInfoChangeStoreIdWhiteList(storeId);
    }
}
