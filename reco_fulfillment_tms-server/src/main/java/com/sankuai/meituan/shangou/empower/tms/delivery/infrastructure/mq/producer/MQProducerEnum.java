package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer;

import lombok.Getter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/11
 */
@Getter
public enum MQProducerEnum {

	/**
	 * 发起配送指令
	 * (用于异步或延时发起配送)
	 */
	DELIVERY_LAUNCH_COMMAND("shangou_empower_delivery_launch_delivery_topic", "com.sankuai.shangou.empower.orderbiz"),

	/**
	 * 新供给发起配送指令
	 * (用于异步或延时发起配送)
	 */
	NEW_SUPPLY_DELIVERY_LAUNCH_COMMAND("shangou_empower_new_supply_delivery_launch_delivery_topic", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 取消配送指令
	 * (用于异步取消配送)
	 *
	 * @since 1.4
	 */
	DELIVERY_CANCEL_COMMAND("shangou_empower_delivery_cancel_topic", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 配送流水异步记录指令
	 * (用于异步记录配送流水)
	 *
	 * @since 1.4
	 */
	DELIVERY_LOG_COMMAND("shangou_empower_delivery_log_topic", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 配送变更通知
	 * (主要通知订单，更新订单订单侧配送进度)
	 */
	DELIVERY_CHANGE_NOTIFY("shangou_empower_delivery_change_topic", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 转单通知订单
	 */
	DELIVERY_TRANS_NOTIFY("shangou_empower_delivery_trans_topic", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 锁单结束通知订单
	 */
	ORDER_LOCK_END_NOTIFY("shangou_empower_order_lock_end_topic", "com.sankuai.sgfulfillment.tms"),

	DELAY_NOTIFY_DELIVERY_STATUS_TOPIC("delay_notify_delivery_status_change", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 通用运单变更同步通知，订单、cbizmanagement、tms等服务都可监听
	 */
	DELIVERY_CHANGE_ASYNC_OUT("shangou_empower_delivery_change_async_out_topic", "com.sankuai.sgfulfillment.tms"),
	/**
	 * 骑手分配超时检查
	 * (用于延时计算骑手接单是否超时)
	 */
	RIDER_ASSIGN_TIME_OUT("shangou_empower_delivery_overtime_check_topic", "com.sankuai.shangou.empower.orderbiz"),
	/**
	 * 骑手位置同步
	 * (用于异步推进配送进度)
	 *
	 * @since 1.4
	 */
	RIDER_POSITION_SYNC("rider_location_sync", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 清除配送异常
	 * (用于兜底清除配送失败异常)
	 */
	CLEAR_DELIVERY_EXCEPTION("shangou_empower_delivery_clear_exception", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 配送超时检查
	 */
	DELIVERY_TIMEOUT_CHECK("shangou_empower_delivery_timeout_check_topic", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 配送超时，用于通知外部服务
	 */
	DELIVERY_TIMEOUT_NOTIFY("shangou_empower_delivery_timeout_notify_topic", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 配送统一回调主题
	 */
	DELIVERY_UNIFIED_CALLBACK("shangou_empower_delivery_unify_callback", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 歪马配送状态检查主题
	 */
	DH_DELIVERY_STATUS_CHECK("shangou_empower_delivery_dh_status_check", "com.sankuai.sgfulfillment.tms"),

	/**
	 *
	 */
	NEW_SUPPLY_DELIVERY_TIMEOUT_CHECK("shangou_empower_delivery_new_supply_status_check", "com.sankuai.sgfulfillment.tms"),

	DELIVERY_ORDER_TRANSFORM_TOPIC("shangou_empower_delivery_order_transform_topic", "com.sankuai.sgfulfillment.tms"),

	/**
	 *  同步骑手位置到渠道的topic
	 */
	RIDER_LOCATION_SYNC_TO_CHANNEL_TOPIC("rider_location_sync_to_channel_topic", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 订单轨迹
	 */
	ORDER_TRACK_LOG("shangou.empower.order.track.topic", "com.sankuai.shangou.empower.orderbiz"),


	/**
	 * 保时洁图片送审topic
	 */
	CREDIT_AUDIT_PIC_BATCH_LISTENER("credit.audit.pic.batch.listener", "autoaudit-pic-mq"),


	/**
	 * 送达照片送审自动审批触发消息
	 */
	CREDIT_AUDIT_AUTOMATIC_PASS_TOPIC("delivery.proof.photo.credit.audit.automatic.pass.topic", "com.sankuai.sgfulfillment.tms"),

	SG_EMPOWER_DELIVERY_HU_SYNC_TOPIC("shangou_empower_delivery_hu_sync_topic","com.sankuai.sgfulfillment.tms"),

	/**
	 * 线下推广自提的拣货下发消息
	 */
	OFFLINE_PROMOTE_PICK_PUSH_DOWN_TOPIC("notify_offline_promote_pick_order_push_down","com.sankuai.waimai.sc.pickselectservice"),

	/**
	 * OFW履约信息回传
	 */
	OFC_OFW_FULFILLMENT_UPSTREAM_TOPIC("ofc_ofw_fulfillment_upstream_topic","com.sankuai.qnh.ofc.ofw"),
	/**
	 * 歪马转换配送方式消息
	 */
	DRUNK_HORSE_TRANS_DELIVERY_TYPE("drunk_horse_trans_delivery_type", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 自配送订单配送状态对外推送
	 */
	SELF_DELIVERY_CHANGE_OUTER_NOTIFY("shangou_empower_self_delivery_change_outer_notify_topic", "com.sankuai.sgfulfillment.tms"),

	/**
	 * 订单履约看板消息
	 */
	FULL_FILL_MESSAGE_TOPIC("full.fill.message.topic", "com.sankuai.waimai.sc.pickselectservice"),

	/**
	 * 同步配送异常
	 */
	DELIVERY_EXCEPTION_NOTIFY_TOPIC("delivery.exception.notify.topic","com.sankuai.sgfulfillment.tms"),

	/**
	 * 配送切换OFC二次校验topic
	 */
	DELIVERY_ORDER_STATUS_DELAY_CHECK_TOPIC("delivery.order.status.delay.check.topic","com.sankuai.sgfulfillment.tms"),

	/**
	 * tms发送封签容具归还超时topic
	 */
	SEAL_CONTAINER_RETURN_TIMEOUT_TOPIC("seal_container_return_timeout_topic",  "com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	PLATFORM_DELIVERY_CHECK_TOPIC("shangou_empower_platform_delivery_check_topic","com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	QNH_DELIVERY_LAUNCH_TOPIC("qnh_delivery_launch_msg","com.sankuai.sgfulfillment.tms", "com.sankuai.mafka.castle.daojiacommon"),

	;



	private final String topic;
	private final String appkey;
	private final String nameSpace;

	MQProducerEnum(String topic, String appkey) {
		this.topic = topic;
		this.appkey = appkey;
		this.nameSpace = "waimai";
	}

	MQProducerEnum(String topic, String appkey, String nameSpace) {
		this.topic = topic;
		this.appkey = appkey;
		this.nameSpace = nameSpace;
	}

}
