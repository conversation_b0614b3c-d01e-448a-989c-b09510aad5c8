package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryMarkSquirrelOperationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.PlatformDeliveryUpdateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.ClearDeliveryExceptionMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.NO_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.DELIVERY_CANCELLED;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.DELIVERY_DONE;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.DELIVERY_FAILED;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.DELIVERY_REJECTED;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.RIDER_ARRIVED_SHOP;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.RIDER_ASSIGNED;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.RIDER_TAKEN_GOODS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Slf4j
@Component
@Order()
@SuppressWarnings("rawtypes")
public class OrderPlatformDeliveryUpdateMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryProcessApplicationService deliveryProcessApplicationService;
	@Resource
	private DeliveryTimeOutCheckService deliveryTimeOutCheckService;
    @Resource
    private MafkaDelayMessageProducer<ClearDeliveryExceptionMsg> clearDeliveryExceptionProducer;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;
	@Autowired
	private DeliveryMarkSquirrelOperationService deliveryMarkSquirrelOperationService;

	private static final Integer PRIVATE_DOMAIN_CHANNEL = 0;

	private static final List<Integer> NOT_COVER_CODE = Arrays.asList(
			DeliveryExceptionCodeEnum.SELF_DELIVERY.getCode(),
			DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode(),
			DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode(),
			DeliveryExceptionCodeEnum.RECALL_RIDER_FAIL.getCode(),
			DeliveryExceptionCodeEnum.RECALL_SELF_RIDER_FAIL.getCode());

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.ORDER_DELIVERY_UPDATE;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费订单配送状态变更消息: {}", mafkaMessage);
		OrderPlatformDeliveryUpdateMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			OrderKey orderKey = new OrderKey(message.getTenantId(), message.getShopId(), message.getOrderId());
			Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderKey, false);
			if (orderInfoQueryResult.isFail()) {
				if (orderInfoQueryResult.getFailure().isNeedRetry()) {
					log.warn("查询订单[{}]失败,将会进行重试", orderKey);
					return CONSUME_FAILURE;
				} else {
					log.warn("查询订单[{}]异常,将会放弃执行", orderKey);
					return CONSUME_SUCCESS;
				}
			}

			if (orderInfoQueryResult.getInfo().isSelfDelivery()) {
				log.info("自配送订单，不处理");
				return CONSUME_SUCCESS;
			}

			DynamicChannelType dynamicChannelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfoQueryResult.getInfo().getOrderBizType());
			if (dynamicChannelType == null || Objects.equals(PRIVATE_DOMAIN_CHANNEL,dynamicChannelType.getChannelStandard())) {
				log.error("私域渠道平台配送订单，不处理");
				return CONSUME_SUCCESS;
			}

			DeliveryOrder deliveryOrder;
			orderKey = new OrderKey(message.getTenantId(), orderInfoQueryResult.getInfo().getWarehouseId(), message.getOrderId());
			List<DeliveryOrder> deliveryOrders = deliveryOrderRepository.getDeliveryOrders(orderKey);
			if (CollectionUtils.isEmpty(deliveryOrders)) {
				deliveryOrder = new DeliveryOrder(orderInfoQueryResult.getInfo(), DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode());
				deliveryOrder.activate();
				deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
				deliveryMarkSquirrelOperationService.saveDeliveryMark(deliveryOrder);
                // 保存运单、发送配送超时检查
                deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);
                // 发送异常清除检查
				triggerExceptionClearMonitor(Optional.ofNullable(deliveryOrder.getEstimatedDeliveryTime())
						.orElse(deliveryOrder.getEstimatedDeliveryEndTime()), orderKey);

			} else if (deliveryOrders.size() > 1) {
				log.error("消费订单配送状态变更消息失败：一个自配送订单查询到多个运单，将放弃处理");
				return CONSUME_SUCCESS;

			} else {
				deliveryOrder = deliveryOrders.get(0);
			}

			if(!MccConfigUtils.getDHTenantIdList().contains(orderKey.getTenantId())){
				if(isMaltFarmDeliveryOrder(deliveryOrder)){
					log.info("麦芽田配送，不处理 orderKey:{}",orderKey);
					return CONSUME_SUCCESS;
				}
			}
			PlatformDeliveryUpdateCmd updateCmd = buildPlatformDeliveryUpdateCmd(deliveryOrder, message, orderInfoQueryResult.getInfo());
			DeliveryExceptionInfo offlineExceptionInfo = getOfflineExceptionInfo(deliveryOrder);
			if (updateCmd != null) {
				try {
					if(updateCmd.getStatus()!=null){
						Cat.logEvent("platform_delivery_status", ""+updateCmd.getStatus().getCode());
						if(orderInfoQueryResult.getInfo().getOriginalDistributeType()!=null && updateCmd.getStatus() == RIDER_ASSIGNED){
							Cat.logEvent("platform_delivery_code", ""+orderInfoQueryResult.getInfo().getOriginalDistributeType());
						}
					}

				}catch (Exception e){
					log.error("cat status error",e);
				}
				deliveryProcessApplicationService.updatePlatformDelivery(updateCmd, getExceptionInfo(updateCmd, orderInfoQueryResult.getInfo(), offlineExceptionInfo,message));
			}

			return CONSUME_SUCCESS;

		} catch (Exception e) {
			log.error("消费订单配送状态变更消息失败，将会进行重试", e);
			return CONSUME_FAILURE;
		}
	}
	private PlatformDeliveryUpdateCmd buildPlatformDeliveryUpdateCmd(DeliveryOrder deliveryOrder, OrderPlatformDeliveryUpdateMessage message, OrderInfo orderInfo) {
		DeliveryExceptionInfo noException = new DeliveryExceptionInfo(NO_EXCEPTION, StringUtils.EMPTY);

		LocalDateTime updateTime = Optional.ofNullable(message.getUpdateTime())
				.map(TimeUtil::fromMilliSeconds)
				.orElse(LocalDateTime.now());
		Map<String,String> extMap = new HashMap<>();
		if(StringUtils.isNotEmpty(message.getExtJson())){
			try {
				extMap = JsonUtil.fromJson(message.getExtJson(), new TypeReference<Map<String, String>>() {});

			}catch (Exception e){
				log.error("ExtJson to map error , extJson:{}",message.getExtJson(),e);
			}
		}

		Rider rider = StringUtils.isNotBlank(orderInfo.getRiderName()) && StringUtils.isNotBlank(orderInfo.getRiderPhone()) ?
				new Rider(orderInfo.getRiderName(), orderInfo.getRiderPhone(), orderInfo.getRiderPhoneToken()) : null;

		Integer isRecallDelivery = buildRecallDelivery(message, deliveryOrder.getOrderBizType());

		Integer isFourWheelDelivery = null;
		Integer isManual = null;
		Integer performanceServiceFee = null;
		if(MapUtils.isNotEmpty(extMap)){
			if(extMap.containsKey("isFourWheelDelivery")){
				isFourWheelDelivery = Integer.parseInt(extMap.get("isFourWheelDelivery"));
			}
			if(extMap.containsKey("isManual")){
				isManual = Integer.parseInt(extMap.get("isManual"));
			}
		}

		DistributeStatusEnum orderDistributeStatus = DistributeStatusEnum.enumOf(message.getDistributeStatus());
		if (orderDistributeStatus != null) {
			switch (orderDistributeStatus) {
				case WAIT_FOR_ASSIGN_RIDER:
					if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.platformDeliveryActiveSwitch()){
						deliveryOrder.activate();
					}
					return new PlatformDeliveryUpdateCmd(deliveryOrder, WAITING_TO_ASSIGN_RIDER, rider, noException, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case RIDER_ASSIGNED:
					if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.platformDeliveryActiveSwitch()){
						deliveryOrder.activate();
					}
					return new PlatformDeliveryUpdateCmd(deliveryOrder, RIDER_ASSIGNED, rider, noException, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case RIDER_REACH_SHOP:
					if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.platformDeliveryActiveSwitch()){
						deliveryOrder.activate();
					}
					return new PlatformDeliveryUpdateCmd(deliveryOrder, RIDER_ARRIVED_SHOP, rider, noException, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case RIDER_TAKE_GOODS:
					if(com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.platformDeliveryActiveSwitch()){
						deliveryOrder.activate();
					}
					return new PlatformDeliveryUpdateCmd(deliveryOrder, RIDER_TAKEN_GOODS, rider, noException, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case RIDER_DELIVERED:
					return new PlatformDeliveryUpdateCmd(deliveryOrder, DELIVERY_DONE, rider, noException, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case DISTRIBUTE_CANCELED:
					return new PlatformDeliveryUpdateCmd(deliveryOrder, DELIVERY_CANCELLED, rider, noException, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case RIDER_TAKE_GOODS_FAILED:
					return new PlatformDeliveryUpdateCmd(
							deliveryOrder,
							DELIVERY_FAILED,
							rider,
							new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.UNKNOWN, "骑手取货失败"),
							updateTime,
							isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case DISTRIBUTE_FAILED:
					return new PlatformDeliveryUpdateCmd(
							deliveryOrder,
							DELIVERY_FAILED,
							rider,
							new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.UNKNOWN, "配送失败"),
							updateTime,
							isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);

				case DISTRIBUTE_REJECTED:
					return new PlatformDeliveryUpdateCmd(
							deliveryOrder,
							DELIVERY_REJECTED,
							rider,
							new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.UNKNOWN, "物流拒单"),
							updateTime,
							isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);
				case DISTRIBUTE_NORMAL:
					return new PlatformDeliveryUpdateCmd(deliveryOrder, deliveryOrder.getStatus(), rider, noException, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);
				case DISTRIBUTE_EXCEPTION:
					DeliveryExceptionInfo exceptionInfo = buildExceptionInfo(message,noException,deliveryOrder.getOrderBizType());

					PlatformDeliveryUpdateCmd updateCmd = new PlatformDeliveryUpdateCmd(deliveryOrder, deliveryOrder.getStatus(), rider, exceptionInfo, updateTime, isRecallDelivery,isFourWheelDelivery,isManual,performanceServiceFee);
					if(extMap!=null && extMap.containsKey("failType")){
						updateCmd.setFailType(extMap.get("failType"));
					}
					if(extMap!=null && extMap.containsKey("dealDeadline")){
						updateCmd.setDealDeadline(extMap.get("dealDeadline"));
					}
					return updateCmd;
				default:
					log.warn("不处理的订单配送状态[{}]，将放弃处理", orderDistributeStatus);
			}
		}
		return null;
	}

	private Integer buildRecallDelivery(OrderPlatformDeliveryUpdateMessage message, Integer orderBizType) {
		try {
			if (!MccConfigUtils.getJddjRecallDeliverySwitch()) {
				return RecallDeliveryEnum.NO_NEED_RECALL.getCode();
			}

			if (Objects.isNull(message) || StringUtils.isEmpty(message.getChannelStatusCode()) || Objects.isNull(orderBizType)) {
				return RecallDeliveryEnum.NO_NEED_RECALL.getCode();
			}

			// 当前仅对京东平台配送判断并赋值isRecallDelivery
			if (!orderBizType.equals(DynamicOrderBizType.JING_DONG.getValue())) {
				return RecallDeliveryEnum.NO_NEED_RECALL.getCode();
			}

			List<Integer> recallDeliveryStatusList = MccConfigUtils.getJddjRecallDeliveryStatusList();
			if (CollectionUtils.isEmpty(recallDeliveryStatusList)) {
				return RecallDeliveryEnum.NO_NEED_RECALL.getCode();
			}

			Integer channelStatusCode = Integer.valueOf(message.getChannelStatusCode());
			return recallDeliveryStatusList.contains(channelStatusCode) ? RecallDeliveryEnum.NEED_RECALL.getCode() :
					RecallDeliveryEnum.NO_NEED_RECALL.getCode();
		} catch (Exception e) {
			log.error("buildRecallDelivery error", e);
			return RecallDeliveryEnum.NO_NEED_RECALL.getCode();
		}
	}

	private DeliveryExceptionInfo buildExceptionInfo(OrderPlatformDeliveryUpdateMessage message,DeliveryExceptionInfo noException,Integer orderBizType){
		if(StringUtils.isEmpty(message.getChannelStatusCode()) || orderBizType == null){
			return noException;
		}
		if(orderBizType.equals(DynamicOrderBizType.ELE_ME.getValue())){
			if("17".equals(message.getChannelStatusCode())){
				return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "配送异常",DeliveryExceptionCodeEnum.DELIVERY_EXCEPTION_UPLOAD.getCode());
			}
		}else if(orderBizType.equals(DynamicOrderBizType.JING_DONG.getValue())){
			if("25".equals(message.getChannelStatusCode())){
				return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "取货失败",DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL.getCode());
			}else if("26".equals(message.getChannelStatusCode())){
				return noException;
			}else if("27".equals(message.getChannelStatusCode())){
				return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "取货失败待审核",DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode());
			}else if("28".equals(message.getChannelStatusCode())){
				return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "骑手异常上报",DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode());
			}else if("29".equals(message.getChannelStatusCode())){
				return noException;
			}
		}
		return noException;
	}

	private DeliveryExceptionInfo getOfflineExceptionInfo(DeliveryOrder deliveryOrder) {
		if(deliveryOrder == null){
			return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY);
		}

		return new DeliveryExceptionInfo(
				Optional.ofNullable(deliveryOrder.getExceptionType()).orElse(DeliveryExceptionTypeEnum.NO_EXCEPTION),
				Optional.ofNullable(deliveryOrder.getExceptionDescription()).orElse(StringUtils.EMPTY),
				Optional.ofNullable(deliveryOrder.getDeliveryExceptionCode()).orElse(DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode()));
	}

	private DeliveryExceptionInfo getExceptionInfo(PlatformDeliveryUpdateCmd cmd, OrderInfo orderInfo, DeliveryExceptionInfo offlineExceptionInfo,OrderPlatformDeliveryUpdateMessage message) {
		//配送完成或者订单完结，清理异常
		if (deliveryOrOrderFinish(cmd.getStatus(), orderInfo)) {
			return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY);
		}

		if(cmd.getDeliveryOrder()!=null && cmd.getDeliveryOrder().getStatus().isFinalStatus()){
			return new DeliveryExceptionInfo(cmd.getDeliveryOrder().getExceptionType(), cmd.getDeliveryOrder().getExceptionDescription(),cmd.getDeliveryOrder().getDeliveryExceptionCode());
		}

		if(cmd.getExceptionInfo().getExceptionCode()!=null
				&& (cmd.getExceptionInfo().getExceptionCode() == DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode()
					|| cmd.getExceptionInfo().getExceptionCode() == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL.getCode()
					|| cmd.getExceptionInfo().getExceptionCode() == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode())){
			return cmd.getExceptionInfo();
		}
		DistributeStatusEnum orderDistributeStatus = DistributeStatusEnum.enumOf(message.getDistributeStatus());
		boolean isCover = Objects.equals(DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode(),cmd.getDeliveryOrder().getDeliveryExceptionCode()) && orderDistributeStatus == DistributeStatusEnum.DISTRIBUTE_NORMAL;
		if(!isCover && (Objects.equals(cmd.getDeliveryOrder().getOrderBizType(),
				DynamicOrderBizType.JING_DONG.getValue()))){
			isCover = "26".equals(message.getChannelStatusCode()) || "29".equals(message.getChannelStatusCode());
		}
		List<Integer> notCoverCodeList = MccConfigUtils.getOrderPlatformDeliveryNotCoverCodeList();
		if (CollectionUtils.isEmpty(notCoverCodeList)) {
			log.error("notCoverCodeList is empty");
			throw new CommonRuntimeException("notCoverCodeList is empty");
		}
		if(!isCover && notCoverCodeList.contains(cmd.getDeliveryOrder().getDeliveryExceptionCode())){
			return new DeliveryExceptionInfo(cmd.getDeliveryOrder().getExceptionType(), cmd.getDeliveryOrder().getExceptionDescription(),cmd.getDeliveryOrder().getDeliveryExceptionCode());
		}

		Integer exceptionCode = Optional.ofNullable(cmd.getExceptionInfo())
				.map(DeliveryExceptionInfo::getExceptionCode)
				.orElse(0);
		// 配送未到终态、预计送达时间未送达异常信息需要保留
		Long tenantId = Optional.ofNullable(orderInfo).map(OrderInfo::getOrderKey).map(OrderKey::getTenantId).orElse(0L);
		boolean estimatedTimeoutExceptionCheck = offlineExceptionInfo != null
				&& Objects.equals(offlineExceptionInfo.getExceptionCode(), DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode())
				&& !MccConfigUtils.getDHTenantIdList().contains(String.valueOf(tenantId));
		if(exceptionCode != 0){
			// 超时未完成、伴随这个配送周期、只更新最新异常描述、保存历史超时异常code
			return estimatedTimeoutExceptionCheck
					? new DeliveryExceptionInfo(cmd.getExceptionInfo().getExceptionType(),
					cmd.getExceptionInfo().getExceptionDescription(),
					DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode())
					: cmd.getExceptionInfo();
		}
		if(estimatedTimeoutExceptionCheck){
			// 恢复超时未完成异常描述信息
			return new DeliveryExceptionInfo(offlineExceptionInfo.getExceptionType(),
					DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getDesc(),
					DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode());
		}

		return cmd.getExceptionInfo();
	}

	private boolean deliveryOrOrderFinish(DeliveryStatusEnum deliveryStatusEnum, OrderInfo orderInfo){
		return (orderInfo != null && orderInfo.isFinished()) || deliveryStatusEnum == DeliveryStatusEnum.DELIVERY_DONE;
	}

	private OrderPlatformDeliveryUpdateMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			OrderPlatformDeliveryUpdateMessage message = translateMessageWithBodyHolder(mafkaMessage, OrderPlatformDeliveryUpdateMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
			Preconditions.checkNotNull(message.getShopId(), "shopId is null");
			Preconditions.checkNotNull(message.getOrderId(), "orderId is null");
			Preconditions.checkNotNull(message.getDistributeStatus(), "distributeStatus is null");
			return message;

		} catch (Exception e) {
			log.error("解析订单配送状态变更消息失败:{}", mafkaMessage, e);
			return null;
		}
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class OrderPlatformDeliveryUpdateMessage {
		/**
		 * 租户id
		 */
		private Long tenantId;
		/**
		 * 门店id
		 */
		private Long shopId;
		/**
		 * 订单id
		 */
		private Long orderId;
		/**
		 * 配送状态
		 */
		private Integer distributeStatus;
		/**
		 * 变更时间
		 */
		private Long updateTime;

		/**
		 * 渠道状态code
		 */
		private String channelStatusCode;

		/**
		 * 额外信息
		 */
		private String extJson;
	}

	private void triggerExceptionClearMonitor(LocalDateTime estimatedDeliveryTime, OrderKey orderKey) {
		// 过滤歪马租户
		if (MccConfigUtils.getDHTenantIdList().contains(String.valueOf(orderKey.getTenantId()))) {
			return;
		}
		if (Lion.getConfigRepository().getBooleanValue("delivery.clear.exception.fallback", false)) {
			return;
		}
		if(estimatedDeliveryTime == null){
			return;
		}
		long delayMillis = com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils
				.getDeliveryExceptionClearDelayMillis(estimatedDeliveryTime);
		clearDeliveryExceptionProducer.sendDelayMessageInMillis(new ClearDeliveryExceptionMsg(orderKey, TimeUtil.toMilliSeconds(estimatedDeliveryTime)), Math.max(delayMillis, 5000));
	}

	private boolean isMaltFarmDeliveryOrder(DeliveryOrder deliveryOrder) {
		DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelDto) || Objects.isNull(deliveryChannelDto.getDeliveryPlatFormCode())) {
			return false;
		}

		return DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode() == deliveryChannelDto.getDeliveryPlatFormCode();
	}

}
