package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.sankuai.banma.deliverywaybill.order.callback.client.callback.DeliveryCallbackThriftService;
import com.sankuai.banma.deliverywaybill.order.callback.client.callback.ResponseCodeEnum;
import com.sankuai.banma.deliverywaybill.order.callback.client.callback.Status;
import com.sankuai.banma.deliverywaybill.order.callback.client.callback.request.DeliveryCancelResultNotifyRequest;
import com.sankuai.banma.deliverywaybill.order.callback.client.callback.request.DeliveryChangeNotifyRequest;
import com.sankuai.banma.deliverywaybill.order.callback.client.callback.request.DeliveryLaunchResultNotifyRequest;
import com.sankuai.banma.deliverywaybill.order.callback.client.callback.response.DeliveryNotifyResponse;
import com.sankuai.meituan.banma.thrift.upstream.open.vo.BmSelfDeliveryPkgStatus;
import com.sankuai.meituan.banma.thrift.upstream.open.vo.SelfPkgCancelReasonEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryCancelCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryChangeCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryLaunchCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 美团运单中心COS系统提供的配送统一回调服务.
 *
 * <AUTHOR>
 * @since 2021/3/11 16:31
 */
@Slf4j
@Service
public class CosUnifiedDeliveryCallbackThriftServiceImpl implements DeliveryCallbackThriftService {

	@Resource
	private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryNotifyResponse notifyDeliveryLaunchResult(DeliveryLaunchResultNotifyRequest request) {
		try {
			String requestErrorMsg = request.validate();
			if (requestErrorMsg != null) {
				Cat.logEvent("CALLBACK_LAUNCH_" + request.getDeliveryChannelId(), "FAIL_BY_PARAM");
				return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), requestErrorMsg));
			}

			DeliveryChannel deliveryChannelInfo = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(Integer.parseInt(request.getDeliveryChannelId()));
			if (DeliveryChannel.UNKNOWN_CARRIER_NAME.equals(deliveryChannelInfo.getCarrierName())) {
				Cat.logEvent("CALLBACK_LAUNCH_" + request.getDeliveryChannelId(), "FAIL_BY_UNSUPPORTED_CHANNEL");
				return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), "不支持的配送渠道"));
			}

			deliveryUnifiedCallbackMessageProducer.sendMessage(
					new DeliveryUnifiedCallbackMessage(
							DeliveryCallbackTypeEnum.LAUNCH_CALLBACK,
							new DeliveryLaunchCallbackInfo(
									Long.valueOf(request.getBizStoreId()),
									Long.valueOf(request.getBizOrderId()),
									request.getIsLaunchSuccess(),
									deliveryChannelInfo.getCarrierCode(),
									request.getServicePackage(),
									String.valueOf(request.getPkgId()),
									request.getDistance(),
									request.getDeliveryFee(),
									request.getLaunchFailReasonDesc()
							)
					),
					Long.valueOf(request.getBizOrderId())
			);

			Cat.logEvent("CALLBACK_LAUNCH_" + request.getDeliveryChannelId(), "SUCCESS");
			return new DeliveryNotifyResponse(Status.SUCCESS);
		} catch (Exception e) {
			Cat.logEvent("CALLBACK_LAUNCH_" + request.getDeliveryChannelId(), "ERROR");
			log.error("CosUnifiedDeliveryCallbackThriftservice.notifyDeliveryLaunchResult error", e);
			return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), "exception detected"));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryNotifyResponse notifyDeliveryCancelResult(DeliveryCancelResultNotifyRequest request) {
		try {
			String requestErrorMsg = request.validate();
			if (requestErrorMsg != null) {
				Cat.logEvent("CALLBACK_CANCEL_" + request.getDeliveryChannelId(), "FAIL_BY_PARAM");
				return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), requestErrorMsg));
			}

			deliveryUnifiedCallbackMessageProducer.sendMessage(
					new DeliveryUnifiedCallbackMessage(
							DeliveryCallbackTypeEnum.CANCEL_CALLBACK,
							new DeliveryCancelCallbackInfo(
									Long.valueOf(request.getBizStoreId()),
									Long.valueOf(request.getBizOrderId()),
									String.valueOf(request.getPkgId()),
									request.getIsCancelSuccess(),
									request.getCancelFailReasonDesc()
							)
					),
					Long.valueOf(request.getBizOrderId())
			);

			Cat.logEvent("CALLBACK_CANCEL_" + request.getDeliveryChannelId(), "SUCCESS");
			return new DeliveryNotifyResponse(Status.SUCCESS);
		} catch (Exception e) {
			Cat.logEvent("CALLBACK_CANCEL_" + request.getDeliveryChannelId(), "ERROR");
			log.error("CosUnifiedDeliveryCallbackThriftservice.notifyDeliveryCancelResult error", e);
			return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), "exception detected"));
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public DeliveryNotifyResponse notifyDeliveryChange(DeliveryChangeNotifyRequest request) {
		Cat.logEvent("CALLBACK_CHANGE_" + request.getDeliveryChannelId(), "HIT");
		try {
			String requestErrorMsg = request.validate();
			if (requestErrorMsg != null) {
				return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), requestErrorMsg));
			}

			// 解析渠道
			DeliveryChannel deliveryChannelInfo = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(Integer.parseInt(request.getDeliveryChannelId()));
			if (DeliveryChannel.UNKNOWN_CARRIER_NAME.equals(deliveryChannelInfo.getCarrierName())) {
				return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), "不支持的配送渠道"));
			}

			// 解析状态
			DeliveryStatusEnum deliveryStatus = translateDeliveryStatus(request.getDeliveryStatus());
			if (deliveryStatus == null) {
				return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), "不识别的配送状态"));
			}

			Cat.logEvent("CALLBACK_STATUS_CHANGE_" + request.getDeliveryChannelId(), deliveryStatus.getCode()+"");

			Rider rider = StringUtils.isNotEmpty(request.getRiderName()) && StringUtils.isNotEmpty(request.getRiderPhone()) ?
					new Rider(request.getRiderName(), request.getRiderPhone(), null) : null;

			deliveryUnifiedCallbackMessageProducer.sendMessage(
					new DeliveryUnifiedCallbackMessage(
							DeliveryCallbackTypeEnum.CHANGE_CALLBACK,
							new DeliveryChangeCallbackInfo(
									Long.valueOf(request.getBizOrderId()),
									deliveryChannelInfo.getCarrierCode(),
									String.valueOf(request.getPkgId()),
									request.getServicePackage(),
									DeliveryEventEnum.getEventByStatus(deliveryStatus),
									buildExceptionInfo(request, deliveryStatus),
									TimeUtil.fromSeconds(request.getUpdateTime()),
									rider,
									request.getDistance(),
									request.getDeliveryFee()
							)
					),
					Long.valueOf(request.getBizOrderId())
			);

			return new DeliveryNotifyResponse(Status.SUCCESS);
		} catch (Exception e) {
			log.error("CosUnifiedDeliveryCallbackThriftService.notifyDeliveryChange error", e);
			return new DeliveryNotifyResponse(new Status(ResponseCodeEnum.FAIL.getCode(), "exception detected"));
		}
	}

	private DeliveryStatusEnum translateDeliveryStatus(Integer sourceStatusCode) {
		if (sourceStatusCode == null || BmSelfDeliveryPkgStatus.findByValue(sourceStatusCode) == null) {
			return null;
		}

		switch (BmSelfDeliveryPkgStatus.findByValue(sourceStatusCode)) {
			case SEND_DELIVERY:
				return DeliveryStatusEnum.DELIVERY_LAUNCHED;

			case DISTRIBUTED:
				return DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER;

			case RIDER_GRABBED:
				return DeliveryStatusEnum.RIDER_ASSIGNED;

			case RIDER_ARRIVE_POI:
				return DeliveryStatusEnum.RIDER_ARRIVED_SHOP;

			case RIDER_FETCHED:
				return DeliveryStatusEnum.RIDER_TAKEN_GOODS;

			case RIDER_DELIVERED:
				return DeliveryStatusEnum.DELIVERY_DONE;

			case CANCELED:
				return DeliveryStatusEnum.DELIVERY_CANCELLED;

			default:
				return null;
		}
	}

	private DeliveryExceptionInfo buildExceptionInfo(DeliveryChangeNotifyRequest request, DeliveryStatusEnum deliveryStatus) {
		if (deliveryStatus == DeliveryStatusEnum.DELIVERY_CANCELLED) {
			CancelTypeEnum cancelType = translateCancelType(request.getCancelReasonCode());
			if (cancelType != null) {
				switch (cancelType) {
					case PASSIVE_CANCEL:
						return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM, request.getCancelReasonDesc());

					case UNKNOWN_CANCEL:
						return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM, "其它原因取消");
				}
			}
		}

		return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY);
	}

	private CancelTypeEnum translateCancelType(Integer cancelReasonCode) {
		if (cancelReasonCode == null || SelfPkgCancelReasonEnum.findByValue(cancelReasonCode) == null) {
			return null;
		}

		switch (SelfPkgCancelReasonEnum.findByValue(cancelReasonCode)) {
			case USER_TYPE:
				return CancelTypeEnum.ACTIVE_CANCEL;

			case THIRD_TYPE:
				return CancelTypeEnum.PASSIVE_CANCEL;

			case OTHER_TYPE:
			default:
				return CancelTypeEnum.UNKNOWN_CANCEL;
		}
	}
}
