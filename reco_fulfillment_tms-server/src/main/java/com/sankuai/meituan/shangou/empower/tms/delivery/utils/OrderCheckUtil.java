package com.sankuai.meituan.shangou.empower.tms.delivery.utils;

import com.dianping.cat.Cat;
import com.meituan.mtrace.Tracer;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderDownTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class OrderCheckUtil {

    /**
     * 判断当前订单是否锁单
     */
    public static boolean isLockedOrder(OrderInfo orderInfo, boolean isMedicineUW) {
        if (!com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.isSwitchLockEndLaunchDelivery()) {
            return false;
        }
        Integer orderBizType = orderInfo.getOrderBizType();
        if (!orderBizType.equals(DynamicOrderBizType.MEITUAN_WAIMAI.getValue())) {
            return false;
        }
        if (com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId()) || isMedicineUW) {
            return false;
        }
        if (orderInfo.getOrderLockLabel() == null) {
            return false;
        }
        if (orderInfo.getOrderLockLabel()) {
            return true;
        }
        return false;
    }

    public static boolean isDownOrder(OrderInfo orderInfo, boolean isMedicineUW, DeliveryPoi deliveryPoi) {
        try {
            if(Tracer.isTest()){
                return false;
            }

            if (com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.switchOrderDown()) {
                return false;
            }
            //平台配送不降级
            if (deliveryPoi.getDeliveryPlatform().equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM)) {
                return false;
            }
            if (com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId()) || isMedicineUW) {
                return false;
            }
            if (orderInfo.getDownFlag() == null || orderInfo.getDownFlag() == OrderDownTypeEnum.NO_DOWN.getValue()) {
                return false;
            }
            String degradeModules = orderInfo.getDegradeModules();
            if (StringUtils.isEmpty(degradeModules)) {
                return false;
            }
            List<Integer> needDownType = MccConfigUtils.getNeedDownType();
            if (CollectionUtils.isEmpty(needDownType)) {
                return false;
            }
            List<Integer> degradeModuleList = Arrays.stream(degradeModules.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            List<Integer> downType = needDownType.stream().filter(degradeModuleList::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(downType)) {
                return true;
            }
        } catch (Exception e) {
            Cat.logEvent("down", "down.error");
        }
        return false;
    }


    /**
     * 是否是锁单2.0订单
     * @param orderInfo 订单信息
     * @return true 是  false否
     */
    public static boolean isLockOrderV2(OrderInfo orderInfo, DeliveryPoi deliveryPoi) {
        return MccConfigUtils.getPaotuiLockSwitch()
                && MccConfigUtils.isPaotuiLockOrderV2GrayTenant(orderInfo.getOrderKey().getTenantId())
                && MccConfigUtils.isPaotuiLockAggDeliveryPlatform(deliveryPoi.getDeliveryPlatform().getCode());
    }


}
