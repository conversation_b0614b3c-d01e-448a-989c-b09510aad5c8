package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.repository;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannelRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.DeliveryChannelDOMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.mapper.extension.DeliveryChannelDOExMapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryChannelDOExample;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository(value = "mySQLDeliveryChannelRepository")
public class MySQLDeliveryChannelRepository implements DeliveryChannelRepository {

    @Resource
    private DeliveryChannelDOMapper deliveryChannelDOMapper;

    @Resource
    private DeliveryChannelDOExMapper deliveryChannelDOExMapper;

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Optional<DeliveryChannel> getDeliveryChannelByLogisticMark(Integer deliveryPlatFormCode,
                                                                      Integer orderChannelCode, String logisticMark) {
        DeliveryChannelDOExample example = new DeliveryChannelDOExample();
        example.createCriteria().andDeliveryPlatformCodeEqualTo(deliveryPlatFormCode)
                .andOrderChannelCodeEqualTo(Objects.isNull(orderChannelCode) ? NumberUtils.INTEGER_ZERO : orderChannelCode)
                .andLogisticMarkEqualTo(logisticMark);
        List<DeliveryChannel> deliveryChannelList = translate(deliveryChannelDOMapper.selectByExample(example));

        if (CollectionUtils.isEmpty(deliveryChannelList)) {
            log.warn("getDeliveryChannelByLogisticMark, deliveryChannelList is empty");
            return Optional.empty();
        }
        return Optional.ofNullable(deliveryChannelList.iterator().next());
    }

    @Override
    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Optional<DeliveryChannel> getDeliveryChannelByCarrierCode(Integer carrierCode) {
        DeliveryChannelDOExample example = new DeliveryChannelDOExample();
        example.createCriteria().andCarrierCodeEqualTo(carrierCode);
        List<DeliveryChannel> deliveryChannelList = translate(deliveryChannelDOMapper.selectByExample(example));

        if (CollectionUtils.isEmpty(deliveryChannelList)) {
            log.warn("getDeliveryChannelByCarrierCode, deliveryChannelList is empty");
            return Optional.empty();
        }
        return Optional.ofNullable(deliveryChannelList.iterator().next());
    }

    @Override
    @MethodLog(logResponse = true)
    public List<DeliveryChannel> getBatchDeliveryChannelList(Long startId, Long endId) {
        DeliveryChannelDOExample example = new DeliveryChannelDOExample();
        example.createCriteria().andIdGreaterThanOrEqualTo(startId).andIdLessThanOrEqualTo(endId);
        return translate(deliveryChannelDOMapper.selectByExample(example));
    }

    @Override
    @MethodLog(logRequest = false, logResponse = true)
    public Map<Integer, Optional<DeliveryChannel>> getDeliveryChannelMapByCarrierCodeSet(Set<Integer> carrierCodeSet) {
        DeliveryChannelDOExample example = new DeliveryChannelDOExample();
        example.createCriteria().andCarrierCodeIn(Lists.newArrayList(carrierCodeSet));
        List<DeliveryChannelDO> deliveryChannelDOList = deliveryChannelDOMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(deliveryChannelDOList)) {
            return carrierCodeSet.stream().collect(Collectors.toMap(carrierCode -> carrierCode, carrierCode -> Optional.empty()));
        }

        Map<Integer, DeliveryChannelDO> deliveryChannelDoMap = deliveryChannelDOList.stream().collect(Collectors.toMap(DeliveryChannelDO::getCarrierCode, Function.identity()));
        Map<Integer, Optional<DeliveryChannel>> resultMap = Maps.newHashMap();
        carrierCodeSet.forEach(carrierCode -> {
            if (deliveryChannelDoMap.containsKey(carrierCode)) {
                resultMap.put(carrierCode, Optional.of(translate(deliveryChannelDoMap.get(carrierCode))));
            } else {
                resultMap.put(carrierCode, Optional.empty());
            }
        });
        return resultMap;
    }

    @Override
    @MethodLog(logResponse = true)
    public Long getMaxId() {
        return deliveryChannelDOExMapper.selectMaxId();
    }

    private DeliveryChannel translate(DeliveryChannelDO deliveryChannelDO) {
        DeliveryChannel deliveryChannel = new DeliveryChannel();
        deliveryChannel.setId(deliveryChannelDO.getId());
        deliveryChannel.setLogisticMark(deliveryChannelDO.getLogisticMark());
        deliveryChannel.setDeliveryPlatFormCode(deliveryChannelDO.getDeliveryPlatformCode());
        deliveryChannel.setCarrierCode(deliveryChannelDO.getCarrierCode());
        deliveryChannel.setCarrierName(deliveryChannelDO.getCarrierName());
        deliveryChannel.setOrderChannelCode(deliveryChannelDO.getOrderChannelCode());
        return deliveryChannel;
    }

    private List<DeliveryChannel> translate(List<DeliveryChannelDO> deliveryChannelDOList) {
        if (CollectionUtils.isEmpty(deliveryChannelDOList)) {
            return Collections.emptyList();
        }

        return deliveryChannelDOList.stream().map(this::translate).collect(Collectors.toList());
    }
}
