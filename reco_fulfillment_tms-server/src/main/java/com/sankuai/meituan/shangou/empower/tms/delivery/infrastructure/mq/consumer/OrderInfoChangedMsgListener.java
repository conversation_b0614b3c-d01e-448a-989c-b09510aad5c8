package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerConfig;
import com.meituan.reco.pickselect.common.mq.consumer.ConsumerRetryException;
import com.meituan.reco.pickselect.common.mq.consumer.RetryConfig;
import com.meituan.reco.pickselect.common.mq.producer.ProducerConfig;
import com.meituan.shangou.saas.order.platform.enums.ChangedFieldEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonLogicException;
import com.sankuai.meituan.shangou.empower.tms.delivery.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.ClearDeliveryExceptionMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.OrderInfoChangeMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryExceptionCallbackInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.callback.DeliveryUnifiedCallbackMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaTopicEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.MedicineTenantWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 订单信息修改消息监听器
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderInfoChangedMsgListener extends AbstractMqListener {

	@Resource
	private MedicineTenantWrapper medicineTenantWrapper;

	@Resource
	private OrderSystemClient orderSystemClient;

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;

	@Resource
	private DeliveryTimeOutCheckService deliveryTimeOutCheckService;

	@Resource
	private DeliveryPlatformClient deliveryPlatformClient;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private MafkaMessageProducer<DeliveryUnifiedCallbackMessage> deliveryUnifiedCallbackMessageProducer;

	@Resource
	private MafkaDelayMessageProducer<ClearDeliveryExceptionMsg> clearDeliveryExceptionProducer;

	@Override
	protected ConsumerConfig config() {
		return MafkaConsumerEnum.ORDER_INFO_CHANGE_CONSUMER;
	}

	@Override
	protected RetryConfig retryConfig() {
		return new RetryConfig() {
			@Override
			public ProducerConfig producerConfig() {
				return MafkaTopicEnum.ORDER_INFO_CHANGE_TOPIC;
			}

			@Override
			public int maxRetry() {
				return MccConfigUtils.getOrderInfoChangeMqMaxRetryTimes();
			}

			@Override
			public int delayInSeconds() {
				return MccConfigUtils.getOrderInfoChangeMqDelaySeconds();
			}
		};
	}

	@Override
	protected void consume(MafkaMessage mafkaMessage) throws ConsumerRetryException {
		try {
			OrderInfoChangeMsg.Body msgBody = translateMessage(mafkaMessage);
			if (Objects.isNull(msgBody)) {
				log.error("OrderInfoChangedMsgListener, msgBody is null");
				throw new CommonLogicException("OrderInfoChangedMsgListener, msg is null");
			}
			if (!isNeedConsume(msgBody)) {
				return;
			}

			consume(msgBody);
		} catch (CommonLogicException e) {
			log.error("OrderInfoChangedMsgListener CommonLogicException, message={}", mafkaMessage, e);
		} catch (Exception e) {
			log.error("OrderInfoChangedMsgListener failed, message={}", mafkaMessage, e);
			throw new ConsumerRetryException(e);
		}
	}

	private OrderInfoChangeMsg.Body translateMessage(MafkaMessage mafkaMessage) {
		try {
			return Optional.ofNullable(mafkaMessage)
					.map(MafkaMessage::getBody)
					.map(Object::toString)
					.map(it -> JsonUtil.fromJson(it, OrderInfoChangeMsg.class))
					.map(OrderInfoChangeMsg::parseBody).orElse(null);
		} catch (Exception e) {
			log.error("OrderInfoChangedMsgListener translateMessage error", e);
			Cat.logEvent("ORDER_INFO_CHANGE_CONSUMER_FAILED", "MESSAGE_WRONG");
			return null;
		}
	}

	private boolean isNeedConsume(OrderInfoChangeMsg.Body msgBody) {
		Long tenantId = msgBody.getTenantId();
		Long storeId = msgBody.getPoiId();
		if (Objects.isNull(tenantId) || Objects.isNull(storeId)) {
			return false;
		}

		// 隔离歪马和医药
		if (MccConfigUtils.checkIsDHTenant(tenantId) || medicineTenantWrapper.isUwmsMedicineTenant(tenantId)) {
			return false;
		}

		// 门店白名单校验
		if (!MccConfigUtils.isOrderInfoChangeStoreIdWhiteList(storeId)) {
			return false;
		}

		// changedFields只有在订单信息修改的时候才会有数据，第一次落单时无数据
		if (CollectionUtils.isEmpty(msgBody.getChangedFields())) {
			return false;
		}

		return true;
	}

	private void consume(OrderInfoChangeMsg.Body msgBody) {
		log.info("OrderInfoChangedMsgListener start consume, msgBody is {}", msgBody);
		Long orderId = msgBody.getOrderId();
		Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderId, false);
		if (orderInfoQueryResult.isFail() || Objects.isNull(orderInfoQueryResult.getInfo())) {
			log.warn("query orderInfo fail, orderId is {}, orderInfoQueryResult is {}", orderId, orderInfoQueryResult);
			throw new CommonRuntimeException("OrderInfoChangedMsgListener, orderInfoQueryResult is fail");
		}
		OrderInfo orderInfo = orderInfoQueryResult.getInfo();

		Optional<DeliveryOrder> deliveryOrderOptional = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(orderInfo.getWarehouseId())){
			deliveryOrderOptional = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderId,orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		}else {
			deliveryOrderOptional = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderId);
		}

		if (!deliveryOrderOptional.isPresent()) {
			handleDeliveryOrderNotExists(msgBody, orderInfo);
			return;
		}
		DeliveryOrder deliveryOrder = deliveryOrderOptional.get();

		// 如果修改了预计送达时间，重新计算整单配送超时
		if (msgBody.hasFieldChanged(ChangedFieldEnum.DELIVERY_TIME) || msgBody.hasFieldChanged(ChangedFieldEnum.DELIVERY_END_TIME)) {
			// 修改后的订单预计送达时间，从订单查询最新的值
			LocalDateTime estimatedDeliveryTime = Optional.ofNullable(orderInfo.getEstimatedDeliveryEndTime()).orElse(orderInfo.getEstimatedDeliveryTime());
			triggerDeliveryTimeOutCheck(estimatedDeliveryTime, deliveryOrder);
		}

		// 如果生成了运单，用最新的订单字段更新运单
		updateDeliveryOrder(deliveryOrder, orderInfo);

		// 通知聚合配送平台订单信息变更
		Integer deliveryChannelId = deliveryOrder.getDeliveryChannel();
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannelId);
		if (isAggDeliveryPlatform(deliveryChannel)) {
			Optional<Failure> failureOptional = notifyAggDeliveryPlatform(deliveryOrder, orderInfo, msgBody.getChangedFields());
			if (failureOptional.isPresent()) {
				throw new CommonRuntimeException(failureOptional.get().getFailureCode(), failureOptional.get().getFailureMessage());
			}
		}
	}

	private void handleDeliveryOrderNotExists(OrderInfoChangeMsg.Body msgBody, OrderInfo orderInfo) {
		Long orderId = orderInfo.getOrderKey().getOrderId();
		log.info("OrderInfoChangedMsgListener, deliveryOrder is not exists, orderId is {}", orderId);

		/*
		 * 对应牵牛花没有生成运单，此时修改了地址会同时修改预计送达时间，需要重新触发配送异常清理
		 * eg：没有到达发单节点牵牛花没生成运单
		 * eg：预订单发商家自己送，预定送达时间前牵牛花没生成运单
		 *
		*/
		if (msgBody.hasFieldChanged(ChangedFieldEnum.DELIVERY_TIME) || msgBody.hasFieldChanged(ChangedFieldEnum.DELIVERY_END_TIME)) {
			log.info("OrderInfoChangedMsgListener deliveryOrder is not exists eta changed, orderId is {}", orderId);
			// 修改后的订单预计送达时间，从订单查询最新的值
			LocalDateTime estimatedDeliveryTime = Optional.ofNullable(orderInfo.getEstimatedDeliveryEndTime()).orElse(orderInfo.getEstimatedDeliveryTime());
			triggerExceptionClearMonitor(estimatedDeliveryTime, orderInfo.getOrderKey());
		}

		/*
		 * 针对发青云配送的订单产生了退款地址变更费的场景
		 * 退款地址变更费产生的场景是修改地址后整单退，订单取消会触发牵牛花取消青云配送，此时没有激活运单，仍需要同步给青云
		*/
		if (msgBody.hasFieldChanged(ChangedFieldEnum.ADDRESS_CHANGE_FEE_NOTES)) {
			log.info("OrderInfoChangedMsgListener deliveryOrder is not exists addressChangeFeeNotes changed, orderId is {}", orderId);
			// 查询最近一次的终态运单
			Optional<DeliveryOrder> deliveryOrderOptional = deliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderId,orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
			if (deliveryOrderOptional.isPresent()) {
				DeliveryOrder deliveryOrder = deliveryOrderOptional.get();
				DeliveryStatusEnum deliveryStatus = deliveryOrder.getStatus();
				Integer deliveryChannelId = deliveryOrder.getDeliveryChannel();
				DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryChannelId);
				// 对于发青云且已取消配送的订单，同步青云侧退单地址变更费
				if (Objects.equals(DeliveryStatusEnum.DELIVERY_CANCELLED, deliveryStatus) && deliveryOrder.isDapDeliveryOrder(deliveryChannel)) {
					Optional<Failure> failureOptional = notifyAggDeliveryPlatform(deliveryOrder, orderInfo, msgBody.getChangedFields());
					if (failureOptional.isPresent()) {
						throw new CommonRuntimeException(failureOptional.get().getFailureCode(), failureOptional.get().getFailureMessage());
					}
				}
			}
		}
	}

	private void updateDeliveryOrder(DeliveryOrder deliveryOrder, OrderInfo orderInfo) {
		deliveryOrder.setReceiver(orderInfo.getReceiver());
		deliveryOrder.setEstimatedDeliveryTime(orderInfo.getEstimatedDeliveryTime());
		deliveryOrder.setEstimatedDeliveryEndTime(orderInfo.getEstimatedDeliveryEndTime());
		deliveryOrderRepository.save(deliveryOrder);
	}

	private boolean isAggDeliveryPlatform(DeliveryChannel deliveryChannel) {
		if (Objects.isNull(deliveryChannel)) {
			return false;
		}

		Integer deliveryPlatFormCode = deliveryChannel.getDeliveryPlatFormCode();
		if (Objects.isNull(deliveryPlatFormCode)) {
			return false;
		}

		return DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode() == deliveryPlatFormCode
				|| DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode() == deliveryPlatFormCode;
	}

	private Optional<Failure> notifyAggDeliveryPlatform(DeliveryOrder deliveryOrder, OrderInfo orderInfo, List<Integer> changedFields) {
		return deliveryPlatformClient.syncOrderInfoChange(deliveryOrder, orderInfo, changedFields);
	}

	private void triggerDeliveryTimeOutCheck(LocalDateTime estimatedDeliveryTime, DeliveryOrder deliveryOrder) {
		// 如果最新的预计送达时间早于当前时间，且当前订单不是配送超时状态，直接让订单流转到配送超时
		if (estimatedDeliveryTime.isBefore(LocalDateTime.now()) && !deliveryOrder.isDeliveryTimeout()) {
			turn2EstimatedDeliveryTimeOut(deliveryOrder);
			return;
		}

		// 如果最新的预计送达时间晚于当前时间，且订单状态是配送超时，清除配送超时异常
		if (deliveryOrder.isDeliveryTimeout()) {
			deliveryOrder.clearException();
		}

		// 用最新的订单预计送达时间触发配送超时检查 && 配送异常清理
		deliveryOrder.setEstimatedDeliveryEndTime(estimatedDeliveryTime);
		deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);
		OrderKey orderKey = new OrderKey(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getOrderId());
		triggerExceptionClearMonitor(estimatedDeliveryTime, orderKey);
	}

	private void turn2EstimatedDeliveryTimeOut(DeliveryOrder deliveryOrder) {
		Long orderId = deliveryOrder.getOrderId();
		deliveryUnifiedCallbackMessageProducer.sendMessage(
				new DeliveryUnifiedCallbackMessage(
						DeliveryCallbackTypeEnum.EXCEPTION_CALLBACK,
						new DeliveryExceptionCallbackInfo(
								orderId,
								new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER,
										DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getDesc(),
										DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode()),
								LocalDateTime.now())
				),
				orderId);
	}

	private void triggerExceptionClearMonitor(LocalDateTime estimatedDeliveryTime, OrderKey orderKey) {
		long delayMillis = com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils.getDeliveryExceptionClearDelayMillis(estimatedDeliveryTime);
		clearDeliveryExceptionProducer.sendDelayMessageInMillis(new ClearDeliveryExceptionMsg(orderKey, TimeUtil.toMilliSeconds(estimatedDeliveryTime)), Math.max(delayMillis, 5000));
	}

}
