package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryAsyncOutTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import org.springframework.stereotype.Component;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DHDeliveryStatusCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderOperateApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderRepository;

import lombok.extern.slf4j.Slf4j;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 歪马配送状态检查消费者.
 *
 * <AUTHOR>
 * @since 2021/11/23 21:56
 */
@Slf4j
@Component
public class DhDeliveryStatusCheckMessageListener extends AbstractDeadLetterConsumer {

    public static final Long CHECK_DELAY_MILLIS = 10000L;

    private static final String DH_RIDER_STATUS_CHECK = "DH_RIDER_DELIVERY_STATUS_CHECK";

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;
    @Resource
    private RiderDeliveryOrderRepository riderDeliveryOrderRepository;
    @Resource
    private RiderOperateApplicationService riderOperateApplicationService;

    @Resource
    private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;

    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DH_DELIVERY_STATUS_CHECK_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
        log.info("开始检查歪马自营骑手配送状态. mafkaMessage:{}", mafkaMessage);
        DHDeliveryStatusCheckMessage message = translateMessage(mafkaMessage);
        if (message == null) {
            return CONSUME_SUCCESS;
        }
        DeliveryStatusEnum expectedDeliveryStatus = DeliveryStatusEnum.valueOf(message.getExpectedStatus());
        if (expectedDeliveryStatus == null) {
            log.warn("期望运单状态无效, expectedStatus:{}", message.getExpectedStatus());
            return CONSUME_SUCCESS;
        }

        try {
            // 1. 检查门店是歪马门店且为自营配送
            if (!MccConfigUtils.getDHTenantIdList().contains(message.getTenantId().toString())) {
                log.info("非歪马租户不检查, tenantId:{}", message.getTenantId());
                return CONSUME_SUCCESS;
            }
            Optional<DeliveryPoi> deliveryPoi = deliveryPoiRepository.queryDeliveryPoi(message.getTenantId(), message.getStoreId());
            if (!deliveryPoi.isPresent() || deliveryPoi.get().getDeliveryPlatform() != DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY) {
                log.info("歪马租户但非自营配送，不检查, storeId:{}", message.getStoreId());
                return CONSUME_SUCCESS;
            }
            // 2. 查询运单
            Optional<RiderDeliveryOrder> deliveryOrderOptional = riderDeliveryOrderRepository.getActiveDeliveryOrderForceMaster(
                    message.getTenantId(), message.getStoreId(), message.getOrderId());
            if (!deliveryOrderOptional.isPresent()) {
                log.info("订单的运单不存在，不检查, orderId:{}", message.getOrderId());
                return CONSUME_SUCCESS;
            }

            //如果当前运单不是自营配送运单，则return
            if (!Objects.equals(deliveryOrderOptional.get().getDeliveryChannelEnum(), DeliveryChannelEnum.MERCHANT_DELIVERY)) {
                log.info("当前运单非自营配送运单,不检查, orderId:{}, deliveryOrderId: {}", message.getOrderId(), deliveryOrderOptional.get().getId());
                return CONSUME_SUCCESS;
            }

            RiderDeliveryOrder riderDeliveryOrder = deliveryOrderOptional.get();
            DeliveryStatusEnum currentStatus = riderDeliveryOrder.getStatus();
            // 3. 根据期望状态检查骑手配送状态
            Cat.logEvent(DH_RIDER_STATUS_CHECK, "TOTAL");
            // 目前只检查骑手状态是否为已取货
            if (expectedDeliveryStatus == DeliveryStatusEnum.RIDER_TAKEN_GOODS) {
                //拣配分离模式 拣货完成不联动运单状态
                if (riderDeliveryOrder.getRiderDeliveryExtInfo() != null && Objects.equals(riderDeliveryOrder.getRiderDeliveryExtInfo().getPickDeliverySplitTag(), true)) {
                    //如果当前仅配送运单已经被领取，需要在拣货完成的时候发push,触发骑手的待取货-配送任务中的拣货信息刷新
                    if (Objects.nonNull(riderDeliveryOrder.getRiderInfo()) && Objects.nonNull(riderDeliveryOrder.getRiderInfo().getRiderAccountId())) {
                        DeliveryChangeSyncOutMessage<DeliveryChangeSyncOutMessage.PickDeliverySplitPickDoneBody> syncOutMessage =
                                new DeliveryChangeSyncOutMessage<>(DeliveryAsyncOutTypeEnum.PICK_DONE_IN_PICK_DELIVERY_SPLIT.getValue(),
                                        new DeliveryChangeSyncOutMessage.Head(riderDeliveryOrder.getTenantId(),
                                                riderDeliveryOrder.getStoreId(), riderDeliveryOrder.getId(), riderDeliveryOrder.getCustomerOrderKey().getOrderBizType(), riderDeliveryOrder.getCustomerOrderKey().getOrderId(),
                                                riderDeliveryOrder.getCustomerOrderKey().getChannelOrderId(), riderDeliveryOrder.getStatus().getCode()),
                                        new DeliveryChangeSyncOutMessage.PickDeliverySplitPickDoneBody(riderDeliveryOrder.getRiderInfo().getRiderAccountId()));
                        riderDeliveryOrderSyncOutClient.asyncOut(syncOutMessage);
                    }

                    return CONSUME_SUCCESS;
                }
                handleExpectRiderTakeGoods(currentStatus, expectedDeliveryStatus, riderDeliveryOrder);
            }
            return CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("消费检查歪马自营骑手配送状态消息失败，将会进行重试消费", e);
            return CONSUME_FAILURE;
        }
    }

    private void handleExpectRiderTakeGoods(DeliveryStatusEnum currentStatus,DeliveryStatusEnum expectedDeliveryStatus,
                                            RiderDeliveryOrder riderDeliveryOrder) {
        boolean needHandle = handleExpectRiderTakeGoodsCheck(currentStatus, expectedDeliveryStatus, riderDeliveryOrder);
        if (!needHandle) {
            return;
        }
        // 将运单补至已取货状态，若失败记录信息即可，不进行二次尝试。
        try {
            Optional<Failure> failure = riderOperateApplicationService.takeAway(riderDeliveryOrder.getId(), riderDeliveryOrder.getRiderInfo(), null,riderDeliveryOrder.getTenantId(),riderDeliveryOrder.getStoreId(), null);
            if (failure.isPresent()) {
                log.error("将运单推至已取货状态失败， failure:{}", failure);
            }
        } catch (Exception e) {
            log.error("将运单推至已取货状态失败", e);
        }
        return;
    }

    /**
     * 检查是否进行兜底的运单状态流转.
     *
     * @param currentStatus          当前运单状态
     * @param expectedDeliveryStatus 期望运单状态
     * @param riderDeliveryOrder     骑手运单
     * @return 是否进行兜底的运单状态流转
     */
    private boolean handleExpectRiderTakeGoodsCheck(DeliveryStatusEnum currentStatus, DeliveryStatusEnum expectedDeliveryStatus,
                                                    RiderDeliveryOrder riderDeliveryOrder) {
        // 状态检查若直接通过，进行埋点并且不做任何处理
        if (currentStatus.getCode() >= expectedDeliveryStatus.getCode()) {
            Cat.logEvent(DH_RIDER_STATUS_CHECK, "PASS");
            return false;
        }
        // 状态检查未通过，将骑手状态流转至已取货
        Cat.logEvent(DH_RIDER_STATUS_CHECK, "NOT_PASS");
        StaffRider rider = riderDeliveryOrder.getRiderInfo();
        // 若骑手信息为空，或骑手当前状态不为已接单状态，则不处理
        if (rider == null || currentStatus != DeliveryStatusEnum.RIDER_ASSIGNED) {
            log.info("自营骑手运单状态检查未通过，但当前状态不为骑手已接单或骑手信息为空，因此放弃将运单推至已取货状态，deliveryOrder:{}",
                    riderDeliveryOrder);
            Cat.logEvent(DH_RIDER_STATUS_CHECK, "NOT_PASS_UNHANDLED");
            return false;
        }
        // 仅当当前状态为骑手已接单并且骑手信息不为空时，才去将运单补至已取货状态
        log.info("自营骑手运单状态检查未通过，将运单推至已取货状态，deliveryOrder:{}", riderDeliveryOrder);
        Cat.logEvent(DH_RIDER_STATUS_CHECK, "NOT_PASS_HANDLED");
        return true;
    }


    private DHDeliveryStatusCheckMessage translateMessage(MafkaMessage mafkaMessage) {
        try {
            DHDeliveryStatusCheckMessage message = translateMessage(mafkaMessage, DHDeliveryStatusCheckMessage.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            Preconditions.checkNotNull(message.getTenantId(), "tenantId is empty");
            Preconditions.checkNotNull(message.getStoreId(), "storeId is empty");
            Preconditions.checkNotNull(message.getOrderId(), "orderId is empty");
            Preconditions.checkNotNull(message.getExpectedStatus(), "expectedStatus is empty");
            return message;
        } catch (Exception e) {
            log.error("解析歪马自营骑手配送状态检查消息失败:{}", mafkaMessage, e);
            Cat.logEvent("DH_DELIVERY_STATUS_CHECK_CONSUME_FAILED", "MESSAGE_WRONG");
            return null;
        }
    }
}
