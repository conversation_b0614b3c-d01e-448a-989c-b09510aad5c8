package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryProcessApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.DeliveryCancelCommandMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.RiderAssignTimeOutCheckMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SequentialPollingStrategyConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_FAILURE;
import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;

/**
 * 骑手分配超时消息消费者
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/14
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class RiderAssignTimeOutCheckMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private PushClient pushClient;
	@Resource
	private DeliveryProcessApplicationService deliveryProcessApplicationService;
	@Resource
	private MafkaMessageProducer<DeliveryCancelCommandMessage> deliveryCancelMessageProducer;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.RIDER_ASSIGN_TIME_OUT;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费骑手分配超时检查消息：{}", mafkaMessage);
		RiderAssignTimeOutCheckMessage message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		try {
			DeliveryOrder deliveryOrder = getDeliveryOrder(message);
			if (deliveryOrder == null) {
				log.warn("DeliveryOrder[{}] query failed, will ignore this message", message);
				return CONSUME_SUCCESS;
			}

			if (deliveryOrder.getStatus() == DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER) {
				handleRiderAssignTimeout(deliveryOrder, mafkaMessage);
			}
		} catch (Exception e) {
			log.error("Consume RiderAssignTimeOutCheckMessage[{}] failed, need retry", message, e);
			return CONSUME_FAILURE;
		}

		return CONSUME_SUCCESS;
	}

	private void handleRiderAssignTimeout(DeliveryOrder deliveryOrder, MafkaMessage mafkaMessage) {
		deliveryOrder.onException(
				new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "无骑手接单"),
				LocalDateTime.now()
		);

		if (Objects.isNull(deliveryOrder.getDeliveryChannel())) {
			log.warn("handleRiderAssignTimeout, deliveryChannel is null");
			return;
		}

		DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelEnum)) {
			log.error("handleRiderAssignTimeout, deliveryChannelEnum is null");
			return;
		}
		switch (deliveryChannelEnum) {
			case ORDER_PLATFORM_DELIVERY:
				handleOrderPlatformDeliveryException(deliveryOrder);
				break;

			case HAI_KUI_DELIVERY:
			case FENG_NIAO_DELIVERY:
				handleThirdPartyDeliveryException(deliveryOrder, mafkaMessage);
				break;

			default:
				log.info("该配送渠道无需处理骑手分配超时, deliveryId={}", deliveryOrder.getId());
		}
		if (!deliveryOrderRepository.saveDeliveryOrder(deliveryOrder)) {
			throw new MessageNeedReconsumeException("Save DeliveryOrder[" + deliveryOrder.getId() + "] failed, need retry");
		}
	}

	private void handleOrderPlatformDeliveryException(DeliveryOrder deliveryOrder) {
		//标记订单配送超时
		Optional<Failure> failure = orderSystemClient.setOrderDeliveryTimeout(deliveryOrder);
		if (failure.isPresent()) {
			if (failure.get().isNeedRetry()) {
				throw new MessageNeedReconsumeException(String.format("修改订单配送异常失败，需要进行重试, deliveryId=%d", deliveryOrder.getId()));
			} else {
				log.error("修改订单配送异常失败, 将放弃本次异常标记与异常通知, deliveryId={}", deliveryOrder.getId());
				return;
			}
		}
		//触发异常push
		pushClient.pushDeliveryException(deliveryOrder);
	}

	private void handleThirdPartyDeliveryException(DeliveryOrder deliveryOrder, MafkaMessage mafkaMessage) {
		//查询配送门店信息
		Optional<DeliveryPoi> opDeliveryPoi = Optional.empty();
		if(MccConfigUtils.getDHTenantIdList().contains(deliveryOrder.getTenantId()+"")){
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(deliveryOrder.getTenantId(), deliveryOrder.getStoreId());
		}else {
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
					deliveryOrder.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),
					orderBizType.getChannelId());
		}
		if (!opDeliveryPoi.isPresent() || DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM != opDeliveryPoi.get().getDeliveryPlatform()) {
			log.warn("DeliveryStore[{}] query failed, will give up handling this timeout exception", deliveryOrder.getStoreId());
			return;
		}
		SelfBuiltDeliveryPoi deliveryPoi = (SelfBuiltDeliveryPoi) opDeliveryPoi.get();
		if (!deliveryPoi.canLaunchDelivery()) {
			log.warn("门店[{}]在自建平台没有可用的配送渠道，将放弃处理超时", deliveryPoi.getStoreId());
			return;
		}

		//检查配送策略是否是轮询,如果还有下一个可配送渠道
		if (deliveryPoi.getDeliveryStrategy() == DeliveryStrategyEnum.SEQUENTIAL_POLLING) {
			SequentialPollingStrategyConfig strategyConfig = (SequentialPollingStrategyConfig) deliveryPoi.getDeliveryStrategyConfig();
			if (strategyConfig.hasNextAvailableDeliveryChannel(ThirdDeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel()))) {
				//1.取消超时运单的激活状态
				deliveryOrder.deactivate();
				deliveryOrderRepository.saveDeliveryOrderActiveStatus(deliveryOrder);

				//2.发起新渠道配送
				Optional<Failure> launchNewDeliveryFailure =
						deliveryProcessApplicationService.launchDelivery(deliveryOrder.getOrderKey(), deliveryPoi, !isLastRetry(mafkaMessage), true, false);
				if (launchNewDeliveryFailure.isPresent()) {
					if (launchNewDeliveryFailure.get().isNeedRetry()) {
						throw new MessageNeedReconsumeException(
								String.format("原渠道配送接单超时，发起新渠道配送失败，需要进行重试, failure=%s", launchNewDeliveryFailure.get())
						);
					} else {
						log.error("原渠道配送接单超时,发起新渠道配送失败,将放弃本次异常处理，orderKey={}", deliveryOrder.getOrderKey());
						deliveryOrder.activate();
						deliveryOrderRepository.saveDeliveryOrderActiveStatus(deliveryOrder);
					}

				} else {
					//3. 取消当前超时运单
					deliveryCancelMessageProducer.sendMessage(
							new DeliveryCancelCommandMessage(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId())
					);
				}
			}
		}
	}

	private DeliveryOrder getDeliveryOrder(RiderAssignTimeOutCheckMessage message) {
		if (message.getDeliveryId() != null) {

			if(MccConfigUtils.getDeliveryQueryTenantSwitch(message.getTenantId())){
			 return deliveryOrderRepository.getDeliveryOrderWithTenant(message.getDeliveryId(),message.getTenantId(),message.getShopId());
			}
			return deliveryOrderRepository.getDeliveryOrder(message.getDeliveryId());

		} else {
			List<DeliveryOrder> deliveryOrders =
					deliveryOrderRepository.getDeliveryOrders(new OrderKey(message.getTenantId(), message.getShopId(), message.getOrderId()));
			if (CollectionUtils.isEmpty(deliveryOrders) || deliveryOrders.size() > 1) {
				return null;
			} else {
				return deliveryOrders.get(0);
			}
		}
	}

	private RiderAssignTimeOutCheckMessage translateMessage(MafkaMessage mafkaMessage) {
		try {
			RiderAssignTimeOutCheckMessage message = translateMessageWithBodyHolder(mafkaMessage, RiderAssignTimeOutCheckMessage.class);
			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
			Preconditions.checkNotNull(message.getShopId(), "shopId is null");
			Preconditions.checkNotNull(message.getOrderId(), "orderId is null");

			return message;
		} catch (Exception e) {
			log.error("解析骑手分配超时检查消息失败:{}", mafkaMessage, e);
			return null;
		}
	}
}
