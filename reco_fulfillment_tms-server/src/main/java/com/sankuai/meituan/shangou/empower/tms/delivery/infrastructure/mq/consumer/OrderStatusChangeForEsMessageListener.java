package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;


import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.DeliveryOrderEsPatternDao;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.utils.MccConfigUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum.CANCELED;


/**
 * <AUTHOR>
 * 用于异构运单db数据到es
 */
@Slf4j
@Component
@SuppressWarnings("rawtypes")
public class OrderStatusChangeForEsMessageListener extends AbstractDeadLetterConsumer {

	@Resource
	private TenantRemoteService tenantRemoteService;

	@Resource
	private DeliveryOrderEsPatternDao deliveryOrderEsPatternDao;

	@Override
	protected MQConsumerEnum consumerConfig() {
		return MQConsumerEnum.ORDER_STATUS_CHANGE_FOR_ES;
	}

	@Override
	protected ConsumeStatus consume(MafkaMessage mafkaMessage) {
		log.info("开始消费赋能订单状态变更消息用于异构ES：{}", mafkaMessage);
		MessageDTO message = translateMessage(mafkaMessage);
		if (message == null) {
			return CONSUME_SUCCESS;
		}

		if (MccConfigUtils.orderStatusConsumeFilterDrunkHorseSwitch()
				&& com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.checkIsDHTenant(message.getTenantId())) {
			return CONSUME_SUCCESS;
		}

		Long shopId = message.getShopId();
		if(!com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils.getDHTenantIdList().contains(message.getTenantId().toString())) {
			if(message.getWarehouseId() != null){
				shopId = message.getWarehouseId();
			}
		}

		if (message.isOnlineOrder()) {
			try {
				OrderKey orderKey = new OrderKey(message.getTenantId(), shopId, message.getOrderId());
				OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(message.getStatus());
				if (orderStatusEnum.equals(CANCELED)) {
					log.info("order status is canceled, order id is {}", orderKey.getOrderId());
					processCancel(orderKey.getOrderId(), shopId);
				}
			} catch (Exception e) {
				log.warn("处理订单状态变更失败， message={}", message, e);
				return ConsumeStatus.CONSUME_FAILURE;
			}
		}
		return CONSUME_SUCCESS;
	}

	private void processCancel(Long orderId, Long storeId) {
		DeliveryOrderEsPo deliveryOrderEsPo = deliveryOrderEsPatternDao.getDeliveryOrderEsPoByOrderId(orderId, storeId);
		if (Objects.isNull(deliveryOrderEsPo)) {
			log.warn("processCancel, deliveryOrderEsPo is null, orderId: {}, storeID: {}", orderId, storeId);
			return;
		}

		deliveryOrderEsPo.setOrderStatus(CANCELED.getValue());
		deliveryOrderEsPatternDao.updateDeliveryOrderEsPo(deliveryOrderEsPo);
	}

	private MessageDTO translateMessage(MafkaMessage mafkaMessage) {
		try {
			MessageDTO message = translateMessageWithBodyHolder(mafkaMessage, MessageDTO.class);

			Preconditions.checkNotNull(message, "empty mafkaMessage");
			Preconditions.checkNotNull(message.getTenantId(), "tenantId is null");
			Preconditions.checkNotNull(message.getShopId(), "shopId is null");
			Preconditions.checkNotNull(message.getOrderId(), "orderId is null");
			Preconditions.checkNotNull(message.getStatus(), "status is null");
			Preconditions.checkNotNull(message.getOrderSource(), "orderSource is null");
			Preconditions.checkNotNull(message.getSourceStatus(), "sourceStatus is null");
			return message;
		} catch (Exception e) {
			log.error("解析订单状态变更消息失败:{}", mafkaMessage, e);
			return null;
		}
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class MessageDTO {
		private Long tenantId;
		private Long shopId;
		private Long orderId;
		private Integer orderSource;
		private Integer status;
		private Integer sourceStatus;
		private Long warehouseId;
		/**
		 * 补单标识
		 */
		private Boolean compensate;
		/**
		 * 线上最新的配送状态 DistributeStatusEnum
		 */
		private Integer onlineDistributeStatus;

		/**
		 * 标记qnh侧jddj/elm订单延迟下发
		 */
		private boolean delayPush;

		public boolean isOnlineOrder() {
			return Objects.equals(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), orderSource)
					|| Objects.equals(OrderSourceEnum.OTO_ONLINE.getValue(), orderSource)
					|| Objects.equals(OrderSourceEnum.GLORY.getValue(), orderSource);
		}
	}
}
