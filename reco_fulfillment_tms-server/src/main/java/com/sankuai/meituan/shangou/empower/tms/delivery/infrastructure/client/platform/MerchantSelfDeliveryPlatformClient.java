package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform;

import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.rider.client.message.SelfDeliveryLaunchMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LaunchFailure;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaMessageProducer;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderLocationRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.TimeUtil;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo.NO_EXCEPTION;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEventEnum.DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Slf4j
@Component
public class MerchantSelfDeliveryPlatformClient extends AbstractDeliveryPlatformClient {

	@Resource
	private PushClient pushClient;
	@Resource
	private RiderLocationRepository riderLocationRepository;

	@Resource
	private MafkaMessageProducer<DeliveryChangeSyncOutMessage> deliveryChangeSyncOutMessageProducer;

	@Override
	public DeliveryPlatformEnum getDeliveryPlatform() {
		return DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY;
	}

	@Resource
	private MafkaMessageProducer<SelfDeliveryLaunchMsg> selfDeliveryLaunchMessageProducer;

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark) {
		//推进到等待分配骑手状态，并记录流水
		deliveryOrder.onChange(DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER, NO_EXCEPTION, null, LocalDateTime.now());

		// 发送新任务通知 PUSH (压测时跳过发PUSH流程)
		if (!MccConfigUtils.getPressureTestTenantIds().contains(deliveryOrder.getTenantId())) {
			pushClient.pushMerchantSelfDeliveryNewDeliveryTask(deliveryOrder, orderInfo);
		}

		// 自营骑手新任务业务埋点
		Cat.logEvent("SELF_RIDER_NEW_TASK_CREATED", "SUCCESS");

		// 给ofc发消息通知
		try {
			SelfDeliveryLaunchMsg selfDeliveryLaunchMsg = new SelfDeliveryLaunchMsg();
			selfDeliveryLaunchMsg.setTenantId(deliveryOrder.getTenantId());
			selfDeliveryLaunchMsg.setStoreId(deliveryOrder.getStoreId());
			selfDeliveryLaunchMsg.setChannelOrderId(deliveryOrder.getChannelOrderId());
			selfDeliveryLaunchMsg.setOrderBizType(deliveryOrder.getOrderBizType());
			selfDeliveryLaunchMessageProducer.sendMessage(selfDeliveryLaunchMsg);
		} catch (Exception e) {
			log.error("发送自送下发消息失败");
		}

		return Optional.empty();
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder) {
		DeliveryStatusEnum beforeStatus = deliveryOrder.getStatus();
		//推进状态
		deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, NO_EXCEPTION, deliveryOrder.getRiderInfo(), LocalDateTime.now());

		//推送取消通知
		pushClient.pushMerchantSelfDeliveryOrderCancelled(deliveryOrder, beforeStatus);

		//发送MQ消息通知外部
		if (MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId()) || !DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch()) {
			Long currentRiderAccountId = null;
			if (Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
				Rider riderInfo = deliveryOrder.getRiderInfo();
				if (Objects.nonNull(riderInfo) && (riderInfo instanceof StaffRider)) {
					currentRiderAccountId = ((StaffRider)riderInfo).getRiderAccountId();
				}
			}

			deliveryChangeSyncOutMessageProducer.sendMessage(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.DELIVERY_CANCEL.getValue(),
					new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),deliveryOrder.getId(), deliveryOrder.getOrderBizType(),deliveryOrder.getOrderId(), deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus().getCode()),
					new DeliveryChangeSyncOutMessage.DeliveryCancelBody(currentRiderAccountId)));
		}

		return Optional.empty();
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi) {
		if(deliveryOrder==null || deliveryOrder.getRiderInfo()==null){
			return Optional.empty();
		}
		try {
			StaffRider rider= (StaffRider) deliveryOrder.getRiderInfo();
			if(rider.getRiderAccountId()==null){
				return Optional.empty();
			}
			RiderLocationDetail riderLocationDetail = riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
			if(riderLocationDetail==null){
				return Optional.empty();
			}
			log.info("MerchantSelfDeliveryPlatformClient.queryRiderLocation riderLocationDetail:{}", riderLocationDetail);
			return Optional.of(new CoordinatePoint(riderLocationDetail.getLongitude(), riderLocationDetail.getLatitude(), TimeUtil.toMilliSeconds(riderLocationDetail.getTime())));
		}catch (Exception e){
			log.error("MerchantSelfDeliveryPlatformClient.queryRiderLocation error .deliveryOrder:{},deliveryPoi:{}",deliveryOrder,deliveryPoi);
		}
		return Optional.empty();
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto, DeliveryPoi deliveryPoi) {
		return queryRiderLocation(deliveryOrder, deliveryPoi);
	}

	@Override
	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder) {
		if (deliveryOrder == null || deliveryOrder.getRiderInfo() == null) {
			return Optional.empty();
		}
		try {
			StaffRider rider = (StaffRider) deliveryOrder.getRiderInfo();
			if (rider.getRiderAccountId() == null) {
				return Optional.empty();
			}
			RiderLocationDetail riderLocationDetail =
					riderLocationRepository.getStaffRiderLocation(rider.getRiderAccountId());
			if (riderLocationDetail == null) {
				return Optional.empty();
			}
			log.info("MerchantSelfDeliveryPlatformClient.queryRiderLocation riderLocationDetail:{}", riderLocationDetail);
			return Optional.of(new CoordinatePoint(riderLocationDetail.getLongitude(),
					riderLocationDetail.getLatitude(), TimeUtil.toMilliSeconds(riderLocationDetail.getTime())));
		} catch (Exception e) {
			log.error("MerchantSelfDeliveryPlatformClient.queryRiderLocation error .deliveryOrder:{}", deliveryOrder);
		}
		return Optional.empty();
	}

	@Override
	public Optional<Failure> cancelDeliveryForTransOrder(DeliveryOrder deliveryOrder) {
		return cancelDelivery(deliveryOrder);
	}

	@Override
	public Optional<Failure> cancelDeliveryForOFC(DeliveryOrder deliveryOrder) {
		DeliveryStatusEnum beforeStatus = deliveryOrder.getStatus();
		//推进状态
		deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, NO_EXCEPTION, deliveryOrder.getRiderInfo(), LocalDateTime.now());

		//推送取消通知
		pushClient.pushMerchantSelfDeliveryOrderCancelled(deliveryOrder, beforeStatus);
		return Optional.empty();
	}
}
