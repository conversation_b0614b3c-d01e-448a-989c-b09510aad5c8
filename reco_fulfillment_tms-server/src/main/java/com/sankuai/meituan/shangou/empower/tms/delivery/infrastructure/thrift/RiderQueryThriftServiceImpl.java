package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.thrift;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.common.TPageInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.RiderDeliveryQueryAppService;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.CoordinateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.DeliveryRiskControlOrderDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.application.RiderQueryApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.convert.RiderQueryResponseConvertUtils;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.*;

/**
 * 自营骑手查询运单信息.
 *
 * <AUTHOR>
 * @since 2021/6/14 18:25
 */
@Slf4j
@Service
public class RiderQueryThriftServiceImpl implements RiderQueryThriftService {

	@Resource
	private RiderQueryApplicationService riderQueryApplicationService;
	@Resource
	private RiderDeliveryQueryAppService riderDeliveryQueryAppService;
	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	private final LocalDateTime DEFAULT_TIME = LocalDateTime.of(1970,1,1,0,0,0);

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public PageQueryDeliveryOrderResponse pageQueryDeliveryOrder(PageQueryDeliveryOrderRequest request) {
		String check = request.validate();
		if (check != null) {
			return new PageQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), check));
		}

		try {
			// 校验配送状态有效
			if (DeliveryStatusEnum.valueOf(request.getDeliveryStatus()) == null) {
				return new PageQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), INVALID_PARAM.getMessage()));
			}
			PageResult<RiderDeliveryOrder> pageResult = queryDeliveryOrders(request);
			return translateToResponse(pageResult);
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryDeliveryOrder4SelfRider error", e);
			return new PageQueryDeliveryOrderResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public QueryDeliveryQuantityResponse queryDeliveryQuantity(QueryDeliveryQuantityRequest request) {
		String check = request.validate();
		if (check != null) {
			return new QueryDeliveryQuantityResponse(new Status(INVALID_PARAM.getCode(), check));
		}

		try {
			Map<Integer, Integer> statusQuantityMap = new HashMap<>(request.getDeliveryStatusList().size());
			request.getDeliveryStatusList()
					.stream()
					.filter(statusCode -> Objects.nonNull(DeliveryStatusEnum.valueOf(statusCode)))
					.forEach(statusCode -> statusQuantityMap.put(statusCode, Math.toIntExact(countDeliveryOrder(request, statusCode))));

			return new QueryDeliveryQuantityResponse(statusQuantityMap);
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryDeliveryQuantity error", e);
			return new QueryDeliveryQuantityResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public QueryActivateDeliveryOrderCntDetailResponse queryActivateDeliveryOrderCntDetail(QueryActivateDeliveryOrderCntDetailRequest request) {
		String check = request.validate();
		if (check != null) {
			return new QueryActivateDeliveryOrderCntDetailResponse(new Status(INVALID_PARAM.getCode(), check));
		}

		try {
			List<RiderDeliveryOrder> waitGetDeliveryOrderList =
					riderQueryApplicationService.batchQueryDeliveryOrders(request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER);
			List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> newJustDeliveryOrderKeyList = waitGetDeliveryOrderList.stream()
					.filter(deliveryOrder -> Objects.nonNull(deliveryOrder.getRiderDeliveryExtInfo()) && Objects.equals(deliveryOrder.getRiderDeliveryExtInfo().getPickDeliverySplitTag(), true))
					.map(deliveryOrder -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(deliveryOrder.getCustomerOrderKey().getChannelOrderId(), deliveryOrder.getCustomerOrderKey().getOrderBizType()))
					.distinct()
					.collect(Collectors.toList());
			int newJustDeliveryOrderCnt = newJustDeliveryOrderKeyList.size();

			List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> newPickDeliveryOrderKeyList = waitGetDeliveryOrderList.stream()
					.map(deliveryOrder -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(deliveryOrder.getCustomerOrderKey().getChannelOrderId(), deliveryOrder.getCustomerOrderKey().getOrderBizType()))
					.filter(channelOrderKey -> !newJustDeliveryOrderKeyList.contains(channelOrderKey))
					.distinct()
					.collect(Collectors.toList());
			int newPickDeliveryOrderCnt = newPickDeliveryOrderKeyList.size();


			List<RiderDeliveryOrder> waitTakeDeliveryOrderList =
					riderQueryApplicationService.batchQueryDeliveryOrders(request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.RIDER_ASSIGNED, request.getRiderAccountId());
			List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> waitTakeDeliveryOrderKeyList = waitTakeDeliveryOrderList.stream()
					.map(deliveryOrder -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(deliveryOrder.getCustomerOrderKey().getChannelOrderId(), deliveryOrder.getCustomerOrderKey().getOrderBizType()))
					.distinct()
					.collect(Collectors.toList());
			int waitTakeDeliveryTaskCnt = waitTakeDeliveryOrderKeyList.size();

			List<RiderDeliveryOrder> inDeliveryOrderList =
					riderQueryApplicationService.batchQueryDeliveryOrders(request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.RIDER_TAKEN_GOODS, request.getRiderAccountId());
			List<QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey> inDeliveryOrderKeyList = inDeliveryOrderList.stream()
					.map(deliveryOrder -> new QueryActivateDeliveryOrderCntDetailResponse.ChannelOrderKey(deliveryOrder.getCustomerOrderKey().getChannelOrderId(), deliveryOrder.getCustomerOrderKey().getOrderBizType()))
					.distinct()
					.collect(Collectors.toList());
			int inDeliveryOrderCnt = inDeliveryOrderKeyList.size();

			return new QueryActivateDeliveryOrderCntDetailResponse(Status.SUCCESS,
					newJustDeliveryOrderCnt, newJustDeliveryOrderKeyList,
					newPickDeliveryOrderCnt, newPickDeliveryOrderKeyList,
					waitTakeDeliveryTaskCnt, waitTakeDeliveryOrderKeyList,
					inDeliveryOrderCnt, inDeliveryOrderKeyList);

		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryDeliveryQuantity error", e);
			return new QueryActivateDeliveryOrderCntDetailResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	public QueryTimeoutDeliveryOrderNumResponse queryTimeoutDeliveryOrderNum(QueryTimeoutDeliveryOrderNumRequest request) {
		String check = request.validate();
		if (check != null) {
			return new QueryTimeoutDeliveryOrderNumResponse(new Status(INVALID_PARAM.getCode(), check));
		}

		try {
			Map<Integer, Integer> statusMap = new HashMap<>(request.getDeliveryStatusList().size());
			request.getDeliveryStatusList()
					.stream()
					.filter(statusCode -> Objects.nonNull(DeliveryStatusEnum.valueOf(statusCode)))
					.forEach(statusCode -> statusMap.put(statusCode, Math.toIntExact(countTimeoutDeliveryOrder(request, statusCode))));
			return new QueryTimeoutDeliveryOrderNumResponse(statusMap);
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryTimeoutDeliveryOrderNum error", e);
			return new QueryTimeoutDeliveryOrderNumResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public PageQueryDeliveryOrderResponse pageQueryCompletedDeliveryOrder(PageQueryCompletedDeliveryOrderRequest request) {
		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new PageQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
		}

		try {
			PageResult<RiderDeliveryOrder> pageResult = riderQueryApplicationService.queryCompletedDeliveryOrders(
					request.getTenantId(), request.getStoreId(), request.getRiderAccountId(), new PageRequest(request.getPage(), request.getPageSize())
			);

			return translateToResponse(pageResult);
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.pageQueryCompletedDeliveryOrder error", e);
			return new PageQueryDeliveryOrderResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public PageQueryDeliveryOrderResponse pageQueryInProgressDeliveryOrder(PageQueryInProgressDeliveryOrderRequest request) {

		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new PageQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
		}
		try {
			PageResult<RiderDeliveryOrder> pageResult = riderQueryApplicationService.queryInProgressDeliveryOrders(
					request.getTenantId(), request.getStoreId(), request.getRiderAccountId(), new PageRequest(request.getPage(), request.getPageSize())
			);


			List<PricingRouteInfoDO> pricingRouteInfoDOList = Collections.emptyList();
			if (request.needReturnPricingRouteInfo && CollectionUtils.isNotEmpty(pageResult.getInfo())) {
				List<Long> deliveryOrderIds = pageResult.getInfo().stream().map(RiderDeliveryOrder::getId).collect(Collectors.toList());
				pricingRouteInfoDOList = riderQueryApplicationService.queryPriceRouteInfo(request.getTenantId(), deliveryOrderIds);
			}

			return translateToResponseWithRouteInfo(pageResult, pricingRouteInfoDOList);
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.pageQueryInProgressDeliveryOrder error", e);
			return new PageQueryDeliveryOrderResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public PageQueryDeliveryOrderResponse queryAllRiderDeliveryOrder(QueryAllDeliveryOrderRequest request) {
		try {
			List<RiderDeliveryOrder> riderDeliveryOrders = riderQueryApplicationService.queryAllRiderDeliveryOrder(request.getTenantId(), request.getStoreId(), request.getRiderAccountId());
			if (CollectionUtils.isEmpty(riderDeliveryOrders)) {
				return new PageQueryDeliveryOrderResponse(Status.SUCCESS, null, Collections.emptyList());
			}

			List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(Sets.newHashSet(IListUtils.mapTo(riderDeliveryOrders, RiderDeliveryOrder::getDeliveryChannel)));
			Map<Integer, Integer> carrierCodePlatformCodeMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getDeliveryPlatFormCode, (v1, v2) -> v2));
			return new PageQueryDeliveryOrderResponse(Status.SUCCESS, null, IListUtils.mapTo(riderDeliveryOrders, riderDeliveryOrder -> translate2TRiderDeliveryOrderWithChannel(riderDeliveryOrder, carrierCodePlatformCodeMap)));
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryAllRiderDeliveryOrder error", e);
			return new PageQueryDeliveryOrderResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public StoreDeliveryMonitorResponse storeDeliveryMonitor(StoreDeliveryMonitorRequest request) {
		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new StoreDeliveryMonitorResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
		}
		try {
			return riderQueryApplicationService.queryDeliveryMonitoringInfo(request.getTenantId(), request.getStoreId(), request.getAppId());
		} catch (IllegalArgumentException e) {
			log.error("QueryDeliveryInfoThriftService.storeDeliveryMonitor request param error, request: {}", request, e);
			return new StoreDeliveryMonitorResponse(new Status(INVALID_PARAM.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.storeDeliveryMonitor error, request: {}", request, e);
			return new StoreDeliveryMonitorResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}

	}

	@Override
	@MethodLog(logRequest = false,logResponse = true)
	public StoreRiderAccountListResponse storeRiderAccountList(StoreRiderAccountListRequest request) {
		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new StoreRiderAccountListResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
		}
		try {
			return riderQueryApplicationService.storeRiderAccountList(request.getTenantId(), request.getStoreId(), request.getAppId());
		} catch (IllegalArgumentException e) {
			log.error("QueryDeliveryInfoThriftService.storeRiderAccountList request param error, request: {}", request, e);
			return new StoreRiderAccountListResponse(new Status(INVALID_PARAM.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.storeRiderAccountList error, request: {}", request, e);
			return new StoreRiderAccountListResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	public PageQueryDeliveryExceptionListResponse pageQueryDeliveryExceptionList(PageQueryDeliveryExceptionRequest request) {
		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new PageQueryDeliveryExceptionListResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
		}
		try {
			return riderQueryApplicationService.pageQueryDeliveryExceptionList(request);
		} catch (IllegalArgumentException e) {
			log.error("QueryDeliveryInfoThriftService.pageQueryDeliveryExceptionList request param error, request: {}", request, e);
			return new PageQueryDeliveryExceptionListResponse(new Status(INVALID_PARAM.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.pageQueryDeliveryExceptionList error, request: {}", request, e);
			return new PageQueryDeliveryExceptionListResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	public DeliveryExceptionResponse queryDeliveryExceptionByChannelOrder(QueryDeliveryExceptionByChannelOrderRequest request) {
		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new DeliveryExceptionResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
		}
		try {
			return riderQueryApplicationService.queryDeliveryExceptionByChannelOrder(request);
		} catch (IllegalArgumentException e) {
			log.error("QueryDeliveryInfoThriftService.pageQueryDeliveryExceptionList request param error, request: {}", request, e);
			return new DeliveryExceptionResponse(new Status(INVALID_PARAM.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.pageQueryDeliveryExceptionList error, request: {}", request, e);
			return new DeliveryExceptionResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	public QueryDeliveryProofPhotoResponse queryDeliveryProofPhotosByOrderId(Long orderId) {
		if (orderId == null || orderId <= 0L) {
			return new QueryDeliveryProofPhotoResponse(new Status(INVALID_PARAM.getCode(), "订单id不合法"), Collections.emptyList(), null, null, null);
		}

		return riderQueryApplicationService.queryDeliveryProofPhotoInfos(orderId);
	}

	@Override
	public QueryDeliveryOrderWithCurrentRiderResponse queryDeliveryOrderByPoiAndStatusList(QueryDeliveryOrderByPoiAndStatusListRequest request) {
		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new QueryDeliveryOrderWithCurrentRiderResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg));
		}
		try {
			List<RiderDeliveryOrder> riderDeliveryOrders = riderQueryApplicationService.queryDeliveryOrdersByPoiAndStatusList(
					request.getTenantId(), request.getStoreIdList(), request.getDeliveryStatusList()
			);

			List<TRiderDeliveryOrderWithCurrentRider> tRiderDeliveryOrders = Optional
					.ofNullable(riderDeliveryOrders)
					.orElse(Lists.newArrayList())
					.stream().map(this::translate2TRiderDeliveryOrderWithCurrentRider)
					.collect(Collectors.toList());

			return new QueryDeliveryOrderWithCurrentRiderResponse(Status.SUCCESS, tRiderDeliveryOrders);
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryDeliveryOrderByPoiAndStatusList error, request: {}", request, e);
			return new QueryDeliveryOrderWithCurrentRiderResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}
	}

	@Override
	@MethodLog(logRequest = true,logResponse = true)
	public BatchQueryDeliveryOrderResponse queryDeliveryOrderByIds(QueryDeliveryOrderByIdListRequest request) {
		String checkErrorMsg = request.validate();
		if (checkErrorMsg != null) {
			return new BatchQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), checkErrorMsg), null);
		}
		try {
			List<RiderDeliveryOrder> riderDeliveryOrders = riderQueryApplicationService.queryByIdList(request.getDeliveryOrderIds(),request.getTenantId(),request.getStoreId());

			List<PricingRouteInfoDO> pricingRouteInfoDOList = Collections.emptyList();
			if (request.isNeedReturnPricingRouteInfo() && CollectionUtils.isNotEmpty(riderDeliveryOrders)) {
				List<Long> deliveryOrderIds = riderDeliveryOrders.stream().map(RiderDeliveryOrder::getId).collect(Collectors.toList());
				pricingRouteInfoDOList = riderQueryApplicationService.queryPriceRouteInfo(request.getTenantId(), deliveryOrderIds);
			}

			return translateToResponseWithRouteInfo(riderDeliveryOrders, pricingRouteInfoDOList);
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryDeliveryOrderByIds error, request: {}", request, e);
			return new BatchQueryDeliveryOrderResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()), null);
		}
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false,logResponse = true)
	public BatchQueryDeliveryOrderResponse queryDeliveryOrderByOrderIdList(@Valid QueryDeliveryOrderByOrderIdListRequest request) {
		List<RiderDeliveryOrder> riderDeliveryOrders;
		if (Objects.equals(request.getReturnCanceledDeliveryOrder(), true)) {
			riderDeliveryOrders = riderQueryApplicationService.batchQueryDeliveryOrderContainsUnActive(request.getTenantId(), request.getStoreId(), request.getOrderIds());
		} else {
			riderDeliveryOrders = riderQueryApplicationService.queryDeliveryOrderByOrderIdList(request.getTenantId(), request.getStoreId(), request.getOrderIds());
		}

		List<TRiderDeliveryOrder> tRiderDeliveryOrders = Optional.ofNullable(riderDeliveryOrders).orElse(Collections.emptyList())
				.stream().map(this::translate2TRiderDeliveryOrder)
				.collect(Collectors.toList());

		return new BatchQueryDeliveryOrderResponse(Status.SUCCESS, tRiderDeliveryOrders);
	}


	@Override
	@CatTransaction
	@MethodLog(logRequest = false,logResponse = true)
	public BatchQueryDeliveryOrderResponse queryDeliveryOrderByOrderIdListV2(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return new BatchQueryDeliveryOrderResponse(Status.SUCCESS, Collections.emptyList());
		}

		if (orderIds.size() > 100) {
			return new BatchQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), "orderId数不能超过100个"), Collections.emptyList());
		}

		List<RiderDeliveryOrder> riderDeliveryOrders = riderQueryApplicationService.queryDeliveryOrderByOrderIdList(orderIds);

		List<TRiderDeliveryOrder> tRiderDeliveryOrders = Optional.ofNullable(riderDeliveryOrders).orElse(Collections.emptyList())
				.stream().map(this::translate2TRiderDeliveryOrder)
				.collect(Collectors.toList());

		return new BatchQueryDeliveryOrderResponse(Status.SUCCESS, tRiderDeliveryOrders);
	}

	@Override
	@MethodLog(logRequest = false, logResponse = true)
	public BatchQueryDeliveryOrderResponse queryAllDeliveryOrderByOrderIdList(List<Long> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return new BatchQueryDeliveryOrderResponse(Status.SUCCESS, Collections.emptyList());
		}

		if (orderIds.size() > 100) {
			return new BatchQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), "orderId数不能超过100个"), Collections.emptyList());
		}


		List<RiderDeliveryOrder> riderDeliveryOrders = riderQueryApplicationService.queryAllDeliveryOrderByOrderIdList(orderIds);
		if (CollectionUtils.isEmpty(riderDeliveryOrders)) {
			return new BatchQueryDeliveryOrderResponse(Status.SUCCESS, null);
		}

		List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(Sets.newHashSet(IListUtils.mapTo(riderDeliveryOrders, RiderDeliveryOrder::getDeliveryChannel)));
		Map<Integer, Integer> carrierCodePlatformCodeMap = deliveryChannelList.stream().collect(Collectors.toMap(DeliveryChannel::getCarrierCode, DeliveryChannel::getDeliveryPlatFormCode, (v1, v2) -> v2));

		List<TRiderDeliveryOrder> tRiderDeliveryOrders = Optional.ofNullable(riderDeliveryOrders).orElse(Collections.emptyList())
				.stream().map(riderDeliveryOrder -> translate2TRiderDeliveryOrderWithChannel(riderDeliveryOrder, carrierCodePlatformCodeMap))
				.collect(Collectors.toList());

		return new BatchQueryDeliveryOrderResponse(Status.SUCCESS, tRiderDeliveryOrders);
	}

	@Override
	@MethodLog(logRequest = false,logResponse = true)
	public QueryRiderArrivalLocationResponse queryRiderArrivalLocationInfo(Long tenantId, Long storeId, Long orderId) {
		try {
			 return riderQueryApplicationService.queryRiderArrivalLocationInfo(tenantId, storeId, orderId);
		} catch (IllegalArgumentException e) {
			log.error("QueryDeliveryInfoThriftService.queryRiderArrivalLocationInfo request param error, " +
					"tenantId: {}, storeId: {}, orderId: {}", tenantId, storeId, orderId, e);
			return new QueryRiderArrivalLocationResponse(new Status(INVALID_PARAM.getCode(), e.getMessage()));
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryRiderArrivalLocationInfo error, " +
					"tenantId: {}, storeId: {}, orderId: {}", tenantId, storeId, orderId, e);
			return new QueryRiderArrivalLocationResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()));
		}

	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false,logResponse = true)
	public PageQueryDeliveryOrderResponse pageQueryCompletedDeliveryOrderByDuration(PageQueryCompletedDeliveryOrderByDurationRequest request) {
		String errMsg = request.validate();
		if (StringUtils.isNotBlank(errMsg)) {
			return new PageQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), errMsg), null, null);
		}

		//查询已送达配送单列表
		PageResult<RiderDeliveryOrder> pageResult = riderQueryApplicationService.queryCompletedDeliveryOrders(request.getStoreId(), request.getRiderAccountId(),
				new PageRequest(request.getPage(), request.getPageSize()),
				TimeUtil.fromMilliSeconds(request.getStartTime()), TimeUtil.fromMilliSeconds(request.getEndTime()),request.getTenantId());

		return translateToResponse(pageResult);
	}

	@Override
	@CatTransaction
	@MethodLog(logRequest = false,logResponse = true)
	public QueryDeliveryRiskControlOrderResponse queryRiskControlOrderByViewOrderIdList(QueryDeliveryRiskControlOrderRequest request) {
		String errMsg = request.validate();
		if (StringUtils.isNotBlank(errMsg)) {
			return new QueryDeliveryRiskControlOrderResponse(new Status(INVALID_PARAM.getCode(), errMsg), null);
		}

		//查询已送达配送单列表
		List<DeliveryRiskControlOrderDO> deliveryRiskControlOrderDOS = riderQueryApplicationService.queryDeliveryRiskControlOrder(request);

		List<TDeliveryRiskControlOrder> tDeliveryRiskControlOrders = deliveryRiskControlOrderDOS
				.stream().map(this::translate2TDeliveryRiskControlOrder)
				.collect(Collectors.toList());

		return new QueryDeliveryRiskControlOrderResponse(Status.SUCCESS, tDeliveryRiskControlOrders);
	}


	@Override
	@CatTransaction
	@MethodLog(logRequest = false,logResponse = true)
	public QueryCompletedStatisticResponse queryCompletedStatistic(QueryCompletedStatisticRequest request) {
		String errMsg = request.validate();
		if (StringUtils.isNotBlank(errMsg)) {
			return new QueryCompletedStatisticResponse(new Status(INVALID_PARAM.getCode(), errMsg), null, null, null, null);
		}

		return riderQueryApplicationService.queryCompletedStatistic(request);
	}

	@Override
	public BatchQueryDeliveryOrderResponse queryDeliveryOrderByRiderAndStatusList(QueryDeliveryOrderByRiderAndStatusRequest request) {
		String check = request.validate();
		if (check != null) {
			return new BatchQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), check), Lists.newArrayList());
		}

		try {
			// 校验配送状态有效
			if (DeliveryStatusEnum.valueOf(request.getDeliveryStatus()) == null) {
				return new BatchQueryDeliveryOrderResponse(new Status(INVALID_PARAM.getCode(), INVALID_PARAM.getMessage()), Lists.newArrayList());
			}
			List<RiderDeliveryOrder> result = batchQueryDeliveryOrders(request);
			return new BatchQueryDeliveryOrderResponse(
					Status.SUCCESS,
					result
							.stream()
							.filter(Objects::nonNull)
							.map(this::translate2TRiderDeliveryOrder)
							.collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("QueryDeliveryInfoThriftService.queryDeliveryOrder4SelfRider error", e);
			return new BatchQueryDeliveryOrderResponse(new Status(SYSTEM_ERROR.getCode(), SYSTEM_ERROR.getMessage()), Lists.newArrayList());
		}
	}

	@Override
	public Long calcSdmsOrderPushDownTimestamp(Long merchantId, Long warehouseId, String tradeOrderNo, Integer orderBizType) {
		return riderDeliveryQueryAppService.calcSdmsOrderPushDownTimestamp(merchantId,warehouseId,tradeOrderNo,orderBizType);
	}

	private List<RiderDeliveryOrder> batchQueryDeliveryOrders(QueryDeliveryOrderByRiderAndStatusRequest request) {
		if (DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode() == request.getDeliveryStatus()) {
			return riderQueryApplicationService.batchQueryDeliveryOrders(
					request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER
			);
		} else {
			return riderQueryApplicationService.batchQueryDeliveryOrders(
					request.getTenantId(), request.getStoreId(),
					DeliveryStatusEnum.valueOf(request.getDeliveryStatus()), request.getRiderAccountId()
			);
		}
	}

	private PageResult<RiderDeliveryOrder> queryDeliveryOrders(PageQueryDeliveryOrderRequest request) {
		PageRequest pageRequest = new PageRequest(request.getPage(), request.getPageSize());

		if (DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode() == request.getDeliveryStatus()) {
			return riderQueryApplicationService.queryDeliveryOrders(
					request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER, pageRequest
			);
		} else {
			return riderQueryApplicationService.queryDeliveryOrders(
					request.getTenantId(), request.getStoreId(),
					DeliveryStatusEnum.valueOf(request.getDeliveryStatus()), request.getRiderAccountId(), pageRequest
			);
		}
	}

	private long countDeliveryOrder(QueryDeliveryQuantityRequest request, Integer statusCode) {
		if (DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode() == statusCode) {
			return riderQueryApplicationService.countDeliveryOrder(
					request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER
			);
		} else {
			return riderQueryApplicationService.countDeliveryOrder(
					request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.valueOf(statusCode), request.getRiderAccountId()
			);
		}
	}

	private long countTimeoutDeliveryOrder(QueryTimeoutDeliveryOrderNumRequest request, Integer statusCode) {
		if (DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode() == statusCode) {
			return riderQueryApplicationService.countTimeoutDeliveryOrder(
					request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER
			);
		} else {
			return riderQueryApplicationService.countTimeoutDeliveryOrder(
					request.getTenantId(), request.getStoreId(), DeliveryStatusEnum.valueOf(statusCode), request.getRiderAccountId()
			);
		}
	}

	private PageQueryDeliveryOrderResponse translateToResponseWithRouteInfo(PageResult<RiderDeliveryOrder> pageResult, List<PricingRouteInfoDO> pricingRouteInfoDOList) {
		if (pageResult.isFail()) {
			return new PageQueryDeliveryOrderResponse(new Status(pageResult.getFailure().getFailureCode(), pageResult.getFailure().getFailureMessage()));
		}

		PageQueryDeliveryOrderResponse response = new PageQueryDeliveryOrderResponse();
		response.setPageInfo(new TPageInfo(pageResult.getPage(), pageResult.getPageSize(), Math.toIntExact(pageResult.getTotal())));
		response.setTRiderDeliveryOrders(
				pageResult.getInfo()
						.stream()
						.filter(Objects::nonNull)
						.map(this::translate2TRiderDeliveryOrder)
						.collect(Collectors.toList())
		);

		RiderQueryResponseConvertUtils.fillPricingRouteInfo(response.getTRiderDeliveryOrders(), pricingRouteInfoDOList);
		return response;
	}

	private BatchQueryDeliveryOrderResponse translateToResponseWithRouteInfo(List<RiderDeliveryOrder> riderDeliveryOrders, List<PricingRouteInfoDO> pricingRouteInfoDOList) {
		List<TRiderDeliveryOrder> tRiderDeliveryOrderList = riderDeliveryOrders
				.stream()
				.filter(Objects::nonNull)
				.map(this::translate2TRiderDeliveryOrder)
				.collect(Collectors.toList());


		RiderQueryResponseConvertUtils.fillPricingRouteInfo(tRiderDeliveryOrderList, pricingRouteInfoDOList);

		return new BatchQueryDeliveryOrderResponse(Status.SUCCESS, tRiderDeliveryOrderList);
	}

	private PageQueryDeliveryOrderResponse translateToResponse(PageResult<RiderDeliveryOrder> pageResult) {
		if (pageResult.isFail()) {
			return new PageQueryDeliveryOrderResponse(new Status(pageResult.getFailure().getFailureCode(), pageResult.getFailure().getFailureMessage()));
		}

		PageQueryDeliveryOrderResponse response = new PageQueryDeliveryOrderResponse();
		response.setPageInfo(new TPageInfo(pageResult.getPage(), pageResult.getPageSize(), Math.toIntExact(pageResult.getTotal())));
		response.setTRiderDeliveryOrders(
				pageResult.getInfo()
						.stream()
						.filter(Objects::nonNull)
						.map(this::translate2TRiderDeliveryOrder)
						.collect(Collectors.toList())
		);
		return response;
	}

	/**
	 * 将 DeliveryOrder 转为 TRiderDeliveryOrder.
	 *
	 * @param deliveryOrder 运单
	 * @return TRiderDeliveryOrder
	 */
	private TRiderDeliveryOrder translate2TRiderDeliveryOrder(RiderDeliveryOrder deliveryOrder) {
		TRiderDeliveryOrder riderDeliveryOrder = new TRiderDeliveryOrder();
		riderDeliveryOrder.setDeliveryOrderId(deliveryOrder.getId());
		riderDeliveryOrder.setTenantId(deliveryOrder.getTenantId());
		riderDeliveryOrder.setStoreId(deliveryOrder.getStoreId());
		riderDeliveryOrder.setOrderId(deliveryOrder.getCustomerOrderKey().getOrderId());
		riderDeliveryOrder.setDeliveryDoneTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getDeliveryDoneTime()));
		riderDeliveryOrder.setChannelOrderId(deliveryOrder.getCustomerOrderKey().getChannelOrderId());
		riderDeliveryOrder.setOrderBizTypeCode(deliveryOrder.getCustomerOrderKey().getOrderBizType());
		riderDeliveryOrder.setEstimatedDeliveryTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryTime()));
		riderDeliveryOrder.setEstimatedDeliveryEndTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryEndTime()));
		riderDeliveryOrder.setDeliveryStatus(deliveryOrder.getStatus().getCode());
		riderDeliveryOrder.setChangeFromRider(translate2TStaffRider(deliveryOrder.getChangeFromRider()));
		riderDeliveryOrder.setReceiver(translate2TReceiver(deliveryOrder.getReceiver()));
		riderDeliveryOrder.setStatusLocked(deliveryOrder.checkOrderStatusLocked() ? 1 : 0);
		riderDeliveryOrder.setCanStatusBeLocked(deliveryOrder.canLockStatus() ? 1 : 0);
		riderDeliveryOrder.setDistance(deliveryOrder.getDistance());
		riderDeliveryOrder.setCouldPostDeliveryProofPhoto(deliveryOrder.couldPostDeliveryProofPhoto());
		riderDeliveryOrder.setCurrentRider(translate2TStaffRider(deliveryOrder.getRiderInfo()));
		riderDeliveryOrder.setSignPosition(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getSignPosition).orElse(null));
		riderDeliveryOrder.setSignType(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getSignType).orElse(null));
		riderDeliveryOrder.setIsReserved(deliveryOrder.getCustomerOrderKey().getReserved());
		riderDeliveryOrder.setIsOneYuanOrder(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getIsOneYuanOrder).orElse(null));
		riderDeliveryOrder.setAssessDeliveryTime(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getAssessDeliveryTime).orElse(null));
		riderDeliveryOrder.setLastEventTime(Optional.ofNullable(deliveryOrder.getTimeline().getLastEventTime()).filter(eventTime -> !Objects.equals(DEFAULT_TIME, eventTime)).map(TimeUtil::toMilliSeconds).orElse(null));
		riderDeliveryOrder.setCreateTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getCreateTime()));
		riderDeliveryOrder.setPickDeliverySplitTag(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getPickDeliverySplitTag).orElse(false));

		riderDeliveryOrder.setThirdDelivery(!Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()));
		riderDeliveryOrder.setThirdException(deliveryOrder.getIsThirdException());
		riderDeliveryOrder.setOriginDeliveryStatus(deliveryOrder.getStatus().getCode());
		riderDeliveryOrder.setRewardType(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getRewardType).orElse(null));
		return riderDeliveryOrder;
	}

	/**
	 * 将 DeliveryOrder 转为 TRiderDeliveryOrder.
	 *
	 * @param deliveryOrder 运单
	 * @return TRiderDeliveryOrder
	 */
	private TRiderDeliveryOrder translate2TRiderDeliveryOrderWithChannel(RiderDeliveryOrder deliveryOrder, Map<Integer, Integer> carrierCodePlatformCodeMap) {
		TRiderDeliveryOrder riderDeliveryOrder = new TRiderDeliveryOrder();
		riderDeliveryOrder.setDeliveryOrderId(deliveryOrder.getId());
		riderDeliveryOrder.setTenantId(deliveryOrder.getTenantId());
		riderDeliveryOrder.setStoreId(deliveryOrder.getStoreId());
		riderDeliveryOrder.setOrderId(deliveryOrder.getCustomerOrderKey().getOrderId());
		riderDeliveryOrder.setDeliveryDoneTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getDeliveryDoneTime()));
		riderDeliveryOrder.setChannelOrderId(deliveryOrder.getCustomerOrderKey().getChannelOrderId());
		riderDeliveryOrder.setOrderBizTypeCode(deliveryOrder.getCustomerOrderKey().getOrderBizType());
		riderDeliveryOrder.setEstimatedDeliveryTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryTime()));
		riderDeliveryOrder.setEstimatedDeliveryEndTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryEndTime()));
		riderDeliveryOrder.setDeliveryStatus(deliveryOrder.getStatus().getCode());
		riderDeliveryOrder.setChangeFromRider(translate2TStaffRider(deliveryOrder.getChangeFromRider()));
		riderDeliveryOrder.setReceiver(translate2TReceiver(deliveryOrder.getReceiver()));
		riderDeliveryOrder.setStatusLocked(deliveryOrder.checkOrderStatusLocked() ? 1 : 0);
		riderDeliveryOrder.setCanStatusBeLocked(deliveryOrder.canLockStatus() ? 1 : 0);
		riderDeliveryOrder.setDistance(deliveryOrder.getDistance());
		riderDeliveryOrder.setCouldPostDeliveryProofPhoto(deliveryOrder.couldPostDeliveryProofPhoto());
		riderDeliveryOrder.setCurrentRider(translate2TStaffRider(deliveryOrder.getRiderInfo()));
		riderDeliveryOrder.setSignPosition(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getSignPosition).orElse(null));
		riderDeliveryOrder.setSignType(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getSignType).orElse(null));
		riderDeliveryOrder.setIsReserved(deliveryOrder.getCustomerOrderKey().getReserved());
		riderDeliveryOrder.setIsOneYuanOrder(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getIsOneYuanOrder).orElse(null));
		riderDeliveryOrder.setAssessDeliveryTime(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getAssessDeliveryTime).orElse(null));
		riderDeliveryOrder.setLastEventTime(Optional.ofNullable(deliveryOrder.getTimeline().getLastEventTime()).filter(eventTime -> !Objects.equals(DEFAULT_TIME, eventTime)).map(TimeUtil::toMilliSeconds).orElse(null));
		riderDeliveryOrder.setCreateTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getCreateTime()));
		riderDeliveryOrder.setPickDeliverySplitTag(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getPickDeliverySplitTag).orElse(false));

		riderDeliveryOrder.setThirdDelivery(!Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()));
		riderDeliveryOrder.setThirdException(deliveryOrder.getIsThirdException());
		riderDeliveryOrder.setOriginDeliveryStatus(deliveryOrder.getStatus().getCode());
		riderDeliveryOrder.setDeliveryChannelId(deliveryOrder.getDeliveryChannel());
		riderDeliveryOrder.setDeliveryPlatformCode(getPlatformCode(deliveryOrder, carrierCodePlatformCodeMap));
		riderDeliveryOrder.setDeliveryPlatformDesc(getPlatformDesc(deliveryOrder, carrierCodePlatformCodeMap));
		riderDeliveryOrder.setRewardType(Optional.ofNullable(deliveryOrder.getRiderDeliveryExtInfo()).map(RiderDeliveryExtInfo::getRewardType).orElse(null));
		return riderDeliveryOrder;
	}

	private static Integer getPlatformCode(RiderDeliveryOrder deliveryOrder, Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {
		try {
			if (Objects.isNull(deliveryOrder) || Objects.isNull(deliveryOrder.getDeliveryChannel()) || MapUtils.isEmpty(carrierCodePlatformCodeMap)) {
				return null;
			}

			return carrierCodePlatformCodeMap.get(deliveryOrder.getDeliveryChannel());
		} catch (Exception e) {
			log.error("getPlatformCode error", e);
			Cat.logEvent("DH_ADAPT_DAP", "GET_PLATFORM_CODE_ERROR");
			return null;
		}

	}

	private static String getPlatformDesc(RiderDeliveryOrder deliveryOrder, Map<Integer/*CarrierCode*/, Integer/*PlatformCode*/> carrierCodePlatformCodeMap) {
		try {
			if (Objects.isNull(deliveryOrder) || Objects.isNull(deliveryOrder.getDeliveryChannel()) || MapUtils.isEmpty(carrierCodePlatformCodeMap)) {
				return null;
			}

			Integer platformCode = carrierCodePlatformCodeMap.get(deliveryOrder.getDeliveryChannel());
			return Optional.ofNullable(DeliveryPlatformEnum.enumOf(platformCode)).map(DeliveryPlatformEnum::getDesc).orElse("");
		} catch (Exception e) {
			log.error("getPlatformDesc error", e);
			Cat.logEvent("DH_ADAPT_DAP", "GET_PLATFORM_DESC_ERROR");
			return "";
		}
	}

	private TRiderDeliveryOrderWithCurrentRider translate2TRiderDeliveryOrderWithCurrentRider(RiderDeliveryOrder deliveryOrder) {
		TRiderDeliveryOrderWithCurrentRider riderDeliveryOrder = new TRiderDeliveryOrderWithCurrentRider();
		riderDeliveryOrder.setDeliveryOrderId(deliveryOrder.getId());
		riderDeliveryOrder.setTenantId(deliveryOrder.getTenantId());
		riderDeliveryOrder.setStoreId(deliveryOrder.getStoreId());
		riderDeliveryOrder.setOrderId(deliveryOrder.getCustomerOrderKey().getOrderId());
		riderDeliveryOrder.setDeliveryDoneTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getDeliveryDoneTime()));
		riderDeliveryOrder.setChannelOrderId(deliveryOrder.getCustomerOrderKey().getChannelOrderId());
		riderDeliveryOrder.setOrderBizTypeCode(deliveryOrder.getCustomerOrderKey().getOrderBizType());
		riderDeliveryOrder.setEstimatedDeliveryTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryTime()));
		riderDeliveryOrder.setEstimatedDeliveryEndTime(TimeUtil.toMilliSeconds(deliveryOrder.getTimeline().getEstimatedDeliveryEndTime()));
		riderDeliveryOrder.setDeliveryStatus(Optional.of(deliveryOrder.getStatus()).map(DeliveryStatusEnum::getCode).orElse(null));
		riderDeliveryOrder.setCurrentRider(translate2TStaffRider(deliveryOrder.getRiderInfo()));
		riderDeliveryOrder.setReceiver(translate2TReceiver(deliveryOrder.getReceiver()));
		riderDeliveryOrder.setStatusLocked(deliveryOrder.checkOrderStatusLocked() ? 1 : 0);
		riderDeliveryOrder.setCanStatusBeLocked(deliveryOrder.canLockStatus() ? 1 : 0);
		riderDeliveryOrder.setDistance(deliveryOrder.getDistance());
		riderDeliveryOrder.setCouldPostDeliveryProofPhoto(deliveryOrder.couldPostDeliveryProofPhoto());
		return riderDeliveryOrder;
	}

	private TStaffRider translate2TStaffRider(StaffRider changeFromRider) {
		if (changeFromRider == null) {
			return null;
		}

		return new TStaffRider(changeFromRider.getRiderName(), changeFromRider.getRiderPhone(), changeFromRider.getRiderAccountId(), null, null);
	}

	/**
	 * 将 Receiver 转为 TReceiver.
	 *
	 * @param receiver 收货人
	 * @return TReceiver
	 */
	private TReceiver translate2TReceiver(Receiver receiver) {
		TReceiver tReceiver = new TReceiver();
		if (receiver == null) {
			return tReceiver;
		}
		tReceiver.setReceiverName(receiver.getReceiverName());
		tReceiver.setReceiverPhone(receiver.getReceiverPhone());
		tReceiver.setReceiverPrivacyPhone(receiver.getReceiverPrivacyPhone());
		Address receiverAddress = receiver.getReceiverAddress();
		if (receiverAddress == null) {
			return tReceiver;
		}
		tReceiver.setAddressDetail(receiverAddress.getAddressDetail());
		// 返回上游统一采用 火星坐标系
		tReceiver.setCoordinateType(CoordinateTypeEnum.MARS.getCode());
		CoordinatePoint coordinatePoint = receiverAddress.getCoordinatePoint();
		if (receiverAddress.getCoordinateType() == CoordinateTypeEnum.BAIDU && coordinatePoint != null) {
			coordinatePoint = CoordinateUtil.translateFromBaiduToMars(coordinatePoint);
		}
		tReceiver.setLongitude(Optional.ofNullable(coordinatePoint).map(CoordinatePoint::getLongitude).orElse(null));
		tReceiver.setLatitude(Optional.ofNullable(coordinatePoint).map(CoordinatePoint::getLatitude).orElse(null));
		return tReceiver;
	}
	private TDeliveryRiskControlOrder translate2TDeliveryRiskControlOrder(DeliveryRiskControlOrderDO deliveryRiskControlOrderDO) {
		TDeliveryRiskControlOrder tDeliveryRiskControlOrder = new TDeliveryRiskControlOrder();
		tDeliveryRiskControlOrder.setDt(deliveryRiskControlOrderDO.getDt());
		tDeliveryRiskControlOrder.setViewOrderId(deliveryRiskControlOrderDO.getOrderIdView());
		tDeliveryRiskControlOrder.setOrderBizType(deliveryRiskControlOrderDO.getOrderBizType());
		tDeliveryRiskControlOrder.setTenantId(deliveryRiskControlOrderDO.getTenantId());
		tDeliveryRiskControlOrder.setStoreId(deliveryRiskControlOrderDO.getPoiId());
		tDeliveryRiskControlOrder.setRiderAccountId(deliveryRiskControlOrderDO.getRiderAccountId());

		return tDeliveryRiskControlOrder;
	}
}
