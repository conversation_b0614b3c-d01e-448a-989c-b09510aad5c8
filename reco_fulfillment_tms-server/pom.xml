<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
        <artifactId>reco_fulfillment_tms</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>reco_fulfillment_tms-server</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-redis-squirrel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-cluster-limiter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.rhino</groupId>
            <artifactId>rhino-cluster-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_sac_client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>tms-delivery-poi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>tms-delivery-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>tms-delivery-access</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>tms-delivery-rider</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-service-idl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>thrift-xframe-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>zebra-xframe-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper-spring-boot-starter</artifactId>
                </exclusion>

            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>mafka-xframe-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_saas_auth_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.platform</groupId>
            <artifactId>shangou_empower_product_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou</groupId>
            <artifactId>reco_store_saas_message_management_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_biz_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_management_client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou</groupId>
                    <artifactId>store-saas-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.shangou.saas</groupId>
                    <artifactId>reco_store_saas_order_platform_common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                    <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
            <artifactId>reco_qnh_poi_api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>

        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dbunit</groupId>
            <artifactId>dbunit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.springtestdbunit</groupId>
            <artifactId>spring-test-dbunit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.banma</groupId>
            <artifactId>banma_open_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.banma.deliverywaybill.order</groupId>
            <artifactId>ldp-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.banma.deliverywaybill.order</groupId>
            <artifactId>logistic-order-callback-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-client</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ofw-common</artifactId>
            <version>1.0.2</version>
        </dependency>


        <dependency>
            <groupId>com.sankuai.banma.package</groupId>
            <artifactId>unified-logistics-accept-platform-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-redis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.banma.logistics</groupId>
            <artifactId>banma_service_logistics_meta_center_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-message-pusher</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>empty_version</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-ebase-idl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-publisher</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-exception-common</artifactId>
            <version>2.8.1</version>
        </dependency>


        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mobile-oss-api</artifactId>
            <version>1.1.4.7</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ebase-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.waimaidata</groupId>
            <artifactId>waimaidata_disaster_client</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.sankuai.dolphin</groupId>
                <artifactId>thrift-maven-plugin</artifactId>
                <configuration>
                    <appkey>com.sankuai.sgfulfillment.tms</appkey>
                    <codeDir>${basedir}/src/main/java</codeDir>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.meituan.servicecatalog</groupId>
                <artifactId>api-thrift-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>process-generated-class</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>thriftDoc</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
