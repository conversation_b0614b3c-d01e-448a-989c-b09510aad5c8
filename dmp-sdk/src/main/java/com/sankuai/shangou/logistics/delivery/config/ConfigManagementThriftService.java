package com.sankuai.shangou.logistics.delivery.config;

import com.facebook.swift.service.ThriftMethod;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.commons.thrift.publisher.annotation.GatewayApi;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.fulfill.dto.FulfillDataPageQueryDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/9 16:00
 **/
public interface ConfigManagementThriftService {
    @MethodDoc(
            displayName = "通过configKey查询配置",
            description = "通过configKey查询配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "通过configKey查询配置",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "通过configKey查询配置",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    Boolean queryTenantDeliveryConfig(String configKey, Long tenantId);

    @MethodDoc(
            displayName = "通过configKey查询配置",
            description = "通过configKey查询配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "通过configKey查询配置",
                            type = FulfillDataPageQueryDTO.class,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "通过configKey查询配置",
            restExampleResponseData = "{}",
            example = "暂无"
    )
    @ThriftMethod
    TResult<Map<String, Map<String, Integer>>> batchQueryTenantDeliveryConfig(List<Integer> configKeyList, long tenantId, Long storeId);
}
