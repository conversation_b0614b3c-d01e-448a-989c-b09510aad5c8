package com.sankuai.shangou.logistics.delivery.enums;

import java.util.HashMap;
import java.util.Map;

public enum StoreConfigKeyEnum {

    OPERATION_MODE(1),
    SORT_MODE(2),
    INTERNALLY_NAVIGATION(3),
    DELIVERY_COMPLETE_MODE(4),
    M_TERMINAL_MODE(5),
    ORDER_STATISTIC(6),
    DRUNK_HORSE_TENANT(7),
    ;
    private Integer key;

    private final static Map<Integer,StoreConfigKeyEnum> storeConfigKeyEnumMap = new HashMap<>();
    static {
        for (StoreConfigKeyEnum keyEnum : values()){
            storeConfigKeyEnumMap.put(keyEnum.getKey(),keyEnum);
        }
    }

    StoreConfigKeyEnum(Integer key) {
        this.key = key;
    }

    public Integer getKey() {
        return key;
    }

    public static StoreConfigKeyEnum keyToEnum(Integer key){
        if(key == null){
            return null;
        }
        return storeConfigKeyEnumMap.get(key);
    }
}
