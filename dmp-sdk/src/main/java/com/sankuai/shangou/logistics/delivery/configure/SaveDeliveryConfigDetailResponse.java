package com.sankuai.shangou.logistics.delivery.configure;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 门店配置保存请求VO
 *
 * <AUTHOR>
 * @date 2025-07-15
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveDeliveryConfigDetailResponse {

    @FieldDoc(
            description = "请求青云的结果",
            requiredness = Requiredness.REQUIRED
    )
    public Integer code;

    @FieldDoc(
            description = "跳转url",
            requiredness = Requiredness.REQUIRED
    )
    public String dapLinkUrl;

    @FieldDoc(
            description = "是否授权",
            requiredness = Requiredness.REQUIRED
    )
    public Integer isAuthed;

}
