package com.sankuai.shangou.logistics.delivery.configure.value;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.logistics.delivery.configure.DeliveryConfigDetailVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-16
 * @email <EMAIL>
 */
@Data
public class BookingOrderLaunchTimeConfig {

    private Boolean needBusinessHoursPushdown;

    private List<ExpressionNode> subs;

}
