package com.sankuai.shangou.logistics.delivery.configure.value.launchpoint;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.HashMap;
import java.util.Map;

/**
 * 即时单配送发起时间点枚举
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/8
 */
public enum ImmediateOrderDeliveryLaunchPointEnum {
	/**
	 * 商家接单
	 */
	MERCHANT_ACCEPT(1),

	/**
	 * 拣货完成
	 */
	PICK_DONE(2),

	/**
	 * 订单支付完成
	 */
	ORDER_PAID(3),
	;

	private static final Map<Integer, ImmediateOrderDeliveryLaunchPointEnum> CODE_ENUM_MAP = new HashMap<>();
	private final int code;

	static {
		for (ImmediateOrderDeliveryLaunchPointEnum each : values()) {
			CODE_ENUM_MAP.put(each.getCode(), each);
		}
	}

	ImmediateOrderDeliveryLaunchPointEnum(int code) {
		this.code = code;
	}

	@JsonValue
	public int getCode() {
		return code;
	}

	@JsonCreator
	public static ImmediateOrderDeliveryLaunchPointEnum enumOf(Integer code) {
		return CODE_ENUM_MAP.get(code);
	}
}
