package com.sankuai.shangou.logistics.delivery.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-08-19
 * @email <EMAIL>
 */
public enum DeliveryConfigOrderTagEnum {

    ALL(0, "全部"),
    RESTAURANT(2010, "餐馆"),
    HIGH_VALUE(2008, "高价值"),
    SEAL_LABEL(2005, "封签（保真送）"),
    FA_CAI_WINE(510, "发财酒"),
    NORMAL(900001, "普通"),
    ;

    private final Integer type;
    private final String desc;
    DeliveryConfigOrderTagEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public final Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static DeliveryConfigOrderTagEnum typeToEnum(Integer type){
        if(type == null){
            return null;
        }
        for (DeliveryConfigOrderTagEnum templateEnum : values()){
            if(Objects.equals(type,templateEnum.getType())){
                return templateEnum;
            }
        }
        return null;
    }
}
