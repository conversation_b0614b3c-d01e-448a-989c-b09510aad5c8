package com.sankuai.shangou.logistics.delivery.configure.value;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-07-15
 */
@Data
public class DeliveryPlatformConfig {
    /**
     * 渠道类型
     */
    private Integer channelType;
    /**
     * 配送平
     */
    private Integer platformCode;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 配送发起点
     */
    private DeliveryLaunchPoint deliveryLaunchPoint;
    /**
     * 配送发起点延迟时间
     */
    private Integer deliveryLaunchDelayMinutes;
    /**
     * 预约单配送发起分钟数
     */
    private Integer bookingOrderDeliveryLaunchMinutes;
    /**
     * 自送预约配送规则
     */
    private BookingOrderLaunchTimeConfig bookingOrderLaunchTimeConfig;


}
