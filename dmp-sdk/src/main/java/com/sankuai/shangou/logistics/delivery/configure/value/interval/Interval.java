package com.sankuai.shangou.logistics.delivery.configure.value.interval;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;


public class Interval {

    private IntervalTypeEnum intervalType;

    private List<IntervalNumber> values;


    /**
     * 小数位数
     */
    private Integer scale;

    @JSONField(serialize = false)
    public IntervalTypeEnum getOriginIntervalType() {
        return intervalType;
    }

    @JSONField(serialize = false)
    public List<IntervalNumber> getOriginValues() {
        return values;
    }

    @JSONField(serialize = false)
    public Integer getScale() {
        return scale;
    }

    public int getIntervalType() {
        return intervalType.getType();
    }

    public List<String> getValues() {

        return Optional.ofNullable(values).orElse(Lists.newArrayList())
                .stream().map(IntervalNumber::getValue).collect(Collectors.toList());
    }

    // 反序列化int转化为IntervalType使用，勿删
    public void setIntervalType(int type) {
        this.intervalType = IntervalTypeEnum.of(type);
    }
    // 反序列化String转化为IntervalType使用，勿删
    public void setValues(List<String> values) {
        this.values = Optional.ofNullable(values).orElse(Lists.newArrayList())
                .stream().map(IntervalNumber::new).collect(Collectors.toList());
    }


    public boolean match(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {

            throw new IllegalArgumentException("param is null");
        }
        if (IntervalTypeEnum.ANY.equals(intervalType)) {
            return true;
        }

        if (Objects.nonNull(scale) && scale.compareTo(bigDecimal.scale()) < 0) {
            // 小数位不符合
            return false;
        }

        IntervalNumber firstIntervalNumber = values.get(0);
        switch (intervalType) {
            case EQUAL: {
                return firstIntervalNumber.isEqualTo(bigDecimal);
            }
            case NOT_EQUAL: {
                return !firstIntervalNumber.isEqualTo(bigDecimal);
            }
            default: {
                break;
            }
        }

        IntervalNumber secondIntervalNumber = values.get(1);
        boolean leftOpen = firstIntervalNumber.isLessThan(bigDecimal);
        boolean rightOpen = secondIntervalNumber.isGreaterThan(bigDecimal);
        boolean leftClose = firstIntervalNumber.isLessThan(bigDecimal) || firstIntervalNumber.isEqualTo(bigDecimal);
        boolean rightClose = secondIntervalNumber.isGreaterThan(bigDecimal) || secondIntervalNumber.isEqualTo(bigDecimal);
        switch (intervalType) {
            case ALL_OPEN: {
                return leftOpen && rightOpen;
            }
            case ALL_CLOSE: {
                return leftClose && rightClose;
            }
            case LEFT_OPEN: {
                return leftOpen && rightClose;
            }
            case RIGHT_OPEN: {
                return leftClose && rightOpen;
            }
            default:
                throw new IllegalArgumentException("no intervalType found!");
        }
    }

    public List<String> values() {

        return getOriginValues().stream().map(IntervalNumber::getValue).collect(Collectors.toList());
    }

    public void values(List<String> values) {

        this.values = values.stream().map(IntervalNumber::new).collect(Collectors.toList());
    }



    public void setScale(Integer scale) {
        this.scale = scale;
    }


    public BigDecimal rangeNumber() {
        if (!this.getOriginIntervalType().isRangeInterval()) {
            throw new IllegalArgumentException("不是范围区间");
        }
        IntervalNumber intervalNumber1 = this.getOriginValues().get(0);
        IntervalNumber intervalNumber2 = this.getOriginValues().get(1);
        ;
        if (!intervalNumber1.isNotInfinity() || !intervalNumber2.isNotInfinity()) {
            throw new IllegalArgumentException("存在无尽数，无法比较");
        }
        return intervalNumber2.convertToBigDecimal().subtract(intervalNumber1.convertToBigDecimal());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Interval interval = (Interval) o;
        return intervalType == interval.intervalType &&
               collectionEqualsIgnoreOrderAndDuplicate(values, interval.values);
    }

    @Override
    public int hashCode() {
        return com.google.common.base.Objects.hashCode(intervalType, values);
    }

    /**
     * 判断两个list是否相同，忽略元素的顺序和重复
     * e.g:
     * - 忽略顺序: [1,2,3] 与 [3,2,1] 相同
     * - 忽略重复: [1,1,2] 与 [1,2,2] 相同
     */
    private static Boolean collectionEqualsIgnoreOrderAndDuplicate(Collection<?> list1, Collection<?> list2) {
        //都是空，那么是相等的
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return true;
        }
        //只有一边是空的，那么不相等
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return false;
        }

        return list1.containsAll(list2) && list2.containsAll(list1);
    }
}
