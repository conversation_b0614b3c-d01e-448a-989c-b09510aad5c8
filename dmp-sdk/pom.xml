<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.shangou</groupId>
        <artifactId>logistics-dmp</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dmp-sdk</artifactId>
    <version>${revision}</version>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>zebra-xframe-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-publisher</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-common-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.sankuai.shangou.utility</groupId>
                <artifactId>swift-gw-maven-plugin</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <configuration>
                    <appKey>com.sankuai.shangou.logistics.dmp</appKey> <!-- 指定appKey,必填 -->
                    <interfaceName>com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService</interfaceName> <!-- 指定接口路径，必填，注意这个接口是一个配置示例 -->
                    <checkPath>false</checkPath> <!-- 指定是否要校验api的path,可选,默认不校验,如果为true,则会按照API规范来校验-->
                    <flatmap>true</flatmap> <!-- 是否将请求参数打平展开映射,可选,默认不展开, 见上述功能清单第6点 -->
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>