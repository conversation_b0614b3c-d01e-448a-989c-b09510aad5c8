package com.sankuai.shangou.supplychain.tof.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.qnh.ofc.ofw.client.thrift.common.base.OfcStatus;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.DeliveryDimensionPoiQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.DeliveryDimensionPoiQueryResponse;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryCompleteMode;
import com.sankuai.shangou.logistics.delivery.configure.value.DeliveryCompleteMode;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.qnh.ofc.ebase.consts.OrderTypeEnum;
import com.sankuai.shangou.qnh.ofc.ebase.request.QueryFulfillConfigRequest;
import com.sankuai.shangou.qnh.ofc.ebase.response.QueryFulfillConfigResponse;
import com.sankuai.shangou.qnh.ofc.ebase.service.FulfillmentStoreConfigThriftService;
import com.sankuai.shangou.supplychain.tof.assembler.SelfDeliveryOrderAssembler;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryCompleteConfigVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.store.*;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StoreConfigService {

    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;

    @Resource
    private SelfDeliveryOrderAssembler selfDeliveryOrderAssembler;

    @Resource
    private DeliveryConfigurationThriftService deliveryConfigurationThriftService;
    @Resource
    private FulfillmentStoreConfigThriftService fulfillmentStoreConfigThriftService;

    public OperationModeVO getOperationMode(Long tenantId, Long storeId) {
        OperationModeVO operationModeVO = new OperationModeVO();
        QueryFulfillConfigRequest request = new QueryFulfillConfigRequest();
        request.setTenantId(tenantId);
        request.setWarehouseId(storeId);
        request.setOrderType(OrderTypeEnum.SALE_TYPE.getCode());
        QueryFulfillConfigResponse response = fulfillmentStoreConfigThriftService.queryFulfillConfig(request);
        if (Objects.equals(response.getCode(), OfcStatus.SUCCESS.getCode()) && Objects.nonNull(response.getFulfillConfig())) {
            operationModeVO.setPickDeliverySplitType(response.getFulfillConfig().getPickDeliveryWorkMode());
            return operationModeVO;
        }


        if (!MccConfigUtil.checkIsDrunkHorseTenant(tenantId)) {
            operationModeVO.setPickDeliverySplitType(2);
            return operationModeVO;
        }
        TResult<SelfDeliveryPoiConfigDTO> result = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(tenantId, storeId);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            throw new BizException("查询错误配送配置错误");
        }
        operationModeVO.setPickDeliverySplitType(result.getData().getEnablePickDeliverySplit());
        return operationModeVO;
    }

    public SortModeVO getSortMode(Long tenantId, Long storeId) {
        SortModeVO sortModeVO = new SortModeVO();
        if (!MccConfigUtil.checkIsDrunkHorseTenant(tenantId)) {
            sortModeVO.setDeliveryCompleteSortType(2);
            return sortModeVO;
        }
        // 优先使用新的配送门店维度配置
        DeliveryDimensionPoiQueryResponse.DeliveryDimensionPoiDto deliveryConfig = queryDeliveryDimensionConfig(tenantId, storeId);
        if (deliveryConfig != null && deliveryConfig.getCompletedSortMode() != null) {
            sortModeVO.setDeliveryCompleteSortType(deliveryConfig.getCompletedSortMode());
            return sortModeVO;
        }

        // 兜底使用默认配置
        sortModeVO.setDeliveryCompleteSortType(1);
        return sortModeVO;
    }

    public InternallyNavigationVO getInternallyNavigation(Long tenantId, Long storeId) {
        InternallyNavigationVO internallyNavigationVO = new InternallyNavigationVO();
        // 优先使用新的配送门店维度配置
        DeliveryDimensionPoiQueryResponse.DeliveryDimensionPoiDto deliveryConfig = queryDeliveryDimensionConfig(tenantId, storeId);
        if (deliveryConfig != null && deliveryConfig.getInternalNavigationMode() != null) {
            internallyNavigationVO.setNavigationType(deliveryConfig.getInternalNavigationMode());
            return internallyNavigationVO;
        }


        // 兜底使用原来的配置逻辑
        internallyNavigationVO.setNavigationType(1);
        if (!MccConfigUtil.checkIsDrunkHorseTenant(tenantId)) {
            return internallyNavigationVO;
        }
        if (GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, "internally-navigation")) {
            internallyNavigationVO.setNavigationType(2);
        }
        return internallyNavigationVO;
    }

    public DeliveryCompleteModeVO getDeliveryCompleteMode(Long tenantId, Long storeId) {

        DeliveryCompleteModeVO deliveryCompleteModeVO = new DeliveryCompleteModeVO();
        if (!MccConfigUtil.checkIsDrunkHorseTenant(tenantId)) {
            deliveryCompleteModeVO.setDistanceReminder(0);
            return deliveryCompleteModeVO;
        }
        // 优先使用新的配送门店维度配置
        DeliveryDimensionPoiQueryResponse.DeliveryDimensionPoiDto deliveryConfig = queryDeliveryDimensionConfig(tenantId, storeId);
        if (deliveryConfig != null && StringUtils.isNotBlank(deliveryConfig.getDeliveryCompleteMode())) {
            try {
                DeliveryCompleteMode deliveryCompleteMode = JSON.parseObject(deliveryConfig.getDeliveryCompleteMode(), DeliveryCompleteMode.class);
                if (deliveryCompleteMode != null) {
                    return convertToDeliveryCompleteModeVO(deliveryCompleteMode);
                }
            } catch (Exception e) {
                log.error("解析 deliveryCompleteMode 配置失败, tenantId: {}, storeId: {}, config: {}",
                         tenantId, storeId, deliveryConfig.getDeliveryCompleteMode(), e);
            }
        }

        // 兜底使用原来的配置
        DeliveryCompleteConfigVO deliveryCompleteConfigVO = selfDeliveryOrderAssembler.getDeliveryCompleteConfig();
        deliveryCompleteModeVO.setDistanceReminder(1);
        deliveryCompleteModeVO.setDeliveryCompleteConfig(deliveryCompleteConfigVO);
        return deliveryCompleteModeVO;
    }

    @Deprecated
    public ManagerTerminalModeVO getManagerTerminalMode(Long tenantId, Long storeId) {
        ManagerTerminalModeVO managerTerminalModeVO = new ManagerTerminalModeVO();

        if (MccConfigUtil.needCheckPickFinishTenants().contains(tenantId)) {
            managerTerminalModeVO.setPickFinishCheck(1);
        } else {
            managerTerminalModeVO.setPickFinishCheck(0);
        }
        return managerTerminalModeVO;
    }

    //研发配置
    public ShowCompletedDeliveryStatisticConfigVO getOrderStatisticConfig(Long tenantId, Long storeId) {
        ShowCompletedDeliveryStatisticConfigVO showCompletedDeliveryStatisticConfigVO = new ShowCompletedDeliveryStatisticConfigVO();

        if (MccConfigUtil.needDeliveryStatsTenants().contains(tenantId)) {
            showCompletedDeliveryStatisticConfigVO.setShowOneYuanOrderCount(1);
            showCompletedDeliveryStatisticConfigVO.setShowRiskControlOrderCount(1);
        } else {
            showCompletedDeliveryStatisticConfigVO.setShowOneYuanOrderCount(0);
            showCompletedDeliveryStatisticConfigVO.setShowRiskControlOrderCount(0);
        }
        return showCompletedDeliveryStatisticConfigVO;
    }

    /**
     * 查询配送门店维度配置
     */
    public DeliveryDimensionPoiQueryResponse.DeliveryDimensionPoiDto queryDeliveryDimensionConfig(Long tenantId, Long storeId) {
        try {
            DeliveryDimensionPoiQueryRequest request = new DeliveryDimensionPoiQueryRequest();
            request.setTenantId(tenantId);
            request.setStoreId(storeId);

            DeliveryDimensionPoiQueryResponse response = deliveryConfigurationThriftService.queryDeliveryDimensionPoi(request);
            if (response != null && response.getStatus() != null && response.getStatus().getCode() == 0) {
                return response.getDeliveryDimensionPoi();
            }
        } catch (Exception e) {
            log.error("查询配送门店维度配置失败, tenantId={}, storeId={}", tenantId, storeId, e);
        }
        return null;
    }

    /**
     * 将 DeliveryCompleteMode 转换为 DeliveryCompleteModeVO
     */
    private DeliveryCompleteModeVO convertToDeliveryCompleteModeVO(DeliveryCompleteMode source) {
        if (source == null) {
            return null;
        }

        DeliveryCompleteModeVO target = new DeliveryCompleteModeVO();
        target.setDistanceReminder(source.getDistanceReminder());

        if (source.getDeliveryCompleteConfig() != null) {
            target.setDeliveryCompleteConfig(convertToDeliveryCompleteConfigVO(source.getDeliveryCompleteConfig()));
        }

        return target;
    }

    /**
     * 将 DeliveryCompleteConfig 转换为 DeliveryCompleteConfigVO
     */
    private DeliveryCompleteConfigVO convertToDeliveryCompleteConfigVO(DeliveryCompleteMode.DeliveryCompleteConfig source) {
        if (source == null) {
            return null;
        }

        DeliveryCompleteConfigVO target = new DeliveryCompleteConfigVO();
        target.setOperationMode(source.getOperationMode());
        target.setSendPicToCustomerTips(source.getSendPicToCustomerTips());
        target.setNotContactCustomerTips(source.getNotContactCustomerTips());
        target.setUploadImageDurationThreshold(source.getUploadImageDurationThreshold());
        target.setIsShowNotContactCustomerTips(source.getIsShowNotContactCustomerTips());

        // 转换示例图片信息列表
        if (source.getAllExamplePicInfoList() != null) {
            List<DeliveryCompleteConfigVO.ExamplePicInfo> examplePicInfoList = source.getAllExamplePicInfoList().stream()
                    .map(this::convertToExamplePicInfoVO)
                    .collect(Collectors.toList());
            target.setAllExamplePicInfoList(examplePicInfoList);
        }

        // 转换特殊商品上传图片配置列表
        if (source.getSpecialProductUploadPicConfig() != null) {
            List<DeliveryCompleteConfigVO.ProductUploadPicConfig> productUploadPicConfigList = source.getSpecialProductUploadPicConfig().stream()
                    .map(this::convertToProductUploadPicConfigVO)
                    .collect(Collectors.toList());
            target.setSpecialProductUploadPicConfig(productUploadPicConfigList);
        }

        return target;
    }

    /**
     * 转换示例图片信息
     */
    private DeliveryCompleteConfigVO.ExamplePicInfo convertToExamplePicInfoVO(DeliveryCompleteMode.ExamplePicInfo source) {
        if (source == null) {
            return null;
        }

        return new DeliveryCompleteConfigVO.ExamplePicInfo(
                source.getType(),
                source.getName(),
                source.getPicUrl(),
                source.getOrder()
        );
    }

    /**
     * 转换特殊商品上传图片配置
     */
    private DeliveryCompleteConfigVO.ProductUploadPicConfig convertToProductUploadPicConfigVO(DeliveryCompleteMode.SpecialProductUploadPicConfig source) {
        if (source == null) {
            return null;
        }

        // 需要将 String 类型的 productType 转换为 Integer
        Integer productTypeInt = null;
        if (StringUtils.isNotBlank(source.getProductType())) {
            try {
                productTypeInt = Integer.valueOf(source.getProductType());
            } catch (NumberFormatException e) {
                log.warn("无法将 productType 转换为 Integer: {}", source.getProductType());
                // 如果转换失败，可以设置一个默认值或者保持为 null
                productTypeInt = null;
            }
        }

        return new DeliveryCompleteConfigVO.ProductUploadPicConfig(
                productTypeInt,
                source.getPicTypeList(),
                source.getIsForceUploadPic(),
                source.getNeedUploadPicCount()
        );
    }
}
