package com.sankuai.shangou.supplychain.tof.assembler;

import com.dianping.lion.client.Lion;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.request.OCMSOperateCheckRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.AuthPermissionAndDataAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.AuthPermissionAndDataAuthResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.DeliveryDimensionPoiQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.DeliveryDimensionPoiQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import com.sankuai.shangou.logistics.warehouse.dto.MaterialTransInfoDto;
import com.sankuai.shangou.logistics.warehouse.dto.TradeOrderKey;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.CompensateType;
import com.sankuai.shangou.logistics.warehouse.message.CompensateFulfillmentOrderMessage;
import com.sankuai.shangou.supplychain.tof.component.*;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.convert.RiderDeliveryConvert;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.*;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.AggregateQueryReq;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.RevenueDetailVo;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.enums.AuthCodeEnum;
import com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum;
import com.sankuai.shangou.supplychain.tof.enums.OrderCouldOperateItem;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.wrapper.DepotGoodsWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OSWServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OutboundServiceWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum.*;
import static com.sankuai.shangou.supplychain.tof.enums.ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO;

/**
 * <AUTHOR>
 * @date 2024-11-06
 * @email <EMAIL>
 */
@Slf4j
@Service
public class AggregateOrderAssembler {

    @Resource
    private SelfDeliveryOrderAssembler selfDeliveryOrderAssembler;
    @Resource
    private DeliveryOrderComponent deliveryOrderComponent;
    @Resource
    private SelfDeliveryPickingAssembler selfDeliveryPickingAssembler;
    @Autowired
    private PickingOrderComponent pickingOrderComponent;
    @Resource
    private RevenueComponent revenueComponent;
    @Resource
    private DeliveryExceptionComponent deliveryExceptionComponent;
    @Resource
    private QuestionnaireComponent questionnaireComponent;
    @Resource
    private ProductHighPriceTagComponent productHighPriceTagComponent;
    @Resource
    private AbnormalOrderComponent abnormalOrderComponent;
    @Resource
    private TurnDeliveryButtonComponent turnDeliveryButtonComponent;
    @Resource
    private SealDeliveryTagComponent sealDeliveryTagComponent;
    @Resource
    private AuthThriftService.Iface authThriftService;
    @Resource
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;
    @Resource
    private AuthenticateService authenticateService;
    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;
    @Resource
    private OSWServiceWrapper oswServiceWrapper;
    @Resource
    private OutboundServiceWrapper outboundServiceWrapper;
    @Resource
    private DepotGoodsWrapper depotGoodsWrapper;
    @Resource
    private DeliveryConfigurationThriftService deliveryConfigurationThriftService;


    private static final List<Integer> TO_CHECK_ITEMS = org.assertj.core.util.Lists.newArrayList(
            OrderCanOperateItem.COMPLETE_PICK.getValue(),
            OrderCanOperateItem.PRINT_RECEIPT.getValue(),
            OrderCanOperateItem.PART_ORDER_REFUND.getValue(),
            OrderCanOperateItem.FULL_ORDER_REFUND.getValue(),
            OrderCanOperateItem.WEIGHT_REFUND.getValue(),
            OrderCanOperateItem.AFTER_SALE_REFUND.getValue(),
            OrderCanOperateItem.SELF_FETCH_FINISH.getValue(),
            OrderCanOperateItem.MONEY_REFUND.getValue(),
            OrderCanOperateItem.DISPATCH_ORDER.getValue()
    );


    public List<AggregateOrderVO> aggregateOrderComponent(AggregateQueryReq request) {

        if (LionConfigUtils.newAggregateQueryFallback()) {
            throw new SystemException("聚合接口已降级");
        }

        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();

        List<AggregateOrderVO> resultList = Lists.newArrayList();
        AggregateContext aggregateContext = new AggregateContext();

        //拣货相关列表，目前只有歪马请求
        if (LionConfigUtils.isQueryPickInfo(tenantId)) {
            appendInProcessPickingOrder(resultList, aggregateContext, request.getLastUpdateTime());
        }

        //运单相关列表
        appendInProcessDeliveryOrder(resultList, aggregateContext, request.getLastUpdateTime());

        enrichAggregateListWithExtInfo(
                resultList,
                Arrays.asList(ComponentTypeEnum.ORDER_REVENUE_INFO,
                        ComponentTypeEnum.LACK_STOCK,
                        ComponentTypeEnum.TURN_DELIVERY_BUTTON_INFO,
                        ComponentTypeEnum.DELIVERY_EXCEPTION_INFO,
                        ComponentTypeEnum.QUESTIONNAIRE,
                        ComponentTypeEnum.SEAL_DELIVER,
                        ComponentTypeEnum.USE_ASSESS_TIME),
                aggregateContext.getTradeOrderVOMap()
        );

        return resultList;
    }

    private void appendInProcessPickingOrder(List<AggregateOrderVO> resultList, AggregateContext aggregateContext, long lastUpdateTime) {
        List<TradeShippingOrderDTO> tradeShippingOrderDTOS = pickingOrderComponent.queryAllTradeShippingOrders(LoginContextUtils.getAppLoginStoreId(), LoginContextUtils.getAppLoginAccountId(), lastUpdateTime);
        if (CollectionUtils.isEmpty(tradeShippingOrderDTOS)) {
            return;
        }

        List<List<TradeShippingOrderDTO>> partitionTradeShippingOrderDTOS = Lists.partition(tradeShippingOrderDTOS, 20);
        for (List<TradeShippingOrderDTO> partitionTradeShippingOrderDTO : partitionTradeShippingOrderDTOS) {
            List<RiderPickingOrderVO> riderPickingOrderVOList = selfDeliveryPickingAssembler.queryBasePickingListTemplate(
                    () -> partitionTradeShippingOrderDTO, aggregateContext
            );
            log.info("appendInProcessPickingOrder, riderPickingOrderVOList = {}", riderPickingOrderVOList);
            if (CollectionUtils.isNotEmpty(riderPickingOrderVOList)) {
                resultList.addAll(riderPickingOrderVOList);
            }
        }
    }

    private void appendInProcessDeliveryOrder(List<AggregateOrderVO> resultList, AggregateContext aggregateContext, long lastUpdateTime) {
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> allDeliveryOrdersMap = deliveryOrderComponent.getAllDeliveryOrders(lastUpdateTime);
        if (MapUtils.isEmpty(allDeliveryOrdersMap)) {
            return;
        }

        List<TradeOrderInfoComponent.TradeOrderKey> existOrderKey = resultList.stream()
                .map(aggregateOrderVO -> {
                    if (aggregateOrderVO instanceof RiderDeliveryOrderVO) {
                        RiderDeliveryOrderVO riderDeliveryOrderVO = (RiderDeliveryOrderVO) aggregateOrderVO;
                        return new TradeOrderInfoComponent.TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(riderDeliveryOrderVO.getChannelId()).getValue(), riderDeliveryOrderVO.getChannelOrderId());
                    } else {
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());

        List<LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> partitionRiderDeliveryOrderMap = Lists.partition(new ArrayList<>(allDeliveryOrdersMap.entrySet()), 20)
                .stream()
                .map(entryList -> entryList.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (o1, o2) -> o1, LinkedHashMap::new)))
                .collect(Collectors.toList());

        for (LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> tRiderDeliveryOrderMap : partitionRiderDeliveryOrderMap) {
            List<RiderDeliveryOrderVO> riderDeliveryOrderVOList = selfDeliveryOrderAssembler.queryBaseRiderOrderTemplate(() -> tRiderDeliveryOrderMap, aggregateContext);
            log.info("appendInProcessDeliveryOrder, riderDeliveryOrderVOList = {}", riderDeliveryOrderVOList);
            if (CollectionUtils.isNotEmpty(riderDeliveryOrderVOList)) {
                for (RiderDeliveryOrderVO riderDeliveryOrderVO : riderDeliveryOrderVOList) {
                    if (existOrderKey.contains(new TradeOrderInfoComponent.TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(riderDeliveryOrderVO.getChannelId()).getValue(), riderDeliveryOrderVO.getChannelOrderId()))) {
                        continue;
                    }
                    resultList.add(riderDeliveryOrderVO);
                }
            }

        }
    }


    private void enrichAggregateListWithExtInfo(
            List<AggregateOrderVO> baseAggregateOrders,
            List<ComponentTypeEnum> extInfoEnums,
            Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap) {

        Long tenantId = LoginContextUtils.getAppLoginTenant();
        Long storeId = LoginContextUtils.getAppLoginStoreId();

        if (CollectionUtils.isEmpty(baseAggregateOrders)) {
            return;
        }

        //发财酒信息
        Map<String, Pair<MaterialTransInfoDto, DepotGoodsDetailDto>> tradeOrderNoAndMaterialSkuInfoMap = getTradeOrderNoAndMaterialSkuInfoMap(baseAggregateOrders, tenantId, storeId);

        for (List<AggregateOrderVO> aggregateOrderVOS : Lists.partition(baseAggregateOrders, 20)) {
            // 获取所有订单键
            List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys = getAllTradeOrderKeys(aggregateOrderVOS);

            // 处理补偿逻辑
            if (extInfoEnums.contains(CHECK_PICK_AND_DELIVERY_ORDER_STATUS)) {
//            handleCompensationLogic(aggregateOrderVOS);
            }

            // 处理其他扩展信息
            Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> exceptionSummaryVOMap = handleDeliveryExceptionInfo(extInfoEnums, allTradeOrderKeys);
            Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> revenueDetailVoMap = handleOrderRevenueInfo(extInfoEnums, tenantId, allTradeOrderKeys);
            Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap = handleQuestionnaireInfo(extInfoEnums, aggregateOrderVOS);
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> highPriceTagMap = handleHighPriceTag(extInfoEnums, allTradeOrderKeys);
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> lackStockMap = handleLackStockInfo(extInfoEnums, allTradeOrderKeys);
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap = handleSealDeliveryTag(extInfoEnums, tenantId, storeId, allTradeOrderKeys);
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> couldTurnDapDeliveryMap = handleTurnDeliveryButtonInfo(extInfoEnums, tenantId, storeId, allTradeOrderKeys, tradeOrderVOMap, sealDeliveryTagMap);

            //三方模块
            List<RiderDeliveryOrderVO> thirdDeliveryOrderVOS = aggregateOrderVOS.stream()
                    .filter(order -> order instanceof RiderDeliveryOrderVO)
                    .map(order -> ((RiderDeliveryOrderVO) order))
                    .filter(order -> !Objects.equals(order.getDeliveryChannelId(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()))
                    .collect(Collectors.toList());
            Map<Integer, DeliveryChannelDto> deliveryChannelDtoMap = Maps.newHashMap();
            Map<String, String> urlMap = Maps.newHashMap();
            Map<TradeOrderInfoComponent.TradeOrderKey, List<Integer>> couldOperateMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(thirdDeliveryOrderVOS)) {
                Set<Integer> channelCodes = thirdDeliveryOrderVOS.stream().map(RiderDeliveryOrderVO::getDeliveryChannelId).collect(Collectors.toSet());
                deliveryChannelDtoMap = deliveryOrderComponent.getDeliveryChannelDtoMap(channelCodes);
                urlMap = deliveryOrderComponent.batchQueryOrderDeliveryUrl(LoginContextUtils.getAppLoginTenant(), LoginContextUtils.getAppLoginStoreId(), IListUtils.mapTo(thirdDeliveryOrderVOS, RiderDeliveryOrderVO::getEmpowerOrderId), null);

                Map<Integer,Boolean> accountAuthCodeMap = queryAccountCode();
                Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap = Maps.newHashMap();
                OCMSOperateCheckRequest checkRequest = new OCMSOperateCheckRequest();
                checkRequest.setTenantId(tenantId);
                checkRequest.setToCheckOperateItems(TO_CHECK_ITEMS);
                checkRequest.setOrderList(thirdDeliveryOrderVOS.stream().map(e -> OCMSOrderKey.builder()
                        .channelOrderId(e.getChannelOrderId())
                        .channelType(e.getChannelId())
                        .build()).collect(Collectors.toList()));
                OCMSOperateCheckResponse response = null;
                try {
                    response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(checkRequest);
                    log.info("查询订单可操作项，request:{}, response:{}", checkRequest, response);
                    if (response.getCouldOperateItems() != null && !response.getCouldOperateItems().isEmpty()) {
                        ocmsOrderKeyListMap.putAll(response.getCouldOperateItems());
                    }
                } catch (TException e) {
                    log.error("查询可操作列表失败,request:{}", checkRequest, e);
                }
                //设置可操作项
                setCouldOperateItem(tradeOrderVOMap, thirdDeliveryOrderVOS, ocmsOrderKeyListMap, accountAuthCodeMap, couldOperateMap);
            }

            // 更新 RiderPickingOrderVO 列表
            RiderDeliveryConvert.updateRiderPickingOrderVOList(aggregateOrderVOS, exceptionSummaryVOMap, revenueDetailVoMap,
                    questionnaireMap, couldTurnDapDeliveryMap, lackStockMap, highPriceTagMap, sealDeliveryTagMap, deliveryChannelDtoMap, urlMap, couldOperateMap, tradeOrderNoAndMaterialSkuInfoMap);
        }
    }

    private DeliveryDimensionPoiQueryResponse.DeliveryDimensionPoiDto queryDeliveryDimensionPoiConfig(Long tenantId, Long storeId) {
        try {
            DeliveryDimensionPoiQueryResponse.DeliveryDimensionPoiDto deliveryDimensionPoi = null;
            DeliveryDimensionPoiQueryResponse response = deliveryConfigurationThriftService.queryDeliveryDimensionPoi(new DeliveryDimensionPoiQueryRequest(tenantId, storeId));
            if (Objects.nonNull(response.getDeliveryDimensionPoi())) {
                deliveryDimensionPoi = response.getDeliveryDimensionPoi();
            }
            return deliveryDimensionPoi;
        } catch (Exception e) {
            log.error("invoke deliveryConfigurationThriftService.queryDeliveryDimensionPoi error", e);
            return null;
        }
    }

    private Map<String, Pair<MaterialTransInfoDto, DepotGoodsDetailDto>> getTradeOrderNoAndMaterialSkuInfoMap(List<AggregateOrderVO> baseAggregateOrders, Long tenantId, Long storeId) {
        try {
            List<RiderDeliveryOrderVO> hasFacaiWineOrders = baseAggregateOrders.stream()
                    .filter(order -> order instanceof RiderDeliveryOrderVO)
                    .map(order -> ((RiderDeliveryOrderVO) order))
                    .filter(RiderDeliveryOrderVO::getIsFacaiWine)
                    .collect(Collectors.toList());
            Map<String, Pair<MaterialTransInfoDto, DepotGoodsDetailDto>> tradeOrderNoAndMaterialSkuInfoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(hasFacaiWineOrders)) {
                //查询发财酒情况
                Map<String, MaterialTransInfoDto> tradeOrderNoAndTransInfoMap = outboundServiceWrapper.queryTransMaterialInfo(
                        LoginContextUtils.getAppLoginAccountId(),
                        IListUtils.mapTo(hasFacaiWineOrders, riderDeliveryOrderVO -> {
                            TradeOrderKey tradeOrderKey = new TradeOrderKey();
                            tradeOrderKey.setTradeOrderNo(riderDeliveryOrderVO.getChannelOrderId());
                            tradeOrderKey.setTradeChannelType(DynamicOrderBizType.channelId2OrderBizType(riderDeliveryOrderVO.getChannelId()).getValue());
                            return tradeOrderKey;
                        })
                );
                //查询发财酒货品信息
                if (MapUtils.isNotEmpty(tradeOrderNoAndTransInfoMap)) {
                    List<String> facaiWineSkuIds = IListUtils.mapTo(tradeOrderNoAndTransInfoMap.values(), MaterialTransInfoDto::getMaterialSkuId);
                    Map<String, DepotGoodsDetailDto> skuIdAndDepotGoodsMap = depotGoodsWrapper.queryBySkuId(tenantId, storeId, facaiWineSkuIds);
                    tradeOrderNoAndMaterialSkuInfoMap = IListUtils.nullSafeStream(tradeOrderNoAndTransInfoMap.entrySet())
                            .filter(entry -> skuIdAndDepotGoodsMap.containsKey(entry.getValue().getMaterialSkuId()))
                            .collect(Collectors.toMap(Map.Entry::getKey, entry -> Pair.of(entry.getValue(), skuIdAndDepotGoodsMap.get(entry.getValue().getMaterialSkuId()))));
                }
            }
            return tradeOrderNoAndMaterialSkuInfoMap;
        } catch (Exception e) {
            log.error("getTradeOrderNoAndMaterialSkuInfoMap error", e);
            return Maps.newHashMap();
        }
    }

    private void setCouldOperateItem(Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap, List<RiderDeliveryOrderVO> thirdDeliveryOrderVOS, Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap, Map<Integer, Boolean> accountAuthCodeMap, Map<TradeOrderInfoComponent.TradeOrderKey, List<Integer>> couldOperateMap) {
        thirdDeliveryOrderVOS.forEach(order -> {
            List<Integer> couldOperateItems = org.assertj.core.util.Lists.newArrayList();
            ocmsOrderKeyListMap.entrySet().stream()
                    .filter(entry -> StringUtils.equals(entry.getKey().getChannelOrderId(), order.getChannelOrderId())
                            && entry.getKey().getChannelType() == order.getChannelId())
                    .findFirst()
                    .ifPresent(entry -> {
                        if (CollectionUtils.isNotEmpty(entry.getValue())) {
                            couldOperateItems.addAll(entry.getValue());
                        }
                    });

            //权限code与订单可操作项diff
            List<Integer> finalCouldOperateItems = couldOperateItems.stream().filter(operateItem->{
                return Objects.isNull(accountAuthCodeMap.get(operateItem)) || BooleanUtils.isTrue(accountAuthCodeMap.get(operateItem));//没有配置，或者权限没有返回都当做有权限
            }).collect(Collectors.toList());
            log.info("权限过滤，order:{}, account:{}, couldOperateItems:{}, finalCouldOperateItems:{}", order.getChannelOrderId(), LoginContextUtils.getAppLoginAccountId(), couldOperateItems, finalCouldOperateItems);
            List<Integer> deliveryChannelButtons = getDeliveryChannelButtons(order.getTenantId(), order.getStoreId(), order, tradeOrderVOMap.get(new TradeOrderInfoComponent.TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(order.getChannelId()).getValue(), order.getChannelOrderId())));
            finalCouldOperateItems.addAll(deliveryChannelButtons);

            couldOperateMap.put(new TradeOrderInfoComponent.TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(order.getChannelId()).getValue(), order.getChannelOrderId()), finalCouldOperateItems);
        });
    }

    private List<Integer> getDeliveryChannelButtons(long tenantId, long storeId, RiderDeliveryOrderVO order, TradeOrderVO tradeOrderVO) {

        try {

            //1.操作人要有权限
            Map<String, Boolean> permissions = isHasPermissionV2(org.assertj.core.util.Lists.newArrayList(AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode()));
            boolean hasTurnSelfAuth = permissions.getOrDefault(AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode(), Boolean.FALSE);

            //校验转三方配置
            TResult<SelfDeliveryPoiConfigDTO> selfDeliveryPoiConfigDTOTResult = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(tenantId, storeId);
            if (!selfDeliveryPoiConfigDTOTResult.isSuccess() || Objects.isNull(selfDeliveryPoiConfigDTOTResult.getData())) {
                throw new BizException("查询错误配送配置错误");
            }

            boolean orderStatusAfterConfirmed = tradeOrderVO.getOrderStatus() >= OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
            boolean orderStatusNotCompleted = !Objects.equals(tradeOrderVO.getOrderStatus(), OrderStatusEnum.COMPLETED.getValue()) && !Objects.equals(tradeOrderVO.getOrderStatus(), OrderStatusEnum.CANCELED.getValue());
            log.info("hasTurnSelfAuth = {}, orderStatusAfterConfirmed = {}, orderStatusNotCompleted = {}", hasTurnSelfAuth, orderStatusAfterConfirmed, orderStatusNotCompleted);
            //有权限 && 接单<订单状态<终态
            if (hasTurnSelfAuth && orderStatusAfterConfirmed && orderStatusNotCompleted) {
                return Lists.newArrayList(OrderCouldOperateItem.TURN_SELF_DELIVERY.getValue());
            }

            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("fillDeliveryOperateItemsForAllOrder error", e);
            return Lists.newArrayList();
        }
    }

    private Map<String, Boolean> isHasPermissionV2(List<String> currPermissionCodes) {
        try {
            long accountId = LoginContextUtils.getAppLoginAccountId();
            int appId = AppLoginContextHolder.getAppLoginContext().getAppId();
            return accountAuthPermissions(accountId, appId, currPermissionCodes);
        }catch (Exception e){
            log.error("查询权限元素权限失败", e);
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }

    private Map<String, Boolean> accountAuthPermissions(long accountId, int authId, List<String> permissionCodes) {
        if (CollectionUtils.isEmpty(permissionCodes)) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> result = permissionCodes
                .stream()
                .distinct()
                .collect(Collectors.toMap(Function.identity(),
                        s -> false));
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setAppId(authId);
        request.setPermissionCodes(permissionCodes);
        AccountAuthPermissionsResponse response;
        try {
            log.info("批量查询权限code是否有权限 request:{}", request);
            response = authenticateService.accountAuthPermissions(request);
            log.info("批量查询权限code是否有权限 response:{}", response);
        }
        catch (Exception e) {
            log.error("批量查询权限code是否有权限异常 request:{}", request);
            return result;
        }
        if (response == null || response.sacStatus == null || response.sacStatus.code != 0) {
            log.error("批量查询权限code是否有权限失败,request:{},response:{}", request, response);
            return result;
        }
        if (MapUtils.isNotEmpty(response.getAuthResult())) {
            response.getAuthResult().forEach(result::put);
        }
        return result;
    }


    private CompensateFulfillmentOrderMessage buildCompensateMessageFromShippingOrder(TradeShippingOrderDTO tradeShippingOrderDTO) {
        CompensateFulfillmentOrderMessage message = new CompensateFulfillmentOrderMessage();
        message.setMerchantId(tradeShippingOrderDTO.getMerchantId());
        message.setWarehouseId(tradeShippingOrderDTO.getWarehouseId());
        message.setTradeOrderNo(tradeShippingOrderDTO.getTradeOrderNo());
        message.setTradeOrderBizType(tradeShippingOrderDTO.getTradeChannelType());
        message.setCompensateForPickType(CompensateType.COMPENSATE_FOR_NEW_DELIVERY.getCode());
        return message;
    }


    private List<TradeOrderInfoComponent.TradeOrderKey> getAllTradeOrderKeys(List<AggregateOrderVO> orders) {
        return orders.stream()
                .filter(order -> order instanceof RiderDeliveryOrderVO)
                .map(order -> (RiderDeliveryOrderVO) order)
                .map(order -> new TradeOrderInfoComponent.TradeOrderKey(DynamicOrderBizType.channelId2OrderBizType(order.getChannelId()).getValue(), order.getChannelOrderId()))
                .collect(Collectors.toList());
    }

//    private void handleCompensationLogic(List<AggregateOrderVO> orders) {
//        for (RiderPickingOrderVO order : orders) {
//            if (Objects.isNull(order.getDeliveryOrderVO())) {
//                Cat.logEvent("PICK_DELIVERY_MISMATCH", "DELIVERY_MISS");
//                sendCompensateForDeliveryMessage(order.getPickingOrderVO());
//            }
//        }
//    }

    private Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryExceptionSummaryVO> handleDeliveryExceptionInfo(
            List<ComponentTypeEnum> extInfoEnums, List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys) {
        if (extInfoEnums.contains(DELIVERY_EXCEPTION_INFO)) {
            try {
                return deliveryExceptionComponent.queryRiderReportException(allTradeOrderKeys);
            } catch (Exception e) {
                log.error("查询配送异常失败", e);
            }
        }
        return new HashMap<>();
    }

    private Map<TradeOrderInfoComponent.TradeOrderKey, RevenueDetailVo> handleOrderRevenueInfo(
            List<ComponentTypeEnum> extInfoEnums, Long tenantId, List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys) {
        if (extInfoEnums.contains(ORDER_REVENUE_INFO)) {
            try {
                return revenueComponent.revenueComponent(tenantId, allTradeOrderKeys);
            } catch (Exception e) {
                log.error("查询营收数据失败", e);
            }
        }
        return new HashMap<>();
    }

    private Map<Long, List<DeliveryQuestionnaireDTO>> handleQuestionnaireInfo(
            List<ComponentTypeEnum> extInfoEnums, List<AggregateOrderVO> orders) {
        if (extInfoEnums.contains(QUESTIONNAIRE) && MccConfigUtil.getQuestionnaireSwitch()) {
            try {
                List<Long> deliveryOrderIds = orders.stream()
                        .filter(order -> order instanceof RiderDeliveryOrderVO)
                        .map(order -> ((RiderDeliveryOrderVO) order).getDeliveryOrderId())
                        .collect(Collectors.toList());
                return questionnaireComponent.queryDeliveryQuestionnaireMap(deliveryOrderIds);
            } catch (Exception e) {
                log.error("查询配送问卷失败", e);
            }
        }
        return new HashMap<>();
    }

    private Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> handleHighPriceTag(
            List<ComponentTypeEnum> extInfoEnums, List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys) {
        if (extInfoEnums.contains(HIGH_PRICE_TAG)) {
            try {
                return productHighPriceTagComponent.batchGetProductHighPriceTag(allTradeOrderKeys);
            } catch (Exception e) {
                log.error("查高价值标签失败", e);
            }
        }
        return Collections.emptyMap();
    }

    private Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> handleLackStockInfo(
            List<ComponentTypeEnum> extInfoEnums, List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys) {
        if (extInfoEnums.contains(LACK_STOCK) && LionConfigUtils.getDrunkHorseTenantIds().contains(LoginContextUtils.getAppLoginTenant())) {
            try {
                return abnormalOrderComponent.getLackStockTag(allTradeOrderKeys);
            } catch (Exception e) {
                log.error("查是否缺货失败", e);
            }
        }
        return Collections.emptyMap();
    }

    private Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> handleSealDeliveryTag(
            List<ComponentTypeEnum> extInfoEnums, Long tenantId, Long storeId, List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys) {
        if (extInfoEnums.contains(SEAL_DELIVER) && GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
            try {
                return sealDeliveryTagComponent.batchGetSealDeliveryTag(allTradeOrderKeys);
            } catch (Exception e) {
                log.error("查询封签交付标签失败", e);
            }
        }
        return Collections.emptyMap();
    }

    private Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> handleTurnDeliveryButtonInfo(
            List<ComponentTypeEnum> extInfoEnums, Long tenantId, Long storeId,
            List<TradeOrderInfoComponent.TradeOrderKey> allTradeOrderKeys,
            Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap,
            Map<TradeOrderInfoComponent.TradeOrderKey, Boolean> sealDeliveryTagMap) {
        if (extInfoEnums.contains(TURN_DELIVERY_BUTTON_INFO) && LionConfigUtils.getDrunkHorseTenantIds().contains(LoginContextUtils.getAppLoginTenant())) {
            try {
                if (GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false) && MapUtils.isEmpty(sealDeliveryTagMap)) {
                    sealDeliveryTagMap = sealDeliveryTagComponent.batchGetSealDeliveryTag(allTradeOrderKeys);
                }
                return turnDeliveryButtonComponent.queryTurnDeliveryButton(tradeOrderVOMap, sealDeliveryTagMap);
            } catch (Exception e) {
                log.error("查询是否可以转青云失败", e);
            }
        }
        return Collections.emptyMap();
    }


    private Map<Integer, Boolean> queryAccountCode() {
        try {
            Map<String, Integer> permissionCodeMap = fetchPermissionFromConfig();
            if (Objects.nonNull(LoginContextUtils.getAppLoginStoreId()) && MapUtils.isNotEmpty(permissionCodeMap)){
                int dataAuthType = PermissionGroupTypeEnum.POI.getValue();
                int authId = AppLoginContextHolder.getAppLoginContext().getAppId();
                if (authId == AppIdEnum.APP_9.getAuthAppId()) {
                    // 共享前置仓数据权限
                    dataAuthType = PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue();
                }
                AuthPermissionAndDataAuthRequest request = new AuthPermissionAndDataAuthRequest();
                request.setAccountId(LoginContextUtils.getAppLoginAccountId());
                request.setAppId(authId);
                request.setDataAuthCode(String.valueOf(LoginContextUtils.getAppLoginStoreId()));
                request.setDataAuthType(dataAuthType);
                request.setPermissionCodes(Lists.newArrayList(permissionCodeMap.keySet()));
                AuthPermissionAndDataAuthResponse response = authThriftService.authPermissionAndDataAuth(request);

                Map<String, Boolean> authMap = response.getIsAccountHaveAuth();
                log.info("请求订单元素权限: result:{}", response);
                if(org.apache.commons.collections.MapUtils.isEmpty(authMap)){
                    return Maps.newHashMap();
                }
                return authMap.entrySet().stream().collect(Collectors.toMap(entry->permissionCodeMap.get(entry.getKey()), entry->entry.getValue(), (f,s)->s));
            }
        }catch (Exception e){
            log.error("查询权限元素权限失败", e);
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }

    private Map<String, Integer> fetchPermissionFromConfig() {
        String permissionCodes = Lion.getConfigRepository("com.sankuai.sgshopmgmt.empower.pieapi").get("order.operator.auth.codes");
        if (StringUtils.isNotBlank(permissionCodes)){
            /**
             * 解析  ACCEPT_ORDER,1;REFUND,2;.....
             * ***/
            List<String> permissionMap = Splitter.on(";").splitToList(permissionCodes);
            return permissionMap.stream().map(e-> Splitter.on(",").splitToList(e)).filter(e->e != null && e.size() >= 2).collect(Collectors.toMap(e->e.get(0), e-> NumberUtils.toInt(e.get(1)), (f, s)->s));
        }
        return Maps.newHashMap();
    }



    @Data
    public static class AggregateContext {

        private Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap;

        public void appendTradeOrderVOMap(Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap) {
            if (MapUtils.isEmpty(this.tradeOrderVOMap)) {
                setTradeOrderVOMap(tradeOrderVOMap);
            } else {
                this.tradeOrderVOMap.putAll(Optional.ofNullable(tradeOrderVOMap).orElse(Maps.newHashMap()));
            }

        }
    }

}
