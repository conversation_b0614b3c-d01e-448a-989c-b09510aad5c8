package com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "查询tab数量请求"
)
@ApiModel("查询tab数量请求")
@Data
public class TabCountRequest {

    @FieldDoc(
            description = "查询tab数量请求",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "查询tab数量请求")
    private Boolean ignorePickCount;

}
