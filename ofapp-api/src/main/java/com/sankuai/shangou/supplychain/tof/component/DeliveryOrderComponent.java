package com.sankuai.shangou.supplychain.tof.component;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverBaseInfoParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TPricingRouteInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TReceiver;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryOrderByIdListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.BatchQueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.PageQueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelBatchQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelBatchQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.DeliveryOrderUrlRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryOrderUrlResponse;
import com.sankuai.shangou.commons.auth.login.context.holder.AppLoginContextHolder;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.log.MethodLog;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.commons.utils.time.TimeUtils;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.PageVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.CoordinationVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.DeliveryOrderVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.RouteInfoVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.PageQueryRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.QueryIncrementRiderCompletedOrderRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.QueryRiderCompletedOrderRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.request.QueryRiderInProgressOrderRequest;
import com.sankuai.shangou.supplychain.tof.controller.vo.order.TradeOrderVO;
import com.sankuai.shangou.supplychain.tof.enums.ErrorCodeEnum;
import com.sankuai.shangou.supplychain.tof.enums.FilterSpilitTypeEnum;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.utils.MemPageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.common.annotation.Nullable;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil.desensitizeReceiverInfo;

/**
 * <AUTHOR>
 * @date 2023-12-19
 * @email <EMAIL>
 */
@Slf4j
@Component
public class DeliveryOrderComponent {

    @Resource
    private RiderQueryThriftService riderQueryThriftService;
    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;
    @Resource
    private DeliveryChannelThriftService deliveryChannelThriftService;
    @Resource
    private DeliveryOperationThriftService deliveryOperationThriftService;

    private static final Integer UNKNOWN_DELIVERY_PLATFORM_CODE = -1;
    private static final String UNKNOWN_CARRIER_NAME = "未知承运商";

    public Map<TradeOrderInfoComponent.TradeOrderKey, DeliveryOrderVO> deliveryOrderComponent(Map<TradeOrderInfoComponent.TradeOrderKey, TradeOrderVO> tradeOrderVOMap) {
        if (MapUtils.isEmpty(tradeOrderVOMap)) {
            return Maps.newHashMap();
        }
        try {
            List<Long> orderIds = tradeOrderVOMap.values().stream().map(TradeOrderVO::getOrderId).collect(Collectors.toList());
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByOrderIdListV2 begin. request:{}", orderIds);
            BatchQueryDeliveryOrderResponse response = riderQueryThriftService.queryDeliveryOrderByOrderIdListV2(orderIds);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByOrderIdListV2 end. request:{}, response:{}",
                    orderIds, response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByOrderIdListV2 wrong. request:{}, response:{}",
                        orderIds, response);
                throw new SystemException(response.getStatus().getMsg());
            }
            if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
                return Maps.newHashMap();
            }

            return response.getTRiderDeliveryOrders()
                    .stream()
                    .filter(tRiderDeliveryOrder -> tradeOrderVOMap.containsKey(new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), tRiderDeliveryOrder.getChannelOrderId())))
                    .collect(Collectors.toMap(
                            it -> new TradeOrderInfoComponent.TradeOrderKey(it.getOrderBizTypeCode(), String.valueOf(it.getChannelOrderId())),
                            it -> {
                                TradeOrderVO tradeOrderVO = tradeOrderVOMap.get(new TradeOrderInfoComponent.TradeOrderKey(it.getOrderBizTypeCode(), it.getChannelOrderId()));
                                return convertFromDTO(it, tradeOrderVO);
                            },
                            (older, newer) -> newer
                    ));

        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByOrderIdListV2 error.", e);
            throw new SystemException("内部错误");
        }
    }

    public Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> completedDeliveryOrder(QueryRiderCompletedOrderRequest req, FilterSpilitTypeEnum filterSpilitTypeEnum) {
        try {
            PageQueryCompletedDeliveryOrderByDurationRequest request = buildRequest(req);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration begin. request:{}", request);
            PageQueryDeliveryOrderResponse response = riderQueryThriftService.pageQueryCompletedDeliveryOrderByDuration(request);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration end. request:{}, response:{}",
                    request, response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                throw new BizException(response.getStatus().getMsg());
            }
            List<TRiderDeliveryOrder> tRiderDeliveryOrders = response.getTRiderDeliveryOrders();
            if (CollectionUtils.isEmpty(tRiderDeliveryOrders)) {
                return Pair.of(new PageVO(1,0, 1), Maps.newLinkedHashMap());
            }
            switch (filterSpilitTypeEnum) {
                case KEEP_SPLIT:
                    tRiderDeliveryOrders = tRiderDeliveryOrders.stream()
                            .filter(tRiderDeliveryOrder -> Objects.nonNull(tRiderDeliveryOrder.getPickDeliverySplitTag()) && tRiderDeliveryOrder.getPickDeliverySplitTag())
                            .collect(Collectors.toList());
                    break;
                case KEEP_NOT_SPLIT:
                    tRiderDeliveryOrders = tRiderDeliveryOrders.stream()
                            .filter(tRiderDeliveryOrder -> Objects.isNull(tRiderDeliveryOrder.getPickDeliverySplitTag()) || !tRiderDeliveryOrder.getPickDeliverySplitTag())
                            .collect(Collectors.toList());
                    break;
                case NO_FILTER:
                default:
                    break;
            }

            LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
            tRiderDeliveryOrders.forEach(
                    tRiderDeliveryOrder -> {
                        TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                        deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                    }
            );

            return Pair.of(new PageVO(response.getPageInfo()), deliveryOrderVOMap);

        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration error.", e);
            throw new ThirdPartyException("查询已送达运单失败");
        }
    }

    public Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> completedDeliveryOrder(QueryIncrementRiderCompletedOrderRequest req, FilterSpilitTypeEnum filterSpilitTypeEnum) {
        try {
            PageQueryCompletedDeliveryOrderByDurationRequest request = buildRequest(req);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration begin. request:{}", request);
            PageQueryDeliveryOrderResponse response = riderQueryThriftService.pageQueryCompletedDeliveryOrderByDuration(request);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration end. request:{}, response:{}",
                    request, response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                throw new BizException(response.getStatus().getMsg());
            }
            List<TRiderDeliveryOrder> tRiderDeliveryOrders = response.getTRiderDeliveryOrders();
            if (CollectionUtils.isEmpty(tRiderDeliveryOrders)) {
                return Pair.of(new PageVO(1,0, 1), Maps.newLinkedHashMap());
            }
            switch (filterSpilitTypeEnum) {
                case KEEP_SPLIT:
                    tRiderDeliveryOrders = tRiderDeliveryOrders.stream()
                            .filter(tRiderDeliveryOrder -> Objects.nonNull(tRiderDeliveryOrder.getPickDeliverySplitTag()) && tRiderDeliveryOrder.getPickDeliverySplitTag())
                            .collect(Collectors.toList());
                    break;
                case KEEP_NOT_SPLIT:
                    tRiderDeliveryOrders = tRiderDeliveryOrders.stream()
                            .filter(tRiderDeliveryOrder -> Objects.isNull(tRiderDeliveryOrder.getPickDeliverySplitTag()) || !tRiderDeliveryOrder.getPickDeliverySplitTag())
                            .collect(Collectors.toList());
                    break;
                case NO_FILTER:
                default:
                    break;
            }

            LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
            tRiderDeliveryOrders.forEach(
                    tRiderDeliveryOrder -> {
                        TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                        deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                    }
            );

            return Pair.of(new PageVO(response.getPageInfo()), deliveryOrderVOMap);

        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration error.", e);
            throw new ThirdPartyException("查询已送达运单失败");
        }
    }

    public Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> pageQueryDeliveryOrderList(PageQueryRequest pageRequest, Integer deliveryStatus, Long accountId, FilterSpilitTypeEnum filterSpilitTypeEnum) {
        if (LionConfigUtils.isNewDeliveryListGrayStore(LoginContextUtils.getAppLoginStoreId())) {
            return memPageQueryDeliveryOrderList(pageRequest, deliveryStatus, accountId, filterSpilitTypeEnum);
        }

        PageQueryDeliveryOrderRequest request = new PageQueryDeliveryOrderRequest();
        request.setTenantId(LoginContextUtils.getAppLoginTenant());
        request.setStoreId(LoginContextUtils.getAppLoginStoreId());
        request.setPage(pageRequest.getPage());
        request.setPageSize(pageRequest.getSize());
        request.setDeliveryStatus(deliveryStatus);
        request.setRiderAccountId(accountId);
        log.info("start invoke riderQueryThriftService.pageQueryDeliveryOrder, request: {}", JSON.toJSONString(request));
        PageQueryDeliveryOrderResponse response = riderQueryThriftService.pageQueryDeliveryOrder(request);
        log.info("end invoke riderQueryThriftService.pageQueryDeliveryOrder, response: {}", JSON.toJSONString(response));

        if (response.getStatus().getCode() != 0) {
            throw new BizException("查询配送单列表失败");
        }

        if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
            return Pair.of(new PageVO(response.getPageInfo()), new LinkedHashMap<>());
        }


        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
        response.getTRiderDeliveryOrders().forEach(
                tRiderDeliveryOrder -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                    deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                }
        );

        return Pair.of(new PageVO(response.getPageInfo()), deliveryOrderVOMap);
    }


    public Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> queryDeliveryOrderByIdAndCheckStatusAndRider(Long tenantId, Long deliveryOrderId,
                                                                                                                                                @Nullable Integer expectDeliveryStatus,
                                                                                                                                                @Nullable Long expectRiderAccountId,Long storeId) {
        QueryDeliveryOrderByIdListRequest request = new QueryDeliveryOrderByIdListRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setDeliveryOrderIds(Collections.singletonList(deliveryOrderId));
        request.setNeedReturnPricingRouteInfo(true);
        BatchQueryDeliveryOrderResponse response;
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByIds begin. request:{}", JSON.toJSONString(request));
            response = riderQueryThriftService.queryDeliveryOrderByIds(request);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByIds end. response:{}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration error.", e);
            throw new ThirdPartyException("查询已送达运单失败");
        }
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new BizException(response.getStatus().getMsg());
        }
        if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
            return Pair.of(new PageVO(1, 0, 1), Maps.newLinkedHashMap());
        }

        TRiderDeliveryOrder deliveryOrder = response.getTRiderDeliveryOrders().get(0);
        //预期状态不为空时，校验状态
        if (Objects.nonNull(expectDeliveryStatus) && !Objects.equals(deliveryOrder.getDeliveryStatus(), expectDeliveryStatus)) {
            throw new BizException(ErrorCodeEnum.DELIVERY_ORDER_STATUS_ALREADY_CHANGED.getCode(), ErrorCodeEnum.DELIVERY_ORDER_STATUS_ALREADY_CHANGED.getMessage());
        }

        //预期骑手不为空时，校验骑手
        if (Objects.nonNull(expectRiderAccountId)
                &&!Objects.equals(deliveryOrder.getDeliveryStatus(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode())
                && Objects.nonNull(deliveryOrder.getCurrentRider())
                && !Objects.equals(deliveryOrder.getCurrentRider().getAccountId(), expectRiderAccountId)) {
            throw new BizException(ErrorCodeEnum.RIDER_ALREADY_CHANGED.getCode(), ErrorCodeEnum.RIDER_ALREADY_CHANGED.getMessage());
        }

        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
        response.getTRiderDeliveryOrders().forEach(
                tRiderDeliveryOrder -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                    deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                }
        );

        return Pair.of(new PageVO(1, deliveryOrderVOMap.size(), 1), deliveryOrderVOMap);

    }
    public TRiderDeliveryOrder queryDeliveryOrderById(Long tenantId, Long deliveryOrderId, Long storeId) {
        QueryDeliveryOrderByIdListRequest request = new QueryDeliveryOrderByIdListRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setDeliveryOrderIds(Collections.singletonList(deliveryOrderId));
        request.setNeedReturnPricingRouteInfo(true);
        BatchQueryDeliveryOrderResponse response;
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByIds begin. request:{}", JSON.toJSONString(request));
            response = riderQueryThriftService.queryDeliveryOrderByIds(request);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryOrderByIds end. response:{}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration error.", e);
            throw new ThirdPartyException("查询已送达运单失败");
        }
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new BizException(response.getStatus().getMsg());
        }

        if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
            throw new BizException("运单不存在");
        }


        return response.getTRiderDeliveryOrders().get(0);
    }

    public List<TRiderDeliveryOrder> queryDeliveryOrderByTradeOrderIds(List<Long> tradeOrderIds) {
        try {
            BatchQueryDeliveryOrderResponse response = riderQueryThriftService.queryDeliveryOrderByOrderIdListV2(tradeOrderIds);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                throw new BizException(response.getStatus().getMsg());
            }

            if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
                return Lists.newArrayList();
            }

            return response.getTRiderDeliveryOrders();
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration error.", e);
            throw new ThirdPartyException("查询已送达运单失败");
        }
    }

    public List<TRiderDeliveryOrder> queryAllDeliveryOrderByTradeOrderIds(List<Long> tradeOrderIds) {
        try {
            List<TRiderDeliveryOrder> tRiderDeliveryOrderList = Lists.newArrayList();
            List<List<Long>> partition = Lists.partition(tradeOrderIds, LionConfigUtils.getPartitionSize());
            for (List<Long> partitionTradeOrderIds : partition) {
                BatchQueryDeliveryOrderResponse response = riderQueryThriftService.queryAllDeliveryOrderByOrderIdList(partitionTradeOrderIds);
                log.info("invoke riderQueryThriftService.queryAllDeliveryOrderByOrderIdList, request = {}, response = {}", partitionTradeOrderIds, JSON.toJSONString(response));
                if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                    throw new BizException(response.getStatus().getMsg());
                }
                tRiderDeliveryOrderList.addAll(Optional.ofNullable(response.getTRiderDeliveryOrders()).orElse(Lists.newArrayList()));
            }

            return tRiderDeliveryOrderList;
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrderByDuration error.", e);
            throw new ThirdPartyException("查询已送达运单失败");
        }
    }


    public Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> memPageQueryDeliveryOrderList(PageQueryRequest pageRequest, Integer deliveryStatus, Long accountId, FilterSpilitTypeEnum filterSpilitTypeEnum) {

        QueryDeliveryOrderByRiderAndStatusRequest request = new QueryDeliveryOrderByRiderAndStatusRequest();
        request.setTenantId(LoginContextUtils.getAppLoginTenant());
        request.setStoreId(LoginContextUtils.getAppLoginStoreId());
        request.setDeliveryStatus(deliveryStatus);
        request.setRiderAccountId(accountId);
        log.info("start invoke riderQueryThriftService.queryDeliveryOrderByRiderAndStatusList, request: {}", JSON.toJSONString(request));
        BatchQueryDeliveryOrderResponse response = riderQueryThriftService.queryDeliveryOrderByRiderAndStatusList(request);
        log.info("end invoke riderQueryThriftService.queryDeliveryOrderByRiderAndStatusList, response: {}", JSON.toJSONString(response));

        if (response.getStatus().getCode() != 0) {
            throw new BizException("查询配送单列表失败");
        }

        List<TRiderDeliveryOrder> tRiderDeliveryOrders = response.getTRiderDeliveryOrders();
        if (CollectionUtils.isEmpty(tRiderDeliveryOrders)) {
            return Pair.of(new PageVO(pageRequest.getPage(), 0, 0), new LinkedHashMap<>());
        }
        switch (filterSpilitTypeEnum) {
            case KEEP_SPLIT:
                tRiderDeliveryOrders = tRiderDeliveryOrders.stream()
                        .filter(tRiderDeliveryOrder -> Objects.nonNull(tRiderDeliveryOrder.getPickDeliverySplitTag()) && tRiderDeliveryOrder.getPickDeliverySplitTag())
                        .collect(Collectors.toList());
                break;
            case KEEP_NOT_SPLIT:
                tRiderDeliveryOrders = tRiderDeliveryOrders.stream()
                        .filter(tRiderDeliveryOrder -> Objects.isNull(tRiderDeliveryOrder.getPickDeliverySplitTag()) || !tRiderDeliveryOrder.getPickDeliverySplitTag())
                        .collect(Collectors.toList());
                break;
            case NO_FILTER:
            default:
                break;
        }

        List<TRiderDeliveryOrder> sortedTRiderDeliveryOrderList = tRiderDeliveryOrders
                .stream()
                .sorted((o1, o2) -> {
                    try {
                        //都用期望的*送达时间*来比较
                        long compareKey1 = Objects.nonNull(o1.getAssessDeliveryTime()) ? o1.getAssessDeliveryTime() : o1.getEstimatedDeliveryTime();
                        long compareKey2 = Objects.nonNull(o2.getAssessDeliveryTime()) ? o2.getAssessDeliveryTime() : o2.getEstimatedDeliveryTime();
                        return (int) (compareKey1 - compareKey2);
                    } catch (Exception e) {
                        log.error("delivery order compare error", e);
                        Cat.logEvent("DELIVERY_ORDER", "COMPARE_ERROR");
                        return (int) (o1.getEstimatedDeliveryTime() - o2.getEstimatedDeliveryTime());
                    }
                }).collect(Collectors.toList());

        MemPageUtils.PageInfo<TRiderDeliveryOrder> tRiderDeliveryOrderPageInfo = MemPageUtils.pagingList(sortedTRiderDeliveryOrderList, pageRequest.getPage(), pageRequest.getSize());
        if (CollectionUtils.isEmpty(tRiderDeliveryOrderPageInfo.getPagedList())) {
            return Pair.of(new PageVO(pageRequest.getPage(), tRiderDeliveryOrderPageInfo.getTotal(), tRiderDeliveryOrderPageInfo.getTotalPage()), new LinkedHashMap<>());
        }
        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
        tRiderDeliveryOrderPageInfo.getPagedList().forEach(
                tRiderDeliveryOrder -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                    deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                }
        );

        return Pair.of(new PageVO(pageRequest.getPage(), tRiderDeliveryOrderPageInfo.getTotal(), tRiderDeliveryOrderPageInfo.getTotalPage()), deliveryOrderVOMap);
    }



    /**
     * 查询骑手进行中运单(已接单+已取货).
     *
     * @return PageQueryDeliveryOrderResponse
     */
    public Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> getRiderInProgressDeliveryOrders(QueryRiderInProgressOrderRequest request,
                                                                                                                                    boolean needReturnPricingRouteInfo) {
        PageQueryDeliveryOrderResponse response;
        PageQueryInProgressDeliveryOrderRequest tRequest = new PageQueryInProgressDeliveryOrderRequest();
        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getSize());
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setNeedReturnPricingRouteInfo(needReturnPricingRouteInfo);

        List<Long> storeIdList = getStoreIdList(request.getStoreId());
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店模式：storeIdList={}", storeIdList);
            throw  new BizException("只支持单门店模式");
        }
        tRequest.setStoreId(storeIdList.get(0));
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder begin. request:{}", tRequest);
            response = riderQueryThriftService.pageQueryInProgressDeliveryOrder(tRequest);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder end. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder error.", e);
            throw new ThirdPartyException("查询运单失败");
        }
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder wrong. request:{}, response:{}",
                    tRequest, response);
            throw new BizException(response.getStatus().getMsg());
        }

        if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
            return Pair.of(new PageVO(response.getPageInfo()), new LinkedHashMap<>());
        }

        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
        response.getTRiderDeliveryOrders().forEach(
                tRiderDeliveryOrder -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                    deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                }
        );

        return Pair.of(new PageVO(response.getPageInfo()), deliveryOrderVOMap);
    }

    /**
     * 查询骑手进行中运单(已接单+已取货).
     *
     * @return PageQueryDeliveryOrderResponse
     */
    public Pair<PageVO, LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder>> getRiderInProgressDeliveryOrdersAndCheckStatus(Long deliveryOrderId, Integer expectDeliveryStatus) {
        PageQueryDeliveryOrderResponse response;
        PageQueryInProgressDeliveryOrderRequest tRequest = new PageQueryInProgressDeliveryOrderRequest();
        tRequest.setPage(1);
        tRequest.setPageSize(100);
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        tRequest.setNeedReturnPricingRouteInfo(true);
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder begin. request:{}", tRequest);
            response = riderQueryThriftService.pageQueryInProgressDeliveryOrder(tRequest);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder end. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder error.", e);
            throw new ThirdPartyException("查询运单失败");
        }
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder wrong. request:{}, response:{}",
                    tRequest, response);
            throw new BizException(response.getStatus().getMsg());
        }

        //如果没查到期望的运单 说明状态或骑手已经变了 再单独查一次期望的运单
        if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders()) || response.getTRiderDeliveryOrders()
                .stream()
                .noneMatch(deliveryOrder -> Objects.equals(deliveryOrder.getDeliveryOrderId(), deliveryOrderId))) {
            queryDeliveryOrderByIdAndCheckStatusAndRider(LoginContextUtils.getAppLoginTenant(), deliveryOrderId,
                    expectDeliveryStatus, LoginContextUtils.getAppLoginAccountId(),LoginContextUtils.getAppLoginStoreId());
        }

        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
        response.getTRiderDeliveryOrders().forEach(
                tRiderDeliveryOrder -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                    deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                }
        );

        return Pair.of(new PageVO(response.getPageInfo()), deliveryOrderVOMap);
    }


    /**
     * 查询骑手进行中运单(已接单+已取货).
     *
     * @return PageQueryDeliveryOrderResponse
     */
    public LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> getAllDeliveryOrders(Long lastUpdateTime) {
        PageQueryDeliveryOrderResponse response;
        QueryAllDeliveryOrderRequest tRequest = new QueryAllDeliveryOrderRequest();
        tRequest.setTenantId(LoginContextUtils.getAppLoginTenant());
        tRequest.setStoreId(LoginContextUtils.getAppLoginStoreId());
        tRequest.setRiderAccountId(LoginContextUtils.getAppLoginAccountId());
        tRequest.setLastUpdateTime(lastUpdateTime);
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryAllRiderDeliveryOrder begin. request:{}", tRequest);
            response = riderQueryThriftService.queryAllRiderDeliveryOrder(tRequest);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryAllRiderDeliveryOrder end. request:{}, response:{}", JSON.toJSONString(tRequest), JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder error.", e);
            throw new ThirdPartyException("查询运单失败");
        }
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryAllRiderDeliveryOrder wrong. request:{}, response:{}",
                    tRequest, response);
            throw new BizException(response.getStatus().getMsg());
        }

        if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
            return Maps.newLinkedHashMap();
        }

        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
        response.getTRiderDeliveryOrders().forEach(
                tRiderDeliveryOrder -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                    deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                }
        );

        return deliveryOrderVOMap;
    }

    public  Map<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> queryDeliveryOrderByOrderIdList(Long tenantId, Long storeId, List<Long> orderList, boolean returnCanceledDeliveryOrder) {
        QueryDeliveryOrderByOrderIdListRequest request = new QueryDeliveryOrderByOrderIdListRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setOrderIds(orderList);
        request.setReturnCanceledDeliveryOrder(returnCanceledDeliveryOrder);

        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyMap();
        }
        BatchQueryDeliveryOrderResponse response;
        try {
            log.info("start invoke riderQueryThriftService.pageQueryDeliveryOrder, request: {}", JSON.toJSONString(request));
            response = riderQueryThriftService.queryDeliveryOrderByOrderIdList(request);
            log.info("end invoke riderQueryThriftService.pageQueryDeliveryOrder, response: {}", JSON.toJSONString(response));

        } catch (Exception e) {
            log.error("查询运单信息失败", e);
            throw new ThirdPartyException("查询运单信息失败");
        }

        if (response.getStatus().getCode() != 0) {
            throw new BizException("查询配送单列表失败");
        }

        LinkedHashMap<TradeOrderInfoComponent.TradeOrderKey, TRiderDeliveryOrder> deliveryOrderVOMap = new LinkedHashMap<>();
        response.getTRiderDeliveryOrders().forEach(
                tRiderDeliveryOrder -> {
                    TradeOrderInfoComponent.TradeOrderKey tradeOrderKey = new TradeOrderInfoComponent.TradeOrderKey(tRiderDeliveryOrder.getOrderBizTypeCode(), String.valueOf(tRiderDeliveryOrder.getChannelOrderId()));
                    deliveryOrderVOMap.putIfAbsent(tradeOrderKey, tRiderDeliveryOrder);
                }
        );

        return deliveryOrderVOMap;
    }


    private  List<Long> getStoreIdList(String storeIds) {
        if (org.apache.commons.lang3.StringUtils.isBlank(storeIds)) {
            return Collections.emptyList();
        }
        return Arrays.stream(storeIds.split(",")).distinct().filter(e -> !e.isEmpty()).map(Long::valueOf).collect(Collectors.toList());
    }


    private DeliveryOrderVO convertFromDTO(TRiderDeliveryOrder deliveryOrder, TradeOrderVO tradeOrderVO) {
        DeliveryOrderVO vo = new DeliveryOrderVO();
        vo.setDeliveryOrderId(deliveryOrder.getDeliveryOrderId());
        vo.setTenantId(deliveryOrder.getTenantId());
        vo.setDeliveryStatus(deliveryOrder.getDeliveryStatus());
        vo.setDeliveryOrderTypeName(getDeliveryOrderTypeName(tradeOrderVO.getDeliveryOrderType()));
        vo.setEstimateArriveTimeStart(deliveryOrder.getEstimatedDeliveryTime());
        vo.setEstimateArriveTimeEnd(deliveryOrder.getEstimatedDeliveryEndTime());
        vo.setDeliveryDistance(deliveryOrder.getDistance());
        if (deliveryOrder.getChangeFromRider() != null) {
            vo.setFromRiderName(deliveryOrder.getChangeFromRider().getName());
        }

        long evaluateArriveDeadline;
        if (Objects.nonNull(deliveryOrder.getAssessDeliveryTime())) {
            //新逻辑，考核时间与eta脱钩
            evaluateArriveDeadline = deliveryOrder.getAssessDeliveryTime();
            vo.setShowRewardHint(true);
        } else {
            //原逻辑
            if (tradeOrderVO.getDeliveryOrderType().equals(DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue())) {
                // 预订单：考核时间=预计送达时间+5分钟
                evaluateArriveDeadline = deliveryOrder.getEstimatedDeliveryEndTime() + (5 * 60 * 1000);
            } else {
                // 实时单：考核时间=支付时间+25分钟
                evaluateArriveDeadline = tradeOrderVO.getPayTime() + (25 * 60 * 1000);
            }
        }

        if (deliveryOrder.getDeliveryStatus() != null
                && RiderDeliveryStatusEnum.DELIVERY_DONE.getCode() == deliveryOrder.getDeliveryStatus()) {
            vo.setEvaluateArriveLeftTime(0L);
            if (deliveryOrder.getDeliveryDoneTime() != null
                    && deliveryOrder.getDeliveryDoneTime() > evaluateArriveDeadline) {
                // 已送达，已超时
                vo.setEvaluateArriveTimeout(deliveryOrder.getDeliveryDoneTime() - evaluateArriveDeadline);
            } else {
                // 已送达，未超时
                vo.setEvaluateArriveTimeout(0L);
            }
        } else if (deliveryOrder.getDeliveryStatus() != null
                && (RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode() == deliveryOrder.getDeliveryStatus()
                || RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode() == deliveryOrder.getDeliveryStatus()
                || RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode() == deliveryOrder.getDeliveryStatus())) {
            long tsNow = System.currentTimeMillis();
            if (tsNow > evaluateArriveDeadline) {
                // 配送中，已经超时
                vo.setEvaluateArriveLeftTime(0L);
                vo.setEvaluateArriveTimeout(tsNow - evaluateArriveDeadline);
            } else {
                // 配送中，还未超时
                vo.setEvaluateArriveLeftTime(evaluateArriveDeadline - tsNow);
                vo.setEvaluateArriveTimeout(0L);
            }
        } else {
            // 其他状态暂时无需处理
        }


        // 是否需要脱敏收货人信息
        TReceiver tReceiver = deliveryOrder.getReceiver();
        if (tReceiver != null) {
            try {
                boolean isNeedDesensitize = isNeedDesensitize(deliveryOrder);
                buildReceiverInfo(tReceiver, isNeedDesensitize, vo);
            } catch (Exception e) {
                log.error("try to mask receiver info error", e);
                buildOriginReceiverInfo(vo, tReceiver);
            }

        }
        vo.setDeliveryStatusLocked(deliveryOrder.getStatusLocked());
        vo.setCanStatusBeLocked(deliveryOrder.getCanStatusBeLocked());
        return vo;
    }

    private static void buildOriginReceiverInfo(DeliveryOrderVO vo, TReceiver tReceiver) {
        vo.setReceiverName(tReceiver.getReceiverName());
        vo.setReceiverPhone(tReceiver.getReceiverPhone());
        vo.setReceiverAddress(tReceiver.getAddressDetail());
        vo.setReceiverLongitude(tReceiver.getLongitude());
        vo.setReceiverLatitude(tReceiver.getLatitude());
    }

    private boolean isNeedDesensitize(TRiderDeliveryOrder deliveryOrder) {
        if (Objects.equals(RiderDeliveryStatusEnum.DELIVERY_DONE.getCode(), deliveryOrder.getDeliveryStatus())) {
            return System.currentTimeMillis() - deliveryOrder.getDeliveryDoneTime() > 1000 * MccConfigUtil.getDesensitizeReceiverInfoTime();
        } else if (Objects.equals(RiderDeliveryStatusEnum.DELIVERY_CANCELLED.getCode(), deliveryOrder.getDeliveryStatus())) {
            return System.currentTimeMillis() - deliveryOrder.getLastEventTime() > 1000 * MccConfigUtil.getDesensitizeReceiverInfoTime();
        }
        return false;
    }

    private void buildReceiverInfo(TReceiver tReceiver, boolean isNeedDesensitize, DeliveryOrderVO vo) {
        if (Objects.isNull(tReceiver)) {
            return;
        }
        DesensitizeReceiverInfoResult receiverInfoResult = new DesensitizeReceiverInfoResult(tReceiver.getReceiverName(), tReceiver.getAddressDetail(), tReceiver.getReceiverPhone(), tReceiver.getReceiverPrivacyPhone());
        if (isNeedDesensitize) {
            DesensitizeReceiverBaseInfoParam receiverBaseInfoParam = new DesensitizeReceiverBaseInfoParam(tReceiver.getReceiverName(), tReceiver.getAddressDetail(), tReceiver.getReceiverPhone(), tReceiver.getReceiverPrivacyPhone());
            DesensitizeReceiverInfoResult result = desensitizeReceiverInfo(receiverBaseInfoParam);
            if (Objects.nonNull(result)) {
                // 脱敏时发生异常 result可能为空 故判空
                receiverInfoResult = result;
            }
        }
        vo.setReceiverName(receiverInfoResult.getReceiverName());
        vo.setReceiverPhone(receiverInfoResult.getReceiverPhone());
        vo.setReceiverAddress(receiverInfoResult.getReceiverAddress());
        vo.setReceiverLatitude(isNeedDesensitize ? null : tReceiver.getLatitude());
        vo.setReceiverLongitude(isNeedDesensitize ? null : tReceiver.getLongitude());
    }


    public DeliveryOrderVO buildDeliveryOrderVO(TRiderDeliveryOrder deliveryOrder, OCMSOrderVO ocmsOrderVO, boolean useAssessTime) {
        DeliveryOrderVO vo = new DeliveryOrderVO();
        vo.setDeliveryOrderId(deliveryOrder.getDeliveryOrderId());
        vo.setTenantId(deliveryOrder.getTenantId());
        vo.setDeliveryStatus(deliveryOrder.getDeliveryStatus());
        vo.setOriginalDeliveryStatus(deliveryOrder.getOriginDeliveryStatus());
        if (Objects.nonNull(ocmsOrderVO)) {
            DeliveryOrderType deliveryOrderType = ocmsOrderVO.getIsBooking() == 1 ?
                    DeliveryOrderType.DELIVERY_BY_BOOK_TIME : DeliveryOrderType.DELIVERY_RIGHT_NOW;
            vo.setDeliveryOrderTypeName(getDeliveryOrderTypeName(deliveryOrderType.getValue()));

            long evaluateArriveDeadline;
            if (MccConfigUtil.checkIsDrunkHorseTenant(deliveryOrder.getTenantId())) {
                if (useAssessTime && Objects.nonNull(deliveryOrder.getAssessDeliveryTime())) {
                    //新逻辑，考核时间与eta脱钩
                    evaluateArriveDeadline = deliveryOrder.getAssessDeliveryTime();
                    vo.setShowRewardHint(true);
                } else {
                    //原逻辑
                    if (deliveryOrderType.equals(DeliveryOrderType.DELIVERY_BY_BOOK_TIME)) {
                        // 预订单：考核时间=预计送达时间+5分钟
                        if (LionConfigUtils.isNewPreOrderAssessGrayStore(deliveryOrder.getStoreId()) && useAssessTime) {
                            evaluateArriveDeadline = deliveryOrder.getEstimatedDeliveryEndTime() + LionConfigUtils.preOrderAssessTimePlusMills();
                        } else {
                            evaluateArriveDeadline = deliveryOrder.getEstimatedDeliveryEndTime() + (5 * 60 * 1000);
                        }
                    } else {
                        // 实时单：考核时间=支付时间+25分钟
                        evaluateArriveDeadline = ocmsOrderVO.getPayTime() + (25 * 60 * 1000);
                    }
                }
            } else {
                evaluateArriveDeadline = deliveryOrder.getEstimatedDeliveryEndTime();
            }


            vo.setEvaluateArriveDeadline(evaluateArriveDeadline);
            if (deliveryOrder.getDeliveryStatus() != null
                    && RiderDeliveryStatusEnum.DELIVERY_DONE.getCode() == deliveryOrder.getDeliveryStatus()) {
                vo.setEvaluateArriveLeftTime(0L);
                if (deliveryOrder.getDeliveryDoneTime() != null
                        && deliveryOrder.getDeliveryDoneTime() > evaluateArriveDeadline) {
                    // 已送达，已超时
                    vo.setEvaluateArriveTimeout(deliveryOrder.getDeliveryDoneTime() - evaluateArriveDeadline);
                } else {
                    // 已送达，未超时
                    vo.setEvaluateArriveTimeout(0L);
                }
            } else if (deliveryOrder.getDeliveryStatus() != null
                    && (RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode() == deliveryOrder.getDeliveryStatus()
                    || RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode() == deliveryOrder.getDeliveryStatus()
                    || RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode() == deliveryOrder.getDeliveryStatus())) {
                long tsNow = System.currentTimeMillis();
                if (tsNow > evaluateArriveDeadline) {
                    // 配送中，已经超时
                    vo.setEvaluateArriveLeftTime(0L);
                    vo.setEvaluateArriveTimeout(tsNow - evaluateArriveDeadline);
                } else {
                    // 配送中，还未超时
                    vo.setEvaluateArriveLeftTime(evaluateArriveDeadline - tsNow);
                    vo.setEvaluateArriveTimeout(0L);
                }
            } else {
                // 其他状态暂时无需处理
            }

            // 名酒馆标识
            vo.setIsMtFamousTavern(ocmsOrderVO.getIsMtFamousTavern());
        }

        vo.setEstimateArriveTimeStart(deliveryOrder.getEstimatedDeliveryTime());
        vo.setEstimateArriveTimeEnd(deliveryOrder.getEstimatedDeliveryEndTime());
        vo.setDeliveryDistance(deliveryOrder.getDistance());
        if (deliveryOrder.getChangeFromRider() != null) {
            vo.setFromRiderName(deliveryOrder.getChangeFromRider().getName());
        }


        // 是否需要脱敏收货人信息
        TReceiver tReceiver = deliveryOrder.getReceiver();
        if (tReceiver != null) {
            try {
                boolean isNeedDesensitize = isNeedDesensitize(deliveryOrder);
                buildReceiverInfo(tReceiver, isNeedDesensitize, vo);
            } catch (Exception e) {
                log.error("try to mask receiver info error", e);
                buildOriginReceiverInfo(vo, tReceiver);
            }

        }

        vo.setDeliveryStatusLocked(deliveryOrder.getStatusLocked());
        vo.setCanStatusBeLocked(deliveryOrder.getCanStatusBeLocked());
        vo.setSigningPosition(deliveryOrder.getSignPosition());

        if (Objects.equals(deliveryOrder.getDeliveryStatus(), RiderDeliveryStatusEnum.DELIVERY_DONE.getCode())) {
            vo.setDeliveryDoneTime(deliveryOrder.getDeliveryDoneTime());
        }
        vo.setIsOneYuanOrder(deliveryOrder.getIsOneYuanOrder());

        if (Objects.nonNull(deliveryOrder.getPricingRouteInfo())) {
            TPricingRouteInfo pricingRouteInfo = deliveryOrder.getPricingRouteInfo();
            RouteInfoVO routeInfoVO = new RouteInfoVO();
            routeInfoVO.setRouteId(pricingRouteInfo.getRouteId());
            if (Objects.nonNull(pricingRouteInfo.getOrigin())) {
                routeInfoVO.setStoreCoordination(new CoordinationVO(pricingRouteInfo.getOrigin().getLongitude(), pricingRouteInfo.getOrigin().getLatitude()));
            }
            if (CollectionUtils.isNotEmpty(pricingRouteInfo.getPolyline())) {
                routeInfoVO.setRoutePoints(pricingRouteInfo.getPolyline().stream()
                        .map(point -> new CoordinationVO(point.getLongitude(), point.getLatitude()))
                        .collect(Collectors.toList()));
            }
            routeInfoVO.setDistance(pricingRouteInfo.getDistance());
            routeInfoVO.setDuration(pricingRouteInfo.getDuration());

            vo.setRouteInfoVO(routeInfoVO);
        }

        //拣配分离标签
        vo.setIsPickDeliverySplit(deliveryOrder.getPickDeliverySplitTag());
        vo.setCreateTime(deliveryOrder.getCreateTime());

        //三方相关
        vo.setIsThirdDelivery(Optional.ofNullable(deliveryOrder.getThirdDelivery()).orElse(false));
        vo.setIsThirdException(Optional.ofNullable(deliveryOrder.getThirdException()).orElse(false));

        if (Objects.nonNull(deliveryOrder.getCurrentRider())) {
            vo.setRiderAccountId(deliveryOrder.getCurrentRider().getAccountId());
        }

        vo.setDeliveryChannelId(deliveryOrder.getDeliveryChannelId());
        vo.setDeliveryPlatformCode(deliveryOrder.getDeliveryPlatformCode());
        vo.setDeliveryPlatformDesc(deliveryOrder.getDeliveryPlatformDesc());
        vo.setAssessRewardShowType(deliveryOrder.getRewardType());
        return vo;
    }

    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    private String getDeliveryOrderTypeName(Boolean isReserve) {
        if (Objects.isNull(isReserve)) {
            return "未知";
        }

        if (isReserve) {
            return "预订";
        } else {
            return "立即送达";
        }
    }


    private PageQueryCompletedDeliveryOrderByDurationRequest buildRequest(QueryRiderCompletedOrderRequest req) {
        PageQueryCompletedDeliveryOrderByDurationRequest request = new PageQueryCompletedDeliveryOrderByDurationRequest();

        if (Objects.isNull(AppLoginContextHolder.getAppLoginContext().getLoginUser())) {
            throw new BizException("未获取到用户信息, 请先登录");
        }

        String storeIdStr = AppLoginContextHolder.getAppLoginContext().getStoreIds();
        if (StringUtils.isEmpty(storeIdStr) || storeIdStr.split(",").length > 1) {
            throw new SystemException("仅支持单门店维度");
        }
        LocalDate localDate = LocalDate.parse(req.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        request.setTenantId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId());
        request.setRiderAccountId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getAccountId());
        request.setStoreId(Long.parseLong(storeIdStr));
        request.setPage(req.getPage());
        request.setPageSize(req.getSize());
        request.setStartTime(TimeUtils.toMilliSeconds(LocalDateTime.of(localDate, LocalTime.MIN)));
        request.setEndTime(TimeUtils.toMilliSeconds(LocalDateTime.of(localDate, LocalTime.MAX)));

        return request;
    }

    private PageQueryCompletedDeliveryOrderByDurationRequest buildRequest(QueryIncrementRiderCompletedOrderRequest req) {
        PageQueryCompletedDeliveryOrderByDurationRequest request = new PageQueryCompletedDeliveryOrderByDurationRequest();

        if (Objects.isNull(AppLoginContextHolder.getAppLoginContext().getLoginUser())) {
            throw new BizException("未获取到用户信息, 请先登录");
        }

        String storeIdStr = AppLoginContextHolder.getAppLoginContext().getStoreIds();
        if (StringUtils.isEmpty(storeIdStr) || storeIdStr.split(",").length > 1) {
            throw new SystemException("仅支持单门店维度");
        }
        request.setTenantId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getTenantId());
        request.setRiderAccountId(AppLoginContextHolder.getAppLoginContext().getLoginUser().getAccountId());
        request.setStoreId(Long.parseLong(storeIdStr));
        request.setPage(1);
        request.setPageSize(100);
        request.setStartTime((req.getLastUpdateTime()));
        request.setEndTime(TimeUtils.toMilliSeconds(LocalDateTime.now()));

        return request;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "DeliveryOrderComponent.getDeliveryChannelDtoMap", fallBackMethod = "getDeliveryChannelDtoMapFallback",
            timeoutInMilliseconds = 1000)
    public Map<Integer, DeliveryChannelDto> getDeliveryChannelDtoMap(Set<Integer> channelCodes){
        HashMap<Integer, DeliveryChannelDto> channelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(channelCodes)){
            return channelMap;
        }
        RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(3, 100);
        DeliveryChannelBatchQueryByCarrierCodeRequest request = new DeliveryChannelBatchQueryByCarrierCodeRequest();
        request.setCarrierCodeSet(channelCodes);
        try {
            DeliveryChannelBatchQueryResponse response = retryTemplate.execute((RetryCallback<DeliveryChannelBatchQueryResponse, Exception>) retryContext -> deliveryChannelThriftService.batchQueryDeliveryChannelByCarrierCodeList(request));

            if (Objects.isNull(response)|| response.getStatus().getCode() != FailureCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty((response.getDeliveryChannelDtoList()))) {

                Arrays.stream(DeliveryChannelEnum.values())
                        .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                        .forEach(e -> channelMap.put(e.getCode(), translateFromDeliveryChannelEnum(e.getCode())));
                log.info("getDeliveryChannelDtoMap from enum");
                return channelMap;
            }
            log.info("getDeliveryChannelDtoMap from rpc");
            return response.getDeliveryChannelDtoList()
                    .stream()
                    .filter(channel -> Objects.nonNull(channel) && Objects.nonNull(channel.getCarrierCode()))
                    .collect(Collectors.toMap(DeliveryChannelDto::getCarrierCode, Function.identity()));
        }catch (Exception e){
            log.error("DeliveryChannelService getDeliveryChannelDtoMap error",e);
            Arrays.stream(DeliveryChannelEnum.values())
                    .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                    .forEach(a -> channelMap.put(a.getCode(), translateFromDeliveryChannelEnum(a.getCode())));
            return channelMap;
        }
    }

    private DeliveryChannelDto translateFromDeliveryChannelEnum(Integer carrierCode) {
        if (null == carrierCode){
            return null;
        }
        DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(carrierCode);
        if (Objects.isNull(deliveryChannelEnum)) {
            return getUnknownDeliveryChannel(carrierCode);
        }

        DeliveryChannelDto deliveryChannelDto = new DeliveryChannelDto();
        deliveryChannelDto.setLogisticMark(org.apache.commons.lang3.StringUtils.EMPTY);
        if (Objects.isNull(deliveryChannelEnum.getDeliveryPlatform())) {
            // 平台配送的deliveryPlatform为null
            deliveryChannelDto.setDeliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE);
        } else {
            deliveryChannelDto.setDeliveryPlatFormCode(deliveryChannelEnum.getDeliveryPlatform().getCode());
        }
        deliveryChannelDto.setCarrierCode(deliveryChannelEnum.getCode());
        deliveryChannelDto.setCarrierName(deliveryChannelEnum.getName());
        deliveryChannelDto.setOrderChannelCode(NumberUtils.INTEGER_ZERO);

        return deliveryChannelDto;
    }

    private DeliveryChannelDto getUnknownDeliveryChannel(Integer carrierCode) {
        return DeliveryChannelDto.builder()
                .logisticMark(org.apache.commons.lang3.StringUtils.EMPTY)
                .deliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE)
                .carrierCode(carrierCode)
                .carrierName(UNKNOWN_CARRIER_NAME)
                .orderChannelCode(NumberUtils.INTEGER_ZERO).build();
    }

    private Map<Integer, DeliveryChannelDto> getDeliveryChannelDtoMapFallback(Set<Integer> channelCodes){
        log.error("getDeliveryChannelDtoMapFallback {}", channelCodes);
        return Maps.newHashMap();
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "DeliveryOrderComponent.batchQueryOrderDeliveryUrl", fallBackMethod = "batchQueryOrderDeliveryUrlFallback",
            timeoutInMilliseconds = 1000)
    public Map<String,String> batchQueryOrderDeliveryUrl(Long tenantId, Long storeId, List<Long> orderIdList, List<String> marksIdList) {
        if (!MccConfigUtil.dapOrderLinkNoAuthSwitch()) {
            return Collections.emptyMap();
        }
        if(CollectionUtils.isEmpty(orderIdList) && CollectionUtils.isEmpty(marksIdList)){
            return Collections.emptyMap();
        }
        DeliveryOrderUrlRequest orderUrlRequest = new DeliveryOrderUrlRequest();
        orderUrlRequest.setTenantId(tenantId);
        orderUrlRequest.setStoreId(storeId);
        orderUrlRequest.setOrderIdList(orderIdList);
        orderUrlRequest.setFulfillMarkIdList(marksIdList);
        DeliveryOrderUrlResponse response = deliveryOperationThriftService.queryOrderDeliveryUrl(orderUrlRequest);
        if(response==null || org.apache.commons.collections.MapUtils.isEmpty(response.getOrderUrlMap())){
            return Collections.emptyMap();
        }
        return response.getOrderUrlMap();
    }

    private Map<String,String> batchQueryOrderDeliveryUrlFallback(Long tenantId, Long storeId, List<Long> orderIdList,List<String> marksIdList, Throwable t) {
        log.warn("batchQueryOrderDeliveryUrl {} {}", storeId, t.getMessage(), t);
        return Maps.newHashMap();
    }
}
