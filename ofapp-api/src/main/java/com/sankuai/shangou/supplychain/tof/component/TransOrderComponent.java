package com.sankuai.shangou.supplychain.tof.component;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.sac.dto.model.SacAccountDto;
import com.meituan.shangou.sac.dto.request.search.AccountSearchRequest;
import com.meituan.shangou.sac.dto.request.search.QueryRoleIdsByAccountIdsRequest;
import com.meituan.shangou.sac.dto.request.search.QuerySacAccountRoleRequest;
import com.meituan.shangou.sac.dto.response.SacCommonResponse;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.meituan.shangou.sac.dto.response.search.AccountSearchResponse;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.MApiPermissionThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.RoleInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.PageQueryAccountListRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.DeliveryDimensionPoiQueryResponse;
import com.sankuai.meituan.shangou.saas.common.mobile.MobileUtil;
import com.sankuai.shangou.commons.auth.login.utils.LoginContextUtils;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.QueryEmpByFuzzyNameRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmpAccountWithRoleDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.sdms.sdk.limit.dto.LimitItemDTO;
import com.sankuai.shangou.supplychain.tof.config.LionConfigUtils;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.LimitItemDetailVO;
import com.sankuai.shangou.supplychain.tof.controller.vo.delivery.SelfRiderInfo;
import com.sankuai.shangou.supplychain.tof.controller.vo.picking.PickerInfoVO;
import com.sankuai.shangou.supplychain.tof.service.StoreConfigService;
import com.sankuai.shangou.supplychain.tof.utils.LimitTakeOrderUtils;
import com.sankuai.shangou.supplychain.tof.utils.MccConfigUtil;
import com.sankuai.shangou.supplychain.tof.wrapper.AuthThriftWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.LaborManagementServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.LimitAcceptServiceWrapper;
import com.sankuai.shangou.supplychain.tof.wrapper.OSWServiceWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/1 17:45
 **/
@Component
@Slf4j
public class TransOrderComponent {


    @Resource
    private LaborManagementServiceWrapper laborManagementServiceWrapper;

    @Resource
    private MApiPermissionThriftService.Iface mApiPermissionThriftService;

    @Resource
    private SacAccountSearchThriftService sacAccountSearchThriftService;

    @Resource
    private TEmployeeService tEmployeeService;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private LimitAcceptServiceWrapper limitAcceptServiceWrapper;

    @Resource
    private StoreConfigService storeConfigService;

    // 固定最多展示 20 条待转单骑手信息
    private static final Integer RIDER_SEARCH_MAX_NUM = 20;

    // 歪马仅拣货权限点
    private static final String WM_JUST_PICK_PERMISSION = "NEW_TASK_ONLY_PICK";


    public List<SelfRiderInfo> queryTransferableRiderBySearchKey(long storeId, String searchKey, String currentRiderPhone) {
        boolean isPhone = MobileUtil.checkMobile(searchKey);
        long tenantId = SessionContext.getCurrentSession().getTenantId();
        List<TransEmployeeInfo> empList = new ArrayList<>();
        if (isPhone) {
            // 按电话搜索骑手（员工账号）
            empList = searchEmployeeInfoListByPhoneName(storeId, searchKey, tenantId, null, currentRiderPhone,null);
        } else {
            // 按姓名搜索骑手（员工账号）
            if(MccConfigUtil.useRiderChangeByNameMethodNew()){
                empList = searchEmployeeInfoListByPhoneName(storeId, null, tenantId, null, currentRiderPhone,searchKey);

            }else {
                empList = searchEmployeeInfoListByName(storeId, searchKey, tenantId, null, currentRiderPhone);
            }

        }

        //append培训结果
        Map<Long, List<LimitItemDTO>> limitAcceptItemMap = new HashMap<>();
        Map<Long, Boolean> trainingResultMap = new HashMap<>();
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), storeId, GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            limitAcceptItemMap.putAll(limitAcceptServiceWrapper.queryLimitItemByAccountIdList(
                    LoginContextUtils.getAppLoginTenant(), empList.stream().map(TransEmployeeInfo::getAccountId).collect(Collectors.toList()), storeId));
        } else {
            trainingResultMap.putAll(getTrainingResult(tenantId, storeId, empList));
        }

        return empList.stream()
                .map(emp -> this.trans2RiderInfo(emp, limitAcceptItemMap.get(emp.getAccountId()), trainingResultMap.get(emp.getAccountId())))
                .collect(Collectors.toList());
    }

    public List<PickerInfoVO> queryTransferablePickerBySearchKey(long storeId, String searchKey, Long currentPickerAccountId) {

        boolean isPhone = MobileUtil.checkMobile(searchKey);
        long tenantId = SessionContext.getCurrentSession().getTenantId();
        List<TransEmployeeInfo> empList = new ArrayList<>();
        if (isPhone) {
            // 按电话搜索骑手（员工账号）
            empList = searchEmployeeInfoListByPhoneName(storeId, searchKey, tenantId, currentPickerAccountId, null,null);
        } else {
            // 按姓名搜索骑手（员工账号）
            empList = searchEmployeeInfoListByName(storeId, searchKey, tenantId, currentPickerAccountId, null);

        }

        //append培训结果
        Map<Long, List<LimitItemDTO>> limitAcceptItemMap = new HashMap<>();
        Map<Long, Boolean> trainingResultMap = new HashMap<>();
        if (GrayConfigUtils.judgeIsGrayStore(LoginContextUtils.getAppLoginTenant(), storeId, GrayKeyEnum.LIMIT_ACCEPT_ORDER.getGrayKey(), false)) {
            limitAcceptItemMap.putAll(limitAcceptServiceWrapper.queryLimitItemByAccountIdList(
                    LoginContextUtils.getAppLoginTenant(), empList.stream().map(TransEmployeeInfo::getAccountId).collect(Collectors.toList()), storeId));
        } else {
            trainingResultMap.putAll(getTrainingResult(tenantId, storeId, empList));
        }


        //append 是否有仅拣货tab权限
        List<Long> hasPermissionAccountIds = authThriftWrapper.queryAccountListByPoiAndAuth(tenantId,
                storeId,
                WM_JUST_PICK_PERMISSION);


        return empList.stream()
                .map(emp -> trans2PickerInfo(emp, hasPermissionAccountIds.contains(emp.getAccountId()),
                        limitAcceptItemMap.get(emp.getAccountId()), trainingResultMap.get(emp.getAccountId())))
                .collect(Collectors.toList());

    }

    private SelfRiderInfo trans2RiderInfo(TransEmployeeInfo transEmployeeInfo, List<LimitItemDTO> limitItemDTOS, Boolean isCompleteTrain) {
        SelfRiderInfo selfRiderInfo = new SelfRiderInfo();
        selfRiderInfo.setTempRiderFlag(transEmployeeInfo.getTempRiderFlag());
        selfRiderInfo.setPhone(transEmployeeInfo.getPhone());
        selfRiderInfo.setName(transEmployeeInfo.getName());
        selfRiderInfo.setAccountId(transEmployeeInfo.getAccountId());
        if (CollectionUtils.isNotEmpty(limitItemDTOS)) {
            selfRiderInfo.setLimitItemList(limitItemDTOS.stream()
                    // 限制接单类型，1-直接限制直接过，2-先提示后限制，需要先判断是否已经超过限制开始时间
                    .filter(LimitTakeOrderUtils::checkCurrentIsLimit)
                    .map(limitItemDTO -> {
                        LimitItemDetailVO limitItemDetailVO = new LimitItemDetailVO();
                        limitItemDetailVO.setReason(limitItemDTO.getReason());
                        return limitItemDetailVO;
            }).collect(Collectors.toList()));
        }

        //培训结果默认通过
        selfRiderInfo.setIsCompleteTrain(isCompleteTrain == null || isCompleteTrain);

        return selfRiderInfo;
    }

    private PickerInfoVO trans2PickerInfo(TransEmployeeInfo transEmployeeInfo, Boolean hasJustPickPermission,
                                          List<LimitItemDTO> limitItemDTOS, Boolean isCompleteTrain) {
        PickerInfoVO selfPickerInfo = new PickerInfoVO();
        selfPickerInfo.setTempRiderFlag(transEmployeeInfo.getTempRiderFlag());
        selfPickerInfo.setPhone(transEmployeeInfo.getPhone());
        selfPickerInfo.setName(transEmployeeInfo.getName());
        selfPickerInfo.setAccountId(transEmployeeInfo.getAccountId());
        selfPickerInfo.setHasJustPickPermission(hasJustPickPermission);
        if (CollectionUtils.isNotEmpty(limitItemDTOS)) {
            selfPickerInfo.setLimitItemList(limitItemDTOS.stream()
                    // 限制接单类型，1-直接限制直接过，2-先提示后限制，需要先判断是否已经超过限制开始时间
                    .filter(LimitTakeOrderUtils::checkCurrentIsLimit)
                    .map(limitItemDTO -> {
                        LimitItemDetailVO limitItemDetailVO = new LimitItemDetailVO();
                        limitItemDetailVO.setReason(limitItemDTO.getReason());
                return limitItemDetailVO;
            }).collect(Collectors.toList()));
        }

        //培训结果默认通过
        selfPickerInfo.setIsCompleteTrain(isCompleteTrain == null || isCompleteTrain);

        return selfPickerInfo;
    }

    private List<TransEmployeeInfo> searchEmployeeInfoListByPhoneName(long storeId, String phoneSearchKey,
                                                                  long tenantId,
                                                                  @Nullable Long currentRiderAccountId,
                                                                  @Nullable String currentRiderPhone,String nameSearchKey) {
        List<TransEmployeeInfo> transEmployeeInfoList = new ArrayList<>();
        List<AccountInfoVo> accountInfoVos = new ArrayList<>();

        if(StringUtils.isNotEmpty(phoneSearchKey)){
            if (LionConfigUtils.isNewAuthApiGrayTenant(tenantId)) {
                accountInfoVos = queryAccountByPhoneAndName(tenantId, phoneSearchKey,null);
            } else {
                accountInfoVos = queryAccountByPhoneOld(tenantId, phoneSearchKey);
            }
        }else if(StringUtils.isNotEmpty(nameSearchKey)){
            accountInfoVos = queryAccountByPhoneAndName(tenantId, null,nameSearchKey);
        }


        if (CollectionUtils.isEmpty(accountInfoVos)) {
            return Collections.emptyList();
        }

        DeliveryDimensionPoiQueryResponse.DeliveryDimensionPoiDto dimensionPoiDto = storeConfigService.queryDeliveryDimensionConfig(tenantId,storeId);
        if(dimensionPoiDto!=null && CollectionUtils.isNotEmpty(dimensionPoiDto.getRiderTransRoles())){
            accountInfoVos = accountInfoVos.stream().filter(v->{
                if(CollectionUtils.isEmpty(v.getRoleList())){
                    return  false;
                }
                return v.getRoleList().stream().anyMatch(r->dimensionPoiDto.getRiderTransRoles().contains(r.getId()));
            }).collect(Collectors.toList());
        }

        // 根据门店权限过滤
        List<AccountInfoVo> accountsWithPermission = authThriftWrapper.batchQueryAccountListWithPoiPermission(tenantId, storeId);
        List<Long> accountIdsWithPermission = accountsWithPermission.stream().map(AccountInfoVo::getAccountId).collect(Collectors.toList());
        List<AccountInfoVo> finalAccounts = Lists.newArrayListWithExpectedSize(RIDER_SEARCH_MAX_NUM);
        for (AccountInfoVo account : accountInfoVos) {
            if (!accountIdsWithPermission.contains(account.getAccountId())) {
                continue;
            }
            if (Objects.nonNull(currentRiderAccountId) && Objects.equals(account.getAccountId(), currentRiderAccountId)) {
                continue;
            }
            if (StringUtils.isNotBlank(currentRiderPhone) && Objects.equals(account.getMobile(), currentRiderPhone)) {
                continue;
            }

            finalAccounts.add(account);
            if (finalAccounts.size() >= RIDER_SEARCH_MAX_NUM) {
                break;
            }
        }
        // 查询员工详细信息，并填充给 transEmployeeInfo
        Map<Long, AccountInfoVo> empIdAccountMap = finalAccounts.stream().collect(Collectors.toMap(AccountInfoVo::getStaffId,
                Function.identity(), (ov, nv) -> nv));
        Map<Long, EmployeeDTO> empMap = oswServiceWrapper.queryEmpByIds(tenantId, Lists.newArrayList(empIdAccountMap.keySet()));

        for (Map.Entry<Long, AccountInfoVo> empId2Account : empIdAccountMap.entrySet()) {
            EmployeeDTO employee = empMap.get(empId2Account.getKey());

            //因为培训不通过会阻止转单 所以这里默认为培训通过 防止影响配送效率
            transEmployeeInfoList.add(new TransEmployeeInfo(empId2Account.getValue().getAccountId(), employee.getEmpName(),
                    employee.getPhoneNumber(), isTempRider(empId2Account.getValue())));
        }
        return transEmployeeInfoList;
    }

    /**
     * 判断账号是否为临时骑手账号
     * 临时账号的判断条件：账号类型为外部账号 && 角色包含临时骑手角色
     * @param accountInfoVo 骑手信息
     * @return 是否为临时骑手账号
     */
    private static Boolean isTempRider(AccountInfoVo accountInfoVo) {
        Objects.requireNonNull(accountInfoVo);

        if (CollectionUtils.isEmpty(accountInfoVo.getRoleList())) {
            return false;
        }

        return accountInfoVo.getAccountType() == AccountTypeEnum.OUTSOURCING.getValue()
                && accountInfoVo.getRoleList().stream()
                .anyMatch(roleInfoVo -> MccConfigUtil.tempRiderRoleIds().contains(String.valueOf(roleInfoVo.getId())));
    }

    private List<TransEmployeeInfo> searchEmployeeInfoListByName(long storeId, String searchKey, long tenantId,
                                                                 Long currentRiderAccountId,
                                                                 String currentRiderPhone) {
        List<EmpAccountWithRoleDTO> empAccountWithRoleDTOS = queryEmpByFuzzyNameWithStorePermission(storeId, searchKey, tenantId);
        // 过滤掉当前骑手
        return IListUtils.mapTo(
                empAccountWithRoleDTOS.stream()
                        .filter(v -> !(Objects.nonNull(currentRiderAccountId) && Objects.equals(v.getAccountId(), currentRiderAccountId)))
                        .filter(v -> !(StringUtils.isNotBlank(currentRiderPhone) && Objects.equals(v.getPhoneNumber(), currentRiderPhone)))
                        .collect(Collectors.toList()),
                empAccountWithRoleDTO -> {
                    TransEmployeeInfo selfRiderInfo = new TransEmployeeInfo();
                    selfRiderInfo.setAccountId(empAccountWithRoleDTO.getAccountId());
                    selfRiderInfo.setName(empAccountWithRoleDTO.getEmpName());
                    selfRiderInfo.setPhone(empAccountWithRoleDTO.getPhoneNumber());
                    selfRiderInfo.setTempRiderFlag(isTempRider(empAccountWithRoleDTO));
                    return selfRiderInfo;
                });
    }

    private List<EmpAccountWithRoleDTO> queryEmpByFuzzyNameWithStorePermission(long storeId, String searchKey, long tenantId) {
        try {
            QueryEmpByFuzzyNameRequest request = new QueryEmpByFuzzyNameRequest();
            request.setTenantId(tenantId);
            request.setEmpName(searchKey);
            request.setStoreId(storeId);
            TResult<List<EmpAccountWithRoleDTO>> result = tEmployeeService.queryEmpByFuzzyNameWithStorePermission(request);
            if (!result.isSuccess()) {
                throw new ThirdPartyException("模糊查询员工信息失败");
            }
            return Optional.ofNullable(result.getData()).orElse(Lists.newArrayList());
        } catch (Exception e) {
            log.error("tEmployeeService.queryEmpByFuzzyNameWithStorePermission error", e);
            throw new ThirdPartyException("模糊查询员工信息失败");
        }
    }

    private static Boolean isTempRider(EmpAccountWithRoleDTO empAccountWithRoleDTO) {

        if (CollectionUtils.isEmpty(empAccountWithRoleDTO.getRoleList())) {
            return false;
        }

        return empAccountWithRoleDTO.getAccountType() == AccountTypeEnum.OUTSOURCING.getValue()
                && empAccountWithRoleDTO.getRoleList().stream()
                .anyMatch(roleInfoVo -> MccConfigUtil.tempRiderRoleIds().contains(String.valueOf(roleInfoVo.getRoleId())));
    }

    /**
     * 根据租户 ID 和电话号码哈查询账号信息. 走旧鉴权接口
     *
     * @param tenantId 租户 ID
     * @param phoneNum 电话号码
     * @return List<AccountInfoVo> NotEmpty
     */
    private List<AccountInfoVo> queryAccountByPhoneOld(long tenantId, String phoneNum) {
        PageQueryAccountListRequest tRequest = new PageQueryAccountListRequest();
        tRequest.setTenantId(tenantId);
        tRequest.setMobile(phoneNum);
        tRequest.setValid(1);
        AccountInfoListResponse response;
        try {
            log.info("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList. request:{}", tRequest);
            response = mApiPermissionThriftService.queryAccountInfoList(tRequest);
            log.info("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList. response:{}", response);
        } catch (Exception e) {
            log.error("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList error. request:{}", tRequest, e);
            throw new ThirdPartyException("按电话号码查询账号失败");
        }

        if (!Objects.equals(Optional.ofNullable(response).map(AccountInfoListResponse::getCode).orElse(null),
                ResultCodeEnum.SUCCESS.getValue())) {
            log.warn("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList fail. request:{}, response:{}", tRequest,
                    response);
            throw new BizException("按电话号码查询账号失败");
        }
        List<AccountInfoVo> accountInfoList = response.getAccountInfoList();
        if (CollectionUtils.isEmpty(accountInfoList)) {
            return Collections.emptyList();
        }
        return accountInfoList;
    }

    /**
     * 根据租户 ID 和电话号码哈查询账号信息. 走新鉴权接口
     *
     * @param tenantId 租户 ID
     * @param phoneNum 电话号码
     * @return List<AccountInfoVo> NotEmpty
     */
    private List<AccountInfoVo> queryAccountByPhoneAndName(long tenantId, String phoneNum,String name) {
        AccountSearchRequest tRequest = new AccountSearchRequest();
        tRequest.setTenantId(tenantId);
        if(StringUtils.isNotEmpty(phoneNum)){
            tRequest.setMobile(phoneNum);
        }

        if(StringUtils.isNotEmpty(name)){
            tRequest.setFuzzyAccountNameOrEmpName(name);
        }

        tRequest.setIncludeInvalid(false);
        tRequest.setSize(LionConfigUtils.getSearchSize());

        AccountSearchResponse response = null;
        List<SacAccountDto> accountInfoListDto = new ArrayList<>();
        Map<Long, List<Long>> roleListMap = new HashMap<>();
        int searchTimes = 0;

        do {
            searchTimes++;

            if (response != null && response.getHasMore()) {
                // 滚动查询
                tRequest.setSearchAfterAccountId(response.getSearchAfterAccountId());
            }

            try {
                log.info("RiderManageWrapper call SacAccountSearchThriftService#search. request:{}", tRequest);
                response = sacAccountSearchThriftService.search(tRequest);
                log.info("RiderManageWrapper call SacAccountSearchThriftService#search. response:{}", response);
            } catch (Exception e) {
                log.error("RiderManageWrapper call SacAccountSearchThriftService#search error. request:{}", tRequest, e);
                throw new ThirdPartyException("按电话号码查询账号失败");
            }

            if (!Objects.equals(Optional.ofNullable(response).map(AccountSearchResponse::getSacStatus).map(SacStatus::getCode).orElse(null),
                    ResultCodeEnum.SUCCESS.getValue())) {
                log.warn("RiderManageWrapper call SacAccountSearchThriftService#search fail. request:{}, response:{}", tRequest,
                        response);
                throw new BizException("按电话号码查询账号失败");
            }

            List<SacAccountDto> accountDtoList = response.getData();

            // 获取roleList
            QueryRoleIdsByAccountIdsRequest roleListReq = new QueryRoleIdsByAccountIdsRequest();
            roleListReq.setTenantId(tenantId);
            roleListReq.setAccountIds(accountDtoList.stream().map(SacAccountDto::getAccountId).collect(Collectors.toList()));
            SacCommonResponse<Map<Long, List<Long>>> roleListResponse = sacAccountSearchThriftService.queryRoleIdsByAccountIds(roleListReq);
            if (roleListResponse.getSacStatus().getCode() != ResultCodeEnum.SUCCESS.getValue() || roleListResponse.getResult() == null) {
                throw new BizException("按电话号码查询账号时，查询角色权限列表失败");
            }

            roleListMap.putAll(roleListResponse.getResult());
            accountInfoListDto.addAll(accountDtoList);

            if (searchTimes > LionConfigUtils.getMaxSearchTimes()) {
                break;
            }

        } while (response.getHasMore());

        if (CollectionUtils.isEmpty(accountInfoListDto)) {
            return Collections.emptyList();
        }

        return accountInfoListDto.stream()
                .map(accountInfo -> convertAccountInfoDtoToVo(accountInfo, roleListMap))
                .collect(Collectors.toList());
    }

    /**
     * 转换账号信息使Dto兼容Vo
     *
     * @param sacAccountDto 账号信息 DTO
     * @return AccountInfoVo 账号信息 VO
     */
    private AccountInfoVo convertAccountInfoDtoToVo(SacAccountDto sacAccountDto, Map<Long, List<Long>> roleListMap) {
        AccountInfoVo accountInfoVo = new AccountInfoVo();
        accountInfoVo.setTenantId(sacAccountDto.getTenantId());
        accountInfoVo.setAccountId(sacAccountDto.getAccountId());
        accountInfoVo.setStaffId(sacAccountDto.getStaffId());
        List<RoleInfoVo> roleInfoVos = new ArrayList<>(Fun.map(
                roleListMap.get(sacAccountDto.getAccountId()), id -> {
                    RoleInfoVo roleInfoVo = new RoleInfoVo();
                    roleInfoVo.setId(id);
                    return roleInfoVo;
                }
        ));
        accountInfoVo.setRoleList(roleInfoVos);
        accountInfoVo.setMobile(sacAccountDto.getMobile());
        accountInfoVo.setValid(sacAccountDto.getValid());
        accountInfoVo.setAccountType(sacAccountDto.getAccountType());
        accountInfoVo.setUsername(sacAccountDto.getUsername());
        accountInfoVo.setEpAccountId(sacAccountDto.getEpAccountId());
        accountInfoVo.setMobileSync(sacAccountDto.getMobileSync());
        accountInfoVo.setMisId(sacAccountDto.getMtDxMis());
        accountInfoVo.setCreateTime(sacAccountDto.getCreateTime());
        accountInfoVo.setAccountId(sacAccountDto.getAccountId());
        accountInfoVo.setHsAccountName(sacAccountDto.getHsAccountName());
        accountInfoVo.setMeituanMisRelatedSource(sacAccountDto.getMtDxMisRelatedSource() == null ? 0 : sacAccountDto.getMtDxMisRelatedSource());
        accountInfoVo.setSupplierId(sacAccountDto.getSupplierId());
        accountInfoVo.setSupplierName(sacAccountDto.getSupplierName());
        accountInfoVo.setAccountManageType(sacAccountDto.getAccountManageType());
        return accountInfoVo;
    }

    private Map<Long, Boolean> getTrainingResult(Long tenantId, Long storeId, List<TransEmployeeInfo> empList) {
        try {
            if (!MccConfigUtil.isTrainingGrayStore(storeId)) {
                return Collections.emptyMap();
            }

            if (CollectionUtils.isEmpty(empList)) {
                return Collections.emptyMap();
            }

            return laborManagementServiceWrapper.batchQueryTrainingResult(tenantId, empList.stream().map(TransEmployeeInfo::getAccountId).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("查询培训结果失败", e);
            Cat.logEvent("QUERY_TRAINING_RESULT", "ERROR");
            return Collections.emptyMap();
        }
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class TransEmployeeInfo {
        @FieldDoc(
                description = "骑手账号 ID", requiredness = Requiredness.REQUIRED
        )
        private Long accountId;

        @FieldDoc(
                description = "骑手姓名", requiredness = Requiredness.REQUIRED
        )
        private String name;

        @FieldDoc(
                description = "骑手电话", requiredness = Requiredness.REQUIRED
        )
        private String phone;

        @FieldDoc(
                description = "是否为临时骑手", requiredness = Requiredness.REQUIRED
        )
        private Boolean tempRiderFlag;

//        @FieldDoc(
//                description = "是否培训通过", requiredness = Requiredness.OPTIONAL
//        )
//        private Boolean isCompleteTrain;
//
//        @FieldDoc(
//                description = "限制项列表", requiredness = Requiredness.OPTIONAL
//        )
//        private List<LimitItemDetailVO> limitItemDetailVOS;
    }
}
