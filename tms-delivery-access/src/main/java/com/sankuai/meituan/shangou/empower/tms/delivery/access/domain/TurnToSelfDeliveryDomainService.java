package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.ResultStatus;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.SelfDeliveryRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.SelfDeliveryCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.SelfDeliveryResult;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.TransferOrderSquirrelServiceClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 订单平台配送转自配送领域服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/6
 */
@Slf4j
@Service
public class TurnToSelfDeliveryDomainService {

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private LaunchDeliveryDomainService launchDeliveryDomainService;

	@Resource
	private ChannelOrderDockingThriftService.Iface channelOrderDockingThriftServiceClient;

	@Resource
	private DeliveryNotifyService deliveryNotifyService;

	@Resource
	private RouteFacade routeFacade;

	@Resource
	private TransferOrderSquirrelServiceClient transferOrderSquirrelServiceClient;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@Resource
	private TenantSystemClient tenantSystemClient;

	private static final Integer PRIVATE_DOMAIN_CHANNEL = 0;

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> turnToThirdDelivery(DeliveryPoi deliveryPoi,
	                                             OrderInfo orderInfo,
	                                             DeliveryChannelEnum deliveryChannel,
	                                             String servicePackage,
	                                             BigDecimal estimatedDeliveryFee) {
		//查当前所有运单
		List<DeliveryOrder> deliveryOrders = new ArrayList<>();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMasterWithTenant(orderInfo.getOrderKey().getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
		}else {
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderInfo.getOrderKey().getOrderId());
		}


		//先转为自配送
		Optional<Failure> turnToSelfDeliveryFailure = turnOrderToSelfDelivery(orderInfo, deliveryOrders);
		if (turnToSelfDeliveryFailure.isPresent()) {
			return turnToSelfDeliveryFailure;
		}

		//发起三方配送
		return launchDeliveryDomainService.launchDelivery(deliveryPoi, orderInfo, deliveryChannel, servicePackage, estimatedDeliveryFee, false,PlatformSourceEnum.OMS,null);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> turnToMerchantSelfDelivery(OrderInfo orderInfo) {
		//先将订单转为自配送
		List<DeliveryOrder> deliveryOrders = new ArrayList<>();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(orderInfo.getOrderKey().getTenantId())){
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMasterWithTenant(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		}else {
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderInfo.getOrderKey().getOrderId());
		}

		Optional<Failure> turnToSelfDeliveryFailure = turnOrderToSelfDelivery(orderInfo, deliveryOrders);
		if (turnToSelfDeliveryFailure.isPresent()) {
			return turnToSelfDeliveryFailure;
		}

		//流转订单配送状态到自配送
		orderSystemClient.turnOrderDeliveryStatusToSelfDelivery(
				orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getOrderId(), orderInfo.getOrderBizType(), null
		);

		//创建商家自配送运单
		DeliveryOrder deliveryOrder = DeliveryOrder.createMerchantDeliveryOrder(orderInfo);
		deliveryOrderRepository.save(deliveryOrder);

		return Optional.empty();
	}


	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	public Optional<Failure> turnToMerchantSelfDeliveryForDrunkHorse(OrderInfo orderInfo, StaffRider rider) {
		//校验转配送的次数
		List<DeliveryOrder> deliveryOrderList = new ArrayList<>();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(orderInfo.getOrderKey().getTenantId())){
			deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersForceMasterWithTenant(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		}else {
			deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderInfo.getOrderKey().getOrderId());
		}

		if (getLaunchMerchantDeliveryCount(deliveryOrderList) >= MccConfigUtils.dhTransDeliveryChannelTimesLimit()) {
			return Optional.of(new Failure(false, FailureCodeEnum.TURN_DELIVERY_TYPE_TIMES_EXCEED_LIMIT));
		}

		List<DeliveryOrder> sortedDeliveryOrderList = deliveryOrderList.stream()
				.sorted(Comparator.comparing(DeliveryOrder::getCreateTime))
				.collect(Collectors.toList());
		DeliveryOrder lastDeliveryOrder = sortedDeliveryOrderList.get(sortedDeliveryOrderList.size() - 1);

		//校验最后一次配送的配送方式
		if (Objects.equals(lastDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
			return Optional.of(new Failure(false, FailureCodeEnum.TRANS_SELF_DELIVERY_OPERATE_OPERATE));
		}

		//将配送平台保存在缓存中
		String key = transferOrderSquirrelServiceClient.getKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
		transferOrderSquirrelServiceClient.set(key,new TransferOperateInfo(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY, null, null));

		//发送MQ
		deliveryNotifyService.notifyDeliveryTransMsg(orderInfo.getOrderKey().getTenantId(),
				orderInfo.getOrderKey().getStoreId(),
				orderInfo.getOrderKey().getOrderId(),
				rider.getRiderAccountId(),
				rider.getRiderName(),
				rider.getRiderPhone());

		return Optional.empty();
	}

	private Integer getLaunchMerchantDeliveryCount(List<DeliveryOrder> deliveryOrders) {
		if (CollectionUtils.isEmpty(deliveryOrders)) {
			return 0;
		}

		return (int) deliveryOrders.stream()
				 .filter(deliveryOrder -> Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()))
				 .count();
	}

	private Optional<Failure> turnOrderToSelfDelivery(OrderInfo orderInfo, List<DeliveryOrder> deliveryOrders) {
		//该订单无任何运单不处理，有多个运单不处理
		if (CollectionUtils.isEmpty(deliveryOrders) || deliveryOrders.size() > 1) {
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		//当前运单非平台配送运单不处理
		DeliveryOrder currentDeliveryOrder = deliveryOrders.get(0);
		if (DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() != currentDeliveryOrder.getDeliveryChannel()) {
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		//调订单转自配送接口
		Optional<Failure> failure = orderSystemClient.turnToSelfDelivery(
				orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getOrderId(), orderInfo.getOrderBizType(), orderInfo.getChannelOrderId()
		);
		if (failure.isPresent()) {
			log.warn("订单[{}]转自配送失败, 失败原因：{}", orderInfo.getOrderKey(), failure.get());
			return failure;
		}

		//取消当前运单
		Optional<Failure> cancelFailure = currentDeliveryOrder.cancel();
		if (cancelFailure.isPresent()) {
			log.warn("订单[{}]转自配送后，取消当前平台配送运单失败，失败原因：{}", orderInfo.getOrderKey(), cancelFailure.get());
			return cancelFailure;
		}
		return Optional.empty();
	}


	private Optional<Failure> turnOrderToSelfDelivery(OrderInfo orderInfo, DeliveryOrder currentDeliveryOrder) {
		//调订单转自配送接口
		Optional<Failure> failure = orderSystemClient.turnToSelfDelivery(
				orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getOrderId(), orderInfo.getOrderBizType(), orderInfo.getChannelOrderId()
		);
		if (failure.isPresent()) {
			log.warn("订单[{}]转自配送失败, 失败原因：{}", orderInfo.getOrderKey(), failure.get());
			return failure;
		}

		//取消当前运单
		Optional<Failure> cancelFailure = currentDeliveryOrder.cancel();
		if (cancelFailure.isPresent()) {
			log.warn("订单[{}]转自配送后，取消当前平台配送运单失败，失败原因：{}", orderInfo.getOrderKey(), cancelFailure.get());
			return cancelFailure;
		}
		return Optional.empty();
	}


	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> turnToAggregationDelivery(DeliveryPoi deliveryPoi,
													   OrderInfo orderInfo,
													   DeliveryPlatformEnum deliveryPlatform, Operator operator) {
		//查当前所有运单
		List<DeliveryOrder> deliveryOrders = new ArrayList<>();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(deliveryPoi.getTenantId())){
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMasterWithTenant(orderInfo.getOrderKey().getOrderId(),deliveryPoi.getTenantId(),deliveryPoi.getStoreId());
		}else {
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderInfo.getOrderKey().getOrderId());
		}

		//先转为自配送
		SelfDeliveryResult turnToSelfDeliveryFailure= turnOrderToSelfOrder(orderInfo, deliveryOrders,deliveryPoi,operator, deliveryPlatform);

		//美团、有赞、抖音、私域渠道、非平台配送则直接发起转单
		if (directTurnOrderToSelfOrder(orderInfo, turnToSelfDeliveryFailure)) {
			if (MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId())) {
				deliveryNotifyService.notifyDeliveryTransMsg(deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), orderInfo.getOrderKey().getOrderId());
			} else {
				deliveryNotifyService.notifyDeliveryTransMsg(deliveryPoi.getTenantId(), deliveryPoi.getStoreId(), orderInfo.getOrderKey().getOrderId(), deliveryPoi.getDeliveryPlatform().getCode());
			}
			return Optional.empty();
		}
		if ((Objects.equals(DynamicOrderBizType.ELE_ME.getValue(), orderInfo.getOrderBizType())
				|| Objects.equals(DynamicOrderBizType.JING_DONG.getValue(), orderInfo.getOrderBizType())) && turnToSelfDeliveryFailure.getStatus() == 0) {
			return Optional.empty();
		}
		if (turnToSelfDeliveryFailure.getStatus() == SelfDeliveryCodeEnum.INVALID_TRANS.getCode()) {
			return Optional.empty();
		}
		return Optional.of(new Failure(false, FailureCodeEnum.ORDER_TRANS_SELF_ERROR, turnToSelfDeliveryFailure.getMsg()));
	}

	private boolean directTurnOrderToSelfOrder (OrderInfo orderInfo, SelfDeliveryResult turnToSelfDeliveryFailure) {
		List<Integer> orderBizTypes = Arrays.asList(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), DynamicOrderBizType.YOU_ZAN_MIDDLE.getValue(), DynamicOrderBizType.DOU_YIN.getValue());
		boolean isPrivateChannel = false;
		DynamicChannelType dynamicChannelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
		if (Objects.nonNull(dynamicChannelType)) {
			isPrivateChannel = Objects.equals(dynamicChannelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL);
		}
		if ((orderBizTypes.contains(orderInfo.getOrderBizType()) || isPrivateChannel) && turnToSelfDeliveryFailure.getStatus() == SelfDeliveryCodeEnum.SUCCESS.getCode()) {
			return true;
		}
		if (turnToSelfDeliveryFailure.getStatus() == SelfDeliveryCodeEnum.AGG_SUCCESS.getCode()) {
			return true;
		}
		return false;
	}

	private SelfDeliveryResult turnOrderToSelfOrder(OrderInfo orderInfo, List<DeliveryOrder> deliveryOrders,DeliveryPoi deliveryPoi,Operator operator,DeliveryPlatformEnum deliveryPlatform) {

		if (CollectionUtils.isEmpty(deliveryOrders)) {
			if (orderInfo.getOriginalDistributeType() == DistributeTypeEnum.SELF_DELIVERY.getValue()) {
				return SelfDeliveryResult.isSuccess();
			}
			if (Objects.equals(DynamicOrderBizType.YOU_ZAN_MIDDLE.getValue(), orderInfo.getOrderBizType()) || Objects.equals(DynamicOrderBizType.DOU_YIN.getValue(), orderInfo.getOrderBizType())) {
				return SelfDeliveryResult.isSuccess();
			} else {
				//无运单可能是订单是自配送，但是没开牵牛花配送，直接平台转自配送
				return platformToSelfDelivery(orderInfo,deliveryPoi,operator,null,null);
			}
		}

		//当前运单非平台配送运单不处理
		DeliveryOrder currentDeliveryOrder = deliveryOrders.stream().filter(new Predicate<DeliveryOrder>() {
			@Override
			public boolean test(DeliveryOrder deliveryOrder) {
				return deliveryOrder.isActive();
			}
		}).findFirst().orElse(deliveryOrders.stream().sorted(new Comparator<DeliveryOrder>() {
			@Override
			public int compare(DeliveryOrder o1, DeliveryOrder o2) {
				return o1.getCreateTime().isAfter(o2.getCreateTime()) ? -1:1;
			}
		}).findFirst().get());
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(currentDeliveryOrder.getDeliveryChannel());
		if (deliveryChannel.getDeliveryPlatFormCode() == deliveryPlatform.getCode()) {
			return SelfDeliveryResult.inValidTrans();
		}
		//美团、京东、饿了么平台配送转自配送
		if (DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() == currentDeliveryOrder.getDeliveryChannel()) {
			if (Objects.equals(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), orderInfo.getOrderBizType()) && currentDeliveryOrder.getStatus().isFinalStatus()) {
				return SelfDeliveryResult.isSuccess();
			}
			return platformToSelfDelivery(orderInfo,deliveryPoi,operator, currentDeliveryOrder,null);
		}
		//有赞、抖音、私域渠道平台配送转自配送
		if (DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode()) {
			if (notNeedInvokeChannel(orderInfo, currentDeliveryOrder)) {
				return SelfDeliveryResult.isSuccess();
			}
			return platformToSelfDelivery(orderInfo,deliveryPoi,operator, currentDeliveryOrder, deliveryChannel.getLogisticMark());
		}
		return SelfDeliveryResult.isSuccess();
	}


	private boolean notNeedInvokeChannel(OrderInfo orderInfo, DeliveryOrder deliveryOrder) {
		DynamicChannelType dynamicChannelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
		if (dynamicChannelType == null) {
			return false;
		}
		if (Objects.equals(dynamicChannelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL)) {
			return false;
		}
		if (!Objects.equals(DynamicOrderBizType.YOU_ZAN_MIDDLE.getValue(), orderInfo.getOrderBizType()) && !Objects.equals(DynamicOrderBizType.DOU_YIN.getValue(), orderInfo.getOrderBizType())) {
			return false;
		}
		if (Objects.isNull(deliveryOrder)) {
			List<Integer> distributeStatusList = Arrays.asList(DistributeStatusEnum.DISTRIBUTE_UNKNOWN.getValue(), DistributeStatusEnum.UN_KNOWN.getValue(), DistributeStatusEnum.DISTRIBUTE_CANCELED.getValue());
			return distributeStatusList.contains(orderInfo.getDistributeStatus());
		}
		return deliveryOrder.getStatus().getCode() == DeliveryStatusEnum.INIT.getCode()||
				deliveryOrder.getStatus().getCode() == DeliveryStatusEnum.DELIVERY_CANCELLED.getCode() ||
				deliveryOrder.getStatus().getCode() == DeliveryStatusEnum.DELIVERY_REJECTED.getCode();
	}

	private SelfDeliveryResult platformToSelfDelivery(OrderInfo orderInfo, DeliveryPoi deliveryPoi, Operator operator, DeliveryOrder deliveryOrder, String logisticMark){
		DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
				orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
		SelfDeliveryRequest selfDeliveryRequest=new SelfDeliveryRequest();
		selfDeliveryRequest.setTenantId(deliveryPoi.getTenantId());
		selfDeliveryRequest.setOrderId(orderInfo.getChannelOrderId());
		selfDeliveryRequest.setChannelId(orderBizType.getChannelId());
		selfDeliveryRequest.setStoreId(orderInfo.getOrderKey().getStoreId());
		if(orderInfo!=null && orderInfo.getWarehousePoiId()!=null){
			selfDeliveryRequest.setStoreId(orderInfo.getWarehousePoiId());
		}
		if(operator!=null){
			selfDeliveryRequest.setOperator(operator.getOperatorName());
		}
		if (Objects.nonNull(deliveryOrder) && StringUtils.isNotBlank(logisticMark)) {
			selfDeliveryRequest.setChannelDeliveryId(deliveryOrder.getChannelDeliveryId());
			selfDeliveryRequest.setDeliveryChannel(logisticMark);
		}
		if (Objects.nonNull(deliveryOrder) && Objects.equals(DynamicOrderBizType.DOU_YIN.getValue(), orderInfo.getOrderBizType())) {
			selfDeliveryRequest.setIsQnhManagement(Optional.ofNullable(deliveryOrder.getIsQhnManagement()).orElse(0));
		}
		if (orderInfo!=null && orderInfo.getOrderKey()!= null) {
			selfDeliveryRequest.setDeliveryOrderId(String.valueOf(orderInfo.getOrderKey().getOrderId()));
		}
		String channelPoiId = tenantSystemClient.queryChannelPoiIdByPoiId(orderInfo.getOrderKey().getTenantId(), orderInfo.getPoiId(), orderBizType.getChannelId());
		selfDeliveryRequest.setChannelStoreId(channelPoiId);

		try {
			ResultStatus resultStatus = channelOrderDockingThriftServiceClient.selfDelivery(selfDeliveryRequest);
			log.info("selfDelivery SelfDeliveryRequest:{} ,resultStatus:{}",selfDeliveryRequest,resultStatus);
			if(resultStatus.getCode() == 0){
				return SelfDeliveryResult.builder().status(resultStatus.getCode())
						.msg(null).build();
			}
			return SelfDeliveryResult.builder()
					.status(FailureCodeEnum.ORDER_TRANS_SELF_ERROR.getCode())
					.msg(resultStatus.getMsg()).build();
		} catch (TException e) {
			log.error("selfDelivery error SelfDeliveryRequest:{}",selfDeliveryRequest,e);
			return SelfDeliveryResult.builder()
					.status(FailureCodeEnum.ORDER_TRANS_SELF_ERROR.getCode())
					.msg("订单转为自配送失败").build();
		}
	}

}
