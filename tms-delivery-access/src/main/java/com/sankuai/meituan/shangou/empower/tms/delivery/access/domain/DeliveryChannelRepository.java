package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;

/**
 * 配送渠道接口
 *
 * <AUTHOR>
 * @date 2023/4/7
 */
public interface DeliveryChannelRepository {

    /**
     * @description: 查询配送渠道信息
     * @param: deliveryPlatFormCode 配送平台code
     * @param: orderChannelCode 订单渠道code，主要针对平台配送
     * @param: logisticMark 承运商信息，麦芽田是channelMark，青云和有赞是channelCode
     * @return 配送渠道信息
    */
    Optional<DeliveryChannel> getDeliveryChannelByLogisticMark(Integer deliveryPlatFormCode, Integer orderChannelCode, String logisticMark);

    /**
     * @description: 查询配送渠道信息
     * @param: carrierCode 牵牛花维护的承运商code
     * @return 配送渠道信息
     */
    Optional<DeliveryChannel> getDeliveryChannelByCarrierCode(Integer carrierCode);

    /**
     * @description: 批量查询配送渠道信息
     * @return 全量配送渠道信息
     */
    default List<DeliveryChannel> getBatchDeliveryChannelList(Long startId, Long endId) {return Collections.emptyList();}

    /**
     * @description: 查询MySQL中主键ID的最大值
     * @return 主键ID的最大值
     */
    default Long getMaxId() {return NumberUtils.LONG_ZERO;}

    /**
     * @description: 根据承运商code列表批量查询配送渠道信息
     * @param: carrierCodeList 承运商code列表
     * @return 配送渠道信息
     */
    Map<Integer, Optional<DeliveryChannel>> getDeliveryChannelMapByCarrierCodeSet(Set<Integer> carrierCodeSet);

    /**
     * @description: 更新全量配送渠道信息
     * @param: allDeliveryChannelList 全量配送渠道信息
     */
    default Boolean updateAllDeliveryChannelList(List<DeliveryChannel> allDeliveryChannelList) {return true;}
}
