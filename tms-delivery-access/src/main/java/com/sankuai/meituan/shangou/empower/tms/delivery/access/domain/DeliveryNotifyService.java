package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;

public interface DeliveryNotifyService {

    void notifyDeliveryTransMsg(Long tenantId, Long storeId, Long orderId);

    void notifyDeliveryTransMsg(Long tenantId, Long storeId, Long orderId, Long riderAccountId, String riderName, String riderPhone);

    void notifyDeliveryTrace(OrderTrackEvent event);

    void notifyDeliveryTransMsg(Long tenantId, Long storeId, Long orderId, Integer deliveryPlatformCode);

}
