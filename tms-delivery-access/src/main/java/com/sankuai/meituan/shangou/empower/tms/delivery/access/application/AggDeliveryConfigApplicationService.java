package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.AggDeliveryPlatformAppConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryAggDeliveryRedirectModuleKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryRedirectDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryRedirectModule;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 聚合配送平台相关应用层服务
 * <AUTHOR>
 */
@Service
@Slf4j
public class AggDeliveryConfigApplicationService {

    @Autowired
    private DapLinkRepository dapLinkRepository;

    @Autowired
    private DeliveryOrderRepository deliveryOrderRepository;

    @Autowired
    @Qualifier("squirrelDeliveryChannelRepository")
    private DeliveryChannelRepository deliveryChannelRepository;

    @MethodLog(logRequest = false, logResponse = true)
    @CatTransaction
    public List<DeliveryOrderRedirectModule> queryAggDeliveryConfig(List<OrderDeliveryDetail> deliveryDetails, Map<Long, Integer> orderId2DeliveryPlatformCodeMap, Long shopId) {
        try {
            if (CollectionUtils.isEmpty(deliveryDetails) || MapUtils.isEmpty(orderId2DeliveryPlatformCodeMap)) {
                return Collections.emptyList();
            }

            List<OrderDeliveryDetail> maltDeliveryDetails = deliveryDetails.stream().filter(deliveryDetail -> Objects.equals(orderId2DeliveryPlatformCodeMap.get(deliveryDetail.getOrderKey().getOrderId()), DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())).collect(Collectors.toList());
            List<OrderDeliveryDetail> dapDeliveryDetails = deliveryDetails.stream().filter(deliveryDetail -> Objects.equals(orderId2DeliveryPlatformCodeMap.get(deliveryDetail.getOrderKey().getOrderId()), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())).collect(Collectors.toList());

            List<DeliveryOrderRedirectModule> result = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(maltDeliveryDetails)) {
                List<DeliveryOrderRedirectModule> maltRedirectModules = maltDeliveryDetails.stream().map(deliveryDetail -> {
                    QueryAggDeliveryConfigParam param = buildQueryAggDeliveryConfigParam(deliveryDetail);
                    return getMaltRedirectModule(param, shopId);
                }).collect(Collectors.toList());
                result.addAll(maltRedirectModules);
            }
            if (CollectionUtils.isNotEmpty(dapDeliveryDetails)) {
                // 按门店维度批量查询青云url
                List<QueryDapLinkParam> queryDapLinkParamList = dapDeliveryDetails.stream().map(this::buildQueryDapLinkParam).collect(Collectors.toList());
                Map<String, String> dapLinkUrlMap = batchQueryDapRedirectUrl(queryDapLinkParamList);
                List<DeliveryOrderRedirectModule> dapRedirectModules = dapDeliveryDetails.stream()
                        .map(deliveryDetail -> {
                            QueryAggDeliveryConfigParam param = buildQueryAggDeliveryConfigParam(deliveryDetail);
                            return getDapRedirectModule(param, dapLinkUrlMap, shopId);
                        }).collect(Collectors.toList());
                result.addAll(dapRedirectModules);
            }
            return result;
        } catch (Exception e) {
            log.error("AggDeliveryConfigApplicationService queryAggDeliveryConfig error", e);
            return Collections.emptyList();
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public List<TDeliveryRedirectDetail> queryAggDeliveryConfig(List<QueryAggDeliveryRedirectModuleKey> orderKeys) {
        try {
            if (CollectionUtils.isEmpty(orderKeys)) {
                return Collections.emptyList();
            }

            // 查询激活运单详情
            List<Long> orderIds = orderKeys.stream().map(QueryAggDeliveryRedirectModuleKey::getEmpowerOrderId).collect(Collectors.toList());
            List<DeliveryOrder> deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersByOrderIdListSlave(orderIds);
            List<DeliveryOrder> activeDeliveryOrderList = getActiveDeliveryOrderList(deliveryOrderList);

            if (CollectionUtils.isEmpty(activeDeliveryOrderList)) {
                return Collections.emptyList();
            }

            // 查询承运商信息
            Set<Integer> carrierCodeSet = activeDeliveryOrderList.stream().map(DeliveryOrder::getDeliveryChannel).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, Optional<DeliveryChannel>> deliveryChannelMap = deliveryChannelRepository.getDeliveryChannelMapByCarrierCodeSet(carrierCodeSet);

            // key为赋能订单ID，value为deliveryPlatformCode
            Map<Long, Integer> orderId2DeliveryPlatformCodeMap = getOrderId2DeliveryPlatformCodeMap(activeDeliveryOrderList, deliveryChannelMap);

            List<DeliveryOrder> maltDeliveryOrders = activeDeliveryOrderList.stream().filter(deliveryDetail -> Objects.equals(orderId2DeliveryPlatformCodeMap.get(deliveryDetail.getOrderKey().getOrderId()), DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())).collect(Collectors.toList());
            List<DeliveryOrder> dapDeliveryOrders = activeDeliveryOrderList.stream().filter(deliveryDetail -> Objects.equals(orderId2DeliveryPlatformCodeMap.get(deliveryDetail.getOrderKey().getOrderId()), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())).collect(Collectors.toList());

            List<DeliveryOrderRedirectModule> redirectModuleList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(maltDeliveryOrders)) {
                List<DeliveryOrderRedirectModule> maltRedirectModules = maltDeliveryOrders.stream().map(deliveryDetail -> {
                    QueryAggDeliveryConfigParam param = buildQueryAggDeliveryConfigParam(deliveryDetail);
                    return getMaltRedirectModule(param, orderKeys.get(0).getStoreId());
                }).collect(Collectors.toList());
                redirectModuleList.addAll(maltRedirectModules);
            }
            if (CollectionUtils.isNotEmpty(dapDeliveryOrders)) {
                // 按门店维度批量查询青云url
                List<QueryDapLinkParam> queryDapLinkParamList = dapDeliveryOrders.stream().map(this::buildQueryDapLinkParam).collect(Collectors.toList());
                Map<String, String> dapLinkUrlMap = batchQueryDapRedirectUrl(queryDapLinkParamList);
                List<DeliveryOrderRedirectModule> dapRedirectModules = dapDeliveryOrders.stream()
                        .map(deliveryOrder -> {
                            QueryAggDeliveryConfigParam param = buildQueryAggDeliveryConfigParam(deliveryOrder);
                            return getDapRedirectModule(param, dapLinkUrlMap, orderKeys.get(0).getStoreId());
                        }).collect(Collectors.toList());
                redirectModuleList.addAll(dapRedirectModules);
            }

            if (CollectionUtils.isEmpty(redirectModuleList)) {
                return Collections.emptyList();
            }
            return redirectModuleList.stream().map(this::buildDeliveryRedirectDetail).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("AggDeliveryConfigApplicationService queryAggDeliveryConfig error", e);
            return Collections.emptyList();
        }
    }

    private DeliveryOrderRedirectModule getMaltRedirectModule(QueryAggDeliveryConfigParam param, Long shopId) {
        Long orderId = param.getOrderId();
        Long storeId = param.getStoreId();
        Long fulfillOrderId = param.getFulfillOrderId();
        Integer platformSource = param.getPlatformSource();
        Integer deliveryStatusCode = param.getDeliveryStatusCode();
        Integer deliveryChannelCode = param.getDeliveryChannelCode();
        Integer exceptionTypeCode = param.getExceptionTypeCode();

        String finalOrderId = getOrderId(orderId, platformSource, fulfillOrderId);

        // 不展示URL处理
        if (isMaltNotShowUrl(deliveryStatusCode, deliveryChannelCode)) {
            return buildEmptyModule(orderId);
        }

        AggDeliveryPlatformAppConfig maltConfig = AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode());
        if (Objects.isNull(maltConfig)) {
            return buildEmptyModule(orderId);
        }
        boolean isException = Objects.nonNull(exceptionTypeCode) && !Objects.equals(exceptionTypeCode, DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode());
        DeliveryRedirectModule deliveryRedirectModule = AggDeliveryPlatformEnum.MALT_FARM.fillDeliveryRedirectModule(maltConfig, finalOrderId, storeId, isException);
        if (! Objects.equals(storeId, shopId)){
            // 发生了转单需要置为空
            deliveryRedirectModule.setUrl(StringUtils.EMPTY);
            deliveryRedirectModule.setUrlText(StringUtils.EMPTY);
        }

        DeliveryOrderRedirectModule deliveryOrderRedirectModule = new DeliveryOrderRedirectModule();
        deliveryOrderRedirectModule.setOrderId(orderId);
        deliveryOrderRedirectModule.setDeliveryRedirectModule(deliveryRedirectModule);
        return deliveryOrderRedirectModule;
    }

    private DeliveryOrderRedirectModule getDapRedirectModule(QueryAggDeliveryConfigParam param, Map<String, String> dapUrlMap, Long shopId) {
        Long orderId = param.getOrderId();
        Long storeId = param.getStoreId();
        Long fulfillOrderId = param.getFulfillOrderId();
        Integer platformSource = param.getPlatformSource();
        Integer deliveryStatusCode = param.getDeliveryStatusCode();
        Integer exceptionTypeCode = param.getExceptionTypeCode();

        String finalOrderId = getOrderId(orderId, platformSource, fulfillOrderId);

        // 不展示URL处理
        if (isDapNotShowUrl(deliveryStatusCode)) {
            return buildEmptyModule(orderId);
        }

        String dapUrl = dapUrlMap.get(finalOrderId);
        boolean isException = Objects.nonNull(exceptionTypeCode) && !Objects.equals(exceptionTypeCode, DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode());
        DeliveryRedirectModule deliveryRedirectModule = AggDeliveryPlatformEnum.DAP_DELIVERY.fillDeliveryRedirectModule(dapUrl, isException);
        if (! Objects.equals(storeId, shopId)){
            // 发生了转单需要置为空
            deliveryRedirectModule.setUrl(StringUtils.EMPTY);
            deliveryRedirectModule.setUrlText(StringUtils.EMPTY);
        }

        DeliveryOrderRedirectModule deliveryOrderRedirectModule = new DeliveryOrderRedirectModule();
        deliveryOrderRedirectModule.setOrderId(orderId);
        deliveryOrderRedirectModule.setDeliveryRedirectModule(deliveryRedirectModule);
        return deliveryOrderRedirectModule;
    }

    private String getOrderId(Long orderId, Integer platformSource, Long fullFillOrderId) {
        String finalOrderId = String.valueOf(orderId);
        // orderId兼容转单场景
        if (isTransfer(platformSource)) {
            finalOrderId = PlatformSourceEnum.OFC.toPrefixOrderId(String.valueOf(fullFillOrderId));
        }
        return finalOrderId;
    }

    private boolean isMaltNotShowUrl(Integer deliveryStatusCode, Integer deliveryChannelCode) {
        // 针对麦芽田，配送拒单或者麦芽田转自配送后，不展示链接
        return Objects.equals(deliveryStatusCode, DeliveryStatusEnum.INIT.getCode())
                || Objects.equals(deliveryStatusCode, DeliveryStatusEnum.DELIVERY_REJECTED.getCode())
                || Objects.equals(deliveryChannelCode, DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode());
    }

    private boolean isDapNotShowUrl(Integer deliveryStatusCode) {
        // 针对青云配送拒单不展示链接
        return Objects.equals(deliveryStatusCode, DeliveryStatusEnum.INIT.getCode()) || Objects.equals(deliveryStatusCode, DeliveryStatusEnum.DELIVERY_REJECTED.getCode());
    }

    /**
     * 是否发生了跨门店履约转单
    */
    private boolean isTransfer(Integer platformSource) {
        return Objects.equals(platformSource, PlatformSourceEnum.OFC.getCode());
    }

    private Map<String, String> batchQueryDapRedirectUrl(List<QueryDapLinkParam> queryDapLinkParamList) {
        if (CollectionUtils.isEmpty(queryDapLinkParamList)) {
            return Collections.emptyMap();
        }

        List<String> orderIds = queryDapLinkParamList.stream().map(queryDapLinkParam -> getOrderId(queryDapLinkParam.getOrderId(), queryDapLinkParam.getPlatformSource(), queryDapLinkParam.getFulfillOrderId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyMap();
        }

        Map<Long, List<String>> storeId2OrderIdsMap = queryDapLinkParamList.stream()
                .collect(Collectors.groupingBy(QueryDapLinkParam::getStoreId, Collectors.mapping(queryDapLinkParam -> getOrderId(queryDapLinkParam.getOrderId(), queryDapLinkParam.getPlatformSource(), queryDapLinkParam.getFulfillOrderId()), Collectors.toList())));
        Map<String, String> result = Maps.newHashMap();
        storeId2OrderIdsMap.forEach((storeId, markIds) -> {
            Map<String, String> dapLinkUrlMap = dapLinkRepository.batchQueryDapLinkUrl(storeId, markIds);
            result.putAll(dapLinkUrlMap);
        });

        return result;
    }

    private DeliveryOrderRedirectModule buildEmptyModule(Long orderId) {
        DeliveryOrderRedirectModule deliveryOrderRedirectModule = new DeliveryOrderRedirectModule();
        DeliveryRedirectModule deliveryRedirectModule = new DeliveryRedirectModule();
        deliveryRedirectModule.setTitle("暂无配送状态");
        deliveryRedirectModule.setUrl(StringUtils.EMPTY);
        deliveryRedirectModule.setUrlText(StringUtils.EMPTY);
        deliveryOrderRedirectModule.setOrderId(orderId);
        deliveryOrderRedirectModule.setDeliveryRedirectModule(deliveryRedirectModule);
        return deliveryOrderRedirectModule;
    }

    private QueryAggDeliveryConfigParam buildQueryAggDeliveryConfigParam(OrderDeliveryDetail deliveryDetail) {
        return QueryAggDeliveryConfigParam.builder()
                .orderId(deliveryDetail.getOrderKey().getOrderId())
                .fulfillOrderId(deliveryDetail.getFulfillOrderId())
                .storeId(deliveryDetail.getStoreId())
                .platformSource(deliveryDetail.getPlatformSource())
                .deliveryStatusCode(Optional.ofNullable(deliveryDetail.getStatus()).orElse(DeliveryStatusEnum.INIT).getCode())
                .deliveryChannelCode(deliveryDetail.getDeliveryChannel())
                .exceptionTypeCode(Optional.ofNullable(deliveryDetail.getExceptionType()).orElse(DeliveryExceptionTypeEnum.NO_EXCEPTION).getCode())
                .build();
    }

    private QueryAggDeliveryConfigParam buildQueryAggDeliveryConfigParam(DeliveryOrder deliveryOrder) {
        return QueryAggDeliveryConfigParam.builder()
                .orderId(deliveryOrder.getOrderId())
                .fulfillOrderId(deliveryOrder.getFulfillmentOrderId())
                .storeId(deliveryOrder.getStoreId())
                .platformSource(Optional.ofNullable(deliveryOrder.getPlatformSourceEnum()).orElse(PlatformSourceEnum.OMS).getCode())
                .deliveryStatusCode(Optional.ofNullable(deliveryOrder.getStatus()).orElse(DeliveryStatusEnum.INIT).getCode())
                .deliveryChannelCode(deliveryOrder.getDeliveryChannel())
                .exceptionTypeCode(Optional.ofNullable(deliveryOrder.getExceptionType()).orElse(DeliveryExceptionTypeEnum.NO_EXCEPTION).getCode())
                .build();
    }

    private List<DeliveryOrder> getActiveDeliveryOrderList(List<DeliveryOrder> deliveryOrderList) {
        if (CollectionUtils.isEmpty(deliveryOrderList)) {
            return Collections.emptyList();
        }

        Map<Long, List<DeliveryOrder>> orderId2DeliveryOrderIdMap = deliveryOrderList.stream().collect(Collectors.groupingBy(DeliveryOrder::getOrderId));
        return orderId2DeliveryOrderIdMap.values().stream().map(DeliveryOrder::filterActiveDeliveryOrder).collect(Collectors.toList());
    }

    private Map<Long, Integer> getOrderId2DeliveryPlatformCodeMap(List<DeliveryOrder> deliveryOrderList, Map<Integer, Optional<DeliveryChannel>> deliveryChannelMap) {
        if (CollectionUtils.isEmpty(deliveryOrderList) || MapUtils.isEmpty(deliveryChannelMap)) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = Maps.newHashMap();
        deliveryOrderList.forEach(deliveryOrder -> {
            Integer deliveryChannelId = deliveryOrder.getDeliveryChannel();
            Optional<DeliveryChannel> deliveryChannelOptional = deliveryChannelMap.get(deliveryChannelId);
            if (deliveryChannelOptional.isPresent()) {
                DeliveryChannel deliveryChannel = deliveryChannelOptional.get();
                result.put(deliveryOrder.getOrderId(), deliveryChannel.getDeliveryPlatFormCode());
            }
        });

        return result;
    }

    private QueryDapLinkParam buildQueryDapLinkParam(OrderDeliveryDetail dapDeliveryDetail) {
        return QueryDapLinkParam.builder()
                .orderId(dapDeliveryDetail.getOrderKey().getOrderId())
                .fulfillOrderId(dapDeliveryDetail.getFulfillOrderId())
                .platformSource(dapDeliveryDetail.getPlatformSource())
                .storeId(dapDeliveryDetail.getStoreId())
                .build();
    }

    private QueryDapLinkParam buildQueryDapLinkParam(DeliveryOrder deliveryOrder) {
        return QueryDapLinkParam.builder()
                .orderId(deliveryOrder.getOrderId())
                .fulfillOrderId(deliveryOrder.getFulfillmentOrderId())
                .platformSource(Optional.ofNullable(deliveryOrder.getPlatformSourceEnum()).orElse(PlatformSourceEnum.OMS).getCode())
                .storeId(deliveryOrder.getStoreId())
                .build();
    }

    private TDeliveryRedirectDetail buildDeliveryRedirectDetail(DeliveryOrderRedirectModule redirectModule) {
        TDeliveryRedirectModule tDeliveryRedirectModule = new TDeliveryRedirectModule();
        tDeliveryRedirectModule.setTitle(redirectModule.getDeliveryRedirectModule().getTitle());
        tDeliveryRedirectModule.setUrl(redirectModule.getDeliveryRedirectModule().getUrl());
        tDeliveryRedirectModule.setUrlText(redirectModule.getDeliveryRedirectModule().getUrlText());

        TDeliveryRedirectDetail tDeliveryRedirectDetail = new TDeliveryRedirectDetail();
        tDeliveryRedirectDetail.setOrderId(redirectModule.getOrderId());
        tDeliveryRedirectDetail.setTDeliveryRedirectModule(tDeliveryRedirectModule);
        return tDeliveryRedirectDetail;
    }

}
