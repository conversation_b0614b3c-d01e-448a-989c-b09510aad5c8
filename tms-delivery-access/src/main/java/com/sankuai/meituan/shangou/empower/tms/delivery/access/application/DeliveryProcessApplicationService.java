package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.google.common.base.Preconditions;
import com.meituan.reco.pickselect.common.domain.orderTrack.OperationScene;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.PlatformDeliveryUpdateCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.LaunchThirdPartyDeliveryDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryMonitorDomainService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MoneyUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MonitorUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/3
 */
@Slf4j
@Service
@DependsOn({"mafkaDeliveryChangeNotifyServiceImpl"})
// 自建配送平台在用
public class DeliveryProcessApplicationService {

	@Resource
	private LaunchThirdPartyDeliveryDomainService launchThirdPartyDeliveryDomainService;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryMonitorDomainService deliveryMonitorDomainService;
	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	@Resource
	private DeliveryNotifyService deliveryNotifyService;

	@CatTransaction
	public Optional<Failure> launchDelivery(OrderKey orderKey, SelfBuiltDeliveryPoi deliveryPoi, boolean acceptRetry, boolean tryNewChannel, boolean tryFailedChannel) {
		log.info("DeliveryProcessApplicationService.launchDelivery begin, param[orderKey={}, deliveryStore={}, acceptRetry={}]",
				orderKey, deliveryPoi, acceptRetry);

		Preconditions.checkNotNull(orderKey, "launchDeliveryCmd is null");
		Preconditions.checkNotNull(deliveryPoi, "deliveryStore is null");

		try {
			//订单信息校验
			Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
			if (orderInfoQueryResult.isFail()) {
				return Optional.of(orderInfoQueryResult.getFailure());
			}

			Optional<Failure> failure = launchThirdPartyDeliveryDomainService.launchDelivery(
					orderInfoQueryResult.getInfo(), deliveryPoi, acceptRetry, tryFailedChannel, tryNewChannel
			);

			MonitorUtil.logMetric("LAUNCH_THIRD_DELIVERY", failure.isPresent());
			log.info("DeliveryProcessApplicationService.launchDelivery finish, result={}", failure);
			return failure;

		} catch (Exception e) {
			log.error("LaunchThirdPartyDeliveryDomainService.launchDelivery failed with exception", e);
			MonitorUtil.logMetric("LAUNCH_THIRD_DELIVERY", true);
			return Optional.of(new Failure(true, SYSTEM_ERROR));
		}
	}

	@CatTransaction
	public Optional<Failure> manualLaunchDelivery(OrderKey orderKey) {
		try {

			Optional<DeliveryPoi> opDeliveryPoi = Optional.empty();
			if(MccConfigUtils.getDHTenantIdList().contains(orderKey.getTenantId()+"")){
				opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(orderKey.getTenantId(), orderKey.getStoreId());
			}else {
				Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
				if (orderInfoQueryResult.isFail()) {
					return Optional.of(orderInfoQueryResult.getFailure());
				}
				DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
						orderInfoQueryResult.getInfo().getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
				opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderKey.getTenantId(), orderKey.getStoreId(),
						orderBizType.getChannelId());
			}
			if (!opDeliveryPoi.isPresent() || opDeliveryPoi.get().getDeliveryPlatform() != DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM) {
				return Optional.of(new Failure(false, FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP));
			}

			SelfBuiltDeliveryPoi deliveryPoi = (SelfBuiltDeliveryPoi) opDeliveryPoi.get();
			if (!deliveryPoi.canLaunchDelivery()) {
				return Optional.of(new Failure(false, FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP));
			}

			return launchDelivery(orderKey, deliveryPoi, false, false, true);
		} catch (Exception e) {
			log.error("LaunchThirdPartyDeliveryDomainService.launchDelivery failed with exception", e);
			return Optional.of(new Failure(true, SYSTEM_ERROR));
		}

	}

	@CatTransaction
	public void updatePlatformDelivery(PlatformDeliveryUpdateCmd cmd, DeliveryExceptionInfo exceptionInfo) {

		if (cmd.getDeliveryOrder().getDeliveryChannel() != DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode()) {
			log.error("DeliveryProcessApplicationService.updatePlatformDelivery should be only applied to platform delivery order");
			return;
		}

		if (cmd.getUpdateTime().isBefore(cmd.getDeliveryOrder().getLastEventTime())) {
			log.warn("PlatformDeliveryUpdateCmd updateTime[{}] is before delivery order lastEventTime[{}], will ignore updating.", cmd.getUpdateTime(), cmd.getDeliveryOrder().getLastEventTime());
		} else {

			if(StringUtils.isNotBlank(cmd.getFailType())){
				cmd.getDeliveryOrder().setFailType(cmd.getFailType());
			}
			if(StringUtils.isNotBlank(cmd.getDealDeadline())){
				cmd.getDeliveryOrder().setDealDeadline(cmd.getDealDeadline());
			}

			if (Objects.nonNull(cmd.getIsRecallDelivery())) {
				cmd.getDeliveryOrder().setIsRecallDelivery(cmd.getIsRecallDelivery());
			}

			if(Objects.nonNull(cmd.getIsFourWheelDelivery())){
				cmd.getDeliveryOrder().setIsFourWheelDelivery(cmd.getIsFourWheelDelivery());
			}

			if(Objects.nonNull(cmd.getIsManual())){
				cmd.getDeliveryOrder().setIsManual(cmd.getIsManual());
			}

			cmd.getDeliveryOrder().onStatusChange(cmd.getStatus(), cmd.getUpdateTime());

			DeliveryExceptionInfo deliveryExceptionInfo = Optional.ofNullable(exceptionInfo)
					.orElse(new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY));

			boolean isNotExceptionChange = deliveryExceptionInfo.getExceptionType().equals(cmd.getDeliveryOrder().getExceptionType());
			if(isNotExceptionChange){
				isNotExceptionChange = deliveryExceptionInfo.getExceptionCode().equals(cmd.getDeliveryOrder().getDeliveryExceptionCode());
			}

			cmd.getDeliveryOrder().onException(deliveryExceptionInfo, cmd.getUpdateTime());

			if (cmd.getRider() != null) {
				cmd.getDeliveryOrder().onRiderChange(cmd.getRider(), cmd.getUpdateTime());
			}

			deliveryOrderRepository.save(cmd.getDeliveryOrder());
			if(!isNotExceptionChange || (cmd.getDeliveryOrder().getExceptionType() == DeliveryExceptionTypeEnum.NO_EXCEPTION && cmd.getDeliveryOrder().getStatus() == DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER)){
				deliveryMonitorDomainService.triggerDeliveryMonitoring(cmd.getDeliveryOrder());
			}
			if(!isNotExceptionChange){
				sendOrderTrack(cmd,deliveryExceptionInfo);
			}

		}
	}

	private void sendOrderTrack(PlatformDeliveryUpdateCmd cmd,DeliveryExceptionInfo deliveryExceptionInfo){
		TrackOpType trackOpType =null;
		if(deliveryExceptionInfo.getExceptionCode() == DeliveryExceptionCodeEnum.RIDER_REPORT_FAIL.getCode()){
			trackOpType = TrackOpType.DELIVERY_EXCEPTION_UPLOAD_RIDER;

		}else if(deliveryExceptionInfo.getExceptionCode() == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL_AUDITING.getCode()){
			trackOpType = TrackOpType.RIDER_REPORT_AUDIT_FAIL;
		}else if(deliveryExceptionInfo.getExceptionCode() == DeliveryExceptionCodeEnum.RIDER_TAKE_FAIL.getCode()){
			trackOpType = TrackOpType.RIDER_REPORT_FAIL;
		}else if(deliveryExceptionInfo.getExceptionCode() == DeliveryExceptionCodeEnum.DELIVERY_EXCEPTION_UPLOAD.getCode()){
			trackOpType = TrackOpType.DELIVERY_EXCEPTION;
		}
		if(trackOpType == null){
			return;
		}
		Map<String,Object> extMap=new HashMap<>();
		if(StringUtils.isNotBlank(cmd.getFailType())){
			extMap.put("failureType",cmd.getFailType());
		}
		if(StringUtils.isNotBlank(cmd.getDealDeadline())){
			extMap.put("dealDeadline",cmd.getDealDeadline());
		}
		OrderTrackEvent event = new OrderTrackEvent(TrackSource.DELIVERY, trackOpType, 0L,
				OperationScene.DELIVERY_EXCEPTION, cmd.getDeliveryOrder().getTenantId(), cmd.getDeliveryOrder().getChannelOrderId(),
				cmd.getDeliveryOrder().getOrderBizType(), extMap, null);
		deliveryNotifyService.notifyDeliveryTrace(event);
	}


}
