package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.FarmPaoTuiPreDeliveryDetailDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryCancelResultCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryChangeCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryExceptionCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.cmd.DeliveryLaunchResultCallbackCmd;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryCancelFailInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper.BusinessWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.enums.DeliverySignTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.utils.DeliveryRiderMccConfigUtils;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION;

/**
 * 配送手动发起相关应用服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/4
 */
@Slf4j
@Service
@DependsOn({"mafkaDeliveryChangeNotifyServiceImpl"})
public class DeliveryCallbackApplicationService {

	private static final Integer DEFAULT_FAIL_REASON_CODE = 0;

	private static final Integer PRIVATE_DOMAIN_CHANNEL = 0;
	private static final String CANCEL_FAIL_PRE = "取消失败：";

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryWarnEventPublisher deliveryWarnEventPublisher;
	@Resource
	private PushClient pushClient;
	@Resource
	private DeliveryChangeNotifyService deliveryChangeNotifyService;
	@Resource
	private DeliveryTimeOutCheckService deliveryTimeOutCheckService;

	@Resource
	private OcmsChannelClient ocmsChannelClient;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;
	@Resource
	private RouteFacade routeFacade;

	@Resource
	private RiderInfoDeliveryRepository riderInfoDeliveryRepository;

	@Resource
	private BusinessWrapper businessWrapper;

	@Resource
	private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void deliveryLaunchCallback(DeliveryLaunchResultCallbackCmd cmd) {
		//查运单
		Optional<DeliveryOrder> deliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(cmd.getOrderId());

		if (!deliveryOrder.isPresent()) {
			log.error("配送发起结果回调处理失败，未查询到对应运单, cmd={}", cmd);
			deliveryWarnEventPublisher.postEvent(new DeliveryLaunchCallbackHandleFailedEvent(cmd, "该订单无对应的激活中的运单"));
			return;
		}

		//如果渠道运单号不匹配
		if (!StringUtils.equals(deliveryOrder.get().getChannelDeliveryId(), cmd.getChannelDeliveryId())) {
			//如果运单中无渠道运单号，则进行补齐
			if (StringUtils.isEmpty(deliveryOrder.get().getChannelDeliveryId())) {
				deliveryOrder.get().setChannelDeliveryId(cmd.getChannelDeliveryId());

			} else {
				//否则，认定无法匹配，无法处理
				log.error("配送发起结果回调处理失败，运单的渠道运单号不匹配, cmd={}", cmd);
				deliveryWarnEventPublisher.postEvent(new DeliveryLaunchCallbackHandleFailedEvent(cmd, "渠道运单号不匹配"));
				return;
			}
		}

		//更新渠道相关信息
		deliveryOrder.get().setDeliveryChannel(cmd.getDeliveryChannel());
		deliveryOrder.get().setChannelServicePackageCode(cmd.getServicePackage());

		if (cmd.isLaunchSuccess()) {
			//如果运单仍处于初始状态，将其扭转至发起成功
			if (deliveryOrder.get().getStatus() == DeliveryStatusEnum.INIT) {
				deliveryOrder.get().onEvent(DeliveryEventEnum.LAUNCH_DELIVERY, deliveryOrder.get().getCreateTime());
			}
			Optional.ofNullable(cmd.getDistance()).ifPresent(distance -> deliveryOrder.get().setDistance(distance));
			Optional.ofNullable(cmd.getDeliveryFee()).ifPresent(deliveryFee -> deliveryOrder.get().setDeliveryFee(deliveryFee));

			deliveryOrderRepository.save(deliveryOrder.get());
			orderSystemClient.syncDeliveryChangeToOrderSystem(deliveryOrder.get());

		} else {
			deliveryWarnEventPublisher.postEvent(new DeliveryLaunchFailedEvent(deliveryOrder.get(), cmd.getLaunchFailReasonDesc()));

			//配送发起失败，需将其推进至拒单状态
			deliveryOrder.get().onChange(
					DeliveryEventEnum.DELIVERY_REJECT,
					new DeliveryExceptionInfo(LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION, cmd.getLaunchFailReasonDesc()),
					deliveryOrder.get().getRiderInfo(),
					LocalDateTime.now()
			);
			pushClient.pushDeliveryException(deliveryOrder.get());
		}
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void deliveryCancelCallback(DeliveryCancelResultCallbackCmd cmd) {
		//查运单
		Optional<DeliveryOrder> deliveryOrder = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(cmd.getOrderId());

		if (!deliveryOrder.isPresent()) {
			log.error("配送取消结果回调处理失败，未查询到对应运单, cmd={}", cmd);
			deliveryWarnEventPublisher.postEvent(new DeliveryCancelCallbackHandleFailedEvent(cmd, "该订单无对应的激活中的运单"));
			return;
		}

		//如果渠道运单号不匹配
		if (!StringUtils.equals(deliveryOrder.get().getChannelDeliveryId(), cmd.getChannelDeliveryId())) {
			//如果运单中无渠道运单号，则进行补齐
			if (StringUtils.isEmpty(deliveryOrder.get().getChannelDeliveryId())) {
				deliveryOrder.get().setChannelDeliveryId(cmd.getChannelDeliveryId());
				deliveryOrderRepository.save(deliveryOrder.get());

			} else {
				//否则，认定无法匹配，无法处理
				log.error("配送取消结果回调处理失败，运单的渠道运单号不匹配, cmd={}", cmd);
				deliveryWarnEventPublisher.postEvent(new DeliveryCancelCallbackHandleFailedEvent(cmd, "渠道运单号不匹配"));
				return;
			}
		}

		if (!cmd.isCancelSuccess()) {
			DeliveryOrder deliveryOrderPresent = deliveryOrder.get();
			deliveryOrderPresent.setCancelMark(CancelMarkEnum.DEFAULT.getValue());
			deliveryOrderPresent.onException(new DeliveryExceptionInfo(
					DeliveryExceptionTypeEnum.LAUNCH_DELIVERY_FAILED_BY_SYSTEM_EXCEPTION,
					CANCEL_FAIL_PRE + cmd.getCancelFailReasonDesc()), LocalDateTime.now());
			deliveryOrderRepository.save(deliveryOrderPresent);
			//记录取消失败流水
			deliveryChangeNotifyService.notifyDeliveryChangeLog(
					deliveryOrder.get().getId(),
					DeliveryEventEnum.DELIVERY_CANCEL_FAIL,
					new DeliveryCancelFailInfo(DEFAULT_FAIL_REASON_CODE, cmd.getCancelFailReasonDesc()),
					LocalDateTime.now()
			);

			deliveryWarnEventPublisher.postEvent(new DeliveryCancelFailedEvent(deliveryOrder.get(), cmd.getCancelFailReasonDesc()));
			pushClient.pushDeliveryException(deliveryOrder.get());
		}
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> deliveryChangeCallback(DeliveryChangeCallbackCmd cmd) {
		PlatformSourceEnum platformSourceEnum = PlatformSourceEnum.platformCodeToEnum(cmd.getPlatformSource());
		//查运单
		List<DeliveryOrder> deliveryOrders = new ArrayList<>();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersMaxWithOrderId(cmd.getOrderId());
		}else {
			deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(cmd.getOrderId());
		}

		//查订单
		Long orderId = cmd.getOrderId();
		if(platformSourceEnum == PlatformSourceEnum.OFC){
			if(CollectionUtils.isNotEmpty(deliveryOrders)){
				orderId = deliveryOrders.get(0).getOrderId();
			}
		}
		Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(orderId, false);
		//只能同步
		if (orderInfoResult.isFail()) {
			deliveryWarnEventPublisher.postEvent(new DeliveryChangeCallbackHandleFailedEvent(cmd, "状态回调查询订单信息失败"));
			return Optional.of(new Failure(true, FailureCodeEnum.QUERY_ORDER_FAIL));
		}
		OrderInfo orderInfo = orderInfoResult.getInfo();

		List<DeliveryOrder> activeDeliveryOrders = Optional
				.ofNullable(deliveryOrders)
				.orElse(Lists.newArrayList())
				.stream()
				.filter(deliveryOrder -> deliveryOrder.getActiveStatus().equals(DeliveryOrder.DELIVERY_ORDER_ACTIVE))
				.collect(Collectors.toList());

		boolean needCancel = false;
		DeliveryOrder activeDeliveryOrder;
		if (CollectionUtils.isNotEmpty(activeDeliveryOrders)) {
			activeDeliveryOrder = activeDeliveryOrders.get(0);
			if (cmd.getDeliveryEvent() == DeliveryEventEnum.DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER) {
				if (activeDeliveryOrder.getStatus() == DeliveryStatusEnum.DELIVERY_DONE) {
					activeDeliveryOrder.activateSucCount();
					deliveryOrderRepository.save(activeDeliveryOrder);
					Integer deliveryCount = activeDeliveryOrder.getDeliveryCount();
					Long storeId = activeDeliveryOrder.getStoreId();
					Long fulfillId= activeDeliveryOrder.getFulfillmentOrderId();
					activeDeliveryOrder = new DeliveryOrder(orderInfo, cmd.getDeliveryChannel());
					activeDeliveryOrder.setStoreId(storeId);
					activeDeliveryOrder.setOrderKey(new OrderKey(activeDeliveryOrder.getTenantId(),storeId,activeDeliveryOrder.getOrderId()));
					activeDeliveryOrder.activate();
					activeDeliveryOrder.setPlatformSourceEnum(platformSourceEnum);
					activeDeliveryOrder.setFulfillmentOrderId(fulfillId);
					activeDeliveryOrder.setDeliveryCount(deliveryCount == null ? 1 : deliveryCount + 1);
					deliveryOrderRepository.save(activeDeliveryOrder);
					if (orderInfo.getOrderStatus().equals(OrderStatusEnum.CANCELED.getValue())) {
						needCancel = true;
					}
				}
			}
		} else {
			Optional<DeliveryOrder> optExistedDeliveryOrder = Optional
					.ofNullable(deliveryOrders)
					.orElse(Lists.newArrayList())
					.stream()
					.filter(deliveryOrder -> deliveryOrder.getDeliveryChannel().equals(cmd.getDeliveryChannel()) && deliveryOrder.getChannelDeliveryId().equals(cmd.getChannelDeliveryId()))
					.findAny();
			boolean isExistDeliveryOrder = optExistedDeliveryOrder.isPresent();
			if (isExistDeliveryOrder) {
				DeliveryOrder existedDeliveryOrder = optExistedDeliveryOrder.get();
				if (
						((existedDeliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.MALT_FARM.getCode() && StringUtils.isBlank(existedDeliveryOrder.getChannelDeliveryId()) )
						|| (existedDeliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.DAP_DELIVERY.getCode()))
						&& cmd.getDeliveryEvent() == DeliveryEventEnum.DELIVERY_PLATFORM_ACCEPT_DELIVERY_ORDER) {
					log.info("在平台待接单回调处理时创建并激活deliveryOrder. existedDeliveryOrder: {}, cmd: {}", existedDeliveryOrder, cmd);
				} else if (isOrderChannelPlatformDelivery(orderInfo, cmd)) {
					// 抖音平台配送有重新呼叫功能，会先取消上一个配送运单，这里放通让下一个配送运单正常生成
					log.info("抖音&私域渠道平台配送，平台配送取消后重新呼叫平台配送，cmd: {}", cmd);
				} else {
					log.error("过期回调,cmd = {}", cmd);
					return Optional.empty();
				}
			}

			Optional<DeliveryOrder> doneDeliveryOrder = Optional.ofNullable(deliveryOrders)
					.orElse(Lists.newArrayList())
					.stream()
					.filter(deliveryOrder -> deliveryOrder.getStatus().equals(DeliveryStatusEnum.DELIVERY_DONE))
					.max(Comparator.comparing(DeliveryOrder::getCreateTime));
			activeDeliveryOrder = new DeliveryOrder(orderInfo, cmd.getDeliveryChannel());
			activeDeliveryOrder.activate();
			activeDeliveryOrder.setPlatformSourceEnum(platformSourceEnum);
			int deliveryCount = 1;
			if (doneDeliveryOrder.isPresent()) {
				deliveryCount = doneDeliveryOrder.get().getDeliveryCount() == null ? 1 : doneDeliveryOrder.get().getDeliveryCount() + 1;
			}

			Optional<DeliveryOrder> maxDeliveryOrder = Optional.ofNullable(deliveryOrders)
					.orElse(Lists.newArrayList())
					.stream()
					.max(Comparator.comparing(DeliveryOrder::getCreateTime));
			if(maxDeliveryOrder.isPresent()){
				activeDeliveryOrder.setTransType(maxDeliveryOrder.get().getTransType());
				activeDeliveryOrder.setStoreId(maxDeliveryOrder.get().getStoreId());
				activeDeliveryOrder.setOrderKey(new OrderKey(activeDeliveryOrder.getTenantId(),maxDeliveryOrder.get().getStoreId(),activeDeliveryOrder.getOrderId()));
				activeDeliveryOrder.setFulfillmentOrderId(maxDeliveryOrder.get().getFulfillmentOrderId());
			}

			activeDeliveryOrder.setDeliveryCount(deliveryCount);
			if (MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId())) {
				long dhDistance = getDhDistance(activeDeliveryOrder);
				activeDeliveryOrder.setDistance(dhDistance);
			}
			deliveryOrderRepository.save(activeDeliveryOrder);
			if (orderInfo.getOrderStatus().equals(OrderStatusEnum.CANCELED.getValue())) {
				needCancel = true;
			}
		}

		if (Objects.isNull(activeDeliveryOrder)) {
			log.error("配送取消结果回调处理失败，未查询到对应运单, cmd={}", cmd);
			deliveryWarnEventPublisher.postEvent(
					new DeliveryChangeCallbackHandleFailedEvent(cmd, "该订单无对应的激活中的运单"));
			return Optional.empty();
		}

		if(activeDeliveryOrder.getPlatformSourceEnum()!=null && activeDeliveryOrder.getPlatformSourceEnum() != platformSourceEnum){
			log.info("配送平台不一致, cmd={}", cmd);
			return Optional.empty();
		}
		if (MccConfigUtils.filterUnequalDeliveryPlatform() && (! isEqualsDeliveryPlatform(activeDeliveryOrder, cmd))) {
			log.error("配送回调处理失败，回调信息的配送平台与当前运单的配送平台不一致, cmd={}", cmd);
			return Optional.empty();
		}

		if(cmd.getDeliveryEvent() == DeliveryEventEnum.RIDER_ASSIGN
				&& Objects.equals(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), activeDeliveryOrder.getOrderBizType()) && platformSourceEnum == PlatformSourceEnum.OMS){
			if(cmd.getDeliveryChannel()!=null && !DeliveryOrder.isPaotuiDeliveryOrder(cmd.getDeliveryChannel())){
				Long shopId=activeDeliveryOrder.getStoreId();
				if(orderInfo!=null && orderInfo.getWarehousePoiId()!=null){
					shopId = orderInfo.getWarehousePoiId();
				}
				FarmPaoTuiPreDeliveryDetailDTO deliveryDetailDTO=ocmsChannelClient.preDelivery(activeDeliveryOrder.getTenantId(), shopId, activeDeliveryOrder.getChannelOrderId());
				if(deliveryDetailDTO!=null && deliveryDetailDTO.getPayAmount()!=null){
					activeDeliveryOrder.setPlatformFee(new BigDecimal(deliveryDetailDTO.getPayAmount()));
				}
			}
		}
		if(cmd.getDeliveryEvent() == DeliveryEventEnum.RIDER_ASSIGN){
			Cat.logEvent("delivery_platform_"+cmd.getDeliveryPlatformCode(), cmd.getDeliveryChannel()+"");
		}

		doHandleDeliveryChangeCallback(cmd, activeDeliveryOrder, orderInfo);
		if (needCancel) {
			deliveryChangeNotifyService.notifyDeliveryCancel(activeDeliveryOrder.getTenantId(), activeDeliveryOrder.getStoreId(), activeDeliveryOrder.getId());
		}

		return Optional.empty();
	}

	private long getDhDistance(DeliveryOrder activeDeliveryOrder) {
		try {
			Result<Double> distanceResult = routeFacade.queryRidePathDistance(activeDeliveryOrder.getTenantId(), activeDeliveryOrder.getStoreId(), activeDeliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint());
			if (distanceResult.isSuccess() && Objects.nonNull(distanceResult.getInfo())) {
				return distanceResult.getInfo().longValue();
			}
			return 0L;
		} catch (Exception e) {
			log.error("getDhDistance error, orderId = {}", activeDeliveryOrder.getOrderId());
			return 0L;
		}
	}

	private Boolean isEqualsDeliveryPlatform(DeliveryOrder deliveryOrder, DeliveryChangeCallbackCmd cmd) {
		Set<Integer> carrierCodeSet = new HashSet<>();
		carrierCodeSet.add(deliveryOrder.getDeliveryChannel());
		carrierCodeSet.add(cmd.getDeliveryChannel());
		List<DeliveryChannel> deliveryChannels = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(carrierCodeSet);
		Set<Integer> deliveryPlatforms = deliveryChannels.stream().map(DeliveryChannel::getDeliveryPlatFormCode).collect(Collectors.toSet());
		if (CollectionUtils.isNotEmpty(deliveryPlatforms) && deliveryPlatforms.size() == 1) {
			return true;
		}
		log.info("配送回调处理，回调信息的配送平台与当前运单的配送平台不一致, carrierCodeSet={}, deliveryChannels={},deliveryPlatforms={}", carrierCodeSet, deliveryChannels, deliveryPlatforms);
		return false;
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public void deliveryExceptionCallback(DeliveryExceptionCallbackCmd cmd) {
		// 查生效运单
		Optional<DeliveryOrder> deliveryOrderActive = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(cmd.getOrderId());
		// 查最近运单
		Optional<DeliveryOrder> deliveryOrderLatest = deliveryOrderRepository.getLatestDeliveryOrderForceMaster(cmd.getOrderId());

		// 获取运单
		// 如果存在生效运单，则直接返回
		// 如果不存在生效运单，则取最近的一条运单(可能为取消状态)，且满足私域渠道且平台配送，则返回，否则返回空
		Optional<DeliveryOrder> deliveryOrder = Optional.empty();
		if (deliveryOrderActive.isPresent()) {
			deliveryOrder = deliveryOrderActive;
		} else if (deliveryOrderLatest.isPresent()) {
			DeliveryOrder latest = deliveryOrderLatest.get();
			DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(latest.getOrderBizType());
			DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(latest.getDeliveryChannel());

			if (channelType != null
					&& Objects.equals(channelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL)
					&& deliveryChannel.getDeliveryPlatFormCode().equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode())) {
				log.info("获取最近私域渠道配送运单, deliveryOrderLatest={}", latest);
				deliveryOrder = deliveryOrderLatest;
			}
		}
		
		if (!deliveryOrder.isPresent()) {
			log.error("配送异常结果回调处理失败，未查询到对应运单, cmd={}", cmd);
//			deliveryWarnEventPublisher.postEvent(new DeliveryExceptionCallbackHandleFailedEvent(cmd, "该订单无对应的激活中的运单"));
			return;
		}
		Result<OrderInfo> orderInfoResult = orderSystemClient.getOrderInfo(deliveryOrder.get().getOrderId(), false);
		boolean isFastOrder = false;
		if (orderInfoResult.isFail()) {
			return;
		}
		isFastOrder = orderInfoResult.getInfo().fastOrder();
		LocalDateTime lastExceptionEventTime = deliveryOrder.get().getLastExceptionEventTime();
		// 首次处理异常、异常时间为空
		if (lastExceptionEventTime != null && lastExceptionEventTime.isAfter(cmd.getExceptionTime())) {
			log.warn("配送异常结果回调时间过期, cmd={}", cmd);
			return;
		}
		DeliveryExceptionInfo exceptionInfo = getExceptionInfo(cmd, deliveryOrder.get());
		if (exceptionInfo == null) {
			return;
		}
		deliveryOrder.get().onException(exceptionInfo, cmd.getExceptionTime());
		deliveryOrderRepository.save(deliveryOrder.get());
		if (isFastOrder && DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode() == cmd.getCode()) {
			pushClient.pushFastOrderDeliveryTimeoutException(deliveryOrder.get());
		} else {
			pushClient.pushDeliveryException(deliveryOrder.get());
		}
	}

	private void doHandleDeliveryChangeCallback(DeliveryChangeCallbackCmd cmd, DeliveryOrder deliveryOrder, OrderInfo orderInfo) {
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		if (!needDoDeliveryChangeCallback(deliveryOrder, deliveryChannel, cmd.getChangeTime())) {
			return;
		}

		if(deliveryOrder.isDapDeliveryOrder(deliveryChannel)){
			if(deliveryOrder.isActive() && (!deliveryOrder.getStatus().isFinalStatus()) && deliveryOrder.getStatus().getCode()>DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode() && cmd.getDeliveryEvent().getTargetStatus()==DeliveryStatusEnum.DELIVERY_LAUNCHED){
				return;
			}
		}
		// 获取本地保存的异常信息
		DeliveryExceptionInfo offlineExceptionInfo = getOfflineExceptionInfo(deliveryOrder);
		// 需要保存的异常信息
		DeliveryExceptionInfo currentExceptionInfo = getExceptionInfo(cmd, orderInfo, offlineExceptionInfo, deliveryChannel);
		Integer currentExceptionCode = Optional.ofNullable(currentExceptionInfo)
				.map(DeliveryExceptionInfo::getExceptionCode)
				.orElse(0);

		if (isDeliveryTransfer(deliveryOrder.getTenantId(), currentExceptionCode)) {
			if (deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()) {
				return;
			} else {
				log.info("触发转单重置运单逻辑, cmd is {}", cmd);
				deliveryOrder.reset2WaitAssignValue();
			}
		} else {
			//如果渠道运单号不匹配
			if (!StringUtils.equals(deliveryOrder.getChannelDeliveryId(), cmd.getChannelDeliveryId())) {
				//如果运单中无渠道运单号，则进行补齐
				if (StringUtils.isEmpty(deliveryOrder.getChannelDeliveryId())) {
					deliveryOrder.setChannelDeliveryId(cmd.getChannelDeliveryId());

				} else if(!Objects.equals(currentExceptionCode, DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode())) {
					//转单状态回退会导致运单id不一致，需要进行排查转单
					if (cmd.getDeliveryChannel() == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
						// 若麦芽田转自己送时，存在运单号不匹配的情况，说明存在已有运单未取消掉(由于麦芽田回调消息的并发处理)，需抛出异常交由死信进行重试
						log.warn("麦芽田转自己送时，存在运单号不匹配情况. cmd:{}", cmd);
						Cat.logEvent("MALT_TURN_TO_SELF", "FAIL");
						throw new IllegalStateException("麦芽田转自己送时，存在运单号不匹配情况");
					}
					//否则，认定无法匹配，无法处理
					log.error("配送变更回调处理失败，运单的渠道运单号不匹配, cmd={}", cmd);
					deliveryWarnEventPublisher.postEvent(new DeliveryChangeCallbackHandleFailedEvent(cmd, "渠道运单号不匹配"));
					return;
				}
			}

			//更新基础信息
			deliveryOrder.setDeliveryChannel(cmd.getDeliveryChannel());
			deliveryOrder.setChannelServicePackageCode(cmd.getServicePackage());
			//歪马不需要更新距离
			if (!MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())) {
				Optional.ofNullable(cmd.getDistance()).ifPresent(deliveryOrder::setDistance);
			}
			Optional.ofNullable(cmd.getDeliveryFee()).ifPresent(deliveryOrder::setDeliveryFee);
			Optional.ofNullable(cmd.getTipAmount()).ifPresent(deliveryOrder::setTipAmount);
			Optional.ofNullable(cmd.getBaseFee()).ifPresent(deliveryOrder::setBaseFee);
			Optional.ofNullable(cmd.getDiscountFee()).ifPresent(deliveryOrder::setDiscountFee);
			Optional.ofNullable(cmd.getInsuredFee()).ifPresent(deliveryOrder::setInsuredFee);
			if (Objects.nonNull(deliveryOrder.getDeliveryChannel()) && deliveryOrder.isOrderChannelDeliveryPlatFormDeliveryOrder(deliveryChannel)) {
				Optional.ofNullable(cmd.getChannelDeliveryId()).ifPresent(deliveryOrder::setChannelDeliveryId);
			}
			if (cmd.getDeliveryEvent().equals(DeliveryEventEnum.DELIVERY_CANCEL)) {
				deliveryOrder.setCancelMark(CancelMarkEnum.DEFAULT.getValue());
			}
			Optional.ofNullable(cmd.getDeliveryCancelCode()).ifPresent(deliveryOrder::setDeliveryCancelCode);
			Optional.ofNullable(cmd.getOriginWaybillNo()).ifPresent(deliveryOrder::setOriginWaybillNo);
		}

		//歪马租户 如用户点击确认收货驱动运单已送达、或系统自动已送达，或三方运力送达未传签收方式和送达图片的，填充签收方式
		if (MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId()) || !DeliveryRiderMccConfigUtils.fusionSelfDegreeSwitch()) {
			if (Objects.equals(cmd.getDeliveryEvent(), DeliveryEventEnum.FINISH_BY_ORDER_DONE) || Objects.equals(cmd.getDeliveryEvent(), DeliveryEventEnum.RIDER_FINISH_DELIVERY)) {
				deliveryOrder.setSignType(DeliverySignTypeEnum.OTHER.getType());
				//发送MQ
				try {
					if (Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
						StaffRider riderInfo = (StaffRider) deliveryOrder.getRiderInfo();
						if (Objects.nonNull(riderInfo)) {
							DeliveryChangeSyncOutMessage<DeliveryChangeSyncOutMessage.DeliveryDoneByOrderFinishBody> message =
									new DeliveryChangeSyncOutMessage<>(DeliveryAsyncOutTypeEnum.DELIVERY_DONE_BY_ORDER_FINISH.getValue(),
											new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(),
													deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getOrderBizType(), deliveryOrder.getOrderId(),
													deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus().getCode()),
											new DeliveryChangeSyncOutMessage.DeliveryDoneByOrderFinishBody(riderInfo.getRiderAccountId()));
							riderDeliveryOrderSyncOutClient.asyncOut(message);
						}
					}
				} catch (Exception e) {
					log.error("send mq error", e);
				}


			}
		}

		// 新供给处理自配送骑手信息变更同步渠道
		if (isProcessSelfDeliveryRiderChange(deliveryOrder.getTenantId(), cmd)) {
			processSelfDeliveryRiderChange(deliveryOrder, cmd, orderInfo);
		}

		//消费状态变更，骑手变更等
		deliveryOrder.onChange(cmd.getDeliveryEvent(), currentExceptionInfo, getRiderInfo(cmd.getRider(), currentExceptionInfo, deliveryOrder), cmd.getChangeTime());

		//歪马 && 三方配送 发送MQ触发push推送
		if (MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId()) && !Objects.equals(deliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
			DeliveryChangeSyncOutMessage<DeliveryChangeSyncOutMessage.ThirdDeliveryOrderChangeBody> message =
					new DeliveryChangeSyncOutMessage<>(DeliveryAsyncOutTypeEnum.THIRD_PART_DELIVERY_ORDER_CHANGE.getValue(),
							new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(),
									deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getOrderBizType(), deliveryOrder.getOrderId(),
									deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus().getCode()),
							new DeliveryChangeSyncOutMessage.ThirdDeliveryOrderChangeBody());

			riderDeliveryOrderSyncOutClient.asyncOut(message);
		}
		triggerTimeoutCheck(deliveryOrder, offlineExceptionInfo, cmd.getChangeTime(), currentExceptionCode);
		if (MccConfigUtils.duplicateMessagePush()) {
			deliveryOrder.onChange(cmd.getDeliveryEvent(), getExceptionInfo(cmd, orderInfo, offlineExceptionInfo, deliveryChannel), cmd.getRider(), cmd.getChangeTime());
		}

		//如果是歪马的接单消息 更新骑手状态
		if (MccConfigUtils.getOnlyUpdateRiderInfoSwitch()
				&& MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())
				&& Objects.equals(deliveryOrder.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())
				&& Objects.equals(cmd.getDeliveryEvent(), DeliveryEventEnum.RIDER_ASSIGN)) {
			try {
				ocmsChannelClient.onlySyncRiderInfo(deliveryOrder);
			} catch (Exception e) {
				log.error("同步骑手信息失败", e);
			}

		}


		// 有赞渠道的平台配送目前不需要同步骑手坐标信息
		DynamicOrderBizType orderBizType = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
		if (DynamicOrderBizType.YOU_ZAN_MIDDLE.equals(orderBizType) && deliveryOrder.isOrderChannelDeliveryPlatFormDeliveryOrder(deliveryChannel)) {
			log.info("平台配送目前不需要同步骑手坐标信息, cmd: {}", cmd);
			return;
		}

		Optional<DeliveryStatusEnum> syncRiderPositionPointOptional = deliveryOrder.getSyncRiderPositionPoint(deliveryChannel);
		if (Objects.nonNull(deliveryChannel) && Objects.nonNull(deliveryChannel.getDeliveryPlatFormCode()) && syncRiderPositionPointOptional.isPresent() && deliveryOrder.getStatus().equals(syncRiderPositionPointOptional.get())) {
			if (businessWrapper.isDhAndMedicineTenant(deliveryOrder.getTenantId())) {
				deliveryChangeNotifyService.notifySyncRiderPosition(deliveryOrder.getId(), DeliveryPlatformEnum.enumOf(deliveryChannel.getDeliveryPlatFormCode()),deliveryOrder.getTenantId(),deliveryOrder.getStoreId());
			} else {
				deliveryChangeNotifyService.notifySyncRiderPosition(deliveryOrder.getId(), DeliveryPlatformEnum.enumOf(deliveryChannel.getDeliveryPlatFormCode()), orderInfo.getOrderBizType(),deliveryOrder.getTenantId(),deliveryOrder.getStoreId());
			}
		}
	}

	private boolean isDeliveryTransfer(Long tenantId, Integer currentExceptionCode) {
		return !DeliveryRiderMccConfigUtils.checkIsDHTenant(tenantId)
				&& MccConfigUtils.getDeliveryTransferConfigSwitch()
				&& Objects.equals(currentExceptionCode, DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode());
	}

	private void triggerTimeoutCheck(DeliveryOrder deliveryOrder, DeliveryExceptionInfo offlineExceptionInfo,
									 LocalDateTime changeTime, Integer currentExceptionCode) {
		if (!deliveryOrder.needDoChange(changeTime)){
			return;
		}
		Integer offlineExceptionCode = Optional.ofNullable(offlineExceptionInfo).map(DeliveryExceptionInfo::getExceptionCode).orElse(0);
		if(offlineExceptionCode == DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode()
				&& !Objects.equals(currentExceptionCode, DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode())){
			// 配送状态回退恢复、如果运单已经整单超时需要进行异常恢复、触发超时检查
			deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);
		}
	}

	private boolean needDoDeliveryChangeCallback(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannel, LocalDateTime changeTime) {
		//开关，不需要校验直接返回true
		if (!Lion.getConfigRepository().getBooleanValue("delivery.time.pre.check", true)) {
			return true;
		}

		//暂时不改变聚合运力逻辑，仅改变麦芽田逻辑
		if ((!deliveryOrder.isMaltFarmDeliveryOrder(deliveryChannel)) && (!deliveryOrder.isDapDeliveryOrder(deliveryChannel))) {
			return true;
		}

		return deliveryOrder.needDoChange(changeTime);
	}

	private DeliveryExceptionInfo getOfflineExceptionInfo(DeliveryOrder deliveryOrder) {
		if(deliveryOrder == null){
			return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY);
		}

		return new DeliveryExceptionInfo(
				Optional.ofNullable(deliveryOrder.getExceptionType()).orElse(DeliveryExceptionTypeEnum.NO_EXCEPTION),
				Optional.ofNullable(deliveryOrder.getExceptionDescription()).orElse(StringUtils.EMPTY),
				Optional.ofNullable(deliveryOrder.getDeliveryExceptionCode()).orElse(DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode()));
	}

	private DeliveryExceptionInfo getExceptionInfo(DeliveryChangeCallbackCmd cmd, OrderInfo orderInfo, DeliveryExceptionInfo offlineExceptionInfo, DeliveryChannel deliveryChannel) {
		//配送完成或者订单完结，清理异常
		if (deliveryOrOrderFinish(cmd.getDeliveryEvent(), orderInfo)) {
			return new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY);
		}
		Long tenantId = Optional.ofNullable(orderInfo).map(OrderInfo::getOrderKey).map(OrderKey::getTenantId).orElse(0L);
		// 歪马租户不进行特殊处理
		if(MccConfigUtils.getDHTenantIdList().contains(String.valueOf(tenantId))){
			return cmd.getExceptionInfo();
		}
		Integer exceptionCode = Optional.ofNullable(cmd.getExceptionInfo())
				.map(DeliveryExceptionInfo::getExceptionCode)
				.orElse(0);
		boolean estimatedTimeoutExceptionCheck = offlineExceptionInfo != null
				&& Objects.equals(offlineExceptionInfo.getExceptionCode(), DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode());
		DynamicChannelType channelType = Optional.ofNullable(orderInfo)
				.map(OrderInfo::getOrderBizType)
				.map(DynamicOrderBizType::orderBizTypeValue2ChannelType)
				.orElse(null);
		boolean isPrivateDomainChannel = channelType != null && Objects.equals(channelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL) && deliveryChannel.getDeliveryPlatFormCode().equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
		// 配送状态回退(转单)优先级高于其它异常、不用特殊处理
		if(Objects.equals(DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode(), exceptionCode)){
			return cmd.getExceptionInfo();
		}
		if(exceptionCode != 0){
			// 超时未完成、伴随这个配送周期、只更新最新异常描述、保存历史超时异常code
			return (estimatedTimeoutExceptionCheck && !isPrivateDomainChannel)
					? new DeliveryExceptionInfo(cmd.getExceptionInfo().getExceptionType(),
					cmd.getExceptionInfo().getExceptionDescription(),
					DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode())
					: cmd.getExceptionInfo();
		}
		if(estimatedTimeoutExceptionCheck){
			// 恢复超时未完成异常描述信息
			return new DeliveryExceptionInfo(offlineExceptionInfo.getExceptionType(),
					DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getDesc(),
					DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode());
		}

		return cmd.getExceptionInfo();
	}

	private boolean deliveryOrOrderFinish(DeliveryEventEnum deliveryEvent, OrderInfo orderInfo){
		return (orderInfo != null && orderInfo.isFinished()) ||
				(deliveryEvent == DeliveryEventEnum.RIDER_FINISH_DELIVERY
				|| deliveryEvent == DeliveryEventEnum.FINISH_BY_ORDER_DONE);
	}

	private Rider getRiderInfo(Rider rider, DeliveryExceptionInfo currentExceptionInfo, DeliveryOrder deliveryOrder){
		if(rider != null){
			return rider;
		}
		Integer exceptionCode = Optional.ofNullable(currentExceptionInfo)
				.map(DeliveryExceptionInfo::getExceptionCode)
				.orElse(0);
		// 骑手信息为空、且是状态回退(转单)、需要清除历史骑手信息
		if(Objects.equals(DeliveryExceptionCodeEnum.DELIVERY_STATUS_ROLLBACK.getCode(), exceptionCode)){
			return Rider.buildEmptyRider();
		}
		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(deliveryOrder.getOrderBizType());
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		boolean isPrivateDomainChannel = channelType != null && Objects.equals(channelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL) && deliveryChannel.getDeliveryPlatFormCode().equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
		if(isPrivateDomainChannel){
			return Rider.buildEmptyRider();
		}
		return null;
	}

	private DeliveryExceptionInfo getExceptionInfo(DeliveryExceptionCallbackCmd cmd, DeliveryOrder deliveryOrder) {
		DeliveryExceptionInfo deliveryExceptionInfo = new DeliveryExceptionInfo(cmd.getExceptionType(), cmd.getReason(), cmd.getCode());
		if(deliveryOrder.getTenantId() == null || deliveryOrder.getDeliveryExceptionCode() == null){
			return deliveryExceptionInfo;
		}
		boolean estimatedTimeoutExceptionCheck = Objects.equals(deliveryOrder.getDeliveryExceptionCode(), DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode())
				&& !MccConfigUtils.getDHTenantIdList().contains(String.valueOf(deliveryOrder.getTenantId()));

		DynamicChannelType channelType = DynamicOrderBizType.orderBizTypeValue2ChannelType(deliveryOrder.getOrderBizType());
		DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrder.getDeliveryChannel());
		boolean isPrivateDomainChannel = channelType != null && Objects.equals(channelType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL) && deliveryChannel.getDeliveryPlatFormCode().equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());

		// 超时未完成、伴随这个配送周期、只更新最新异常描述、保存历史超时异常code
		if(estimatedTimeoutExceptionCheck && !isPrivateDomainChannel){
			return new DeliveryExceptionInfo(cmd.getExceptionType(), cmd.getReason(), DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode());
		}
		//私域渠道，渠道推送的异常优先级高于整单超时异常,有渠道异常时，再发生整单超时不处理
		if (isPrivateDomainChannel) {
			Integer deliveryExceptionCode = deliveryOrder.getDeliveryExceptionCode();
			if (DeliveryExceptionCodeEnum.NO_EXCEPTION.getCode() == deliveryExceptionCode ) {
				return deliveryExceptionInfo;
			} else {
				if (cmd.getCode().equals(DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode())) {
					return null;
				}
			}
		}
		return deliveryExceptionInfo;
	}

	private boolean isOrderChannelPlatformDelivery(OrderInfo orderInfo, DeliveryChangeCallbackCmd cmd) {
		DynamicChannelType orderBizType = DynamicOrderBizType.orderBizTypeValue2ChannelType(orderInfo.getOrderBizType());
		if (Objects.isNull(orderBizType)) {
			return false;
		}

		Integer deliveryPlatformCode = cmd.getDeliveryPlatformCode();
		if (Objects.isNull(deliveryPlatformCode)) {
			return false;
		}

		return (orderBizType.equals(DynamicChannelType.DOU_YIN) || Objects.equals(orderBizType.getChannelStandard(), PRIVATE_DOMAIN_CHANNEL)) && deliveryPlatformCode.equals(DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
	}

	private boolean isProcessSelfDeliveryRiderChange(Long tenantId, DeliveryChangeCallbackCmd cmd) {
		return MccConfigUtils.getProcessSelfDeliveryRiderChangeSwitch()
				&& !MccConfigUtils.checkIsDHTenant(tenantId)
				&& isSelfDeliveryPlatform(cmd.getDeliveryPlatformCode())
				&& Objects.nonNull(cmd.getRider())
				&& StringUtils.isNotEmpty(cmd.getRider().getRiderName())
				&& StringUtils.isNotEmpty(cmd.getRider().getRiderPhone());
	}

	private boolean isSelfDeliveryPlatform(Integer deliveryPlatformCode) {
		if (Objects.isNull(deliveryPlatformCode)) {
			return false;
		}

		DeliveryPlatformEnum deliveryPlatformEnum = DeliveryPlatformEnum.enumOf(deliveryPlatformCode);
		return Objects.equals(deliveryPlatformEnum, DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM)
				|| Objects.equals(deliveryPlatformEnum, DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)
				|| Objects.equals(deliveryPlatformEnum, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY);
	}

	/**
	 * 处理聚合配送骑手信息变更
	 * 如果骑手信息有变化返回true
	*/
	private boolean processSelfDeliveryRiderChange(DeliveryOrder deliveryOrder, DeliveryChangeCallbackCmd cmd, OrderInfo orderInfo) {
		boolean isRiderChange;
		Long orderId = cmd.getOrderId();

		try {
			Optional<Rider> riderOptional = riderInfoDeliveryRepository.queryRiderInfo(orderId);
			if (!riderOptional.isPresent()) {
				Rider riderCmd = new Rider(cmd.getRider().getRiderName(), cmd.getRider().getRiderPhone(), StringUtils.EMPTY);
				riderInfoDeliveryRepository.saveRiderInfo(orderId, riderCmd);
				return false;
			}

			Rider riderCache = riderOptional.get();
			isRiderChange = !Objects.equals(riderCache.getRiderName(), cmd.getRider().getRiderName()) || !Objects.equals(riderCache.getRiderPhone(), cmd.getRider().getRiderPhone());
			if (isRiderChange) {
				Rider riderCmd = new Rider(cmd.getRider().getRiderName(), cmd.getRider().getRiderPhone(), StringUtils.EMPTY);
				riderInfoDeliveryRepository.saveRiderInfo(orderId, riderCmd);
				// 这里需要先同步渠道骑手信息变更，再同步渠道后续新骑手的配送状态，因此实现为同步调用
				ocmsChannelClient.syncRiderInfoChange(deliveryOrder, riderCmd, orderInfo.getChannelOrderId());
			}
			return isRiderChange;
		} catch (Exception e) {
			log.error("DeliveryCallbackApplicationService processSelfDeliveryRiderChange failed", e);
			return false;
		}
	}

}
