package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryLaunchFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.es.po.DeliveryOrderEsPo;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;

/**
 * 跑腿锁单兜底发聚合配送领域服务
 */
@Slf4j
@Service
public class PaoTuiLockStatusDomainService {

    public static final String DELIVERY = "delivery";
    public static final String DELIVERY_ORDER = "delivery.order";

    @Resource
    private DeliveryPlatformClient deliveryPlatformClient;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private DeliveryTimeOutCheckService deliveryTimeOutCheckService;

    @Resource
    private DeliveryWarnEventPublisher deliveryWarnEventPublisher;

    @Resource
    private PushClient pushClient;

    @Resource
    private OcmsChannelClient ocmsChannelClient;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @CatTransaction
    @MethodLog(logRequest = false, logResponse = true)
    public Optional<Failure> paoTuiLockStatusNotify(OrderInfo orderInfo, DeliveryPoi deliveryPoi) {

        if( MccConfigUtils.getPaotuiUnLockSwitch()
                && MccConfigUtils.isPaotuiLockOrderV2GrayTenant(orderInfo.getOrderKey().getTenantId())
                && MccConfigUtils.isPaotuiLockAggDeliveryPlatform(deliveryPoi.getDeliveryPlatform().getCode())){
            // 查生效运单
            List<DeliveryOrder> deliveryOrderList = deliveryOrderRepository.getDeliveryOrdersAllWithOrderIdSlave(orderInfo.getOrderKey().getOrderId());
            DeliveryOrder deliveryOrderActive = null;
            Integer deliveryPlatFormCode = null;
            if(CollectionUtils.isNotEmpty(deliveryOrderList)){
                deliveryOrderList.sort(Comparator.comparing(DeliveryOrder::getCreateTime));
                deliveryOrderActive = deliveryOrderList.get(0);
                HashSet<Integer> set=new HashSet<>();
                set.add(deliveryOrderActive.getDeliveryChannel());
                List<DeliveryChannel> deliveryChannelList = deliveryChannelApplicationService.batchQueryDeliveryChannelByCarrierCodeSet(set);
                if(CollectionUtils.isNotEmpty(deliveryChannelList)){
                    deliveryPlatFormCode = deliveryChannelList.get(0).getDeliveryPlatFormCode();
                }
            }
            if(deliveryOrderActive !=null && MccConfigUtils.isPaotuiLockAggDeliveryPlatform(deliveryPlatFormCode)){
                // 麦芽田 && 运单已存在 走2.0 直接推送麦芽田解锁消息
                log.info("同步聚合配送平台跑腿锁单2.0锁单状态变更信息: orderId:{}, isLocked: {}", orderInfo.getOrderKey().getOrderId(), orderInfo.getIsLocked());
                return deliveryPlatformClient.notifyDeliveryPlatformLockStatusChange(deliveryPoi, orderInfo, deliveryOrderActive);
            }
        }

        //校验当前订单的配送状态
        if (!MccConfigUtils.getUnlockLaunchAggDeliveryDistributeStatus(orderInfo.getDistributeStatus())) {
            log.warn("订单[{}]配送状态不满足兜底发起配送", orderInfo.getOrderKey());
            return Optional.empty();
        }

		//判断是否已经在配送中
		if(MccConfigUtils.getLockInDeliverySwitch() && orderInfo.isInDelivery() && StringUtils.isNotBlank(orderInfo.getRiderName())){
			log.info("订单[{}]已经在配送中，因此不用发起配送", orderInfo.getOrderKey().getOrderId());
			return Optional.empty();
		}

        DeliveryChannelEnum channelEnum = DeliveryChannelEnum.MALT_FARM;
        if(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM == deliveryPoi.getDeliveryPlatform()){
            channelEnum = DeliveryChannelEnum.DAP_DELIVERY;
            ocmsChannelClient.cancelDelivery(deliveryPoi.getTenantId(), orderInfo.getPoiId(), orderInfo.getChannelOrderId());
        }

        DeliveryOrder deliveryOrder;
        // 发起新的配送单
        deliveryOrder = DeliveryOrder.fromPaoTuiCustomer(orderInfo, channelEnum.getCode(), orderInfo.getChannelPackageId());
        log.info("Launch a new deliveryOrder: {}", deliveryOrder);
        deliveryOrderRepository.saveDeliveryOrder(deliveryOrder);
        try {
            Optional<LaunchFailure> launchFailure = doLaunch(deliveryPoi, orderInfo, deliveryOrder);
            deliveryTimeOutCheckService.triggerDeliveryTimeOutCheck(deliveryOrder);

            if (launchFailure.isPresent()) {
                String failReason = launchFailure.get().getFailureMessage();
                deliveryWarnEventPublisher.postEvent(new DeliveryLaunchFailedEvent(deliveryOrder, failReason));
                deliveryOrder.onChange(DeliveryEventEnum.DELIVERY_REJECT, launchFailure.get().getExceptionInfo(), null, LocalDateTime.now());
                pushClient.pushDeliveryException(deliveryOrder);
                return Optional.of(new Failure(false, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, failReason));
            }
            // 原1.0逻辑 同步给聚合配送平台等待分配骑手状态
            Optional<Failure> failure = deliveryPlatformClient.syncPaoTuiDeliveryStatusAfterLockOrder(deliveryOrder, orderInfo.getChannelPackageId());
            if (failure.isPresent()) {
                Cat.logEvent(DELIVERY, DELIVERY_ORDER,"1","");
                log.error("同步配送状态失败,可能存在多骑手到店情况");
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("兜底发三方配送失败", e);
            return Optional.of(new Failure(false, FailureCodeEnum.LAUNCH_DELIVERY_FAILED));
        }
    }

    private Optional<LaunchFailure> doLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder){
        try {
            RetryTemplate retryTemplate= RetryTemplateUtil.simpleWithFixedRetry(2,100);
            Optional<LaunchFailure> launchFailure = retryTemplate.execute(new RetryCallback<Optional<LaunchFailure>, Throwable>() {
                @Override
                public Optional<LaunchFailure> doWithRetry(RetryContext retryContext) throws Throwable {
                    Optional<LaunchFailure> launchFailure = deliveryPlatformClient.launch(deliveryPoi, orderInfo, deliveryOrder, TransferOrderMarkEnum.NORMAL_ORDER.getCode());
                    if (launchFailure.isPresent()) {
                        throw new Throwable(String.valueOf(launchFailure.get().getFailureCode()));
                    }
                    return launchFailure;
                }
            });

            if (launchFailure.isPresent()) {
                return Optional.of(new LaunchFailure(false, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, launchFailure.get().getExceptionInfo()));
            }
            return Optional.empty();
        } catch (Throwable e) {
            log.error("发配送失败", e);
            return Optional.of(new LaunchFailure(false, FailureCodeEnum.LAUNCH_DELIVERY_FAILED, "调用渠道网关失败"));
        }
    }
}
