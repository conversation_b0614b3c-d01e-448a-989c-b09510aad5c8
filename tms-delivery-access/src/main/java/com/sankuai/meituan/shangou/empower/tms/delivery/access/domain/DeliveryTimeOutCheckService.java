package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
public class DeliveryTimeOutCheckService {
    @Resource
    private DeliveryChangeNotifyService deliveryChangeNotifyService;

    @CatTransaction
    public void triggerDeliveryTimeOutCheck(DeliveryOrder deliveryOrder) {
        if (!MccConfigUtils.getEstimatedTimeOutCheckSwitch()) {
            return;
        }
        // 过滤歪马租户
        if (MccConfigUtils.getDHTenantIdList().contains(String.valueOf(deliveryOrder.getTenantId()))) {
            return;
        }
        LocalDateTime estimatedDeliveryTime = Optional.ofNullable(deliveryOrder.getEstimatedDeliveryEndTime())
                .orElse(deliveryOrder.getEstimatedDeliveryTime());
        if (estimatedDeliveryTime == null) {
            return;
        }
        long delayMillis = Duration.between(LocalDateTime.now(), estimatedDeliveryTime).toMillis();
        Long orderId = deliveryOrder.getOrderId();
        if(deliveryOrder.getPlatformSourceEnum()!=null && deliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
            orderId = deliveryOrder.getFulfillmentOrderId();
        }
        deliveryChangeNotifyService.notifyDeliveryTimeOutCheckTriggered(orderId, deliveryOrder.getTenantId(),
                deliveryOrder.getStoreId(), estimatedDeliveryTime, Math.max(delayMillis, 5000L));
    }
}
