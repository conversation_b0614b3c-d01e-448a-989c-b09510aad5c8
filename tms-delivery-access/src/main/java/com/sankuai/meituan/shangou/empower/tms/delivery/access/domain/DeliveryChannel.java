package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain;

import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.YzDeliveryChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType.YOU_ZAN;

/**
 * 配送渠道值对象
 * <AUTHOR>
 * @date 2023/4/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class DeliveryChannel {

    private static final Integer UNKNOWN_DELIVERY_PLATFORM_CODE = -1;

    public static final String UNKNOWN_CARRIER_NAME = "未知承运商";

    public static final Integer DEFAULT_ORDER_CHANNEL_CODE = NumberUtils.INTEGER_ZERO;

    private Long id;

    /**
     * 承运商信息，麦芽田是channelMark，青云和有赞是channelCode
     */
    private String logisticMark;

    /**
     * 配送平台code, @see DeliveryPlatformEnum.getCode()
     */
    private Integer deliveryPlatFormCode;

    /**
     * 牵牛花承运商code, @see DeliveryChannelEnum.getCode()
     */
    private Integer carrierCode;

    /**
     * 牵牛花承运商名称, @see DeliveryChannelEnum.getName()
     */
    private String carrierName;

    /**
     * 订单渠道code, @see ChannelType.getValue()，针对平台配送
     */
    private Integer orderChannelCode;

    public static DeliveryChannel translateFromDeliveryChannelEnum(Integer carrierCode) {
        if (Objects.isNull(carrierCode)) {
            log.warn("DeliveryChannel translateFromDeliveryChannelEnum, carrierCode is null");
            return getUnknownDeliveryChannel(NumberUtils.INTEGER_ZERO);
        }

        DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(carrierCode);
        if (Objects.isNull(deliveryChannelEnum)) {
            return getUnknownDeliveryChannel(carrierCode);
        }

        DeliveryChannel deliveryChannel = new DeliveryChannel();
        deliveryChannel.setId(NumberUtils.LONG_ZERO);
        deliveryChannel.setLogisticMark(StringUtils.EMPTY);
        if (Objects.isNull(deliveryChannelEnum.getDeliveryPlatform())) {
            // 平台配送的deliveryPlatform为null
            deliveryChannel.setDeliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE);
        } else {
            deliveryChannel.setDeliveryPlatFormCode(deliveryChannelEnum.getDeliveryPlatform().getCode());
        }
        deliveryChannel.setCarrierCode(deliveryChannelEnum.getCode());
        deliveryChannel.setCarrierName(deliveryChannelEnum.getName());
        deliveryChannel.setOrderChannelCode(NumberUtils.INTEGER_ZERO);

        return deliveryChannel;
    }

    public static DeliveryChannel translateFromDeliveryChannelEnum(Integer deliveryPlatFormCode, Integer orderChannelCode, String logisticMark) {
        DeliveryChannelEnum deliveryChannelEnum = null;
        switch (DeliveryPlatformEnum.enumOf(deliveryPlatFormCode)) {
            case MALT_FARM_DELIVERY_PLATFORM:
                deliveryChannelEnum = FarmDeliveryChannelEnum.mapToDeliveryChannel(logisticMark);
                break;
            case DAP_DELIVERY_PLATFORM:
                deliveryChannelEnum = MccConfigUtils.dapChannelMapping(logisticMark);
                break;
            case ORDER_CHANNEL_DELIVERY_PLATFORM:
                deliveryChannelEnum = getDeliveryChannel4OrderChannelDeliveryPlatform(orderChannelCode, logisticMark);
                break;
            default:
                return getUnknownDeliveryChannel(deliveryPlatFormCode, orderChannelCode, logisticMark);
        }

        if (Objects.isNull(deliveryChannelEnum)) {
            return getUnknownDeliveryChannel(deliveryPlatFormCode, orderChannelCode, logisticMark);
        }

        DeliveryChannel deliveryChannel = new DeliveryChannel();
        deliveryChannel.setId(NumberUtils.LONG_ZERO);
        deliveryChannel.setLogisticMark(logisticMark);
        deliveryChannel.setDeliveryPlatFormCode(deliveryPlatFormCode);
        deliveryChannel.setCarrierCode(deliveryChannelEnum.getCode());
        deliveryChannel.setCarrierName(deliveryChannelEnum.getName());
        deliveryChannel.setOrderChannelCode(orderChannelCode);
        return deliveryChannel;
    }

    public static List<DeliveryChannel> translateFromDeliveryChannelEnum(Set<Integer> carrierCodedSet) {
        return carrierCodedSet.stream().map(DeliveryChannel::translateFromDeliveryChannelEnum).collect(Collectors.toList());
    }

    public static DeliveryChannel getUnknownDeliveryChannel(Integer carrierCode) {
        return DeliveryChannel.builder()
                .id(NumberUtils.LONG_ZERO)
                .logisticMark(StringUtils.EMPTY)
                .deliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE)
                .carrierCode(carrierCode)
                .carrierName(UNKNOWN_CARRIER_NAME)
                .orderChannelCode(NumberUtils.INTEGER_ZERO).build();
    }

    private static DeliveryChannel getUnknownDeliveryChannel(Integer deliveryPlatFormCode, Integer orderChannelCode, String logisticMark) {
        return DeliveryChannel.builder().id(NumberUtils.LONG_ZERO)
                .logisticMark(logisticMark)
                .deliveryPlatFormCode(deliveryPlatFormCode)
                .carrierCode(NumberUtils.INTEGER_ZERO)
                .carrierName(UNKNOWN_CARRIER_NAME)
                .orderChannelCode(orderChannelCode).build();
    }

    private static DeliveryChannelEnum getDeliveryChannel4OrderChannelDeliveryPlatform(Integer orderChannelCode, String logisticMark) {
        try {
            DynamicChannelType channelType = DynamicChannelType.findOf(orderChannelCode);
            if (YOU_ZAN.equals(channelType)) {
                YzDeliveryChannelEnum yzDeliveryChannelEnum = YzDeliveryChannelEnum.getYzDeliveryChannel(
                        Integer.parseInt(logisticMark));
                if (Objects.nonNull(yzDeliveryChannelEnum)) {
                    return yzDeliveryChannelEnum.getDeliveryChannel();
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }
}
