package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform;

import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.DeliveryTenantPoiSyncRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.LinkTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 配送平台客户端
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 */
public interface DeliveryPlatformClient {

	/**
	 * 预发配送
	 * 查询可配送渠道+询价
	 */
	Result<List<DeliveryChannelPreLaunchInfo>> preLaunch(DeliveryPoi deliveryPoi, OrderInfo orderInfo);

	/**
	 * 发起配送
	 */
	Optional<LaunchFailure> launch(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder,Integer transferOrderMark);

	/**
	 * 取消配送
	 */
	Optional<Failure> cancelDelivery(DeliveryOrder deliveryOrder);

	/**
	 * 查询骑手坐标信息
	 */
	Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryPoi deliveryPoi);

	/**
	 * 查询骑手坐标信息
	 */
	Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder, DeliveryChannel deliveryChannelDto, DeliveryPoi deliveryPoi);

	/**
	 * 查询骑手坐标信息
	 */
	Optional<CoordinatePoint> queryRiderLocation(DeliveryOrder deliveryOrder);

	/**
	 * 查询可用的配送规则.
	 */
	List<LaunchRuleInfo> queryAvailableDeliveryRules(DeliveryPlatformEnum deliveryPlatform);

	/**
	 * 创建门店
	 * @param
	 * @return
	 */
	Optional<Long> createShop(DeliveryPoi deliveryPoi);

	/**
	 * 自动发渠道配送
	 * @param deliveryOrder
	 * @return
	 */
	Optional<Failure> autoSend(DeliveryOrder deliveryOrder);


	/**
	 * 创建门店
	 * @param
	 * @return
	 */
	Optional<Failure> closeShop(DeliveryPoi deliveryPoi);


	Optional<Map<String,DeliveryPlatformUrlInfo>> queryLinkInfo(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum);

	Optional<Map<String, DeliveryPlatformUrlInfo>> queryLinkInfoOfToken(DeliveryPoi deliveryPoi, List<String> markIdList, LinkTypeEnum typeEnum, String token, SiteTypeEnum siteType);

	/**
	 * 转自配送后取消原有的配送单
	 * @param
	 * @return
	 */
	Optional<Failure> cancelDeliveryForTransOrder(DeliveryOrder deliveryOrder);

	/**
	 * 转自配送后取消原有的配送单
	 * @param
	 * @return
	 */
	Optional<Failure> cancelDeliveryForOFC(DeliveryOrder deliveryOrder);


	Optional<Failure> deliveryFastAuth(DeliveryPoi deliveryPoi, List<Long> wmStoreIdList, List<ChannelStoreRelation> relationList);

	Result<DapShopAuthResult> deliveryShopAuth(DeliveryPlatformEnum deliveryPlatform, Long storeId);

	/**
	 * 同步渠道门店
	 * @param request
	 * @return
	 */
	Optional<Failure> syncTenantPoi(DeliveryTenantPoiSyncRequest request);

	/**
	 * 手动发起配送
	 */
	Optional<LaunchFailure> manualLaunchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo);


	/**
	 * 跑腿锁单结束后，同步跑腿单等待分配骑手状态
	 */
	Optional<Failure> syncPaoTuiDeliveryStatusAfterLockOrder(DeliveryOrder deliveryOrder, String channelServicePackageId);

	/**
	 * 同步订单信息变更
	 */
	Optional<Failure> syncOrderInfoChange(DeliveryOrder deliveryOrder, OrderInfo orderInfo, List<Integer> changedFields);


	/**
	 * 通知配送平台锁单状态变更
	 * @param deliveryPoi 门店信息
	 * @param orderInfo 订单信息
	 * @param deliveryOrder 运单信息
	 * @return 变更结果
	 */
	Optional<Failure>  notifyDeliveryPlatformLockStatusChange(DeliveryPoi deliveryPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder);
}
