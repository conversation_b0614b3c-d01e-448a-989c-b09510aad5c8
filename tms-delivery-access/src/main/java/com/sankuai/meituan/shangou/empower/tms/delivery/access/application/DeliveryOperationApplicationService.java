package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.mq.Dto.OrderTrackEvent;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.GetAggDeliveryLinkRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.delivery.GetAggDeliveryLinkResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.abn.TradeShippingOrderFacade;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OFCSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryChannelPreLaunchInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.event.DeliveryLaunchFailedEvent;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor.DeliveryWarnEventPublisher;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.AggDeliveryPlatformAppConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.UrlUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.launchpoint.ImmediateOrderDeliveryLaunchPointEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggOrderDetailLinkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggStoreSettingsLinkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Operator;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.meituan.mafka.client.consumer.ConsumeStatus.CONSUME_SUCCESS;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum.*;
import static com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/13
 */
@Slf4j
@Service
public class DeliveryOperationApplicationService {

	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;
	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private LaunchDeliveryDomainService launchDeliveryDomainService;
	@Resource
	private TurnToSelfDeliveryDomainService turnToSelfDeliveryDomainService;
	@Resource
	private DeliveryWarnEventPublisher deliveryWarnEventPublisher;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private DeliveryNotifyService deliveryNotifyService;

	@Resource
	private OFCSystemClient ofcSystemClient;

	@Resource
	private TradeShippingOrderFacade tradeShippingOrderFacade;

	@Resource
	private PaoTuiLockStatusDomainService paoTuiLockStatusDomainService;

	@Resource
	protected DapChannelAggDeliveryThriftService dapChannelAggDeliveryThriftService;

	@Resource
	private AggDeliveryLinkAuthSquirrelOperationService aggDeliveryLinkAuthSquirrelOperationService;

	@Resource
	private DeliveryChannelApplicationService deliveryChannelApplicationService;

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Result<List<DeliveryChannelPreLaunchInfo>> preLaunchDelivery(OrderKey orderKey) {
		Preconditions.checkNotNull(orderKey, "orderKey is null");

		//查询订单信息
		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
		if (orderQueryResult.isFail()) {
			return new Result<>(orderQueryResult.getFailure());
		}

		Optional<DeliveryPoi> opDeliveryPoi ;
		if(MccConfigUtils.getDHTenantIdList().contains(orderKey.getTenantId()+"")){
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(orderKey.getTenantId(), orderKey.getStoreId());
		}else {
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
					orderQueryResult.getInfo().getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderKey.getTenantId(), orderKey.getStoreId(), orderBizType.getChannelId());
		}
		//校验是否接入三方配送
		if (!opDeliveryPoi.isPresent()) {
			return new Result<>(new Failure(false, FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP));
		}

		return launchDeliveryDomainService.preLaunchDelivery(opDeliveryPoi.get(), orderQueryResult.getInfo());
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> launchDelivery(DeliveryPoi deliveryPoi, OrderKey orderKey, boolean acceptRetry,PlatformSourceEnum platformSourceEnum,Long fulfillOrderId) {
		Preconditions.checkNotNull(deliveryPoi, "deliveryPoi is null");
		Preconditions.checkNotNull(orderKey, "orderKey is null");

		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
		if (orderQueryResult.isFail()) {
			return Optional.of(orderQueryResult.getFailure());
		}

		return launchDelivery(deliveryPoi, orderQueryResult.getInfo(), acceptRetry,platformSourceEnum,fulfillOrderId);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> launchDelivery(DeliveryPoi deliveryPoi, OrderInfo orderInfo, boolean acceptRetry, PlatformSourceEnum platformSourceEnum, Long fulfillOrderId) {
		Preconditions.checkNotNull(deliveryPoi, "deliveryPoi is null");
		Preconditions.checkNotNull(orderInfo, "orderInfo is null");

		switch (deliveryPoi.getDeliveryPlatform()) {
			case AGGREGATION_DELIVERY_PLATFORM:
				Optional<Failure> failure = checkOrderDeliverableForThirdDelivery(orderInfo);
				if (failure.isPresent()) {
					return failure;
				}

				return launchDeliveryDomainService.launchDelivery(deliveryPoi, orderInfo, DeliveryChannelEnum.AGGREGATION_DELIVERY, StringUtils.EMPTY, null, acceptRetry,platformSourceEnum,fulfillOrderId);

			case MALT_FARM_DELIVERY_PLATFORM:
				failure = checkOrderDeliverableForThirdDelivery(orderInfo);
				if (failure.isPresent()) {
					return failure;
				}

				return launchDeliveryDomainService.launchDelivery(deliveryPoi, orderInfo, DeliveryChannelEnum.MALT_FARM, StringUtils.EMPTY, null, acceptRetry,platformSourceEnum,fulfillOrderId);

			case MERCHANT_SELF_DELIVERY:
				failure = checkDeliverableForMerchantDelivery(orderInfo);
				if (failure.isPresent()) {
					return failure;
				}

				return launchDeliveryDomainService.launchDelivery(deliveryPoi, orderInfo, DeliveryChannelEnum.MERCHANT_DELIVERY, StringUtils.EMPTY, null, acceptRetry,platformSourceEnum,fulfillOrderId);

			case DAP_DELIVERY_PLATFORM:
				failure = checkOrderDeliverableForThirdDelivery(orderInfo);
				if (failure.isPresent()) {
					return failure;
				}

				return launchDeliveryDomainService.launchDelivery(deliveryPoi, orderInfo, DeliveryChannelEnum.DAP_DELIVERY, StringUtils.EMPTY, null, acceptRetry,platformSourceEnum,fulfillOrderId);

			case ORDER_CHANNEL_DELIVERY_PLATFORM:
				failure = checkOrderDeliverableForPlatformDelivery(orderInfo);
				if (failure.isPresent()) {
					return failure;
				}

				return launchDeliveryDomainService.launchDelivery(deliveryPoi, orderInfo, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY, StringUtils.EMPTY, null, acceptRetry,platformSourceEnum,fulfillOrderId);

			default:
				throw new IllegalStateException("Unexpected value: " + deliveryPoi.getDeliveryPlatform());
		}
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> launchDelivery(OrderKey orderKey, DeliveryChannelEnum deliveryChannel, String servicePackage, BigDecimal estimatedDeliveryFee, boolean acceptRetry) {
		Preconditions.checkNotNull(orderKey, "orderKey is null");
		Preconditions.checkNotNull(deliveryChannel, "deliveryChannel is null");
		Preconditions.checkNotNull(servicePackage, "servicePackage is null");

		//查询订单信息
		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
		if (orderQueryResult.isFail()) {
			return Optional.of(orderQueryResult.getFailure());
		}

		//校验是否接入三方配送
		Optional<DeliveryPoi> opDeliveryPoi ;
		if(MccConfigUtils.getDHTenantIdList().contains(orderKey.getTenantId()+"")){
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(orderKey.getTenantId(), orderKey.getStoreId());
		}else {
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
					orderQueryResult.getInfo().getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderKey.getTenantId(), orderKey.getStoreId(), orderBizType.getChannelId());
		}
		if (!opDeliveryPoi.isPresent()) {
			return Optional.of(new Failure(false, FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP));
		}

		Optional<Failure> failure = checkOrderDeliverable(orderQueryResult.getInfo(), deliveryChannel);
		if (failure.isPresent()) {
			return failure;
		}

		return launchDeliveryDomainService.launchDelivery(opDeliveryPoi.get(), orderQueryResult.getInfo(), deliveryChannel, servicePackage, estimatedDeliveryFee, acceptRetry,null,null);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> turnToThirdDelivery(OrderKey orderKey, DeliveryChannelEnum deliveryChannel, String servicePackage, BigDecimal estimatedDeliveryFee) {
		Preconditions.checkNotNull(orderKey, "orderKey is null");
		Preconditions.checkNotNull(deliveryChannel, "deliveryChannel is null");
		Preconditions.checkNotNull(servicePackage, "servicePackage is null");

		//查询订单信息
		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
		if (orderQueryResult.isFail()) {
			return Optional.of(orderQueryResult.getFailure());
		}

		//校验订单是否可以转三方配送
		OrderInfo orderInfo = orderQueryResult.getInfo();
		if (!orderInfo.canTurnToSelfDelivery()) {
			log.warn("订单[{}]不满足发起配送条件，转三方配送失败", orderInfo);
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		//校验是否接入自配送
		Optional<DeliveryPoi> opDeliveryPoi ;
		if(MccConfigUtils.getDHTenantIdList().contains(orderKey.getTenantId()+"")){
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(orderKey.getTenantId(), orderKey.getStoreId());
		}else {
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
					orderQueryResult.getInfo().getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderKey.getTenantId(), orderKey.getStoreId(), orderBizType.getChannelId());
		}
		if (!opDeliveryPoi.isPresent()) {
			return Optional.of(new Failure(false, FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP));
		}

		//校验订单收货人信息是否完整，是否可以发起配送
		Optional<Failure> failure = checkReceiver(orderInfo);
		if (failure.isPresent()) {
			return failure;
		}

		return turnToSelfDeliveryDomainService.turnToThirdDelivery(
				opDeliveryPoi.get(), orderInfo, deliveryChannel, servicePackage, estimatedDeliveryFee
		);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> turnToAggregationDelivery(OrderInfo orderInfo, DeliveryPlatformEnum deliveryPlatform, Operator operator,Long tenantId) {
		Preconditions.checkNotNull(orderInfo, "orderKey is null");
		Preconditions.checkNotNull(deliveryPlatform, "deliveryPlatform is null");

		//校验是否接入自配送
		DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
				orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
		Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(tenantId, orderInfo.getWarehouseId(), orderBizType.getChannelId()) ;

		if (!opDeliveryPoi.isPresent()) {
			return Optional.of(new Failure(false, FailureCodeEnum.TENANT_SHOP_NOT_CONFIG_DELIVERY_CHANNEL_SHOP));
		}

		//抖音收件人信息解密失败，不能转自配送/聚合配送
		if (isDownOrder(orderInfo)) {
			return Optional.of(new Failure(false, FailureCodeEnum.DOU_YIN_TURN_SELF_AND_AGG_DELIVERY_ERROR));
		}

		//校验订单收货人信息是否完整，是否可以发起配送
		Optional<Failure> failure = checkReceiver(orderInfo);
		if (failure.isPresent()) {
			return failure;
		}

		return turnToSelfDeliveryDomainService.turnToAggregationDelivery(
				opDeliveryPoi.get(), orderInfo, deliveryPlatform,operator
		);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> turnToMerchantSelfDelivery(OrderKey orderKey) {
		Preconditions.checkNotNull(orderKey, "orderKey is null");

		//查询订单信息
		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
		if (orderQueryResult.isFail()) {
			return Optional.of(orderQueryResult.getFailure());
		}

		if (orderQueryResult.getInfo().isFinished()) {
			log.warn("订单[{}]不满足发起配送条件，转商家自配送失败", orderQueryResult.getInfo());
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		return turnToSelfDeliveryDomainService.turnToMerchantSelfDelivery(orderQueryResult.getInfo());
	}


	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	public Optional<Failure> turnToMerchantSelfDeliveryForDrunkHorse(OrderKey orderKey, StaffRider courier) {
		Preconditions.checkNotNull(orderKey, "orderKey is null");

		//查询订单信息
		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, true);
		if (orderQueryResult.isFail()) {
			return Optional.of(orderQueryResult.getFailure());
		}

		if (orderQueryResult.getInfo().isFinished()) {
			log.warn("订单[{}]不满足发起配送条件，转商家自配送失败", orderQueryResult.getInfo());
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_IS_FINAL));
		}

		return turnToSelfDeliveryDomainService.turnToMerchantSelfDeliveryForDrunkHorse(orderQueryResult.getInfo(), courier);
	}


	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> cancelDelivery(OrderKey orderKey) {
		Optional<DeliveryOrder> deliveryOrderOptional = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(orderKey.getTenantId())){
			deliveryOrderOptional = deliveryOrderRepository.getActiveDeliveryOrderWithTenant(orderKey.getOrderId(),orderKey.getTenantId(),orderKey.getStoreId());
		}else {
			deliveryOrderOptional = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderKey.getOrderId());
		}

		if (!deliveryOrderOptional.isPresent()) {
			log.error("DeliverOrder[{}] not exist", orderKey.getOrderId());
			return Optional.of(new Failure(false, NO_CANCEL_DELIVERY.getCode(), NO_CANCEL_DELIVERY.getMessage()));
		}
		//判断状态，判断渠道，调用，然后判断并设置标志位
		DeliveryOrder deliveryOrder = deliveryOrderOptional.get();

		if (Objects.equals(deliveryOrder.getCancelMark(), CancelMarkEnum.CANCELING.getValue())) {
			return Optional.of(new Failure(false, REPEAT_CANCEL_DELIVERY.getCode(), REPEAT_CANCEL_DELIVERY.getMessage()));
		}

		if (deliveryOrder.getDeliveryChannel() == null) {
			return Optional.of(new Failure(false, CANCEL_DELIVERY_CHANNEL.getCode(), CANCEL_DELIVERY_CHANNEL.getMessage()));
		}

		return deliveryOrder.cancel();
	}

	/**
	 * 歪马预订单立即发配送
	 * @param orderKey
	 * @return
	 */
	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> immediatelyDelivery4DrunkHorse(OrderKey orderKey) {
		//校验是否是自营配送
		Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(orderKey.getTenantId(), orderKey.getStoreId());
		if (!opDeliveryPoi.isPresent() || DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY != opDeliveryPoi.get().getDeliveryPlatform()) {
			return Optional.of(new Failure(false, DELIVERY_SHOP_CONFIG_ERR.getCode(), DELIVERY_SHOP_CONFIG_ERR.getMessage()));
		}
		DeliveryPoi deliveryPoi = opDeliveryPoi.get();

		//查订单信息
		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(orderKey, false);
		if (orderQueryResult.isFail()) {
			log.error("查询订单[{}]失败. failure:{}", orderKey, orderQueryResult.getFailure());
			return Optional.of(orderQueryResult.getFailure());
		}
		OrderInfo orderInfo = orderQueryResult.getInfo();

		//校验订单类型
		if (!orderInfo.isBookingOrder()) {
			log.warn("订单类型错误,不是预订单, order: {}", orderKey);
			return Optional.of(new Failure(false, IS_NOT_BOOKING_ORDER.getCode(), IS_NOT_BOOKING_ORDER.getMessage()));
		}

		return launchDelivery(deliveryPoi, orderInfo, true,null,null);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<LaunchFailure> manualLaunchDelivery(Long tenantId, Long storeId, Long orderId, Long operatorId, String appId) {
		if (MccConfigUtils.checkIsDHTenant(tenantId)) {
			log.error("DeliveryOperationApplicationService.manualLaunchDelivery failed, dhTenant not support");
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
		}

		Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(new OrderKey(tenantId, storeId, orderId), false);
		if (orderQueryResult.isFail()) {
			log.error("DeliveryOperationApplicationService.manualLaunchDelivery failed, query orderInfo failed");
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
		}
		OrderInfo orderInfo = orderQueryResult.getInfo();

		DynamicOrderBizType orderBizType = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
		if (Objects.isNull(orderBizType)) {
			sendOrderTrack(tenantId, orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), operatorId, false, appId);
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
		}

		Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(tenantId, storeId, orderBizType.getChannelId());
		if (!opDeliveryPoi.isPresent()) {
			log.error("DeliveryOperationApplicationService.manualLaunchDelivery, opDeliveryPoi is not present");
			sendOrderTrack(tenantId, orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), operatorId, false, appId);
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
		}

		Optional<Failure> failure = checkOrderDeliverableForManualLaunchDelivery(orderInfo, opDeliveryPoi.get());
		if (failure.isPresent()) {
			sendOrderTrack(tenantId, orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), operatorId, false, appId);
			return Optional.of(new LaunchFailure(false, FailureCodeEnum.MANUAL_LAUNCH_DELIVERY_FAILED));
		}

		Optional<LaunchFailure> failureOptional = launchDeliveryDomainService.manualLaunchDelivery(opDeliveryPoi.get(), orderInfo, operatorId, appId);
		sendOrderTrack(tenantId, orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(), operatorId, !failureOptional.isPresent(), appId);
		return failureOptional;
	}

	private Optional<Failure> checkOrderDeliverable(OrderInfo orderInfo, DeliveryChannelEnum deliveryChannel) {
		if (deliveryChannel == DeliveryChannelEnum.MERCHANT_DELIVERY) {
			return checkDeliverableForMerchantDelivery(orderInfo);

		} else {
			return checkOrderDeliverableForThirdDelivery(orderInfo);
		}
	}

	private Optional<Failure> checkDeliverableForMerchantDelivery(OrderInfo orderInfo) {
		if (orderInfo.isNewSupplyStoreDelivery()) {
			log.warn("订单[{}]为到店自提单，将放弃创建运单", orderInfo.getOrderKey());
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}
		//对于歪马线下推广订单，不发配送
		if (MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId())
				&& MccConfigUtils.isDHOfflinePromoteGrayStore(orderInfo.getOrderKey().getStoreId())) {
			//这里对ext和ext下的PullNewSelfPickGoodsOrder/accountId判空过了
			if (orderInfo.isPullNewSelfPickGoodsOrder()) {
				log.warn("订单[{}]为歪马线下地推自提单，将放弃创建运单", orderInfo.getOrderKey());
				return Optional.of(new Failure(false, FailureCodeEnum.OFFLINE_PROMOTE_CREATE_DELIVERY_ERROR));
			}
		}

		if (orderInfo.isFinished()) {
			try {
				//歪马 包含sn或者封签商品的订单 即使订单完成 也需要创建运单
				if (MccConfigUtils.checkIsDHTenant(orderInfo.getOrderKey().getTenantId()) && Objects.equals(orderInfo.getOrderStatus(), OrderStatusEnum.COMPLETED.getValue())) {
					if (GrayConfigUtils.judgeIsGrayStore(orderInfo.getOrderKey().getTenantId(), orderInfo.getPoiId(), GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
						boolean isContainSnOrSealProduct = ofcSystemClient.queryOrderIsContainSnOrSealProduct(orderInfo.getOrderKey().getTenantId(), orderInfo.getPoiId(),
								orderInfo.getChannelOrderId(), orderInfo.getOrderBizType());
						if (isContainSnOrSealProduct) {
							log.info("包含sn或封签商品, 订单完成依旧要下发配送单");
							Cat.logEvent("DH_ADAPT_SEAL", "PUSH_DOWN_BOOKING_ORDER");
							return Optional.empty();
						}

						//有异常任务的订单不能自动完成配送任务
						Optional<AbnOrderDTO> abnOrderDTOOptional = tradeShippingOrderFacade.queryAbnOrderByChannelOrderId(
								orderInfo.getOrderKey().getStoreId(),
								orderInfo.getOrderBizType(),
								orderInfo.getChannelOrderId()
						);
						if (abnOrderDTOOptional.isPresent()) {
							log.info("订单有异常单, 订单完成依旧要下发配送单");
							Cat.logEvent("DH_ADAPT_ABN", "PUSH_DOWN_BOOKING_ORDER");
							return Optional.empty();
						}
					}
				}
			} catch (Exception e) {
				log.error("查询是否包含特殊商品失败", e);
				Cat.logEvent("SEAL", "QUERY_OFC_ERROR");
			}

			log.warn("订单[{}]已到终态，将放弃创建运单", orderInfo.getOrderKey());
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		} else {
			return Optional.empty();
		}
	}

	private Optional<Failure> checkOrderDeliverableForThirdDelivery(OrderInfo orderInfo) {
		if (!orderInfo.canLaunchDelivery()) {
			log.warn("订单[{}]不满足发起配送条件，发三方配送失败", orderInfo);
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		//校验订单收货人信息是否完整，是否可以发起配送
		return checkReceiver(orderInfo);
	}

	private Optional<Failure> checkOrderDeliverableForPlatformDelivery(OrderInfo orderInfo) {
		DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
		if (Objects.isNull(orderBizTypeEnum)) {
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		if (DynamicOrderBizType.YOU_ZAN_MIDDLE.equals(orderBizTypeEnum)) {
			// 兼容历史有赞平台配送逻辑
			return checkOrderDeliverableForThirdDelivery(orderInfo);
		} else if (DynamicOrderBizType.DOU_YIN.equals(orderBizTypeEnum)) {
			if (!orderInfo.canLaunchPlatformDelivery()) {
				log.warn("订单[{}]不满足发起平台配送条件，发平台配送失败", orderInfo);
				return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
			}
			//校验订单收货人信息是否完整，是否可以发起配送
			return checkReceiver(orderInfo);
		} else {
			log.warn("订单[{}]不满足发起平台配送条件，订单渠道不支持，发平台配送失败", orderInfo);
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}
	}

	private Optional<Failure> checkOrderDeliverableForManualLaunchDelivery(OrderInfo orderInfo, DeliveryPoi deliveryPoi) {
		if (!orderInfo.canManualLaunchDelivery()) {
			log.warn("订单[{}]不满足手动发起配送条件，手动发配送失败", orderInfo);
			return Optional.of(new Failure(false, FailureCodeEnum.ORDER_STATUS_ERROR));
		}

		//校验订单收货人信息是否完整，是否可以发起配送
		return checkReceiver(orderInfo);
	}

	private Optional<Failure> checkReceiver(OrderInfo orderInfo) {
		if (orderInfo.getReceiver() == null || !orderInfo.getReceiver().isDeliverable()) {
			log.warn("订单[{}]收件人信息不完整，发三方配送失败", orderInfo);
			deliveryWarnEventPublisher.postEvent(new DeliveryLaunchFailedEvent(orderInfo, "收件人信息不完整"));
			return Optional.of(new Failure(false, FailureCodeEnum.INVALID_RECEIVER_ADDRESS));
		}

		return Optional.empty();
	}

	private void sendOrderTrack(Long tenantId, String channelOrderId, Integer orderBizType, Long operatorId, boolean isSuccess, String appId) {
		OrderTrackEvent event = new OrderTrackEvent();
		event.setTrackSource(TrackSource.DELIVERY.getType());
		event.setTrackOpType(isSuccess ? TrackOpType.MANUAL_LAUNCH_DELIVERY_SUCCESS.getOpType() : TrackOpType.MANUAL_LAUNCH_DELIVERY_FAIL.getOpType());
		event.setAccountIdList(Collections.singletonList(operatorId));
		event.setOperateTime(System.currentTimeMillis());
		event.setTenantId(tenantId);
		event.setUnifyOrderId(channelOrderId);
		event.setOrderBizType(orderBizType);
		event.setAppId(appId);

		deliveryNotifyService.notifyDeliveryTrace(event);
	}

	@CatTransaction
	@MethodLog(logRequest = false, logResponse = true)
	public Optional<Failure> paoTuiLockStatusNotify(OrderInfo orderInfo) {
		DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
				orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
		Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderInfo.getOrderKey().getTenantId(), orderInfo.getWarehouseId(), orderBizType.getChannelId()) ;


		if (!canLaunchDelivery(orderInfo, opDeliveryPoi)) {
			return Optional.empty();
		}
		DeliveryPlatformEnum deliveryPlatform = opDeliveryPoi.get().getDeliveryPlatform();
		List<DeliveryPlatformEnum> lockOrderValidDeliveryPlatformEnums = Lists.newArrayList(MALT_FARM_DELIVERY_PLATFORM, DAP_DELIVERY_PLATFORM);

		if (!lockOrderValidDeliveryPlatformEnums.contains(deliveryPlatform)) {
			log.error("DeliveryStore[{}] is illegal, will give up launchDelivery", orderInfo.getOrderKey().getStoreId());
			return Optional.empty();
		}
		orderInfo.setSelfDelivery(true);
		return paoTuiLockStatusDomainService.paoTuiLockStatusNotify(orderInfo, opDeliveryPoi.get());
	}



	private boolean canLaunchDelivery(OrderInfo orderInfo, Optional<DeliveryPoi> opDeliveryPoi) {
		if (orderInfo.isFinished()) {
			log.warn("订单[{}]已到终态", orderInfo.getOrderKey());
			return false;
		}
//		//判断是否已经在配送中
//		if(orderInfo.isInDelivery() && StringUtils.isNotBlank(orderInfo.getRiderName())){
//			log.info("订单[{}]已经在配送中，因此不用发起配送", orderInfo.getOrderKey().getOrderId());
//			return false;
//		}


		if(!orderInfo.isDeliveryToHome()){
			log.info("到店自提不发配送");
			return false;
		}

		//校验订单收货人信息是否完整，是否可以发起配送
		Optional<Failure> failure = checkReceiver(orderInfo);
		if (failure.isPresent()) {
			return false;
		}

		if (!opDeliveryPoi.isPresent()) {
			log.error("DeliveryStore[{}] is not exists, will give up launchDelivery", orderInfo.getOrderKey().getStoreId());
			return false;
		}

		Optional<DeliveryOrder> deliveryOrderActive = deliveryOrderRepository.getActiveDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId());
		if(deliveryOrderActive.isPresent()){
			DeliveryChannel deliveryChannelDto = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(deliveryOrderActive.get().getDeliveryChannel());

			if(MccConfigUtils.isPaotuiLockAggDeliveryPlatform(deliveryChannelDto.getDeliveryPlatFormCode())){
				return true;
			}

		}

		//校验发单节点
		Boolean checkDeliveryLaunchPoint  = checkDeliveryLaunchPoint(orderInfo, opDeliveryPoi.get());
		if (!checkDeliveryLaunchPoint) {
			return false;
		}
		return true;
	}


	private Boolean checkDeliveryLaunchPoint(OrderInfo orderInfo, DeliveryPoi deliveryPoi) {

		ImmediateOrderDeliveryLaunchPointEnum launchPoint = deliveryPoi.getDeliveryLaunchPoint().getImmediateOrderDeliveryLaunchPointConfig().getLaunchPoint();
		if (launchPoint == ImmediateOrderDeliveryLaunchPointEnum.PICK_DONE
				&& !Objects.equals(orderInfo.getPickStatus(), com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum.PICKED.getValue())) {
			log.error("未达到门店配置的实时单触发点[{}]，暂不发起配送", launchPoint);
			return false;
		}
		return true;
	}


	private boolean isDownOrder(OrderInfo orderInfo) {
		try {
			if (orderInfo.getDownFlag() == null || orderInfo.getDownFlag() == OrderDownTypeEnum.NO_DOWN.getValue()) {
				return false;
			}
			String degradeModules = orderInfo.getDegradeModules();
			if (StringUtils.isEmpty(degradeModules)) {
				return false;
			}
			List<Integer> degradeModuleList = Arrays.stream(degradeModules.split(",")).map(Integer::valueOf).collect(Collectors.toList());
			// 获取订单补偿类型code做交集判断，存在交集则降级
			List<Integer> downOrderCodeList = MccConfigUtils.getDownOrderCompensateTypeCode();
			boolean contains = !Collections.disjoint(degradeModuleList,downOrderCodeList);
			return contains;
		} catch (Exception e) {
			Cat.logEvent("down", "down.error");
		}
		return false;
	}

	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public String queryDapStoreSettingsLink(QueryAggStoreSettingsLinkRequest request) {
		if (MccConfigUtils.getDapLinkAuthSwitch()) {
			if (!MccConfigUtils.checkIsDHTenant(request.getTenantId()) || MccConfigUtils.dapLinkAuthDHStoreGrayList(request.getPoiId())) {
				return buildDapConfigLink(request);
			}
		}
		// 降级走老的链接获取方式
		return queryDapNoAuthStoreConfigUrl(request);
	}

	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public String queryDapOrderDetailLink(QueryAggOrderDetailLinkRequest request, DeliveryOrder deliveryOrder) {
		if (MccConfigUtils.getDapLinkAuthSwitch()) {
			if (!MccConfigUtils.checkIsDHTenant(request.getTenantId()) || MccConfigUtils.dapLinkAuthDHStoreGrayList(request.getPoiId())) {
				return buildDapOrderLink(request, deliveryOrder);
			}
		}
		// 降级走老的链接获取方式
		return queryDapNoAuthOrderDetailUrl(deliveryOrder);
	}

	private String buildDapConfigLink(QueryAggStoreSettingsLinkRequest request) {
		AggDeliveryPlatformEnum platform = AggDeliveryPlatformEnum.DAP_DELIVERY;
		AggDeliveryPlatformAppConfig config = AggDeliveryPlatformAppConfigUtils.getAggAppNewConfig(platform.getCode());
		Long currentTimeMillis = System.currentTimeMillis();
		Map<String, Object> params = new HashMap<>();
		params.put("appkey", config.getAppKey());
		params.put("timestamp", String.valueOf(TimeUtil.fromMilliSeconds2Seconds(currentTimeMillis)));
		params.put("version", config.getVersion());
		params.put("linkType", LinkTypeEnum.SHOP_LINK_TYPE.getType());
		params.put("deviceType", request.getDeviceType());
		params.put("channelShopId", request.getPoiId());
		// 生成临时凭证code
		String originalStr = request.getPoiId() + currentTimeMillis + request.getOperatorAccount() + platform.name();
		String code = DigestUtils.md5Hex(originalStr).toUpperCase();
		params.put("code", code);
		// 缓存鉴权信息
		aggDeliveryLinkAuthSquirrelOperationService.save(code, request, null);
		// 生成签名
		String sign = platform.createAggDeliverySign(params, config.getAppSecret());
		params.put("sign", sign);
		return getAggLinkUrl(params, config.getConfigUrl());
	}

	private String buildDapOrderLink(QueryAggOrderDetailLinkRequest request, DeliveryOrder deliveryOrder) {
		AggDeliveryPlatformEnum platform = AggDeliveryPlatformEnum.DAP_DELIVERY;
		AggDeliveryPlatformAppConfig config = AggDeliveryPlatformAppConfigUtils.getAggAppNewConfig(platform.getCode());
		long currentTimeMillis = System.currentTimeMillis();
		Map<String, Object> params = new HashMap<>();
		params.put("appkey", config.getAppKey());
		params.put("timestamp", String.valueOf(TimeUtil.fromMilliSeconds2Seconds(currentTimeMillis)));
		params.put("version", config.getVersion());
		params.put("linkType", LinkTypeEnum.ORDER_LINK_TYPE.getType());
		params.put("deviceType", request.getDeviceType());
		params.put("channelShopId", request.getPoiId());
		// 生成临时凭证code
		String fulfillmentOrderId = deliveryOrder.getDeliveryOrderId();
		String originalStr = fulfillmentOrderId + currentTimeMillis + request.getOperatorAccount() + platform.name();
		String code = DigestUtils.md5Hex(originalStr).toUpperCase();
		params.put("code", code);
		// 缓存鉴权信息
		aggDeliveryLinkAuthSquirrelOperationService.save(code, request, fulfillmentOrderId);
		// 生成签名
		String sign = platform.createAggDeliverySign(params, config.getAppSecret());
		params.put("sign", sign);
		return getAggLinkUrl(params, config.getRedirectUrl());
	}

	private String getAggLinkUrl(Map<String, Object> params, String baseUrl) {
		try {
			if (StringUtils.isBlank(baseUrl)) {
				return null;
			}
			if (MapUtils.isEmpty(params)) {
				return baseUrl;
			}
			return Joiner.on("?").join(baseUrl, Joiner.on("&").withKeyValueSeparator("=").join(params));
		} catch (Exception e) {
			log.error("getAggLinkUrl error, params:{}, baseUrl:{}", params, baseUrl, e);
			return null;
		}
	}

	private String queryDapNoAuthOrderDetailUrl(DeliveryOrder deliveryOrder) {
		try {
			GetAggDeliveryLinkRequest request = new GetAggDeliveryLinkRequest();
			request.setShopId(deliveryOrder.getStoreId());
			request.setIds(deliveryOrder.getDeliveryOrderId());
			request.setAggDeliveryPlatformId(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
			request.setType(LinkTypeEnum.ORDER_LINK_TYPE.getType());
			GetAggDeliveryLinkResponse res = dapChannelAggDeliveryThriftService.getAggDeliveryLink(request);
			if (res != null && res.getCode() == FailureCodeEnum.SUCCESS.getCode()) {
				if (CollectionUtils.isNotEmpty(res.getLinkDetailList())) {
					return res.getLinkDetailList().get(0).getUrl();
				}
			}
		} catch (TException e) {
			log.error("queryDapOrderDetailUrl error", e);
		}
		return null;
	}

	private String queryDapNoAuthStoreConfigUrl(QueryAggStoreSettingsLinkRequest request) {
		try {
			GetAggDeliveryLinkRequest linkRequest = new GetAggDeliveryLinkRequest();
			linkRequest.setShopId(request.getPoiId());
			linkRequest.setIds(String.valueOf(request.getPoiId()));
			linkRequest.setAggDeliveryPlatformId(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
			linkRequest.setType(LinkTypeEnum.SHOP_LINK_TYPE.getType());
			linkRequest.setToken(request.getEToken());
			linkRequest.setDeviceType(request.getDeviceType());
			GetAggDeliveryLinkResponse res = dapChannelAggDeliveryThriftService.getAggDeliveryLink(linkRequest);
			if (res != null && res.getCode() == FailureCodeEnum.SUCCESS.getCode()) {
				if (CollectionUtils.isNotEmpty(res.getLinkDetailList())) {
					return res.getLinkDetailList().get(0).getUrl();
				}
			}
		} catch (TException e) {
			log.error("queryDapStoreConfigUrl error", e);
		}
		return null;
	}

	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public String queryMaltFarmStoreSettingsLink(QueryAggStoreSettingsLinkRequest request) {
		if (MccConfigUtils.getMaltLinkAuthSwitch()) {
			return buildMaltConfigLink(request);
		}
		return queryMaltNoAuthStoreConfigUrl(request);
	}

	@CatTransaction
	// @MethodLog(logRequest = false, logResponse = true)
	public String queryMaltFarmOrderDetailLink(QueryAggOrderDetailLinkRequest request, DeliveryOrder deliveryOrder) {
		if (MccConfigUtils.getMaltLinkAuthSwitch()) {
			return buildMaltOrderLink(request, deliveryOrder);
		}
		return queryMaltNoAuthOrderDetailUrl(deliveryOrder);
	}

	private String buildMaltConfigLink(QueryAggStoreSettingsLinkRequest request) {
		AggDeliveryPlatformEnum platform = AggDeliveryPlatformEnum.MALT_FARM;
		AggDeliveryPlatformAppConfig config = AggDeliveryPlatformAppConfigUtils.getAggAppNewConfig(platform.getCode());
		Long currentTimeMillis = System.currentTimeMillis();
		Map<String, Object> params = new HashMap<>();
		params.put("route_module", "setting");
		params.put("show_navbar", "false");
		params.put("app_key", config.getAppKey());
		params.put("shop_id", String.valueOf(request.getPoiId()));
		// 生成临时凭证code
		String originalStr = request.getPoiId() + currentTimeMillis + request.getOperatorAccount() + platform.name();
		String code = DigestUtils.md5Hex(originalStr).toUpperCase();
		params.put("code", code);
		// 缓存鉴权信息
		aggDeliveryLinkAuthSquirrelOperationService.save(code, request, null);
		// 生成签名
		String sign = platform.createAggDeliverySign(params, config.getAppSecret());
		params.put("sign", sign);
		return getAggLinkUrl(params, config.getConfigUrl());
	}

	private String buildMaltOrderLink(QueryAggOrderDetailLinkRequest request, DeliveryOrder deliveryOrder) {
		AggDeliveryPlatformEnum platform = AggDeliveryPlatformEnum.MALT_FARM;
		AggDeliveryPlatformAppConfig config = AggDeliveryPlatformAppConfigUtils.getAggAppNewConfig(platform.getCode());
		long currentTimeMillis = System.currentTimeMillis();
		Map<String, Object> params = new HashMap<>();
		params.put("route_module", "orderDetail");
		params.put("show_navbar", "false");
		params.put("app_key", config.getAppKey());
		params.put("shop_id", String.valueOf(request.getPoiId()));
		// 生成临时凭证code
		String fulfillmentOrderId = deliveryOrder.getDeliveryOrderId();
		String originalStr = fulfillmentOrderId + currentTimeMillis + request.getOperatorAccount() + platform.name();
		String code = DigestUtils.md5Hex(originalStr).toUpperCase();
		params.put("origin_id", fulfillmentOrderId);
		params.put("code", code);
		// 缓存鉴权信息
		aggDeliveryLinkAuthSquirrelOperationService.save(code, request, fulfillmentOrderId);
		// 生成签名
		String sign = platform.createAggDeliverySign(params, config.getAppSecret());
		params.put("sign", sign);
		return getAggLinkUrl(params, config.getRedirectUrl());
	}

	private String queryMaltNoAuthStoreConfigUrl(QueryAggStoreSettingsLinkRequest request) {
		AggDeliveryPlatformEnum platform = AggDeliveryPlatformEnum.MALT_FARM;
		AggDeliveryPlatformAppConfig config = AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(platform.getCode());
		String url = MessageFormat.format(config.getConfigUrl(), config.getAppKey(), String.valueOf(request.getPoiId()));
		UrlUtils.UrlInfo urlInfo = platform.getUrlInfo(config, url);
		return UrlUtils.buildUrl4UrlInfo(urlInfo);
	}

	private String queryMaltNoAuthOrderDetailUrl(DeliveryOrder deliveryOrder) {
		AggDeliveryPlatformEnum platform = AggDeliveryPlatformEnum.MALT_FARM;
		AggDeliveryPlatformAppConfig config = AggDeliveryPlatformAppConfigUtils.getAggDeliveryPlatformAppConfig(platform.getCode());
		String url = MessageFormat.format(config.getRedirectUrl(), config.getAppKey(),
				deliveryOrder.getDeliveryOrderId(), String.valueOf(deliveryOrder.getStoreId()));
		UrlUtils.UrlInfo urlInfo = platform.getUrlInfo(config, url);
		return UrlUtils.buildUrl4UrlInfo(urlInfo);
	}
}
