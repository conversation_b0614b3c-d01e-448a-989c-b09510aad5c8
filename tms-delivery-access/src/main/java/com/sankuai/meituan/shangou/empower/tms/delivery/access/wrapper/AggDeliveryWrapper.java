package com.sankuai.meituan.shangou.empower.tms.delivery.access.wrapper;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DapCategoryEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.client.tenant.TenantSystemClient;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024-11-11
 */
@Slf4j
@Component
public class AggDeliveryWrapper {

    @Autowired
    private TenantSystemClient tenantSystemClient;

    private static final String DEFAULT_SMKT_CATEGORY_CODE = "g2_smkt_shop";

    @MethodLog(logRequest = true, logResponse = true)
    public String getCategoryCode(Long tenantId) {
        boolean isDHTenant = MccConfigUtils.getDHTenantIdList().contains(String.valueOf(tenantId));
        Map<String, List<String>> map = MccConfigUtils.getDeliveryPlatformCategoryMapping();
        Function<Boolean, String> defaultfValueFunction = flag -> {
            if (flag) {
                return map.get(String.valueOf(DapCategoryEnum.DRINKS.getCode())).get(0);
            } else {
                // 商超默认值
                return DEFAULT_SMKT_CATEGORY_CODE;
            }
        };
        try {
            List<String> categories = tenantSystemClient.queryTenantCategory(tenantId);

            // 无主营品类
            if (categories.isEmpty()) {
                return defaultfValueFunction.apply(isDHTenant);
            }
            // 有主营品类
            for (List<String> codes : map.values()) {
                if (codes.containsAll(categories)) {
                    return categories.get(0);
                }
            }
            // 主营类目都在lion映射里
            List<String> allMapping = map.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            if (allMapping.containsAll(categories)) {
                if (isDHTenant) {
                    return map.get(String.valueOf(DapCategoryEnum.DRINKS.getCode())).get(0);
                } else {
                    return map.get(String.valueOf(DapCategoryEnum.FLOWERS.getCode())).get(0);
                }
            }
            // 有主营类目不在lion配置中的
            return defaultfValueFunction.apply(isDHTenant);
        } catch (Exception e) {
            log.info("AggDeliveryWrapper.getCategoryCode error: ", e);
            return defaultfValueFunction.apply(isDHTenant);
        }
    }
}
