package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.monitor;

import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryChangeNotifyService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryTimeOutCheckTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.push.PushClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.DeliveryWarnClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.DaySeqNumUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStrategyEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryPoiRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Failure;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SelfBuiltDeliveryPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.SequentialPollingStrategyConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.platform.selfbuilt.ThirdDeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RiderDeliveryOrderRepository;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 配送监控领域服务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/7
 */
@Slf4j
@Service
@Order(Ordered.HIGHEST_PRECEDENCE)
public class DeliveryMonitorDomainService {

	@Resource
	private OrderSystemClient orderSystemClient;
	@Resource
	private DeliveryChangeNotifyService deliveryChangeNotifyService;
	@Resource
	private PushClient pushClient;
	@Resource
	private DeliveryOrderRepository deliveryOrderRepository;
	@Resource
	private RiderDeliveryOrderRepository riderDeliveryOrderRepository;
	@Resource
	private DeliveryWarnClient deliveryWarnClient;
	@Resource
	private DeliveryPoiRepository deliveryPoiRepository;

	private static final List<DeliveryStatusEnum> MERCHANT_SELF_DELIVERY_CHECK_RIDER_ASSIGNED_VALID_STATUS =
			Lists.newArrayList(DeliveryStatusEnum.RIDER_ASSIGNED, DeliveryStatusEnum.RIDER_TAKEN_GOODS, DeliveryStatusEnum.DELIVERY_DONE);
	private static final List<DeliveryStatusEnum> MERCHANT_SELF_DELIVERY_CHECK_RIDER_TAKEN_GOODS_VALID_STATUS =
			Lists.newArrayList(DeliveryStatusEnum.RIDER_TAKEN_GOODS, DeliveryStatusEnum.DELIVERY_DONE);

	/**
	 * 触发配送监控
	 */
	@CatTransaction
	public void triggerDeliveryMonitoring(DeliveryOrder deliveryOrder) {
		try {
			doTriggerDeliveryMonitoring(deliveryOrder);
		} catch (Exception e) {
			log.error("触发配送监控失败", e);
		}
	}

	/**
	 * 触发配送超时监控
	 */
	@CatTransaction
	public Optional<Failure> triggerDeliveryTimeOutMonitoring(OrderInfo orderInfo, @Nullable DeliveryTimeOutCheckTypeEnum deliveryTimeOutCheckTypeEnum) {
		//校验订单是否已取消
		if (OrderStatusEnum.CANCELED.getValue() == orderInfo.getOrderStatus() || OrderStatusEnum.COMPLETED.getValue() == orderInfo.getOrderStatus()) {
			log.info("订单已经取消或已经完成，无需进行超时检查");
			return Optional.empty();
		}

		if (deliveryTimeOutCheckTypeEnum == DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_RIDER_ASSIGNED) {
			merchantSelfDeliveryCheckRiderAssigned(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		} else if (deliveryTimeOutCheckTypeEnum == DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_RIDER_TAKEN_GOODS) {
			merchantSelfDeliveryCheckRiderTakenGoods(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		} else if (deliveryTimeOutCheckTypeEnum == DeliveryTimeOutCheckTypeEnum.MERCHANT_SELF_DELIVERY_CHECK_DELIVERY_WILL_TIMEOUT) {
			merchantSelfDeliveryRiderDeliveryTimeoutWarning(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		} else if (deliveryTimeOutCheckTypeEnum == DeliveryTimeOutCheckTypeEnum.CHECK_DELIVERY_DONE_4_RIDER) {
			checkDeliveryDone4Rider(orderInfo);
		} else if(deliveryTimeOutCheckTypeEnum == DeliveryTimeOutCheckTypeEnum.CHECK_DELIVERY_DONE_4_CUS) {
			checkDeliveryDone4Customer(orderInfo);
		} else if (deliveryTimeOutCheckTypeEnum == DeliveryTimeOutCheckTypeEnum.CHECK_DELIVERY_DONE) {     //为了平滑上线，后期可以去掉
			checkDeliveryDone(orderInfo);
		}

		return Optional.empty();
	}


	private void checkDeliveryDone(OrderInfo orderInfo) {
		//查询运单
		Optional<DeliveryOrder> opDeliveryOrder = deliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		//没有运单 或者 没有到配送完成状态，触发告警
		if (!opDeliveryOrder.isPresent() || opDeliveryOrder.get().getStatus() != DeliveryStatusEnum.DELIVERY_DONE) {
			deliveryWarnClient.pushDeliveryDoneTimeOut(orderInfo, opDeliveryOrder.orElse(null));
			deliveryWarnClient.notifyOrderEstimatedDeliveryTimeOut(orderInfo);
		}
	}

	private void checkDeliveryDone4Rider(OrderInfo orderInfo) {
		//查询运单
		Optional<DeliveryOrder> opDeliveryOrder = deliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		//没有运单 或者 没有到配送完成状态，触发告警
		if (!opDeliveryOrder.isPresent() || opDeliveryOrder.get().getStatus() != DeliveryStatusEnum.DELIVERY_DONE) {
			Cat.logEvent("DH_DELIVERY_TIMEOUT_MONITORING","PUSH_TIMEOUT_MESSAGE");
			deliveryWarnClient.pushDeliveryDoneTimeOut(orderInfo, opDeliveryOrder.orElse(null));
		}
	}

	private void checkDeliveryDone4Customer(OrderInfo orderInfo) {
		//查询运单
		Optional<DeliveryOrder> opDeliveryOrder = deliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderInfo.getOrderKey().getOrderId(),orderInfo.getOrderKey().getTenantId(),orderInfo.getWarehouseId());
		//没有运单 或者 没有到配送完成状态，触发告警
		if (!opDeliveryOrder.isPresent() || opDeliveryOrder.get().getStatus() != DeliveryStatusEnum.DELIVERY_DONE) {
			Cat.logEvent("DH_DELIVERY_TIMEOUT_MONITORING","NOTIFY_EXTERNAL_SERVICE");
			deliveryWarnClient.notifyOrderEstimatedDeliveryTimeOut(orderInfo);
		}
	}

	private void merchantSelfDeliveryCheckRiderTakenGoods(Long orderId,Long tenantId,Long storeId) {
		Optional<RiderDeliveryOrder> opDeliveryOrder = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			opDeliveryOrder = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithTenant(orderId,tenantId,storeId);
		}else {
			opDeliveryOrder = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderId);
		}

		if (!opDeliveryOrder.isPresent()) {
			log.warn("delivery order not exist. orderId: {}", orderId);
			return;
		}

		RiderDeliveryOrder deliveryOrder = opDeliveryOrder.get();
		if (DeliveryStatusEnum.RIDER_ASSIGNED == opDeliveryOrder.get().getStatus()) {
			if (GrayConfigUtils.judgeIsGrayStore(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), GrayKeyEnum.PICK_DELIVERY_SPLIT.getGrayKey())
					&& Objects.nonNull(deliveryOrder.getRiderDeliveryExtInfo())
					&& Objects.equals(deliveryOrder.getRiderDeliveryExtInfo().getPickDeliverySplitTag(), true)) {
				log.info("拣配分离模式下, 配送不发送拣货超时提醒");
				return;
			}
			pushClient.pushMerchantSelfRiderTakenGoodsTimeout(opDeliveryOrder.get());
		}
	}

	private void merchantSelfDeliveryCheckRiderAssigned(Long orderId,Long tenantId,Long storeId) {
		Optional<RiderDeliveryOrder> opDeliveryOrder = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			opDeliveryOrder = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithTenant(orderId,tenantId,storeId);
		}else {
			opDeliveryOrder = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderId);
		}

		if (!opDeliveryOrder.isPresent()) {
			log.warn("delivery order not exist. orderId: {}", orderId);
			return;
		}
		if (DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER == opDeliveryOrder.get().getStatus()) {
			pushClient.pushMerchantSelfRiderAssignTimeout(opDeliveryOrder.get());
		}
	}

	private void merchantSelfDeliveryRiderDeliveryTimeoutWarning(Long orderId,Long tenantId,Long storeId) {
		Optional<RiderDeliveryOrder> opDeliveryOrder = Optional.empty();
		if(MccConfigUtils.getDeliveryQueryTenantSwitch(tenantId)){
			opDeliveryOrder = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMasterWithTenant(orderId,tenantId,storeId);
		}else {
			opDeliveryOrder = riderDeliveryOrderRepository.getCurrentDeliveryOrderForceMaster(orderId);
		}

		if (!opDeliveryOrder.isPresent()) {
			log.warn("delivery order not exist. orderId: {}", orderId);
			return;
		}
		if (DeliveryStatusEnum.DELIVERY_DONE != opDeliveryOrder.get().getStatus()) {
			pushClient.pushMerchantSelfRiderDeliveryTimeoutWarning(opDeliveryOrder.get());
		}
	}

	private void doTriggerDeliveryMonitoring(DeliveryOrder deliveryOrder) {
		if(deliveryOrder.getDeliveryChannel() == DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()){
			log.info("商家自配送暂时无相关监控诉求，放弃触发监控");
			return;
		}

		Result<OrderInfo> orderInfoQueryResult = orderSystemClient.getOrderInfo(deliveryOrder.getOrderKey(), false);
		if (orderInfoQueryResult.isFail()) {
			log.error("Order[{}] query failed, will give up trigger monitoring", deliveryOrder.getOrderKey());
			return;
		}

		//订单已经结束，无需触发监控
		if (orderInfoQueryResult.getInfo().isFinished()) {
			log.info("Order[{}] is under status[{}], will give up trigger monitoring", deliveryOrder.getOrderKey(), orderInfoQueryResult.getInfo().getOrderStatus());
			return;
		}

		if (deliveryOrder.getExceptionType() == DeliveryExceptionTypeEnum.NO_EXCEPTION) {

			if (deliveryOrder.getStatus() == DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER) {
				tryTriggerRiderAssignTimeOutCheck(deliveryOrder, orderInfoQueryResult.getInfo());
			}

		} else {
			triggerExceptionPush(deliveryOrder, orderInfoQueryResult);
		}
	}

	private void triggerExceptionPush(DeliveryOrder deliveryOrder, Result<OrderInfo> orderInfoQueryResult) {
		String daySeq = Optional.ofNullable(orderInfoQueryResult.getInfo())
				.map(orderInfo -> DaySeqNumUtil.getDaySeqNumWithoutDHTenant(orderInfo.getDaySeq(), orderInfo.getDaySeqNum(), orderInfo.getOrderKey().getTenantId()))
				.orElse("0");
		DeliveryExceptionCodeEnum deliveryExceptionCodeEnum = DeliveryExceptionCodeEnum.enumOf(
				Optional.ofNullable(deliveryOrder.getDeliveryExceptionCode()).orElse(0));
		switch (deliveryExceptionCodeEnum){
			case DELIVERY_STATUS_ROLLBACK:
				pushClient.pushDeliveryStatusRollbackException(deliveryOrder, daySeq);
				return;
			case ESTIMATED_TIME_OUT:
				// 配送状态变更、整单超时异常推送需要过滤、避免用户频繁收到推送信息
				return;
			default:
				pushClient.pushDeliveryException(deliveryOrder);
				break;
		}
	}

	/**
	 * 尝试发起骑手接单超时检查
	 */
	private void tryTriggerRiderAssignTimeOutCheck(DeliveryOrder deliveryOrder, OrderInfo orderInfo) {
		DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel());
		if (Objects.isNull(deliveryChannelEnum)) {
			log.warn("tryTriggerRiderAssignTimeOutCheck, deliveryChannelEnum is null");
			return;
		}

		switch (deliveryChannelEnum) {
			case ORDER_PLATFORM_DELIVERY:
				log.info("发起订单平台配送-骑手接单超时检查, deliveryId={}", deliveryOrder.getId());
				tryTriggerRiderAssignTimeOutCheckForOrderPlatformDelivery(deliveryOrder, orderInfo);
				return;
			case HAI_KUI_DELIVERY:
			case FENG_NIAO_DELIVERY:
				log.info("发起三方配送-骑手接单超时检查, deliveryId={}", deliveryOrder.getId());
				tryTriggerRiderAssignTimeOutCheckForThirdPartyDelivery(deliveryOrder);
				return;
			default:
				log.info("无需发起骑手接单超时检查, deliveryId={}", deliveryOrder.getId());
		}
	}

	/**
	 * 尝试触发骑手接单超时检查(订单平台配送)
	 * 默认15分钟，15分钟无骑手接单，向商家提醒异常，商家可以转自配送
	 */
	private void tryTriggerRiderAssignTimeOutCheckForOrderPlatformDelivery(DeliveryOrder deliveryOrder, OrderInfo orderInfo) {
		//预约单不触发
		if (orderInfo.isBookingOrder()) {
			return;
		}

		DistributeTypeEnum originalDistributeType = Optional.ofNullable(orderInfo.getOriginalDistributeType())
				.map(DistributeTypeEnum::enumOf)
				.orElse(DistributeTypeEnum.UN_KNOWN);

		//美团众包(跑腿)不支持转商家自配送，不触发
		if (DynamicOrderBizType.MEITUAN_WAIMAI.getValue() == deliveryOrder.getOrderBizType() && DistributeTypeEnum.ZONG_BAO == originalDistributeType) {
			return;
		}

		//仅支持的原配送类型，才触发监控
		List<DistributeTypeEnum> supportedOriginalDistributeType = ImmutableList.of(
				DistributeTypeEnum.ZHUAN_SONG, DistributeTypeEnum.KUAI_SONG, DistributeTypeEnum.COMBO, DistributeTypeEnum.ZONG_BAO
		);

		if (supportedOriginalDistributeType.contains(originalDistributeType)) {
			deliveryChangeNotifyService.notifyRiderAssignTimeOutCheckTriggered(
					deliveryOrder,
					ConfigUtilAdapter.getInt("delivery_exception_timeout_minutes", 15)
			);
		}
	}

	/**
	 * 尝试触发骑手接单超时检查(三方配送)
	 * 如果商家是配置的按顺序尝试多个配送渠道，在配置的超时时间结束时，如果仍无骑手接单，将取消当前运单，尝试下一个渠道
	 */
	private void tryTriggerRiderAssignTimeOutCheckForThirdPartyDelivery(DeliveryOrder deliveryOrder) {

		Optional<DeliveryPoi> opDeliveryPoi = Optional.empty();
		if(MccConfigUtils.getDHTenantIdList().contains(deliveryOrder.getTenantId()+"")){
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoi(deliveryOrder.getTenantId(), deliveryOrder.getStoreId());
		}else {
			DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(
					DynamicOrderBizType.findOf(deliveryOrder.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
			opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),
					orderBizType.getChannelId());
		}
		if (!opDeliveryPoi.isPresent() || DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM != opDeliveryPoi.get().getDeliveryPlatform()) {
			log.error("DeliveryStore[{}] is illegal, will give up trigger monitoring", deliveryOrder.getStoreId());
			return;
		}
		SelfBuiltDeliveryPoi deliveryPoi = (SelfBuiltDeliveryPoi) opDeliveryPoi.get();
		if (!deliveryPoi.canLaunchDelivery()) {
			log.warn("门店[{}]在自建平台没有可用的配送渠道，将放弃触发骑手接单超时监控", deliveryPoi.getStoreId());
			return;
		}

		if (deliveryPoi.getDeliveryStrategy() == DeliveryStrategyEnum.SEQUENTIAL_POLLING) {
			SequentialPollingStrategyConfig strategyConfig = (SequentialPollingStrategyConfig) deliveryPoi.getDeliveryStrategyConfig();
			if (strategyConfig.hasNextAvailableDeliveryChannel(ThirdDeliveryChannelEnum.valueOf(deliveryOrder.getDeliveryChannel()))) {
				deliveryChangeNotifyService.notifyRiderAssignTimeOutCheckTriggered(
						deliveryOrder,
						strategyConfig.getTimeoutForShiftDeliveryChannelInMinutes());
			}
		}
	}
}
