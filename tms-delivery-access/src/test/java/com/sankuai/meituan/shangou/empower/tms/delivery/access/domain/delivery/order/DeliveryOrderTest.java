package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery.order;

import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.platform.DeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.SpringContextUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
@RunWith(MockitoJUnitRunner.class)
public class DeliveryOrderTest {

	@Mock
	private ApplicationContext applicationContext;
	@Mock
	private DeliveryPlatformClient deliveryPlatformClient;
	@Mock
	private DeliveryChangeNotifyService deliveryChangeNotifyService;

	@Before
	public void setUp() throws Exception {
		SpringContextUtils springContextUtils = new SpringContextUtils();
		springContextUtils.setApplicationContext(applicationContext);

		when(applicationContext.getBean(DeliveryPlatformClient.class)).thenReturn(deliveryPlatformClient);
		when(applicationContext.getBean(DeliveryChangeNotifyService.class)).thenReturn(deliveryChangeNotifyService);
	}

	@Test
	public void should_cancel_do_nothing_with_already_delivery_done() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_DONE);
		assertFalse(deliveryOrder.cancel().isPresent());

		verify(deliveryPlatformClient, never()).cancelDelivery(deliveryOrder);
	}

	@Test
	public void should_cancel_do_nothing_with_already_delivery_rejected() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_REJECTED);
		assertFalse(deliveryOrder.cancel().isPresent());

		verify(deliveryPlatformClient, never()).cancelDelivery(deliveryOrder);
	}

	@Test
	public void should_cancel_do_nothing_with_already_delivery_failed() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_FAILED);
		assertFalse(deliveryOrder.cancel().isPresent());

		verify(deliveryPlatformClient, never()).cancelDelivery(deliveryOrder);
	}

	@Test
	public void should_cancel_do_nothing_with_already_delivery_canceled() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_CANCELLED);
		assertFalse(deliveryOrder.cancel().isPresent());

		verify(deliveryPlatformClient, never()).cancelDelivery(deliveryOrder);
	}

	@Test
	public void should_cancel_success_with_third_party_delivery() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_LAUNCHED);

		when(deliveryPlatformClient.cancelDelivery(deliveryOrder)).thenReturn(Optional.empty());

		assertFalse(deliveryOrder.cancel().isPresent());

		verify(deliveryPlatformClient).cancelDelivery(deliveryOrder);
	}

	@Test
	public void should_cancel_success_with_not_third_party_delivery() {
//		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.MERCHANT_DELIVERY, DeliveryStatusEnum.DELIVERY_LAUNCHED);
//
//		assertFalse(deliveryOrder.cancel().isPresent());
//		assertEquals(DeliveryStatusEnum.DELIVERY_CANCELLED, deliveryOrder.getStatus());
//		assertFalse(deliveryOrder.isActive());
//		assertEquals(DeliveryExceptionTypeEnum.NO_EXCEPTION, deliveryOrder.getExceptionType());
//		assertEquals("", deliveryOrder.getExceptionDescription());
//
//		verify(deliveryPlatformClient, never()).cancelDelivery(deliveryOrder);
//		verify(deliveryChangeNotifyService).notifyDeliveryChangeLog(eq(deliveryOrder.getId()), eq(DeliveryEventEnum.DELIVERY_CANCEL), eq(null), any());
	}

	@Test
	public void should_onEvent_success_with_delivery_done() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.MERCHANT_DELIVERY, DeliveryStatusEnum.DELIVERY_LAUNCHED);

		LocalDateTime changeTime = LocalDateTime.now();
		deliveryOrder.onEvent(DeliveryEventEnum.RIDER_FINISH_DELIVERY, changeTime);

		assertEquals(DeliveryStatusEnum.DELIVERY_DONE, deliveryOrder.getStatus());
		assertEquals(changeTime, deliveryOrder.getLastEventTime());
		assertEquals(changeTime, deliveryOrder.getDeliveryDoneTime());
		assertTrue(deliveryOrder.isActive());

		verify(deliveryChangeNotifyService).notifyDeliveryChangeLog(deliveryOrder.getId(), DeliveryEventEnum.RIDER_FINISH_DELIVERY, null, changeTime);
	}

	@Test
	public void should_onEvent_success_with_non_done_final_status() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.MERCHANT_DELIVERY, DeliveryStatusEnum.DELIVERY_LAUNCHED);

		LocalDateTime changeTime = LocalDateTime.now();
		deliveryOrder.onEvent(DeliveryEventEnum.DELIVERY_FAIL, changeTime);

		assertEquals(DeliveryStatusEnum.DELIVERY_FAILED, deliveryOrder.getStatus());
		assertEquals(changeTime, deliveryOrder.getLastEventTime());
		assertEquals(TimeUtil.getEpochTime(), deliveryOrder.getDeliveryDoneTime());
		assertFalse(deliveryOrder.isActive());

		verify(deliveryChangeNotifyService).notifyDeliveryChangeLog(deliveryOrder.getId(), DeliveryEventEnum.DELIVERY_FAIL, null, changeTime);
	}

	@Test
	public void should_onException_success_with_NO_EXCEPTION() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_LAUNCHED);

		LocalDateTime exceptionTime = LocalDateTime.now();
		deliveryOrder.onException(new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.NO_EXCEPTION, StringUtils.EMPTY), exceptionTime);

		assertEquals(DeliveryExceptionTypeEnum.NO_EXCEPTION, deliveryOrder.getExceptionType());
		assertEquals("", deliveryOrder.getExceptionDescription());
		assertEquals(exceptionTime, deliveryOrder.getLastEventTime());

		verify(deliveryChangeNotifyService, never()).notifyDeliveryChangeLog(any(), any(), any(), any());
	}

	@Test
	public void should_onException_success_with_exception() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.DELIVERY_LAUNCHED);
		LocalDateTime exceptionTime = LocalDateTime.now();
		DeliveryExceptionInfo exceptionInfo = new DeliveryExceptionInfo(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, "运力不足");

		deliveryOrder.onException(exceptionInfo, exceptionTime);

		assertEquals(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_RIDER, deliveryOrder.getExceptionType());
		assertEquals("运力不足", deliveryOrder.getExceptionDescription());
		assertEquals(exceptionTime, deliveryOrder.getLastEventTime());

		verify(deliveryChangeNotifyService).notifyDeliveryChangeLog(deliveryOrder.getId(), DeliveryEventEnum.DELIVERY_EXCEPTION, exceptionInfo, exceptionTime);
	}

	@Test
	public void should_onRiderChange_success_with_rider_not_changed() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.RIDER_TAKEN_GOODS);

		deliveryOrder.onRiderChange(new Rider("original_rider_name", "original_rider_phone", "original_rider_phone_token"),
				LocalDateTime.now());

		assertEquals("original_rider_name", deliveryOrder.getRiderInfo().getRiderName());
		assertEquals("original_rider_phone", deliveryOrder.getRiderInfo().getRiderPhone());
		assertEquals(TimeUtil.getEpochTime(), deliveryOrder.getLastEventTime());
	}

	@Test
	public void should_onRiderChange_success_with_rider_changed() {
		DeliveryOrder deliveryOrder = buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.RIDER_TAKEN_GOODS);

		LocalDateTime changeTime = LocalDateTime.now();
		deliveryOrder.onRiderChange(new Rider("new_rider_name", "new_rider_phone", "new_rider_phone_token"), changeTime);

		assertEquals("new_rider_name", deliveryOrder.getRiderInfo().getRiderName());
		assertEquals("new_rider_phone", deliveryOrder.getRiderInfo().getRiderPhone());
		assertEquals(changeTime, deliveryOrder.getLastEventTime());
	}

	@Test
	public void should_canTurnToSelfDelivery_success() {
		assertFalse(buildDeliveryOrder(DeliveryChannelEnum.FENG_NIAO_DELIVERY, DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER).canTurnToSelfDelivery());
		assertFalse(buildDeliveryOrder(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY, DeliveryStatusEnum.RIDER_TAKEN_GOODS).canTurnToSelfDelivery());
		assertTrue(buildDeliveryOrder(DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY, DeliveryStatusEnum.DELIVERY_REJECTED).canTurnToSelfDelivery());
	}

	private DeliveryOrder buildDeliveryOrder(DeliveryChannelEnum deliveryChannel, DeliveryStatusEnum status) {
		return new DeliveryOrder(
				99L,
				1L,
				2L,
				new OrderKey(1L, 2L, 3L),
				"channel_order_id",
				30,
				101,
				1,
				"1-01",
				false,
				new Receiver(
						"receiver_name",
						"receiver_phone",
						"receiver_privacy_phone",
						new Address("address_detail", CoordinateTypeEnum.MARS, new CoordinatePoint("1.1", "2.2"))),
				LocalDateTime.of(2020, 12, 12, 12, 12, 12),
				LocalDateTime.of(2020, 12, 12, 12, 12, 12),
				deliveryChannel.getCode(),
				"channel_delivery_id",
				0,
				"1",
				status,
				DeliveryExceptionTypeEnum.NO_EXCEPTION,
				StringUtils.EMPTY,
				TimeUtil.getEpochTime(),
				new Rider("original_rider_name", "original_rider_phone", "original_rider_phone_token"),
				0L,
				TimeUtil.getEpochTime(),
				0,
				BigDecimal.ONE,
				99L,
				LocalDateTime.now(),LocalDateTime.now(),0,new BigDecimal(1.0),1,null, 10,false, null,null,null,null,null,null, null, null,null,null,
				null,null,null,null,null,null,null,null, null,null,null,null,null,null
		);
	}
}
