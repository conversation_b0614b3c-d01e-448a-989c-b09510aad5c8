package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery.order;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.CoordinateTypeEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import org.junit.Test;

import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/23
 */
public class OrderInfoTest {

	@Test
	public void should_isDeliverable_success() {
		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.COMPLETED.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).canLaunchDelivery());
		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.CANCELED.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).canLaunchDelivery());

		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.SELF_CASH.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).canLaunchDelivery());
		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_OFFLINE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).canLaunchDelivery());

		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), false, DistributeMethodEnum.HOME_DELIVERY.getValue()).canLaunchDelivery());
		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.STORE_DELIVERY.getValue()).canLaunchDelivery());
		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.UN_KNOWN.getValue()).canLaunchDelivery());

		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE.getValue(), true, DistributeMethodEnum.STORE_DELIVERY.getValue()).canLaunchDelivery());
		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE.getValue(), true, DistributeMethodEnum.UN_KNOWN.getValue()).canLaunchDelivery());

		assertTrue(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).canLaunchDelivery());
	}

	@Test
	public void should_isFinished_success() {
		assertTrue(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.COMPLETED.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).isFinished());
		assertTrue(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.CANCELED.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).isFinished());

		assertFalse(buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).isFinished());
	}

	@Test
	public void should_getShopSequence_success() {
		assertEquals("美#4", buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).getShopSequence());
		assertEquals("京#4", buildOrderInfo(DynamicOrderBizType.JING_DONG.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).getShopSequence());
		assertEquals("饿#4", buildOrderInfo(DynamicOrderBizType.ELE_ME.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).getShopSequence());
		assertEquals("4", buildOrderInfo(DynamicOrderBizType.SELF_PLATFORM.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue()).getShopSequence());
	}

	@Test
	public void should_getGoodsTotalWeight_success() {
		OrderInfo orderInfo = buildOrderInfo(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), true, DistributeMethodEnum.HOME_DELIVERY.getValue());
		assertEquals(1000, orderInfo.getGoodsTotalWeight());

		orderInfo.getGoodsList().add(new OrderInfo.GoodsInfo("sku2", "sku_name2", 5, 300, "份", 500));
		assertEquals(2500, orderInfo.getGoodsTotalWeight());
	}

	private OrderInfo buildOrderInfo(int orderBizType, int orderStatus, int orderSource, boolean isSelfDelivery, int deliveryMethod) {
		return new OrderInfo(
				new OrderKey(1L, 2L, 3L),
				"channel_order_id",
				4L,
				"4-01",
				orderBizType,
				orderSource,
				orderStatus,
				isSelfDelivery,
				deliveryMethod,
				new Receiver("receiver_name",
						"receiver_phone",
						"receiver_privacy_phone",
						new Address("address_detail", CoordinateTypeEnum.MARS, new CoordinatePoint("1.1", "2.2"))
				),
				LocalDateTime.now().plusMinutes(30L),
				LocalDateTime.now().plusMinutes(30L),
				false,
				1,
				1000,
				Lists.newArrayList(
						new OrderInfo.GoodsInfo("sku1", "sku_name1", 2, 300, "份", 0)
				),
				"comments",
				"invoice_title",
				900,
				"123456789",
				false,
				"riderName",
				"riderPhone",
				0,
				LocalDateTime.now(),
				LocalDateTime.now(),
				LocalDateTime.now(), null, null, null, null, null, null, null, Lists.newArrayList(), null, null
		);
	}
}
