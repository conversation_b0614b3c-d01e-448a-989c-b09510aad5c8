package com.sankuai.meituan.shangou.empower.tms.delivery.access.application;

import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrderRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MockInjectUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/12
 */
@RunWith(MockitoJUnitRunner.class)
public class QueryDeliveryInfoApplicationServiceTest {

    private QueryDeliveryInfoApplicationService queryDeliveryInfoApplicationService = new QueryDeliveryInfoApplicationService();

    @Mock
    private DeliveryOrderRepository deliveryOrderRepository;

    private void setUp() {
        try {
            MockInjectUtils.injectField("deliveryOrderRepository", deliveryOrderRepository, queryDeliveryInfoApplicationService);
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }

    /*@Test
    public void buildAggrDeliveryAbility() {
        setUp();
        Mockito.when(storeRepository.getStoreDeliverConfig(anyLong(), anyLong())).thenReturn(buildStore(true, true));
        *//*Mockito.when(deliveryStoreRepository.getDeliveryStore(anyLong(), anyLong(), anyBoolean())).thenReturn(buildDeliveryStore(true));*//*
        DeliveryAbility aggrDeliveryAbility = queryDeliveryInfoApplicationService.getAggrDeliveryAbility(Lists.newArrayList(new OrderKey(1L, 1L, 1L)));
        assertTrue(aggrDeliveryAbility.isHasAggrDeliveryChannel());
        assertTrue(!aggrDeliveryAbility.isHasThirdPartDeliveryChannel());
        assertTrue(aggrDeliveryAbility.canManualLaunchDelivery());

    }

    private List<Store> buildStore(boolean aggr, boolean isManual) {
        return Lists.newArrayList(new Store(1L, 1L, 1L, "", aggr, 1,
                isManual ? DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY : DeliveryLaunchTypeEnum.AUTO_LAUNCH_DELIVERY,
                1, new LaunchDeliveryConfig(LaunchDeliveryTypeEnum.MERCHANT_ACCEPT, 10),
                new BookingOrderLaunchDeliveryConfig(BookingOrderLaunchDeliveryTypeEnum.BEFORE_DELIVERY, 15),false, null));
    }*/

    /*@Test
    public void buildOldDeliveryAbility() {
        setUp();
        Mockito.when(storeRepository.getStoreDeliverConfig(anyLong(), anyLong())).thenReturn(buildStore(false, true));
        Mockito.when(deliveryStoreRepository.getDeliveryStore(anyLong(), anyLong(), anyBoolean())).thenReturn(buildDeliveryStore(true));
        DeliveryAbility aggrDeliveryAbility = queryDeliveryInfoApplicationService.getAggrDeliveryAbility(Lists.newArrayList(new OrderKey(1L, 1L, 1L)));
        assertTrue(!aggrDeliveryAbility.isHasAggrDeliveryChannel());
        assertTrue(aggrDeliveryAbility.isHasThirdPartDeliveryChannel());
        assertTrue(!aggrDeliveryAbility.canManualLaunchDelivery());
    }*/

    /*@Test
    public void buildNoneDeliveryAbilityWhenNoStore() {
        setUp();
        Mockito.when(storeRepository.getStoreDeliverConfig(anyLong(), anyLong())).thenReturn(Lists.newArrayList());
       *//* Mockito.when(deliveryStoreRepository.getDeliveryStore(anyLong(), anyLong(), anyBoolean())).thenReturn(buildDeliveryStore(true));*//*
        DeliveryAbility aggrDeliveryAbility = queryDeliveryInfoApplicationService.getAggrDeliveryAbility(Lists.newArrayList(new OrderKey(1L, 1L, 1L)));
        assertTrue(!aggrDeliveryAbility.isHasAggrDeliveryChannel());
        assertTrue(!aggrDeliveryAbility.isHasThirdPartDeliveryChannel());
        assertTrue(!aggrDeliveryAbility.canManualLaunchDelivery());
    }*/

   /* @Test
    public void buildNoneDeliveryAbilityWhenNoAbility() {
        setUp();
        Mockito.when(storeRepository.getStoreDeliverConfig(anyLong(), anyLong())).thenReturn(buildStore(false, false));
        Mockito.when(deliveryStoreRepository.getDeliveryStore(anyLong(), anyLong(), anyBoolean())).thenReturn(buildDeliveryStore(false));
        DeliveryAbility aggrDeliveryAbility = queryDeliveryInfoApplicationService.getAggrDeliveryAbility(Lists.newArrayList(new OrderKey(1L, 1L, 1L)));
        assertTrue(!aggrDeliveryAbility.isHasAggrDeliveryChannel());
        assertTrue(!aggrDeliveryAbility.isHasThirdPartDeliveryChannel());
        assertTrue(!aggrDeliveryAbility.canManualLaunchDelivery());
    }*/

    @Test
    public void filterAvailableDeliveryOrderWhenActive() {
//        queryDeliveryInfoApplicationService = new QueryDeliveryInfoApplicationService();
//        DeliveryOrder deliveryOrder = queryDeliveryInfoApplicationService.filterAvailableDeliveryOrder(buildActiveDeliveryOrders());
//        assertEquals(Long.valueOf(2L), deliveryOrder.getId());
    }

    @Test
    public void filterAvailableDeliveryOrderWhenNoneActive() {
        queryDeliveryInfoApplicationService = new QueryDeliveryInfoApplicationService();
//        DeliveryOrder deliveryOrder = queryDeliveryInfoApplicationService.filterAvailableDeliveryOrder(buildNoneActiveDeliveryOrders());
//        assertEquals(Long.valueOf(3L), deliveryOrder.getId());
    }

    private List<DeliveryOrder> buildActiveDeliveryOrders() {
        return Lists.newArrayList(buildDeliveryOrder(1L, 1L), buildDeliveryOrder(2L, 0L), buildDeliveryOrder(3L, 3L));
    }

    private List<DeliveryOrder> buildNoneActiveDeliveryOrders() {
        return Lists.newArrayList(buildDeliveryOrder(2L, 1L), buildDeliveryOrder(1L, 4L), buildDeliveryOrder(3L, 3L));
    }

    private DeliveryOrder buildDeliveryOrder(long id, long activeStatus) {
        return new DeliveryOrder(id, null,null, null, null, null,
                null, null, null, null, null, null,null,
                null, null, null, null, null,
                null, null, null, null, null, null,
                null, null, null,null,null,0,new BigDecimal(1),
                1,null, 10, false, null,null,null,null,null,null, null,
                null,null,null,null,null,null,null,null,null,null,null, null, false, null, null, null, null);
    }
}
