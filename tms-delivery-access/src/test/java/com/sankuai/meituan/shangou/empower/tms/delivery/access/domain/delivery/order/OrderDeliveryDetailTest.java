package com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.delivery.order;

import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.DeliveryOrderLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.dms.base.model.value.Address;
import com.sankuai.meituan.shangou.dms.base.model.value.CoordinatePoint;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryAbility;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.TimeUtil;
import com.sankuai.meituan.shangou.dms.base.model.value.Receiver;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Rider;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/13
 */
public class OrderDeliveryDetailTest {

    private DeliveryOrderLog deliveryOrderLog = null;

    private DeliveryOrderLog deliveryOrderCurrentStatusLog = null;

    @Test
    public void manualThirdPart() {

        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(null, buildOrderKey(), buildManualThirdPartCondition(), buildManualAggrDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    @Test
    public void cannotLanuchAnyWhenOrderCanceled() {

        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(null, buildOrderKey(), buildCannotLaunchCondition(), buildManualAggrDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(!orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    @Test
    public void cannotLanuchAnyWhenOrderSelfMarkNull() {

        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(null, buildOrderKey(), buildNullSelfMarkCondition(), buildManualAggrDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(!orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    @Test
    public void cannotLanuchAnyWhenOrderNotConfirmed() {

        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(null, buildOrderKey(), buildNotConfirmedLaunchCondition(), buildManualAggrDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(!orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    @Test
    public void manualThirdPartWhenInit() {
        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(buildInitDeliveryOrder(), buildOrderKey(), buildManualThirdPartCondition(), buildManualAggrDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    private DeliveryOrder buildInitDeliveryOrder() {

        return buildDeliveryOrder(DeliveryChannelEnum.AGGREGATE_DELIVERY_DADA, DeliveryStatusEnum.INIT);
    }

    private DeliveryOrder buildExceptionDeliveryOrder() {

        return buildDeliveryOrder(DeliveryChannelEnum.AGGREGATE_DELIVERY_DADA,
                DeliveryStatusEnum.DELIVERY_CANCELLED,
                DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM);
    }



    @Test
    public void turnThirdPart() {
        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(buildExceptionDeliveryOrder(), buildOrderKey(), buildOnlineDeliveryCondition(), buildManualAggrDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(!orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    @Test
    public void selfMerchant() {
        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(buildExceptionDeliveryOrder(), buildOrderKey(), buildOnlineDeliveryCondition(), buildNoneDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(!orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    @Test
    public void cannotLaunchAnyWhenExceptionAndOldThirdDeliveryAbility() {
        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(buildExceptionDeliveryOrder(), buildOrderKey(), buildOnlineDeliveryCondition(), buildOldSelfDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(!orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(!orderDeliveryDetail.isCanRetryLaunch());
    }

    @Test
    public void retry() {
        OrderDeliveryDetail orderDeliveryDetail = OrderDeliveryDetail.buildFromDeliveryOrder(buildExceptionDeliveryOrder(), buildOrderKey(), buildManualThirdPartCondition(), buildManualAggrDeliveryAbility(), deliveryOrderLog, deliveryOrderCurrentStatusLog);
        assertTrue(!orderDeliveryDetail.isCanManualLaunchThirdPart());
        assertTrue(!orderDeliveryDetail.isCanSelfDelivery());
        assertTrue(!orderDeliveryDetail.isCanLaunchThirdPartWhenException());
        assertTrue(orderDeliveryDetail.isCanRetryLaunch());
    }

    private DeliveryOrder buildDeliveryOrder(DeliveryChannelEnum deliveryChannel, DeliveryStatusEnum status) {
        return buildDeliveryOrder(deliveryChannel, status, DeliveryExceptionTypeEnum.NO_EXCEPTION);
    }

    private DeliveryOrder buildDeliveryOrder(DeliveryChannelEnum deliveryChannel, DeliveryStatusEnum status, DeliveryExceptionTypeEnum exceptionTypeEnum) {
        return new DeliveryOrder(
                99L,
                1L,
                2L,
                buildOrderKey(),
                "channel_order_id",
                30,
                101,
                1,
                "1-01",
                false,
                new Receiver(
                        "receiver_name",
                        "receiver_phone",
                        "receiver_privacy_phone",
                        new Address("address_detail", CoordinateTypeEnum.MARS, new CoordinatePoint("1.1", "2.2"))),
                LocalDateTime.of(2020, 12, 12, 12, 12, 12),
                LocalDateTime.of(2020, 12, 12, 12, 12, 12),
                deliveryChannel.getCode(),
                "channel_delivery_id",
                0,
                "1",
                status,
                exceptionTypeEnum,
                StringUtils.EMPTY,
                TimeUtil.getEpochTime(),
                new Rider("original_rider_name", "original_rider_phone", "original_rider_phone_token"),
                0L,
                TimeUtil.getEpochTime(),
                0,
                BigDecimal.ONE,
                99L,
                LocalDateTime.now(),
                LocalDateTime.now(),
                0,
                new BigDecimal(1.0),
                1, null, 10, false, null,null,null,null,null,null, null, null,null
                ,null, null, null,null,null, null,null,null,null
        );
    }

    private DeliveryAbility buildManualAggrDeliveryAbility() {

        DeliveryAbility deliveryAbility = new DeliveryAbility();
        deliveryAbility.setHasThirdPartDeliveryChannel(true);
        deliveryAbility.setHasAggrDeliveryChannel(true);
        deliveryAbility.setDeliveryLaunchType(DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY);
        return deliveryAbility;
    }

    private DeliveryAbility buildNoneDeliveryAbility() {

        DeliveryAbility deliveryAbility = new DeliveryAbility();
        deliveryAbility.setHasThirdPartDeliveryChannel(false);
        deliveryAbility.setHasAggrDeliveryChannel(false);
        deliveryAbility.setDeliveryLaunchType(DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY);
        return deliveryAbility;
    }

    private DeliveryAbility buildOldSelfDeliveryAbility() {

        DeliveryAbility deliveryAbility = new DeliveryAbility();
        deliveryAbility.setHasThirdPartDeliveryChannel(true);
        deliveryAbility.setHasAggrDeliveryChannel(false);
        deliveryAbility.setDeliveryLaunchType(DeliveryLaunchTypeEnum.MANUAL_LAUNCH_DELIVERY);
        return deliveryAbility;
    }

    private DeliveryConditionInOrder buildManualThirdPartCondition() {
        return new DeliveryConditionInOrder(OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), DistributeMethodEnum.HOME_DELIVERY.getValue(), 1);
    }

    private DeliveryConditionInOrder buildNullSelfMarkCondition() {
        return new DeliveryConditionInOrder(OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), DistributeMethodEnum.HOME_DELIVERY.getValue(), null);
    }

    private DeliveryConditionInOrder buildOnlineDeliveryCondition() {
        return new DeliveryConditionInOrder(OrderStatusEnum.PICKING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), DistributeMethodEnum.HOME_DELIVERY.getValue(), 0);
    }

    private DeliveryConditionInOrder buildCannotLaunchCondition() {
        return new DeliveryConditionInOrder(OrderStatusEnum.COMPLETED.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), DistributeMethodEnum.HOME_DELIVERY.getValue(), 1);
    }

    private DeliveryConditionInOrder buildNotConfirmedLaunchCondition() {
        return new DeliveryConditionInOrder(OrderStatusEnum.PAYING.getValue(), OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue(), DistributeMethodEnum.HOME_DELIVERY.getValue(), 1);
    }

    private OrderKey buildOrderKey() {

        return new OrderKey(1L, 1L, 1L);
    }
}
