package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.base.Preconditions;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.linz.thrift.response.Status;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.PageResultV2;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SimpleUser;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.AccountUserDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateBatchDeleteRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateBatchSaveRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.SupplyRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReasonTemplateBatchSaveResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReviewFailedVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SaleAttrCompatUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuSaveResultDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.PushFieldEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchUpdateTenantSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSpuDetailBizRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.BatchSaveTenantSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantSpuDetailBizResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.item.AbnormalProductClient;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.AccountService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.SupplyRelationService;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantSpuPageQueryBizResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.TenantSpuBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantProductChangeSyncDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.TenantProductChangeSyncTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.BatchReviewTenantProductRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.SubmitTenantProductReviewRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.tenantspu.TenantProductChangeSyncRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TaskSubmitResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.BatchReviewTenantProductResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.SubmitTenantProductReviewResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProductBizTaskThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProductReviewBizThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelaftersalerule.dto.ChannelAfterSaleDetailDTO;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuCartonMeasure;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuCartonMeasureInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.thrift.response.Status;
import com.meituan.linz.thrift.response.ThriftResponse;
import com.meituan.shangou.saas.tenant.thrift.BoothThriftService;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChainRelationEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.response.BoothListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.PageResultV2;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ErrorCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PriceTrendConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SimpleUser;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.AccountUserDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.qualification.ChannelCategoryQualificationVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.BatchSkuReviewRecallApplyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuWithChannelInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChangeChannelSkuFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChangeMultiChannelSkuStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelSkuDetailInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChgChannelSkuStatusForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryCartonMeasureRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryCdqStoreSkuDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryChannelCategoryAfterSaleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryChannelCategoryInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateBatchDeleteRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateBatchSaveRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateDeleteRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateGetRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateSaveRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReviewBatchRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SaveCdqStoreSkuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuListPageByDayAndOpTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuListPageForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewApplyListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewApplySaveRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewInfoGetRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuStoreToReviewCountGetRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuTotalCountOpTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.UpdatePriceAndStockForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.AddTenantSkuApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.BatchChangeSpuFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.BatchChangeSpuStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FindTenantSimilarSkuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.GetTenantSpuBySpuIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.PageQueryOffSaleAndUnquotedRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryOnSaleAndOffSaleCountRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryTenantRegionSpuByNameRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryTenantSkuBySpuIdApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryTenantSpuAbnormalRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.TenantSpuAddOrUpdateApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.TenantSpuDeleteApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.TenantSpuInfoApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.TenantSpuPageQueryApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.TenantSpuQueryFilterCountApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.UpdateStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilterCondition;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.StoreSkuWithChannelPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.SupplyRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quote.QueryQuoteReviewingCountResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.BatchSkuReviewRecallApplyResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.CartonMeasureVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeChannelSkuFrontCategoryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeChannelSkuStatusForAppResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeMultiChannelSkuStatusResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryAfterSaleResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryAfterSaleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryInfoResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryLevelVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelQualificationByCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuForAppVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.QueryStoreOnlineSkuDetailResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReasonTemplateBatchSaveResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReasonTemplateGetResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReasonTemplateListQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReviewBatchResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReviewFailedVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SaveCdqStoreSkuPartitionSuccessResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuListPageForAppResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuReviewInfoGetResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuReviewListQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuStoreToReviewCountGetResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTotalCountOpTypeResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.StoreSkuBaseDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.TenantChannelSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.UpdatePriceStockResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.BatchChangeSpuFrontCategoryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.BatchChangeSpuStatusResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.CartonMeasureConvertFactorVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ProductAbnormalDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ProductAbnormalVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.QueryOnSaleAndOffSaleCountResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.QueryTenantRegionSpuByNameResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantRegionSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSkuSimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuPreviewVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuQueryFilterCountResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuSimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.SaveType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.TenantBizModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.AccountService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.SupplyRelationService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.AbnormalInfoUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.CommonThreadPoolHolder;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.OCMSUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.RequestConvertUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.ResponseConvertUtils;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.OperatorDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.common.response.GeneralResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.BatchQuoteReviewRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QueryQuoteReviewsRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QuoteCommitRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QuoteReviewCountRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.quote.BatchQuoteReviewResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.quote.QueryQuoteReviewsResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.quote.QuoteCommitResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.quote.QuoteReviewCountResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.service.QuoteReviewThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.RegionSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSkuQueryConditionDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.SkuSortEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.BatchChangeStoreSpuFrontCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.BatchUpdateStoreSpuStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.DeleteTenantSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.PageQueryTenantRegionSpuByNameRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.RegionSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuCountRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuPageQueryByStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantChannelSkuRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSkuQueryConditionRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSpuAddSpecRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSpuPreviewRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSpuQueryFilterCountRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.UpdateTenantSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.DeleteTenantSpuResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.PageQueryTenantRegionSpuByNameResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.RegionSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuCountResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuPageQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.TenantAddSpecResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.TenantChannelSkuResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.TenantSkuQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.TenantSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.TenantSpuPreviewResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.TenantSpuQueryFilterCountResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.UpdateTenantSpuResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.RegionSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.StoreSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.TenantSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.request.sku.CdqStoreSkuDetailQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.request.sku.CdqStoreSkuSaveRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.response.sku.CdqStoreSkuDetailQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.response.sku.CdqStoreSkuSaveResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.service.sku.CdqStoreSkuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BaseResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchRecallResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchReviewRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchReviewResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchUpdateStoreStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChainProductConfigResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChangeChannelSkuStatusForAppRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChangeStoreSkuChannelFrontCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelCategoryQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.DeleteReasonTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetReasonTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetReasonTemplateResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetSkuReviewInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetSkuReviewInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryChainProductConfigRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryChannelCategoryByErpCategoryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryChannelCategoryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryReasonTemplateListRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryReasonTemplateListResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuByDayAndOpTypeRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuReviewApplyListRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuReviewListRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuReviewListResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryStoreSkuBySkuIdsRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryTenantSkuCartonMeasureResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.Response;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SaveOnlinePriceAndStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SaveReasonTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuListPageForAppResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuListRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuListResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuPageQueryForAppRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuReviewBatchRecallApplyRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StockSyncChannelResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TaskResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.UpdatePriceStockResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChainProductEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ReviewStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.SkuOperateSourceType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.reponse.sku.SkuChannelStatusChangeResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.request.sku.SkuChannelStatusChangeRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelSkuForAppThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelSkuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.OtherThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.QueryTenantTagRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ReviewReasonTemplateThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.SkuStoreStockThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TagThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantSkuReviewThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantTagResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.product.TenantProductThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalProductInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantProductChangeSyncDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.composesku.BatchUpdateProductDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.TenantProductChangeSyncTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.TenantSpuAbnormalSourceTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchUpdateStatusRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSpuDetailBizRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSpuPageQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.BatchReviewTenantProductRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.SubmitTenantProductReviewRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.tenantspu.TenantProductChangeSyncRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TaskSubmitResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantSpuDetailBizResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantSpuPageQueryBizResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.BatchReviewTenantProductResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.SubmitTenantProductReviewResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProductBizTaskThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProductReviewBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.QnhBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.TenantSpuBizThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelaftersalerule.dto.ChannelAfterSaleDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelaftersalerule.dto.ChannelAfterSaleDetailDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelaftersalerule.request.ChannelAfterSaleQueryRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelaftersalerule.service.ChannelAfterSaleRuleThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelCateQueryByChannelIdRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelCategoriesQueryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.service.ChannelCategoryThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelqualification.ChannelQualificationThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelqualification.dto.ChannelCategoryQualificationDTO;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.constant.WeightType;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuCartonMeasure;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuCartonMeasureInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSpuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.QueryMerchantSpuListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMerchantSpuThriftService;
import com.sankuai.meituan.shangou.saas.common.runtime.RpcInvoker;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/4/11
 * desc: 中台商品库存服务
 */
@Service
@Slf4j
public class OCMSServiceWrapper {

    @Resource
    private ChannelSkuForAppThriftService.Iface channelSkuForAppThriftService;

    @Resource
    private ChannelSkuThriftService.Iface channelSkuThriftService;

    @Resource
    private CdqStoreSkuThriftService.Iface cdqStoreSkuThriftService;

    @Resource
    private OtherThriftService.Iface otherThriftService;

    @Resource
    private StoreSpuThriftService storeSpuThriftService;

    @Resource
    private TenantSkuReviewThriftService.Iface tenantSkuReviewThriftService;

    @Autowired
    private ProductReviewBizThriftService productReviewBizThriftService;

    @Resource
    private ReviewReasonTemplateThriftService.Iface reviewReasonTemplateThriftService;

    @Resource
    private ChannelThriftService.Iface channelThriftService;

    @Resource
    private AuthThriftService.Iface authThriftService;

    @Resource
    private OCMSPriceTrendWrapper ocmsPriceTrendWrapper;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private BoothThriftService boothThriftService;

    @Resource
    private QuoteReviewThriftService quoteReviewThriftService;

    @Resource
    private PoiThriftService poiThriftService;

    @Resource
    private TagThriftService.Iface tagThriftService;

    @Resource
    private SaasPriceServiceWrapper saasPriceServiceWrapper;

    @Resource
    private SkuStoreStockThriftService.Iface skuStoreStockThriftService;

    @Resource
    private TenantSpuThriftService tenantSpuThriftService;

    @Resource
    private TenantSpuBizThriftService tenantSpuBizThriftService;

    @Resource
    private RegionSpuThriftService regionSpuThriftService;

    @Autowired
    private ChannelCategoryThriftService channelCategoryThriftService;

    @Resource
    private QnhBizThriftService qnhBizThriftService;

    @Resource(name = "queryStatusCountAsyncThreadPool")
    private ExecutorService executorService;

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private EmpowerProductWrapper empowerProductWrapper;

    @Autowired
    private TenantProductThriftService.Iface tenantProductThriftService;
    @Autowired
    private ChannelAfterSaleRuleThriftService channelAfterSaleRuleThriftService;

    @Autowired
    private ChannelQualificationThriftService channelQualificationThriftService;

    @Autowired
    private EmpowerMerchantSpuThriftService.Iface empowerMerchantSpuThriftService;

    @Autowired
    private ConfigThriftService configThriftService;

    @Autowired
    private PurchaseBizServiceWrapper purchaseBizServiceWrapper;

    @Autowired
    private ProductBizTaskThriftService productBizTaskThriftService;

    @Autowired
    private SupplyRelationService supplyRelationService;

    @Autowired
    @Qualifier("createSupplyRelationAfterSpuCreateThreadPool")
    private ExecutorService createSupplyRelationAfterSpuCreateThreadPool;

    @Autowired
    private AccountService accountService;

    @Autowired
    private ProductBizServiceWrapper productBizServiceWrapper;

    private final Cache<String, Object> LOCAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private static final String ERP_KEY = "ERP";

    //可重试
    private static final int CODE_CAN_RETRY = 900;

    //不可重试
    private static final int CODE_CANNOT_RETRY = 1;

    private static final int CODE_SUCCESS = 0;

    private static final int ALL_DAYS_CODE = 0;

    private static final int INITIAL_PAGE = 1;
    //已上架
    private static final int ON_SALE = 1;
    //已下架
    private static final int OFF_SALE = 2;
    //未上线
    private static final int NOT_ONLINE = 0;

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryQuoteReviewingCountResponse> queryQuoteReviewingCountList(List<Long> storeIds, User user) {
        try {
            QuoteReviewCountRequest request = new QuoteReviewCountRequest();
            request.setTenantId(user.getTenantId());
            request.setStoreIds(storeIds);
            log.info("quoteReviewThriftService.queryQuoteReviewingCount() request:{}", request);
            QuoteReviewCountResponse queryResponse = quoteReviewThriftService.queryQuoteReviewingCount(request);
            log.info("quoteReviewThriftService.queryQuoteReviewingCount() response:{}", queryResponse);

            QueryQuoteReviewingCountResponse countResponse = new QueryQuoteReviewingCountResponse();
            countResponse.setCount(queryResponse.getQuoteReviewingCount());
            return new CommonResponse(queryResponse.getCode(), queryResponse.getMsg(), countResponse);
        } catch (TException e) {
            log.error("channelPriceThriftService.queryQuoteReviewingNumList() TException ", e);
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse<SkuListPageForAppResponseVO> querySkuListForApp(SkuListPageForAppRequest skuListPageForAppRequest, User user, String boothId) {
        SkuPageQueryForAppRequest request = OCMSUtils.convertRequest(skuListPageForAppRequest, user, boothId);
        log.info("OCMSServiceWrapper.querySkuListForApp  调用channelSkuForAppThriftService.querySkuListForApp() request:{}", request);
        try {
            SkuListPageForAppResponse response = channelSkuForAppThriftService.querySkuListForApp(request);
            log.info("OCMSServiceWrapper.querySkuListForApp  调用channelSkuForAppThriftService.querySkuListForApp() response:{}", response);

            SkuListPageForAppResponseVO skuListPageForAppResponseVO = OCMSUtils.convertResponse(response);

            // 填充价格趋势信息
            fillStoreSkuWithChannelPriceTrendInfo(request.getTenantId(), skuListPageForAppResponseVO);

            return new CommonResponse(response.getCode(), response.getMsg(), skuListPageForAppResponseVO);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    protected void fillStoreSkuWithChannelPriceTrendInfo(Long tenantId, SkuListPageForAppResponseVO skuForAppListPageVO) {
        try {
            if (skuForAppListPageVO == null || CollectionUtils.isEmpty(skuForAppListPageVO.getSkuInfoList())) {
                return;
            }

            List<ChannelSkuForAppVO> skuInfoList = skuForAppListPageVO.getSkuInfoList();

            // 查询门店商品价格趋势, 若价格图标如果直接展示, 则不需要再查询趋势数据
            Map<StoreSkuKey, StoreSkuWithChannelPriceTrendVO> storeSkuKey2PriceTrendMap = Collections.emptyMap();
            if (!MccConfigUtil.isPriceTrendIconDirectShow()) {
                try {
                    List<StoreSkuPriceFilterCondition> filterConditionList =
                            ConverterUtils.convertList(skuInfoList, StoreSkuPriceFilterCondition::build);
                    storeSkuKey2PriceTrendMap = ocmsPriceTrendWrapper.queryStoreSkuPriceTrendMapByFilterCondition(
                            tenantId, filterConditionList, PriceTrendConstants.STORE_SKU_PRICE_TREND_DEFAULT_QUERY_DAYS);
                } catch (Exception e) {
                    log.error("查询门店商品价格趋势错误", e);
                }
            }

            // 设置门店商品价格趋势信息
            for (ChannelSkuForAppVO skuInfo : skuInfoList) {
                StoreSkuKey storeSkuKey = new StoreSkuKey(skuInfo.getStoreId(), skuInfo.getSku());
                StoreSkuWithChannelPriceTrendVO storeSkuWithChannelPriceTrendVO = null;

                if (MapUtils.isNotEmpty(storeSkuKey2PriceTrendMap)) {
                    storeSkuWithChannelPriceTrendVO = storeSkuKey2PriceTrendMap.get(storeSkuKey);
                }

                skuInfo.fillStoreSkuWithChannelPriceTrendInfo(storeSkuWithChannelPriceTrendVO);
            }
        } catch (Exception e) {
            log.error("设置门店商品价格趋势信息错误", e);
        }
    }

    public CommonResponse<SkuListPageForAppResponseVO> querySkuListForAppByDayAndOpType(SkuListPageByDayAndOpTypeRequest skuListPageByDayAndOpTypeRequest, User user) {
        QuerySkuByDayAndOpTypeRequest request = OCMSUtils.convertRequest(skuListPageByDayAndOpTypeRequest, user);
        log.info("OCMSServiceWrapper.querySkuListForAppByDayAndOpType  调用channelSkuForAppThriftService.querySkuByDayAndOpType() request:{}", request);
        try {
            SkuListPageForAppResponse response = channelSkuForAppThriftService.querySkuByDayAndOpType(request);
            log.info("OCMSServiceWrapper.querySkuListForAppByDayAndOpType  调用channelSkuForAppThriftService.querySkuByDayAndOpType() response:{}", response);
            return new CommonResponse(response.getCode(), response.getMsg(), OCMSUtils.convertResponse(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<SkuTotalCountOpTypeResponseVO> querySkuTotalCountForAppByDayAndOpType(SkuTotalCountOpTypeRequest request, User user) {
        SkuTotalCountOpTypeResponseVO vo = new SkuTotalCountOpTypeResponseVO();
        Map<Integer, Integer> skuDayCountMap = Maps.newHashMap();
        log.info("OCMSServiceWrapper.querySkuTotalCountForAppByDayAndOpType  调用channelSkuForAppThriftService.querySkuTotalCountForAppByDayAndOpType() request:{}", request);
        if (CollectionUtils.isNotEmpty(request.getDays())) {
            CountDownLatch countDownLatch = new CountDownLatch(request.getDays().size());
            for (Integer day : request.getDays()) {
                CommonThreadPoolHolder.getInstance().submit(() -> {

                    SkuListPageByDayAndOpTypeRequest skuListPageByDayAndOpTypeRequest = new SkuListPageByDayAndOpTypeRequest();
                    skuListPageByDayAndOpTypeRequest.setOpType(request.getOpType());
                    skuListPageByDayAndOpTypeRequest.setPage(INITIAL_PAGE);
                    skuListPageByDayAndOpTypeRequest.setSize(INITIAL_PAGE);
                    skuListPageByDayAndOpTypeRequest.setStoreId(request.getStoreId());
                    skuListPageByDayAndOpTypeRequest.setStoreCategory(request.getStoreCategory());
                    if (Objects.nonNull(request.getHasStoreCategory())) {
                        skuListPageByDayAndOpTypeRequest.setHasStoreCategory(request.getHasStoreCategory());
                    }
                    //0代表查所有天数，不传值
                    if (!day.equals(ALL_DAYS_CODE)) {
                        skuListPageByDayAndOpTypeRequest.setDays(day);
                    }
                    QuerySkuByDayAndOpTypeRequest querySkuByDayAndOpTypeRequest = OCMSUtils.convertRequest(skuListPageByDayAndOpTypeRequest, user);
                    try {
                        SkuListPageForAppResponse response = channelSkuForAppThriftService.querySkuByDayAndOpType(querySkuByDayAndOpTypeRequest);
                        log.info("OCMSServiceWrapper.querySkuTotalCpuntForAppByDayAndOpType  调用channelSkuForAppThriftService.querySkuByDayAndOpType() response:{}", response);
                        skuDayCountMap.put(day, response.getPageInfo().getTotalSize());
                    } catch (TException e) {
                        throw new CommonRuntimeException(e);
                    } finally {
                        countDownLatch.countDown();
                    }

                });
            }
            try {
                countDownLatch.await(10, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("根据不同天数查询商品总数失败", e);
            }
        }

        vo.setSkuDayCountMap(skuDayCountMap);
        return CommonResponse.success(vo);
    }

    public CommonResponse<UpdatePriceStockResponseVO> updateProductPriceAndStockForApp(UpdatePriceAndStockForAppRequest updatePriceAndStockForAppRequest, User user) {
        SaveOnlinePriceAndStockRequest request = OCMSUtils.convertRequest(updatePriceAndStockForAppRequest, user);
        log.info("OCMSServiceWrapper.updateProductPriceAndStockForApp  调用channelSkuForAppThriftService.updateProductPriceAndStockForApp() request:{}", request);
        try {
            UpdatePriceStockResponse response = channelSkuForAppThriftService.updateProductPriceAndStockForApp(request);
            log.info("OCMSServiceWrapper.updateProductPriceAndStockForApp  调用channelSkuForAppThriftService.updateProductPriceAndStockForApp() response:{}", response);
            int code = response.getCode();
            if (code == CODE_CAN_RETRY || code == CODE_CANNOT_RETRY) {
                code = 0;
            }
            return new CommonResponse(code, response.getMsg(), OCMSUtils.convertResponse(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<ChangeMultiChannelSkuStatusResponse> changeMultiChannelSkuStatus(User user, ChangeMultiChannelSkuStatusRequest request) {
        SkuChannelStatusChangeRequest skuChannelStatusChangeRequest = OCMSUtils.convertRequest(request, user);
        log.info("OCMSServiceWrapper.changeMultiChannelSkuStatus  调用channelSkuThriftService.changeMultiChannelSkuStatus() request:{}", skuChannelStatusChangeRequest);
        try {
            SkuChannelStatusChangeResponse response = channelSkuThriftService.changeMultiChannelSkuStatus(skuChannelStatusChangeRequest);
            return new CommonResponse(response.getCode(), response.getMsg(), OCMSUtils.convertResponse(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<ChangeChannelSkuStatusForAppResponseVO> changeChannelSkuStatusForApp(ChgChannelSkuStatusForAppRequest chgChannelSkuStatusForAppRequest, User user) {
        ChangeChannelSkuStatusForAppRequest request = OCMSUtils.convertRequest(chgChannelSkuStatusForAppRequest, user);
        log.info("OCMSServiceWrapper.changeChannelSkuStatusForApp  调用channelSkuForAppThriftService.changeChannelSkuStatusForApp() request:{}", request);
        try {
            Response response = channelSkuForAppThriftService.changeChannelSkuStatusForApp(request);
            log.info("OCMSServiceWrapper.changeChannelSkuStatusForApp  调用channelSkuForAppThriftService.changeChannelSkuStatusForApp() response:{}", response);
            int code = response.getCode();
            if (code == CODE_CAN_RETRY || code == CODE_CANNOT_RETRY) {
                code = 0;
            }
            return new CommonResponse(code, response.getMsg(), OCMSUtils.convertResponse(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<SaveCdqStoreSkuPartitionSuccessResponse> saveStoreOnlineSku(SaveCdqStoreSkuRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        // 编辑商品 作废待审核报价
        if (request.getSaveType() == SaveType.EDIT_TYPE.getValue() && request.isDeleteToReviewQuote()) {
            deleteToReviewQuotesIgnoreError(user, request.getStoreId(), request.getCdqStoreSkuWithChannelInfoVo().getCdqStoreSkuVo().getSkuId());
        }

        CdqStoreSkuSaveRequest cdqStoreSkuSaveRequest;
        try {
            cdqStoreSkuSaveRequest = OCMSUtils.convertRequest(request, user);
            if ((cdqStoreSkuSaveRequest.getStoreSkuWithChannelInfo().getStoreSkuInfo().getWeightType() == WeightType.WEIGHT.getValue() ||
                    cdqStoreSkuSaveRequest.getStoreSkuWithChannelInfo().getStoreSkuInfo().getWeightType() == WeightType.PIECE.getValue()) &&
                    cdqStoreSkuSaveRequest.getStoreSkuWithChannelInfo().getStoreSkuInfo().getWeight() <= 0) {
                return new CommonResponse(ResponseCodeEnum.FAILED.getValue(), "当前商品是非标品，请填写重量", null);
            }
        } catch (Exception e) {
            throw new CommonRuntimeException(e.getMessage());
        }
        log.info("OCMSServiceWrapper.saveStoreOnlineSku  cdqStoreSkuThriftService.saveStoreOnlineSku() request:{}", cdqStoreSkuSaveRequest);
        try {
            // 保存门店商品
            CdqStoreSkuSaveResponse cdqStoreSkuSaveResponse = cdqStoreSkuThriftService.saveStoreSku(cdqStoreSkuSaveRequest);
            log.info("OCMSServiceWrapper.saveStoreOnlineSku  cdqStoreSkuThriftService.saveStoreOnlineSku() response:{}", cdqStoreSkuSaveResponse);
            if (ResponseCodeEnum.SUCCESS_PARTITION.getValue() == cdqStoreSkuSaveResponse.getCode()) {
                SaveCdqStoreSkuPartitionSuccessResponse partitionSuccessResponse = new SaveCdqStoreSkuPartitionSuccessResponse();
                partitionSuccessResponse.setErrorCode(cdqStoreSkuSaveResponse.getCode());
                partitionSuccessResponse.setErrorMsg(cdqStoreSkuSaveResponse.getMsg());
                return new CommonResponse<>(ResponseCodeEnum.SUCCESS.getValue(), "部分成功", partitionSuccessResponse);
            }

            // 商品创建成功 自动发起报价, 报价失败不影响业务结果
            if (ResponseCodeEnum.SUCCESS.getValue() == cdqStoreSkuSaveResponse.getCode() && request.getSaveType() == SaveType.SAVE_TYPE.getValue()) {
                return quoteIfNecessary(identityInfo, request);
            }
            return new CommonResponse<>(cdqStoreSkuSaveResponse.getCode(), cdqStoreSkuSaveResponse.getMsg(), null);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private void deleteToReviewQuotesIgnoreError(User user, long storeId, String skuId) {
        try {
            QueryQuoteReviewsRequest queryQuoteReviewsRequest = OCMSUtils.convert2ToReviewQuoteQueryRequest(user.getTenantId(), storeId, skuId);
            log.info("OCMSServiceWrapper.queryQuoteReviews request:{}", queryQuoteReviewsRequest);
            QueryQuoteReviewsResponse queryQuoteReviewsResponse = quoteReviewThriftService.queryQuoteReviews(queryQuoteReviewsRequest);
            log.info("OCMSServiceWrapper.queryQuoteReviews response:{}", queryQuoteReviewsResponse);
            if (Objects.isNull(queryQuoteReviewsResponse)
                    || queryQuoteReviewsResponse.getCode() != ResponseCodeEnum.SUCCESS.getValue()
                    || CollectionUtils.isEmpty(queryQuoteReviewsResponse.getQuoteReviewList())) {
                return;
            }

            long quoteRecordId = queryQuoteReviewsResponse.getQuoteReviewList().get(0).getQuoteRecordId();
            BatchQuoteReviewRequest batchQuoteReviewRequest = OCMSUtils.convert2BatchQuoteReviewedRequest(user, quoteRecordId);
            log.info("OCMSServiceWrapper.batchQuoteReviewRequest request:{}", batchQuoteReviewRequest);
            BatchQuoteReviewResponse batchQuoteReviewResponse = quoteReviewThriftService.batchQuoteReview(batchQuoteReviewRequest);
            System.out.println("OCMSServiceWrapper.batchQuoteReview response:" + batchQuoteReviewResponse);
        } catch (Exception e) {
            log.error("作废待审核报价异常, tenantId:{} storeId:{} skuId:{}", user.getTenantId(), storeId, skuId, e);
        }
    }

    private CommonResponse<SaveCdqStoreSkuPartitionSuccessResponse> quoteIfNecessary(IdentityInfo identityInfo, SaveCdqStoreSkuRequest request) {
        if (!request.isQuoteForCreate()) {
            return new CommonResponse<>(ResponseCodeEnum.SUCCESS.getValue(), "操作成功", null);
        }
        QuoteCommitRequest quoteCommitRequest = OCMSUtils.convert2QuoteCommitRequest(identityInfo.getUser(), request, identityInfo.getOs());
        try {
            log.info("OCMSServiceWrapper.quote request:{}", quoteCommitRequest);
            QuoteCommitResponse quoteCommitResponse = quoteReviewThriftService.commitQuote(quoteCommitRequest);
            log.info("OCMSServiceWrapper.quote response:{}", quoteCommitResponse);
            SaveCdqStoreSkuPartitionSuccessResponse partitionSuccessResponse = new SaveCdqStoreSkuPartitionSuccessResponse();
            if (quoteCommitResponse.getCode() != ResultCode.SUCCESS.getCode()) {
                partitionSuccessResponse.setErrorCode(ErrorCodeEnum.QUOTE_FAILED.getCode());
                partitionSuccessResponse.setErrorMsg(ErrorCodeEnum.QUOTE_FAILED.getMessage());
            } else {
                partitionSuccessResponse.setErrorCode(ErrorCodeEnum.QUOTE_REVIEWING.getCode());
                partitionSuccessResponse.setErrorMsg(ErrorCodeEnum.QUOTE_REVIEWING.getMessage());
            }
            return new CommonResponse<>(ResponseCodeEnum.SUCCESS.getValue(), "操作成功", partitionSuccessResponse);
        } catch (Exception e) {
            log.warn("创建门店商品成功，报价异常，request:{}", quoteCommitRequest, e);
            return new CommonResponse<>(ResponseCodeEnum.SUCCESS.getValue(), "操作成功", null);
        }
    }

    public CommonResponse<QueryStoreOnlineSkuDetailResponse> queryDetailStoreOnlineSku(QueryCdqStoreSkuDetailRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        CdqStoreSkuDetailQueryRequest cdqStoreSkuDetailQueryRequest = OCMSUtils.convertRequest(request, user);
        log.info("OCMSServiceWrapper.queryDetailStoreOnlineSku  cdqStoreSkuThriftService.detailStoreOnlineSku() request:{}", cdqStoreSkuDetailQueryRequest);
        try {
            CdqStoreSkuDetailQueryResponse cdqStoreSkuDetailQueryResponse = cdqStoreSkuThriftService.queryStoreSkuDetail(cdqStoreSkuDetailQueryRequest);
            log.info("OCMSServiceWrapper.queryDetailStoreOnlineSku  cdqStoreSkuThriftService.detailStoreOnlineSku() response:{}", cdqStoreSkuDetailQueryResponse);

            QueryStoreOnlineSkuDetailResponse queryStoreOnlineSkuDetailResponse = OCMSUtils.convertQueryStoreOnlineSkuDetailResponse(cdqStoreSkuDetailQueryResponse);

            // 设置门店商品价格趋势信息
            fillStoreSkuWithChannelPriceTrendInfo(user, queryStoreOnlineSkuDetailResponse);

            return new CommonResponse(cdqStoreSkuDetailQueryResponse.getCode(), cdqStoreSkuDetailQueryResponse.getMsg(), queryStoreOnlineSkuDetailResponse);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private void fillStoreSkuWithChannelPriceTrendInfo(User user, QueryStoreOnlineSkuDetailResponse response) {
        try {
            if (response == null || response.getStoreSku() == null) {
                return;
            }

            CdqStoreSkuWithChannelInfoVo storeSkuWithChannelInfoVo = response.getStoreSku();
            CdqStoreSkuVo storeSkuVO = storeSkuWithChannelInfoVo.getCdqStoreSkuVo();
            List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVos = storeSkuWithChannelInfoVo.getChannelSkuDetailInfoVos();
            if (storeSkuVO == null || CollectionUtils.isEmpty(channelSkuDetailInfoVos)) {
                return;
            }

            // 查询用户价格趋势权限
            Map<String, Boolean> priceTrendPermissionMap = authThriftWrapper.isHasPermission(
                    PriceTrendConstants.getPriceTrendPermissionCodes());

            // 查询门店商品价格过滤器
            StoreSkuPriceFilterCondition filterCondition = StoreSkuPriceFilterCondition.build(storeSkuVO);
            StoreSkuPriceFilter storeSkuPriceFilter =
                    saasPriceServiceWrapper.queryStoreSkuPriceFilter(user.getTenantId(), filterCondition);

            // 查询门店商品价格趋势
            StoreSkuWithChannelPriceTrendVO storeSkuWithChannelPriceTrendVO = null;
            try {
                storeSkuWithChannelPriceTrendVO = ocmsPriceTrendWrapper.queryStoreSkuPriceTrend(user.getTenantId(),
                        storeSkuPriceFilter, PriceTrendConstants.STORE_SKU_PRICE_TREND_DEFAULT_QUERY_DAYS);
            } catch (Exception e) {
                log.error("查询门店商品价格趋势错误", e);
            }

            // 设置门店商品市斤价及价格趋势信息
            storeSkuWithChannelInfoVo.fillStoreSkuWithChannelPriceTrendInfo(storeSkuPriceFilter,
                    storeSkuWithChannelPriceTrendVO, priceTrendPermissionMap);
        } catch (Exception e) {
            log.error("设置门店商品市斤价及价格趋势信息错误", e);
        }
    }

    public CommonResponse<ChangeChannelSkuFrontCategoryResponse> changeChannelSkuFrontCategory(ChangeChannelSkuFrontCategoryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        ChangeStoreSkuChannelFrontCategoryRequest changeStoreSkuChannelFrontCategoryRequest = OCMSUtils.convertRequest(request, user);
        log.info("OCMSServiceWrapper.changeChannelSkuFrontCategory  channelSkuThriftService.changeChannelSkuFrontCategory() request:{}", changeStoreSkuChannelFrontCategoryRequest);
        try {
            TaskResponse taskResponse = channelSkuThriftService.changeChannelSkuFrontCategory(changeStoreSkuChannelFrontCategoryRequest);
            log.info("OCMSServiceWrapper.changeChannelSkuFrontCategory  channelSkuThriftService.changeChannelSkuFrontCategory() response:{}", taskResponse);
            return new CommonResponse<>(taskResponse.getCode(), taskResponse.getMsg(), OCMSUtils.convertChangeChannelSkuFrontCategoryResponse(taskResponse));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse<String> saveSkuReviewApply(SkuReviewApplySaveRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        long tenantId = user.getTenantId();
        SubmitTenantProductReviewRequest submitTenantProductReviewRequest = request.convertBizRequest(user);

        List<Long> poiIds = new ArrayList<>();
        //摊主账号需要查询摊位对应的门店
        if (AccountTypeEnum.BOOTH.getValue() == user.getAccountType()) {
            List<Long> boothIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.BOOTH, Long::valueOf);
            if (CollectionUtils.isNotEmpty(boothIds)) {
                BoothListResponse boothListResponse = boothThriftService.queryBoothListByBoothIds(user.getTenantId(), boothIds);
                List<BoothInfoDto> boothInfoDtos = new ArrayList<>();
                if (Objects.nonNull(boothListResponse) && ResultCode.SUCCESS.getCode() == boothListResponse.getStatus().getCode()) {
                    boothInfoDtos.addAll(boothListResponse.getBoothList());
                }
                poiIds.addAll(boothInfoDtos.stream().map(BoothInfoDto::getPoiId).collect(Collectors.toList()));
            }
        } else {
            //普通账号直接查询门店权限
            poiIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI, Long::valueOf);
        }

        if (CollectionUtils.isEmpty(poiIds) || !poiIds.contains(submitTenantProductReviewRequest.getStoreId())) {
            return new CommonResponse<>(ResponseCodeEnum.FAILED.getValue(), "账号没有该门店权限", null);
        }
        if (MccConfigUtil.isProductReviewIgnoreSupplyPurchase(tenantId)) {
            // 命中灰度开关时，清空供货关系信息
            log.info("OCMSServiceWrapper.saveSkuReviewApply productReviewIgnoreSupplyPurchase tenantId={}, ignoreSupplyPurchase=true",
                    tenantId);
            if (submitTenantProductReviewRequest.getSkuList() != null) {
                submitTenantProductReviewRequest.getSkuList().forEach(sku -> {
                    if (sku != null) {
                        sku.setSupplyPurchase(null);
                    }
                });
            }
        }
        log.info("productReviewBizThriftService.submitTenantProductReview request:{}", JacksonUtils.toJson(submitTenantProductReviewRequest));
        try {
            SubmitTenantProductReviewResponse rpcResp = productReviewBizThriftService.submitTenantProductReview(submitTenantProductReviewRequest);
            log.info("productReviewBizThriftService.submitTenantProductReview response:{}", JacksonUtils.toJson(rpcResp));
            if (rpcResp.getStatus() == null || rpcResp.getStatus().getCode() == null) {
                throw new CommonRuntimeException("提报异常");
            }
            if (rpcResp.getStatus().getCode() != 0) {
                return new CommonResponse<>(rpcResp.getStatus().getCode(), rpcResp.getStatus().getMsg(), rpcResp.getDupSkuId());
            }
            // 使用MCC配置控制是否创建供货关系
            if (!MccConfigUtil.isProductReviewIgnoreSupplyPurchase(tenantId) &&
                    StringUtils.isNotBlank(rpcResp.getCreateSpuId()) &&
                    Boolean.TRUE.equals(rpcResp.getCreateStoreSpu()) &&
                    CollectionUtils.isNotEmpty(rpcResp.getSupplyPurchaseList())) {
                log.info("OCMSServiceWrapper.saveSkuReviewApply productReviewNotIgnoreSupplyPurchase tenantId={}, ignoreSupplyPurchase=false",
                        tenantId);
                CommonResponse<Void> createSupplyRelationResp = createSupplyRelationAfterAutoPass(tenantId, request,
                        rpcResp.getCreateSpuId(), user);
                if (!createSupplyRelationResp.isSuccess()) {
                    return new CommonResponse<>(createSupplyRelationResp.getCode(), createSupplyRelationResp.getMessage(), null);
                }
            }
            return new CommonResponse<>(rpcResp.getStatus().getCode(), rpcResp.getStatus().getMsg(), rpcResp.getDupSkuId());
        }
        catch (Exception e) {
            log.error("productReviewBizThriftService.submitTenantProductReview error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private CommonResponse<Void> createSupplyRelationAfterAutoPass(long tenantId, SkuReviewApplySaveRequest request, String createSpuId, User user) {
        List<SupplyRelationAndPurchaseVO> purchaseInfoList = request.getSkuList().stream()
                .filter(sku -> sku.getPurchaseInfo() != null)
                .map(sku -> {
                    SupplyRelationAndPurchaseVO purchaseVO = new SupplyRelationAndPurchaseVO();
                    purchaseVO.setSpec(sku.getSpecName());
                    purchaseVO.setPurchaseStatus(sku.getPurchaseInfo().getPurchaseStatus());
                    purchaseVO.setPurchaseType(sku.getPurchaseInfo().getPurchaseType());
                    purchaseVO.setSupplyRelations(sku.getPurchaseInfo().getSupplyRelations());
                    return purchaseVO;
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(purchaseInfoList)) {
            return CommonResponse.success(null);
        }
        // 无箱规编码，因此manualCreate为true
        return supplyRelationService.createSpuSupplyRelation(tenantId, request.getStoreId(), createSpuId, purchaseInfoList, true,
                SimpleUser.build(user), true);
    }


    public CommonResponse<SkuReviewListQueryResponse> querySkuReviewList(SkuReviewListQueryRequest request) {
        return querySkuReviewList(request, ApiMethodParamThreadLocal.getInstance().get().getUser());
    }

    public CommonResponse<SkuReviewListQueryResponse> querySkuReviewList(SkuReviewListQueryRequest request, User user) {
        QuerySkuReviewListRequest querySkuReviewListRequest = request.convertQuerySkuReviewListRequest(user);

        //搜索和列表分开处理，如果指定搜索门店，则不需要查权限
        if (CollectionUtils.isEmpty(request.getStoreIdSet())) {
            try {
                //查询账号的门店数据权限
                QueryPermissionGroupRequest permissionGroupRequest = new QueryPermissionGroupRequest();
                permissionGroupRequest.setTenantId(user.getTenantId());
                permissionGroupRequest.setAccountId(user.getAccountId());
                permissionGroupRequest.setType(PermissionGroupTypeEnum.POI.getValue());
                QueryPermissionGroupResponse permissionGroupResponse = authThriftService.queryPermissionGroupList(permissionGroupRequest);
                List<PermissionGroupVo> permissionGroupVos = new ArrayList<>();
                if (null != permissionGroupResponse && permissionGroupResponse.getResult().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
                    permissionGroupVos = permissionGroupResponse.getPermissionGroupCodeList();
                }
                if (CollectionUtils.isNotEmpty(permissionGroupVos)) {
                    List<Long> storeIdSet = permissionGroupVos.stream().map(PermissionGroupVo::getCode).map(Long::valueOf).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(storeIdSet)) {
                        log.info("账号门店权限，ids:{}", storeIdSet);
                        querySkuReviewListRequest.setStoreIdSet(Sets.newHashSet(storeIdSet));
                    }
                }
            } catch (TException e) {
                log.error("authThriftService.queryPermissionGroupList error, TException ：", e);
                throw new CommonRuntimeException(e);
            }
        }

        log.info("OCMSServiceWrapper.querySkuReviewList  tenantSkuReviewThriftService.querySkuReviewList() request:{}", querySkuReviewListRequest);
        try {
            QuerySkuReviewListResponse querySkuReviewListResponse = tenantSkuReviewThriftService.querySkuReviewList(querySkuReviewListRequest);
            log.info("OCMSServiceWrapper.querySkuReviewList  tenantSkuReviewThriftService.querySkuReviewList() response:{}", querySkuReviewListResponse);
            return new CommonResponse(querySkuReviewListResponse.getCode(), querySkuReviewListResponse.getMsg(), new SkuReviewListQueryResponse().buildSkuReviewListQueryResponse(querySkuReviewListResponse));
        } catch (TException e) {
            log.error("tenantSkuReviewThriftService.querySkuReviewList error, TException ：", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<SkuReviewInfoGetResponse> getSkuReviewInfo(SkuReviewInfoGetRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        GetSkuReviewInfoRequest getSkuReviewInfoRequest = request.convertGetSkuReviewInfoRequest(user);
        log.info("OCMSServiceWrapper.getSkuReviewInfo  tenantSkuReviewThriftService.getSkuReviewInfo() request:{}", getSkuReviewInfoRequest);
        try {
            GetSkuReviewInfoResponse getSkuReviewInfoResponse = tenantSkuReviewThriftService.getSkuReviewInfo(getSkuReviewInfoRequest);
            log.info("OCMSServiceWrapper.getSkuReviewInfo  tenantSkuReviewThriftService.getSkuReviewInfo() response:{}", getSkuReviewInfoResponse);
            return new CommonResponse(getSkuReviewInfoResponse.getCode(), getSkuReviewInfoResponse.getMsg(), new SkuReviewInfoGetResponse().buildSkuReviewInfoGetResponse(getSkuReviewInfoResponse));
        } catch (TException e) {
            log.error("tenantSkuReviewThriftService.getSkuReviewInfo error, TException:{}", e);
            throw new CommonRuntimeException(e);
        }

    }

    public CommonResponse<SkuStoreToReviewCountGetResponse> getStoreToReviewCount(SkuStoreToReviewCountGetRequest request) {
        int count = 0, pageSize = 10;
        log.info("OCMSServiceWrapper.getStoreToReviewCount tenantSkuReviewThriftService.getSkuReviewInfo() request:{}", request);
        try {
            SkuReviewListQueryRequest queryRequest = new SkuReviewListQueryRequest();
            queryRequest.setTenantId(request.getTenantId());
            queryRequest.setStoreIdSet(Sets.newHashSet(request.getStoreId()));
            queryRequest.setPageNum(INITIAL_PAGE);
            queryRequest.setPageSize(pageSize);
            // 查询待审核列表
            queryRequest.setReviewStatusList(Lists.newArrayList(ReviewStatusEnum.TO_REVIEW.getValue()));

            CommonResponse<SkuReviewListQueryResponse> response = querySkuReviewList(queryRequest);
            if (response != null && response.getData() != null && response.getData().getPageInfoVO() != null) {
                PageInfoVO pageInfoVO = response.getData().getPageInfoVO();
                count = pageInfoVO.getTotalSize();
            }

            log.info("OCMSServiceWrapper.getStoreToReviewCount tenantSkuReviewThriftService.setReviewStatusList() response:{}", response);
            return new CommonResponse(response.getCode(), response.getMessage(), new SkuStoreToReviewCountGetResponse().build(count));
        } catch (Exception e) {
            log.error("tenantSkuReviewThriftService.getSkuReviewInfo error, TException:{}", e);
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse<BatchSkuReviewRecallApplyResponse> batchRecallApply(BatchSkuReviewRecallApplyRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        SkuReviewBatchRecallApplyRequest skuReviewBatchRecallApplyRequest = request.convertSkuReviewBatchRecallApplyRequest(user);
        log.info("OCMSServiceWrapper.batchRecallApply  tenantSkuReviewThriftService.batchRecallApply() request:{}", skuReviewBatchRecallApplyRequest);
        try {
            BatchRecallResponse batchRecallResponse = tenantSkuReviewThriftService.batchRecallApply(skuReviewBatchRecallApplyRequest);
            log.info("OCMSServiceWrapper.batchRecallApply  tenantSkuReviewThriftService.batchRecallApply() response:{}", batchRecallResponse);
            return new CommonResponse(batchRecallResponse.getCode(), batchRecallResponse.getMsg(), new BatchSkuReviewRecallApplyResponse().buildBatchSkuReviewRecallApplyResponse(batchRecallResponse));
        } catch (TException e) {
            log.error("tenantSkuReviewThriftService.batchRecallApply error, TException:{}", e);
            throw new CommonRuntimeException(e);
        }

    }


    public CommonResponse saveReasonTemplate(ReasonTemplateSaveRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        SaveReasonTemplateRequest saveReasonTemplateRequest = request.convertSaveReasonTemplateRequest(user);
        log.info("OCMSServiceWrapper.saveReasonTemplate  reviewReasonTemplateThriftService.saveReasonTemplate() request:{}", saveReasonTemplateRequest);
        try {
            BaseResponse baseResponse = reviewReasonTemplateThriftService.saveReasonTemplate(saveReasonTemplateRequest);
            log.info("OCMSServiceWrapper.saveReasonTemplate  reviewReasonTemplateThriftService.saveReasonTemplate() response:{}", baseResponse);
            return new CommonResponse(baseResponse.getCode(), baseResponse.getMsg(), null);
        } catch (TException e) {
            log.error("reviewReasonTemplateThriftService.saveReasonTemplate error, TException:{}", e);
            throw new CommonRuntimeException(e);
        }

    }

    public CommonResponse<ReasonTemplateBatchSaveResponse> batchSaveReasonTemplate(ReasonTemplateBatchSaveRequest request) {
        try {
            request.selfCheck();
            ReasonTemplateBatchSaveResponse reasonTemplateBatchSaveResponse = new ReasonTemplateBatchSaveResponse();
            reasonTemplateBatchSaveResponse.setFailList(new ArrayList<>());
            User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
            List<SaveReasonTemplateRequest> requestList = request.toSaveReasonTemplateRequestList(user);
            List<Callable<BaseResponse>> callableList = Fun.map(requestList,
                    saveReq -> () -> reviewReasonTemplateThriftService.saveReasonTemplate(saveReq));
            List<Future<BaseResponse>> futureList = Fun.map(callableList, createSupplyRelationAfterSpuCreateThreadPool::submit);
            for (int i = 0; i < futureList.size(); i++) {
                SaveReasonTemplateRequest saveReq = requestList.get(i);
                try {
                    Future<BaseResponse> future = futureList.get(i);
                    BaseResponse baseResponse = future.get();
                    if (Objects.isNull(baseResponse) || !Objects.equals(baseResponse.getCode(), Status.SUCCESS_CODE)) {
                        log.error("saveReasonTemplate fail, saveReq:{}, saveRes:{}", saveReq, baseResponse);
                        ReasonTemplateBatchSaveResponse.FailItem failItem = new ReasonTemplateBatchSaveResponse.FailItem();
                        failItem.setReason(saveReq.getReason());
                        failItem.setMsg(Objects.isNull(baseResponse) ? "NO_RESPONSE" : baseResponse.getMsg());
                        reasonTemplateBatchSaveResponse.getFailList().add(failItem);
                    }
                }
                catch (Exception e) {
                    log.error("saveReasonTemplate exception, saveReq:{}", saveReq, e);
                    ReasonTemplateBatchSaveResponse.FailItem failItem = new ReasonTemplateBatchSaveResponse.FailItem();
                    failItem.setReason(saveReq.getReason());
                    failItem.setMsg("未知异常");
                    reasonTemplateBatchSaveResponse.getFailList().add(failItem);
                }
            }
            return CommonResponse.success(reasonTemplateBatchSaveResponse);
        }
        catch (IllegalArgumentException e) {
            log.error("batchSaveReasonTemplate 参数异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        }
        catch (Exception e) {
            log.error("batchSaveReasonTemplate 未知异常, request:{}", request, e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }


    public CommonResponse deleteReasonTemplate(ReasonTemplateDeleteRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        DeleteReasonTemplateRequest deleteReasonTemplateRequest = request.convertDeleteReasonTemplateRequest(user);
        log.info("OCMSServiceWrapper.deleteReasonTemplate  reviewReasonTemplateThriftService.deleteReasonTemplate() request:{}", deleteReasonTemplateRequest);
        try {
            BaseResponse baseResponse = reviewReasonTemplateThriftService.deleteReasonTemplate(deleteReasonTemplateRequest);
            log.info("OCMSServiceWrapper.deleteReasonTemplate  reviewReasonTemplateThriftService.deleteReasonTemplate() response:{}", baseResponse);
            return new CommonResponse(baseResponse.getCode(), baseResponse.getMsg(), null);
        } catch (TException e) {
            log.error("reviewReasonTemplateThriftService.deleteReasonTemplate error, TException:{}", e);
            throw new CommonRuntimeException(e);
        }

    }

    public CommonResponse batchDeleteReasonTemplate(ReasonTemplateBatchDeleteRequest request) {
        try {
            request.selfCheck();
            User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
            List<DeleteReasonTemplateRequest> requestList = request.toDeleteReasonTemplateRequestList(user);
            List<Callable<BaseResponse>> callableList = Fun.map(requestList,
                    deleteReq -> () -> reviewReasonTemplateThriftService.deleteReasonTemplate(deleteReq));
            List<Future<BaseResponse>> futureList = Fun.map(callableList, createSupplyRelationAfterSpuCreateThreadPool::submit);
            boolean isAllSuccess = true;
            for (int i = 0; i < futureList.size(); i++) {
                try {
                    Future<BaseResponse> future = futureList.get(i);
                    BaseResponse baseResponse = future.get();
                    if (Objects.isNull(baseResponse) || !Objects.equals(baseResponse.getCode(), Status.SUCCESS_CODE)) {
                        log.error("deleteReasonTemplate fail, deleteReq:{}, deleteRes:{}", requestList.get(i), baseResponse);
                        isAllSuccess = false;
                    }
                }
                catch (Exception e) {
                    log.error("deleteReasonTemplate exception, deleteReq:{}", requestList.get(i), e);
                    // 失败一个认为批量接口都失败
                    throw new CommonRuntimeException("删除模版异常");
                }
            }
            return isAllSuccess ? new CommonResponse<>(Status.SUCCESS_CODE, Status.SUCCESS_MSG, null)
                    : new CommonResponse<>(Status.FAILED_CODE, Status.FAILED_MSG, null);
        }
        catch (IllegalArgumentException e) {
            log.error("batchDeleteReasonTemplate 参数异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        }
        catch (Exception e) {
            log.error("batchDeleteReasonTemplate 未知异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }


    public CommonResponse<ReasonTemplateGetResponse> getReasonTemplate(ReasonTemplateGetRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        GetReasonTemplateRequest getReasonTemplateRequest = request.convertGetReasonTemplateRequest(user);
        log.info("OCMSServiceWrapper.getReasonTemplate  reviewReasonTemplateThriftService.getReasonTemplate() request:{}", getReasonTemplateRequest);
        try {
            GetReasonTemplateResponse getReasonTemplateResponse = reviewReasonTemplateThriftService.getReasonTemplate(getReasonTemplateRequest);
            log.info("OCMSServiceWrapper.getReasonTemplate  reviewReasonTemplateThriftService.getReasonTemplate() response:{}", getReasonTemplateResponse);
            return new CommonResponse(getReasonTemplateResponse.getCode(), getReasonTemplateResponse.getMsg(), new ReasonTemplateGetResponse().buildReasonTemplateGetResponse(getReasonTemplateResponse));
        } catch (TException e) {
            log.error("reviewReasonTemplateThriftService.getReasonTemplate error, TException:{}", e);
            throw new CommonRuntimeException(e);
        }

    }


    public CommonResponse<QueryReasonTemplateListResponse> queryReasonTemplateList(ReasonTemplateListQueryRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        QueryReasonTemplateListRequest queryReasonTemplateListRequest = request.convertQueryReasonTemplateListRequest(user);
        log.info("OCMSServiceWrapper.queryReasonTemplateList  reviewReasonTemplateThriftService.queryReasonTemplateList() request:{}", queryReasonTemplateListRequest);
        try {
            QueryReasonTemplateListResponse queryReasonTemplateListResponse = reviewReasonTemplateThriftService.queryReasonTemplateList(queryReasonTemplateListRequest);
            log.info("OCMSServiceWrapper.queryReasonTemplateList  reviewReasonTemplateThriftService.queryReasonTemplateList() response:{}", queryReasonTemplateListResponse);
            return new CommonResponse(queryReasonTemplateListResponse.getCode(), queryReasonTemplateListResponse.getMsg(), new ReasonTemplateListQueryResponse().buildReasonTemplateListQueryResponse(queryReasonTemplateListResponse));
        } catch (TException e) {
            log.error("reviewReasonTemplateThriftService.queryReasonTemplateList error, TException:{}", e);
            throw new CommonRuntimeException(e);
        }

    }


    public CommonResponse<ReviewBatchResponse> batchReview(ReviewBatchRequest request) {
        if (request.getReviewBizType() != 2) {
            BatchReviewRequest batchReviewRequest = request.convertBatchReviewRequest();
            log.info("OCMSServiceWrapper.batchReview  channelThriftService.batchReview() request:{}", batchReviewRequest);
            try {
                BatchReviewResponse batchReviewResponse = channelThriftService.batchReview(batchReviewRequest);
                log.info("OCMSServiceWrapper.batchReview  channelThriftService.batchReview() response:{}", batchReviewResponse);
                return new CommonResponse(batchReviewResponse.getCode(), batchReviewResponse.getMsg(), new ReviewBatchResponse().buildReviewBatchResponse(request.getReviewBizType(), batchReviewResponse));
            }
            catch (TException e) {
                log.error("reviewReasonTemplateThriftService.batchReview error, TException:", e);
                throw new CommonRuntimeException(e);
            }
        }
        else {
            // 走productbiz的门店提报审核
            User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
            BatchReviewTenantProductRequest batchReviewRequest = request.convertTenantBatchReviewRequest(user);
            log.info("productReviewBizThriftService.batchReviewTenantProduct request:{}", JacksonUtils.toJson(batchReviewRequest));
            try {
                BatchReviewTenantProductResponse rpcResp = productReviewBizThriftService.batchReviewTenantProduct(batchReviewRequest);
                log.info("productReviewBizThriftService.batchReviewTenantProduct response:{}", rpcResp);
                if (rpcResp.getStatus() == null || rpcResp.getStatus().getCode() == null) {
                    return CommonResponse.fail(-1, "审核异常");
                }
                if (rpcResp.getData() == null) {
                    return CommonResponse.fail(rpcResp.getStatus().getCode(), rpcResp.getStatus().getMsg());
                }
                List<BatchReviewTenantProductResponse.SuccessItem> successList = rpcResp.getData().getSuccessList();
                List<ReviewFailedVO> createSupplyRelationFailList = new ArrayList<>();
                // 使用MCC配置控制是否创建供货关系
                if (!MccConfigUtil.isProductReviewIgnoreSupplyPurchase(request.getTenantId()) &&
                        CollectionUtils.isNotEmpty(successList)) {
                    log.info("OCMSServiceWrapper.batchReview productReviewNotIgnoreSupplyPurchase tenantId={}, ignoreSupplyPurchase=false",
                            request.getTenantId());
                    // 注意这儿不需要判断是通过操作，通过下面条件过滤的必定是通过
                    List<BatchReviewTenantProductResponse.SuccessItem> waitCreateSupplyRelationList = Fun.filter(successList,
                            item -> StringUtils.isNotBlank(item.getSpuId()) &&
                                    Boolean.TRUE.equals(item.getCreateStoreSpu()) &&
                                    CollectionUtils.isNotEmpty(item.getSupplyPurchaseList()));
                    createSupplyRelationFailList.addAll(createSupplyRelationAfterReviewPass(batchReviewRequest.getTenantId(), waitCreateSupplyRelationList));
                }

                return new CommonResponse(rpcResp.getStatus().getCode(), rpcResp.getStatus().getMsg(), ReviewBatchResponse.buildTenantReviewBatchResponse(rpcResp, createSupplyRelationFailList));
            }
            catch (Exception e) {
                log.error("productReviewBizThriftService.batchReviewTenantProduct error", e);
                throw new CommonRuntimeException(e);
            }
        }
    }

    private List<ReviewFailedVO> createSupplyRelationAfterReviewPass(long tenantId, List<BatchReviewTenantProductResponse.SuccessItem> waitCreateSupplyRelationList) {
        if (CollectionUtils.isEmpty(waitCreateSupplyRelationList)) {
            return Collections.emptyList();
        }
        try {
            List<Callable<CommonResponse<Void>>> tasks = Fun.map(waitCreateSupplyRelationList, waitCreate -> () -> {
                List<SupplyRelationAndPurchaseVO> purchaseInfoList = waitCreate.getSupplyPurchaseList().stream()
                        .map(feedbackSku -> {
                            SupplyRelationAndPurchaseVO purchaseVO = new SupplyRelationAndPurchaseVO();
                            purchaseVO.setSpec(feedbackSku.getSpecName());
                            purchaseVO.setPurchaseStatus(feedbackSku.getPurchaseStatus());
                            purchaseVO.setPurchaseType(feedbackSku.getPurchaseType());
                            purchaseVO.setSupplyRelations(Fun.map(feedbackSku.getSupplyRelations(), feedbackRelation -> {
                                SupplyRelationAndPurchaseVO.SkuSupplyRelationVO vo = new SupplyRelationAndPurchaseVO.SkuSupplyRelationVO();
                                vo.setSupplierId(feedbackRelation.getSupplierId());
                                vo.setSupplyUnit(feedbackRelation.getSupplyUnit());
                                vo.setSupplyUnitRatio(feedbackRelation.getSupplyUnitRatio());
                                vo.setSupplyUnitPrice(feedbackRelation.getSupplyUnitPrice());
                                vo.setMinOrderQuantity(feedbackRelation.getMinOrderQuantity());
                                vo.setMasterFlag(feedbackRelation.getMasterFlag());
                                return vo;
                            }));
                            return purchaseVO;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(purchaseInfoList)) {
                    return CommonResponse.success(null);
                }
                Long submitOperatorId = waitCreate.getSubmitOperatorId();
                if (submitOperatorId == null) {
                    return CommonResponse.success(null);
                }
                Map<Long, AccountUserDto> accountUserDtoMap = accountService.queryTenantUserByAccountIds(tenantId, Collections.singletonList(submitOperatorId));
                if (MapUtils.isEmpty(accountUserDtoMap)) {
                    return CommonResponse.fail(ResultCode.FAIL, "创建供货关系或采购属性失败（无法获取用户信息）");
                }
                SimpleUser submitUser = SimpleUser.buildByAccountUserDto(tenantId, accountUserDtoMap.values().iterator().next());
                // 无箱规编码，因此manualCreate为true
                return supplyRelationService.createSpuSupplyRelation(tenantId, waitCreate.getStoreId(), waitCreate.getSpuId(), purchaseInfoList,
                        true, submitUser, true);
            });
            List<Future<CommonResponse<Void>>> futureList = createSupplyRelationAfterSpuCreateThreadPool.invokeAll(tasks);

            List<ReviewFailedVO> reviewFailedVOList = new ArrayList<>();
            for (int i = 0; i < futureList.size(); i++) {
                BatchReviewTenantProductResponse.SuccessItem waitCreate = waitCreateSupplyRelationList.get(i);
                Future<CommonResponse<Void>> future = futureList.get(i);
                try {
                    CommonResponse<Void> createSupplyResp = future.get();
                    if (!createSupplyResp.isSuccess()) {
                        ReviewFailedVO failedVO = new ReviewFailedVO();
                        failedVO.setBizId(waitCreate.getReviewId());
                        failedVO.setName(waitCreate.getName());
                        failedVO.setReason(createSupplyResp.getMessage());
                        reviewFailedVOList.add(failedVO);
                    }
                }
                catch (Exception e) {
                    log.error("get createSupplyResp fail", e);
                    ReviewFailedVO failedVO = new ReviewFailedVO();
                    failedVO.setBizId(waitCreate.getReviewId());
                    failedVO.setName(waitCreate.getName());
                    failedVO.setReason("创建供货关系或采购属性失败");
                    reviewFailedVOList.add(failedVO);
                }
            }
            return reviewFailedVOList;
        }
        catch (Exception e) {
            log.error("createSupplyRelationAfterReviewPass fail", e);
            return Fun.map(waitCreateSupplyRelationList, waitCreate -> {
                ReviewFailedVO failedVO = new ReviewFailedVO();
                failedVO.setBizId(waitCreate.getReviewId());
                failedVO.setName(waitCreate.getName());
                failedVO.setReason("创建供货关系或采购属性失败");
                return failedVO;
            });
        }
    }


    public CommonResponse<SkuReviewListQueryResponse> querySkuReviewApplyList(SkuReviewApplyListQueryRequest request) {

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        QuerySkuReviewApplyListRequest querySkuReviewApplyListRequest = request.convertQuerySkuReviewApplyListRequest(user);
        log.info("OCMSServiceWrapper.querySkuReviewApplyList  tenantSkuReviewThriftService.querySkuReviewApplyList() request:{}", querySkuReviewApplyListRequest);
        try {
            QuerySkuReviewListResponse querySkuReviewListResponse = tenantSkuReviewThriftService.querySkuReviewApplyList(querySkuReviewApplyListRequest);
            log.info("OCMSServiceWrapper.querySkuReviewApplyList  channelThriftService.querySkuReviewApplyList() response:{}", querySkuReviewListResponse);
            return new CommonResponse(querySkuReviewListResponse.getCode(), querySkuReviewListResponse.getMsg(), new SkuReviewListQueryResponse().buildSkuReviewListQueryResponse(querySkuReviewListResponse));
        } catch (TException e) {
            log.error("tenantSkuReviewThriftService.querySkuReviewApplyList error, TException:", e);
            throw new CommonRuntimeException(e);
        }

    }

    /**
     * 总部审核模块角标
     *
     * @param storeIds
     * @param user
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Integer> querySkuReviewingCountList(List<Long> storeIds, User user) {
        SkuReviewListQueryRequest request = new SkuReviewListQueryRequest();
        // 总部审核模块角标查询当前账号下所有门店（有数据权限）
        request.setStoreIdSet(null);
        request.setTenantId(user.getTenantId());
        request.setReviewStatusList(Lists.newArrayList(ReviewStatusEnum.TO_REVIEW.getValue()));
        // 主要是查总数，所以pageSize和pageNum都是1
        request.setPageSize(1);
        request.setPageNum(1);

        // CommonResponse<SkuReviewListQueryResponse> response = querySkuReviewList(request, user);
        CommonResponse<SkuReviewListQueryResponse> response = productBizServiceWrapper.queryProductReviewList(request);
        int totalCount = 0;
        if (response.getData() != null && response.getData().getPageInfoVO() != null) {
            totalCount = response.getData().getPageInfoVO().getTotalSize();
        }
        return new CommonResponse<>(response.getCode(), response.getMessage(), totalCount);
    }


    public CommonResponse<List<SkuTagCategoryVO>> queryTags() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        QueryTenantTagRequest queryTenantTagRequest = new QueryTenantTagRequest(user.getTenantId());
        log.info("OCMSServiceWrapper.queryTags  tagThriftService.queryTags() request:{}", queryTenantTagRequest);
        try {
            TenantTagResponse response = tagThriftService.queryTags(queryTenantTagRequest);
            log.info("OCMSServiceWrapper.queryTags  tagThriftService.queryTags() response:{}", response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), OCMSUtils.convertTenantTagResponse(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryTenantRegionSpuByNameResponseVO> queryTenantRegionSpuByName(QueryTenantRegionSpuByNameRequest request) {
        if(StringUtils.isBlank(request.getKeyword())){
            QueryTenantRegionSpuByNameResponseVO queryTenantRegionSpuByNameResponseVO=new QueryTenantRegionSpuByNameResponseVO();
            queryTenantRegionSpuByNameResponseVO.setPageInfo(PageInfoVO.initPageInfoVO(request.getPage(),request.getSize()));
            return CommonResponse.success(queryTenantRegionSpuByNameResponseVO);
        }
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        PageQueryTenantRegionSpuByNameRequest pageQueryReq = OCMSUtils.convertRequest(request, user);
        if (pageQueryReq == null) {
            throw new CommonRuntimeException("请求参数错误");
        }
        try {
            PoiMapResponse response = poiThriftService.queryPoiInfoMapByPoiIds(Lists.newArrayList(request.getStoreId()));
            if (response.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                Map<Long, PoiInfoDto> poiInfoMap = response.getPoiInfoMap();
                PoiInfoDto poiInfoDto = poiInfoMap.get(pageQueryReq.getTargetStoreId());
                if (poiInfoDto != null) {
                    pageQueryReq.setRegionCode(poiInfoDto.getDistrict().getCityId());
                }
            } else {
                log.error("调用poiThriftService.queryPoiInfoMapByPoiId失败，poiIds:{}", pageQueryReq.getTargetStoreId());
                throw new CommonRuntimeException(response.getStatus().getMessage());
            }
        } catch (Exception e) {
            log.error("调用poiThriftService.queryPoiInfoMapByPoiIds异常，poiIds:{}", pageQueryReq.getTargetStoreId(), e);
        }

        log.info("OCMSServiceWrapper.queryTenantRegionSpuByName  storeSpuThriftService.pageQueryTenantRegionSpuByName() request:{}", pageQueryReq);
        try {
            PageQueryTenantRegionSpuByNameResponse regionSpuByNameResponse = storeSpuThriftService.pageQueryTenantRegionSpuByName(pageQueryReq);
            log.info("OCMSServiceWrapper.queryTenantRegionSpuByName  storeSpuThriftService.pageQueryTenantRegionSpuByName() response:{}", regionSpuByNameResponse);
            return new CommonResponse(regionSpuByNameResponse.getCode(), regionSpuByNameResponse.getMsg(), OCMSUtils.convertSuggestStoreSpuListResponse(regionSpuByNameResponse));
        } catch (Exception e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<BatchChangeSpuStatusResponseVO> batchChangeStatus(BatchChangeSpuStatusRequest request, User user) {


        BatchUpdateStoreSpuStatusRequest batchUpdateStoreSpuStatusRequest = OCMSUtils.convertRequest(request, user);
        log.info("OCMSServiceWrapper.batchChangeStatus  调用storeSpuThriftService.batchUpdateStoreSpuStatus() request:{}", request);
        try {
            GeneralResponse response = storeSpuThriftService.batchUpdateStoreSpuStatus(batchUpdateStoreSpuStatusRequest);
            log.info("OCMSServiceWrapper.batchChangeStatus  storeSpuThriftService.batchUpdateStoreSpuStatus() response:{}", response);
            int code = response.getCode();
            if (code == CODE_CAN_RETRY || code == CODE_CANNOT_RETRY || code == ResponseCodeEnum.SPU_ON_SALE_CHECK_FAILED.getValue()) {
                code = 0;
            }
            return new CommonResponse(code, response.getMsg(), OCMSUtils.convertResponse(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<BatchChangeSpuStatusResponseVO> batchChangeQnhStatus(BatchChangeSpuStatusRequest request, User user) {
        log.info("OCMSServiceWrapper.batchChangeQnhStatus  调用qnhBizThriftService.batchChangeQnhStatus() request:{}", request);
        try {
            BatchUpdateStatusRequest updateStatusRequest = new BatchUpdateStatusRequest();
            updateStatusRequest.setTenantId(user.getTenantId());
            List<BatchUpdateProductDTO> products = Lists.newArrayList();
            for (String spuId : request.getSpuIdList()) {
                BatchUpdateProductDTO product = new BatchUpdateProductDTO();
                product.setStoreId(request.getStoreId());
                product.setSpuId(spuId);
                products.add(product);
            }
            updateStatusRequest.setProducts(products);
            updateStatusRequest.setStatus(request.getSpuStatus());
            updateStatusRequest.setOperatorId(user.getAccountId());
            updateStatusRequest.setOperatorName(user.getAccountName());

            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse response = qnhBizThriftService
                    .batchUpdateStatus(updateStatusRequest);
            log.info("OCMSServiceWrapper.batchChangeQnhStatus  qnhBizThriftService.batchChangeQnhStatus() response:{}", response);
            return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(), OCMSUtils
                    .convertResponse(response));
        } catch (Exception e) {
            throw new CommonRuntimeException(e);
        }
    }

    //start
    public CommonResponse updateStock(UpdateStockRequest request, User user) {
        boolean enableMultiLocation = tenantWrapper.isEnableMultiLocation(user.getTenantId(), request.getStoreId());
        Set<String> skuSet = request.getSkuStockList().stream().map(it -> it.getSkuId()).collect(Collectors.toSet());
        Map<String, Boolean> skuBatchEnableSwitchMap = empowerProductWrapper.querySkuBatchEnableSwitchMap(user.getTenantId(), request.getStoreId(), skuSet);

        BatchUpdateStoreStockRequest batchUpdateStoreStockRequest = RequestConvertUtils.convertRequest(request, user, enableMultiLocation, skuBatchEnableSwitchMap);

        log.info("OCMSServiceWrapper.updateStock  调用skuStoreStockThriftService.batchUpdateStoreStock() request:{}", request);
        try {
            com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CommonResponse response = skuStoreStockThriftService.batchUpdateStoreStock(batchUpdateStoreStockRequest);
            log.info("OCMSServiceWrapper.updateStock  skuStoreStockThriftService.batchUpdateStoreStock() response:{}", response);
            ResponseHandler.checkResponseAndStatus(response, com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CommonResponse::getCode,
                    com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CommonResponse::getMsg, ResultCode.FAIL);

            if (MccConfigUtil.isPushChannelSynchronouslyWhenUpdateStock()) {
                StockSyncChannelResponse syncChannelResponse = skuStoreStockThriftService.stockSyncChannelSynchronously(
                        RequestConvertUtils.convertToStockSyncRequest(request, user));
                ResponseHandler.checkResponseAndStatus(syncChannelResponse, StockSyncChannelResponse::getCode,
                        StockSyncChannelResponse::getMsg, ResultCode.FAIL);
            }

            return CommonResponse.success(null);
        }catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<QueryOnSaleAndOffSaleCountResponseVO> queryOnSaleAndOffSaleCount(QueryOnSaleAndOffSaleCountRequest request, User user, String boothId) {
        log.info("OCMSServiceWrapper.queryOnSaleAndOffSaleCount  调用storeSpuThriftService.querySpuCountByStatus() request:{}", request);
        List<Integer> statusList = Lists.newArrayList(ON_SALE,OFF_SALE,NOT_ONLINE);
        List<Future<Pair<Integer,StoreSpuCountResponse>>> futureList = Lists.newArrayList();
        //多线程查询
        for(Integer status:statusList){
            Future<Pair<Integer,StoreSpuCountResponse>> future = executorService.submit(()->{
                try {
                    StoreSpuCountRequest storeSpuCountRequest = RequestConvertUtils.convertStoreSpuCountRequest(request, user, boothId);
                    storeSpuCountRequest.getStoreSpuQueryByStatusDTO().setStatus(status);
                    return new Pair<>(status,storeSpuThriftService.querySpuCountByStatus(storeSpuCountRequest));
                } catch (TException e) {
                    throw new CommonRuntimeException(e);
                }
            });
            futureList.add(future);
        }
        //构造返回结果
        QueryOnSaleAndOffSaleCountResponseVO queryOnSaleAndOffSaleCountResponseVO = new QueryOnSaleAndOffSaleCountResponseVO();
        for (Future<Pair<Integer,StoreSpuCountResponse>> future : futureList) {
            try {
                Pair<Integer, StoreSpuCountResponse> pair = future.get();
                if(pair.getKey().equals(NOT_ONLINE)){
                    queryOnSaleAndOffSaleCountResponseVO.setNotOnlineCount(pair.getValue()!=null?pair.getValue().getCount():0);
                }else if(pair.getKey().equals(ON_SALE)){
                    queryOnSaleAndOffSaleCountResponseVO.setOnSaleCount(pair.getValue()!=null?pair.getValue().getCount():0);
                }else if(pair.getKey().equals(OFF_SALE)){
                    queryOnSaleAndOffSaleCountResponseVO.setOffSaleCount(pair.getValue()!=null?pair.getValue().getCount():0);
                }
            } catch (Exception e) {
                log.info("OCMSServiceWrapper.queryOnSaleAndOffSaleCount result error");
            }

        }
        return new CommonResponse(CODE_SUCCESS,"",queryOnSaleAndOffSaleCountResponseVO);
    }

    public CommonResponse<StoreSpuPageQueryResponseVO> pageQueryUnquoted(PageQueryOffSaleAndUnquotedRequest request, User user) {
        StoreSpuPageQueryByStatusRequest storeSpuPageQueryByStatusRequest = RequestConvertUtils.convertStoreSpuPageQueryByStatus(request, user);
        log.info("OCMSServiceWrapper.pageQueryUnquoted  调用StoreSpuThriftService.pageQuerySpuByStatus() request:{}", request);
        try {
            StoreSpuPageQueryResponse response = storeSpuThriftService.pageQuerySpuByStatus(storeSpuPageQueryByStatusRequest);
            log.info("OCMSServiceWrapper.pageQueryUnquoted  storeSpuThriftService.pageQuerySpuByStatus() response:{}", response);
            return new CommonResponse(response.getCode(), response.getMsg(), ResponseConvertUtils.convertStoreSpuPageQuery(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<BatchChangeSpuFrontCategoryResponseVO> batchChangeFrontCategory(BatchChangeSpuFrontCategoryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        BatchChangeStoreSpuFrontCategoryRequest batchChangeStoreSpuFrontCategoryRequest = OCMSUtils.convertRequest(request, user);
        log.info("OCMSServiceWrapper.batchChangeFrontCategory  storeSpuThriftService.batchChangeFrontCategory() request:{}", batchChangeStoreSpuFrontCategoryRequest);
        try {
            GeneralResponse response = storeSpuThriftService.batchChangeStoreSpuFrontCategory(batchChangeStoreSpuFrontCategoryRequest);
            log.info("OCMSServiceWrapper.batchChangeFrontCategory  storeSpuThriftService.batchChangeFrontCategory() response:{}", response);
            int code = response.getCode();
            if (code == CODE_CAN_RETRY || code == CODE_CANNOT_RETRY) {
                code = 0;
            }
            return new CommonResponse<>(code, response.getMsg(), OCMSUtils.convertChangeChannelSpuFrontCategoryResponse(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<TenantSpuSimpleVO> queryTenantSku(QueryTenantSkuBySpuIdApiRequest request, User user) {
            return queryTenantSkuFromBiz(request, user);
    }


    private CommonResponse<TenantSpuSimpleVO> queryTenantSkuFromBiz(QueryTenantSkuBySpuIdApiRequest request, User user) {
        TenantSpuDetailBizRequest rpcRequest = new TenantSpuDetailBizRequest();
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setSpuId(request.getSpuId());
        rpcRequest.setStoreId(request.getStoreId());
        rpcRequest.setIncludeCoveredStores(true);
        TenantSpuDetailBizResponse response = tenantSpuBizThriftService.queryTenantSpuDetail(rpcRequest);
        if (response.getStatus() == null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), ResultCode.INTERNAL_SERVER_ERROR.getErrorMessage());
        }
        if (!response.getStatus().isSuccess()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        if (response.getTenantSpuBizDTO() == null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), ResultCode.STORE_MANAGEMENT_EMPOWER_TASK_NO_SUCH_GOODS.getErrorMessage());
        }

        return CommonResponse.success(TenantSpuSimpleVO.of(response.getTenantSpuBizDTO()));
    }

    public CommonResponse<List<TenantSkuVO>> queryTenantSkuBySpuId(QueryTenantSkuBySpuIdApiRequest request, User user) {
        try {
            TenantSpuDetailBizRequest rpcRequest = new TenantSpuDetailBizRequest();
            rpcRequest.setTenantId(user.getTenantId());
            rpcRequest.setSpuId(request.getSpuId());
            rpcRequest.setIncludeCoveredStores(true);
            TenantSpuDetailBizResponse response = tenantSpuBizThriftService.queryTenantSpuDetail(rpcRequest);
            log.info("OCMSServiceWrapper.queryTenantSkuBySpuId(), 查询总部商品规格, request:{}, user:{}, response:{}", request, user, response);
            if (response.getStatus() == null) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), ResultCode.INTERNAL_SERVER_ERROR.getErrorMessage());
            }
            if (!response.getStatus().isSuccess()) {
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
            }
            if (response.getTenantSpuBizDTO() == null) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), ResultCode.STORE_MANAGEMENT_EMPOWER_TASK_NO_SUCH_GOODS.getErrorMessage());
            }
            return CommonResponse.success(Fun.map(response.getTenantSpuBizDTO().getTenantSkuDTOList(), TenantSkuVO::fromBizDTO));
        } catch (Exception e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<List<TenantChannelSkuVO>> queryChannelSkuList(QueryTenantSkuBySpuIdApiRequest request, User user) {
        try {
            TenantChannelSkuRequest rpcRequest = new TenantChannelSkuRequest();
            rpcRequest.setTenantId(user.getTenantId());
            rpcRequest.setSpuId(request.getSpuId());
            TenantChannelSkuResponse response = tenantSpuThriftService.queryTenantChannelSku(rpcRequest);
            log.info("OCMSServiceWrapper.queryChannelSkuList(), 查询总部渠道商品规格, request:{}, user:{}, response:{}", request, user, response);
            return new CommonResponse<>(response.getCode(), response.getMsg(), ConverterUtils.convertList(response.getTenantChannelSkuDTOS(),TenantChannelSkuVO::convertFromDto));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse<TenantSkuVO> addSku(AddTenantSkuApiRequest request, User user) {
        try {
            TenantSpuAddSpecRequest rpcRequest = new TenantSpuAddSpecRequest();
            rpcRequest.setTenantId(user.getTenantId());
            rpcRequest.setSpuId(request.getSpuId());
            rpcRequest.setOperator(OperatorDTO.builder().operatorId(user.getAccountId()).operatorName(user.getOperatorName()).build());
            rpcRequest.setOperateSource(SkuOperateSourceType.APP.getValue());
            rpcRequest.setTenantSpuAddSpecDTOList(TenantSkuSimpleVO.toDTOList(request.getSkuList()));
            SaleAttrCompatUtils.compat(rpcRequest);
            TenantAddSpecResponse response = tenantSpuThriftService.addSpecWithResp(rpcRequest);
            log.info("OCMSServiceWrapper.addSku(), 新增总部商品规格, request:{}, user:{}, response:{}", request, user, response);

            return new CommonResponse<TenantSkuVO>(response.getCode(), response.getMsg(), TenantSkuVO.ofDTO(response.getSameTenantSku()));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    public List<StoreSkuBaseDetailVO> queryStoreSkuInfoBySkuIds(long tenantId, long storeId, List<String> skuIds, boolean hasInvalid) {

        // 若商品编码列表为空, 直接返回空列表
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        QueryStoreSkuBySkuIdsRequest request = new QueryStoreSkuBySkuIdsRequest();
        request.setTenantId(tenantId).setStoreId(storeId).setSkuIds(skuIds).setHasValid(hasInvalid);
        try {
            StoreSkuResponse response = channelSkuThriftService.queryStoreSkuInfoBySkuIds(request);
            log.info("ChannelSkuThriftService.queryStoreSkuInfoBySkuIds, request:{}, response:{}", request, response);

            if (response.getCode() != CODE_SUCCESS) {
                throw new CommonRuntimeException(response.getMsg());
            }

            return ConverterUtils.convertList(response.getSkuList(), StoreSkuBaseDetailVO::build);

        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    public Map<String, String> querySkuId2SpuIdMap(Long tenantId, Long storeId, Integer channelId, List<String> skuIds) {

        List<ChannelSkuDTO> channelSkuDTOList = queryChannelSkuList(tenantId, storeId, channelId, skuIds);
        if (CollectionUtils.isEmpty(channelSkuDTOList)) {
            return Collections.emptyMap();
        }

        return channelSkuDTOList.stream().collect(Collectors.toMap(ChannelSkuDTO::getSku,
                ChannelSkuDTO::getSpuId, (oldVal, newVal) -> newVal));
    }

    public List<ChannelSkuDTO> queryChannelSkuList(Long tenantId, Long storeId, Integer channelId, List<String> skuIds) {

        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        SkuListRequest request = new SkuListRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setSkuIds(skuIds);

        try {
            SkuListResponse response = channelSkuThriftService.findBySkuIds(request);
            log.info("ChannelSkuThriftService.findBySkuIds request:{}, response:{}", request, response);
            if (response == null || response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                throw new CommonLogicException("查询商品信息错误");
            }

            if (CollectionUtils.isEmpty(response.getSkuInfoList())) {
                return Collections.emptyList();
            }

            return response.getSkuInfoList();
        } catch (TException e) {
            log.error("ChannelSkuThriftService.findBySkuIds exception", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<TenantRegionSpuVO> getTenantRegionSpuInfo(GetTenantSpuBySpuIdRequest request, User user) {
        TenantSpuDetailBizRequest rpcRequest = new TenantSpuDetailBizRequest();
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setSpuId(request.getSpuId());
        rpcRequest.setStoreId(request.getStoreId());

        log.info("tenantSpuBizThriftService.queryTenantSpuDetail() request:{}", rpcRequest);
        TenantSpuDetailBizResponse response = tenantSpuBizThriftService.queryTenantSpuDetail(rpcRequest);
        log.info("tenantSpuBizThriftService.queryTenantSpuDetail() response:{}", response);
        if (response == null || response.getStatus() == null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询主档商品失败");
        }
        if (!response.getStatus().isSuccess()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), String.format("查询主档商品失败, 原因:%s", response.getStatus().getMsg()));
        }
        if (response.getTenantSpuBizDTO() == null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "主档商品不存在");
        }

        TenantSpuBizDTO tenantSpuBizDTO = response.getTenantSpuBizDTO();
        Map<Long, Integer> poiId2GroupIdsMap = new HashMap<>();
        if (BooleanUtils.isTrue(tenantSpuBizDTO.getStoreCategoryCleanSuccess())) {
            poiId2GroupIdsMap = tenantWrapper.batchQueryBindStoreGroupIdsByPoiIds(user.getTenantId(),
                    Collections.singletonList(request.getStoreId()));
        }

        RegionSpuDTO regionSpuDTO = queryRegionSpuInfo(request, user);
        return CommonResponse.success(TenantRegionSpuVO.ofBizDto(tenantSpuBizDTO, request.getStoreId(), poiId2GroupIdsMap,
                regionSpuDTO));
    }

    private RegionSpuDTO queryRegionSpuInfo(GetTenantSpuBySpuIdRequest request, User user) {
        if (request.getStoreId() == null) {
            return null;
        }
        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(Collections.singletonList(request.getStoreId()),
                user.getTenantId());
        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()
                || MapUtils.isEmpty(response.getPoiInfoMap())
                || !response.getPoiInfoMap().containsKey(request.getStoreId())) {
            return null;
        }
        PoiInfoDto poiInfoDto = response.getPoiInfoMap().get(request.getStoreId());

        RegionSpuDetailRequest regionSpuRequest = new RegionSpuDetailRequest();
        regionSpuRequest.setTenantId(user.getTenantId());
        regionSpuRequest.setSpuIds(Lists.newArrayList(request.getSpuId()));
        regionSpuRequest.setRegionId(Long.valueOf(poiInfoDto.getDistrict().getCityId()));
        try {
            RegionSpuDetailResponse regionSpuResponse = regionSpuThriftService.findRegionSpuDetail(regionSpuRequest);
            if (regionSpuResponse == null || CollectionUtils.isEmpty(regionSpuResponse.getRegionSpus())) {
                return null;
            }
            return regionSpuResponse.getRegionSpus().get(0);
        } catch (Exception e) {
            log.error("regionSpuThriftService.findRegionSpuDetail error, request: {}", regionSpuRequest, e);
        }
        return null;
    }

    public CommonResponse<ChannelCategoryRelationVO> queryChannelCategoryByErpCategory(User user, QueryChannelCategoryInfoRequest request) {
        try {
            log.info("OCMSServiceWrapper.queryChannelCategoryByErpCategory  调用channelThriftService.queryChannelCategoryByErpCategory() request:{}", user.getTenantId());
            QueryChannelCategoryByErpCategoryRequest categoryRequest = new QueryChannelCategoryByErpCategoryRequest(user.getTenantId(), request.getErpCategoryCode());
            QueryChannelCategoryResponse response = channelThriftService.queryChannelCategoryByErpCategory(categoryRequest);
            log.info("OCMSServiceWrapper.queryChannelCategoryByErpCategory  调用channelThriftService.queryChannelCategoryByErpCategory() response:{}", response);
            return CommonResponse.success(OCMSUtils.convertQueryChannelCategoryResponse(response));
        } catch (TException e) {
            log.error("OCMSServiceWrapper.querySkuSortInfo  调用channelThriftService.queryChannelCategoryByErpCategory() 异常", e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 获取京东类目
     *
     * @param pId
     * @return
     */
    public CommonResponse<ChannelCategoryInfoResponse> queryChannelCategorys(Long tenantId, String pId, int channelId, int depth) {
        ChannelCategoryQueryRequest request = new ChannelCategoryQueryRequest();
        request.setChannelId(channelId);
        request.setParentIds(ConverterUtils.nonNullConvert(pId, Arrays::asList));
        request.setTenantId(tenantId);
        request.setDepth(depth);

        try {
            log.info("OCMSServiceWrapper.queryChannelCategorys  调用otherThriftService.queryChannelCategory() request:{}", request);
            ChannelCategoryResponse response = otherThriftService.queryChannelCategory(request);
            log.info("OCMSServiceWrapper.queryChannelCategorys  调用otherThriftService.queryChannelCategory() response:{}", response);
            List<ChannelCategoryLevelVO> voList = ConverterUtils.convertList(response.getCategoryList(), ChannelCategoryLevelVO::new);
            ChannelCategoryInfoResponse vo = new ChannelCategoryInfoResponse();
            vo.setCategoryLevelVOList(voList);
            return CommonResponse.success(vo);
        } catch (TException e) {
            log.error("OCMSServiceWrapper.queryChannelCategorys  调用otherThriftService.queryChannelCategory() 异常", e);
            throw new CommonRuntimeException(e);
        }
    }


    /**
     * 根据渠道id查询渠道全部的渠道类目.
     *
     * @param channelId 渠道id
     * @return 渠道全部的渠道类目
     */
    public CommonResponse<ChannelCategoryInfoResponse> queryChannelCategoryByChannelId(
            Long tenantId, Integer channelId) {
        ChannelCateQueryByChannelIdRequest request = new ChannelCateQueryByChannelIdRequest();
        request.setTenantId(tenantId);
        request.setChannelId(channelId);
        try {
            ChannelCategoriesQueryResponse rpcReponse = channelCategoryThriftService.queryByChannelId(request);
            log.info("调用ChannelCategoryThriftService.queryByChannelId, request:{}, response:{}", request, rpcReponse);

            if (rpcReponse.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), rpcReponse.getMessage());
            }

            List<ChannelCategoryLevelVO> voList = ConverterUtils.convertList(rpcReponse.getChannelCategories(),
                    ChannelCategoryLevelVO::new);
            ChannelCategoryInfoResponse response = new ChannelCategoryInfoResponse();
            response.setCategoryLevelVOList(voList);
            return CommonResponse.success(response);
        } catch (TException e) {
            log.error("调用ChannelCategoryThriftService.queryByChannelId异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<ChannelCategoryAfterSaleResponse> queryAfterSale(Long tenantId, QueryChannelCategoryAfterSaleRequest request) {
        ChannelAfterSaleQueryRequest afterSaleQueryRequest = new ChannelAfterSaleQueryRequest();
        afterSaleQueryRequest.setTenantId(tenantId);
        afterSaleQueryRequest.setBrandId(request.getBrandId());
        afterSaleQueryRequest.setSpuId(request.getSpuId());
        afterSaleQueryRequest.setChannelId(request.getChannelId());
        afterSaleQueryRequest.setCategoryId(request.getCategoryId());
        try {
            ThriftResponse<ChannelAfterSaleDTO> response = channelAfterSaleRuleThriftService.queryAfterSale(afterSaleQueryRequest);
            if (response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMessage());
            }

            if (response.getData() == null || CollectionUtils.isEmpty(response.getData().getOptionRules())) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "没有找到对应的售后规则");
            }

            return CommonResponse.success(buildChannelCategoryAfterSaleResponse(response.getData()));
        } catch (TException e) {
            log.error("调用ChannelAfterSaleRuleThriftService.queryAfterSale, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    private ChannelCategoryAfterSaleResponse buildChannelCategoryAfterSaleResponse(ChannelAfterSaleDTO channelAfterSaleDTO){
        ChannelCategoryAfterSaleResponse afterSaleResponse = new ChannelCategoryAfterSaleResponse();

        List<ChannelCategoryAfterSaleVO> afterSaleVOS = Fun.map(channelAfterSaleDTO.getOptionRules(), ChannelCategoryAfterSaleVO::ofDTO);
        afterSaleResponse.setOptionRules(afterSaleVOS);
        // 默认选项
        Optional<ChannelAfterSaleDetailDTO> defaultOption = channelAfterSaleDTO.getOptionRules().stream()
                .filter(each -> BooleanUtils.isTrue(each.getDef())).findFirst();
        if(defaultOption.isPresent()){
            ChannelAfterSaleDetailDTO defaultOne = defaultOption.get();
            afterSaleResponse.setDefaultOption(defaultOne.getType());
            afterSaleResponse.setDefaultOptionName(defaultOne.getText());
        }
        return afterSaleResponse;
    }

    public CommonResponse<ChannelCategoryQualificationVo> queryQualification(ChannelQualificationByCategoryRequest request) {
        ThriftResponse<ChannelCategoryQualificationDTO> response = null;
        try {
            response = channelQualificationThriftService.queryByCategoryId(request.toChannelCategoryQualificationRequest());
            if (response == null || response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询资质失败");
            }
        } catch (TException e) {
            log.error("调用ChannelQualificationThriftService.queryByCategoryId, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        return CommonResponse.success(ChannelCategoryQualificationVo.fromDto(response.getData()));
    }

    /**
     *牵牛花零售商家获取美团渠道类目需要同时获取零售类目和配置的医药类目，背景：https://km.sankuai.com/collabpage/2037928972
     * @param tenantId
     * @param channelId
     * @return
     */
    public CommonResponse<ChannelCategoryInfoResponse> queryRetailAndMedCategoryTreeByChannelId(Long tenantId, Integer channelId) {

        ChannelCateQueryByChannelIdRequest request = new ChannelCateQueryByChannelIdRequest();
        request.setTenantId(tenantId);
        request.setChannelId(channelId);
        try {
            ChannelCategoriesQueryResponse rpcResponse = channelCategoryThriftService.queryRetailAndMedicineCategoryByChannelId(request);
            log.info("调用ChannelCategoryThriftService.queryByChannelId, request:{}, response:{}", request, rpcResponse);

            ResponseHandler.checkResponseAndStatus(rpcResponse, ChannelCategoriesQueryResponse::getCode,
                    ChannelCategoriesQueryResponse::getMessage);
            List<ChannelCategoryLevelVO> voList = ConverterUtils.convertList(rpcResponse.getChannelCategories(),
                    ChannelCategoryLevelVO::new);
            ChannelCategoryInfoResponse response = new ChannelCategoryInfoResponse();
            response.setCategoryLevelVOList(voList);
            return CommonResponse.success(response);
        }
        catch (TException e) {
            log.error("调用ChannelCategoryThriftService.queryRetailAndMedicineCategoryByChannelId异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
        catch (Exception e) {
            log.error("调用ChannelCategoryThriftService.queryRetailAndMedicineCategoryByChannelId异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * @param tenantId
     * @param spuId
     * @param typeList
     * @throws
     * @Description: 数据预览接口
     */
    public CommonResponse<TenantSpuPreviewVO> tenantSpuPreview(Long tenantId, String spuId, List<Integer> typeList) {
        TenantSpuPreviewRequest request = new TenantSpuPreviewRequest();
        request.setTenantId(tenantId);
        request.setOperateSource(SkuOperateSourceType.APP.getValue());
        request.setSpuId(spuId);
        request.setTypeList(typeList);
        try {
            log.info("OCMSServiceWrapper.tenantSpuPreview  tenantSpuThriftService.tenantSpuPreview() request:{}", request);
            TenantSpuPreviewResponse response = tenantSpuThriftService.tenantSpuPreview(request);
            log.info("OCMSServiceWrapper.tenantSpuPreview  调用tenantSpuThriftService.tenantSpuPreview() response:{}", response);
            if (response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMsg());
            }
            TenantSpuPreviewVO vo = new TenantSpuPreviewVO();
            vo.setQuantityUnit(response.getQuantityUnitList());
            return CommonResponse.success(vo);
        } catch (TException e) {
            log.error("OCMSServiceWrapper.tenantSpuPreview  调用tenantSpuThriftService.tenantSpuPreview() 异常", e);
            throw new CommonRuntimeException(e);
        }

    }

    /**
     * @param findTenantSimilarSkuRequest
     * @throws
     * @Description: 寻找三个相似规格，重量相同
     */
    public CommonResponse<List<TenantSkuVO>> findSimilarSpec(Long tenantId, FindTenantSimilarSkuRequest findTenantSimilarSkuRequest) {
        TenantSkuQueryConditionRequest request = new TenantSkuQueryConditionRequest();
        request.setSize(findTenantSimilarSkuRequest.getSize());
        request.setSpuId(findTenantSimilarSkuRequest.getSpuId());
        TenantSkuQueryConditionDTO conditionDTO = new TenantSkuQueryConditionDTO();
        conditionDTO.setWeight(Integer.parseInt(findTenantSimilarSkuRequest.getMaxWeight()));
        conditionDTO.setSortEnum(SkuSortEnum.COVEREDSTORES_DESC);
        request.setTenantSkuQueryConditionDTO(conditionDTO);
        request.setTenantId(tenantId);
        try {
            log.info("OCMSServiceWrapper.findSimilarSpec  tenantSpuThriftService.querySkuByCondition() request:{}", request);
            TenantSkuQueryResponse response = tenantSpuThriftService.querySkuByCondition(request);
            log.info("OCMSServiceWrapper.findSimilarSpec  调用tenantSpuThriftService.querySkuByCondition() response:{}", response);
            if (response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMsg());
            }
            return new CommonResponse<>(response.getCode(), response.getMsg(), TenantSkuVO.ofDTOList(response.getTenantSkuDTOList()));
        } catch (TException e) {
            log.error("OCMSServiceWrapper.findSimilarSpec  调用tenantSpuThriftService.findSimilarSpec() 异常", e);
            throw new CommonRuntimeException(e);
        }


    }

    private TenantSpuSimpleVO buildTenantSpuSimpleVO(TenantSkuQueryResponse skuQueryResponse,TenantSpuDetailResponse detailResponse){
        TenantSpuSimpleVO tenantSpuSimpleVO=new TenantSpuSimpleVO();
        tenantSpuSimpleVO.setTenantSkuVOList(TenantSkuVO.ofDTOList(skuQueryResponse.getTenantSkuDTOList()));
        if (Objects.nonNull(detailResponse.getTenantSpuDTO())){
            tenantSpuSimpleVO.setCanCustomizeSpec(detailResponse.getTenantSpuDTO().getAllowCustomizeSpec());
            tenantSpuSimpleVO.setTenantId(detailResponse.getTenantSpuDTO().getTenantId());
            tenantSpuSimpleVO.setSpuId(detailResponse.getTenantSpuDTO().getSpuId());
        }
        return tenantSpuSimpleVO;
    }


    /**
     * 查询门店在渠道下关联的渠道门店是否支持连锁产品库
     */
    public CommonResponse<Boolean> isChainProductSupported(long tenantId, long storeId, int channelId) {
        try {
            QueryChainProductConfigRequest request = new QueryChainProductConfigRequest();
            request.setTenantId(tenantId);
            request.setStoreId(storeId);
            request.setChannelId(channelId);
            ChainProductConfigResponse response = channelThriftService.queryChainProductConfig(request);
            if (response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMsg());
            }
            return CommonResponse.success(ChainProductEnum.YES.equals(response.getChainProduct()));
        } catch (TException e) {
            log.error("OCMSServiceWrapper.isChainProductSupported 调用 channelThriftService.queryChainProductConfig() 异常", e);
            throw new CommonRuntimeException(e);
        } catch (Exception e) {
            log.error("OCMSServiceWrapper.isChainProductSupported 异常", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<TenantSpuPageQueryResponseVO> queryTenantSpuList(TenantSpuPageQueryApiRequest request, User user) {
        try {
            TenantSpuPageQueryRequest rpcRequest = TenantSpuPageQueryApiRequest.toBizRpcRequest(request, user);
            log.info("OCMSServiceWrapper.queryTenantSpuList 调用tenantSpuBizThriftService.pageQueryTenantSpu() request:{}",rpcRequest);
            TenantSpuPageQueryBizResponse response = tenantSpuBizThriftService.pageQueryTenantSpu(rpcRequest);
            log.info("OCMSServiceWrapper.queryTenantSpuList 调用tenantSpuBizThriftService.pageQueryTenantSpu() response:{}",response);
            if (response == null
                    || response.getStatus() == null
                    || response.getStatus().getCode() == null) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "分页查询主档商品失败");
            }
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMsg());
            }
            TenantSpuPageQueryResponseVO tenantSpuPageQueryResponseVO = TenantSpuPageQueryResponseVO.convertBizResponse(response);
            return new CommonResponse(response.getStatus().getCode(), response.getStatus().getMsg(), tenantSpuPageQueryResponseVO);
        } catch (Exception e) {
            log.error("OCMSServiceWrapper.queryTenantSpuList 调用tenantSpuThriftService.pageQueryTenantSpu() 异常",e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<TenantSpuVO> queryTenantSpuInfo(TenantSpuInfoApiRequest request, User user) {
        try {
            TenantSpuDetailBizRequest rpcRequest = new TenantSpuDetailBizRequest();
            rpcRequest.setTenantId(user.getTenantId());
            rpcRequest.setSpuId(request.getSpu());
            log.info("tenantSpuBizThriftService.queryTenantSpuDetail() request:{}", rpcRequest);
            TenantSpuDetailBizResponse response = tenantSpuBizThriftService.queryTenantSpuDetail(rpcRequest);
            if (response == null || response.getStatus() == null) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询主档商品失败，请重试");
            }
            if (!response.getStatus().isSuccess()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMsg());
            }
            return CommonResponse.success(TenantSpuVO.convertFromBizDto(response.getTenantSpuBizDTO()));
        } catch (Exception e) {
            log.error("OCMSServiceWrapper.queryTenantSpuInfo 调用tenantSpuThriftService.queryTenantSpuDetail() 异常",e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<String> updateTenantSpu(TenantSpuAddOrUpdateApiRequest request, User user) {
        try{
            String errMsg = checkCartonMeasureRelation(request, user);
            if (StringUtils.isNotBlank(errMsg)) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), errMsg);
            }

            int code = ResponseCodeEnum.SUCCESS.getValue();
            String msg = "";
            Boolean changedSpecType = null;

            if (MccConfigUtil.updateTenantSpuToBiz(user.getTenantId())) {
                BatchUpdateTenantSpuRequest rpcRequest = TenantSpuAddOrUpdateApiRequest.toBizRpcRequest(request, user);
                log.info("OCMSServiceWrapper.updateTenantSpu 调用tenantSpuBizThriftService.updateTenantSpu() request:{}", rpcRequest);
                BatchSaveTenantSpuResponse response = tenantSpuBizThriftService.batchUpdateTenantSpu(rpcRequest);
                log.info("OCMSServiceWrapper.updateTenantSpu 调用tenantSpuBizThriftService.updateTenantSpu() response:{}", response);
                if (response == null || response.getStatus() == null) {
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), "更新主档商品失败，请重试");
                }
                if (!response.getStatus().isSuccess()) {
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMsg());
                }
                if (CollectionUtils.isNotEmpty(response.getResultList())) {
                    TenantSpuSaveResultDTO tenantSpuSaveResultDTO = response.getResultList().iterator().next();
                    code = tenantSpuSaveResultDTO.getCode();
                    msg = tenantSpuSaveResultDTO.getMsg();
                } else {
                    code = response.getStatus().getCode();
                    msg = response.getStatus().getMsg();
                }

                List<String> changedSpecTypeSpuIds = Fun.map(
                        Fun.filter(response.getResultList(), detail -> Objects.equals(detail.getCode(), ResultCode.SUCCESS.getCode()) && BooleanUtils.isTrue(detail.getChangedSpecType())
                ), TenantSpuSaveResultDTO::getSpuId);
                changedSpecType = CollectionUtils.isNotEmpty(changedSpecTypeSpuIds);
            }
            else {
                UpdateTenantSpuRequest rpcRequest = TenantSpuAddOrUpdateApiRequest.toRpcRequest(request, user);
                log.info("OCMSServiceWrapper.updateTenantSpu 调用tenantSpuThriftService.updateTenantSpu() request:{}", rpcRequest);
                UpdateTenantSpuResponse response = tenantSpuThriftService.updateTenantSpu(rpcRequest);
                log.info("OCMSServiceWrapper.updateTenantSpu 调用tenantSpuThriftService.updateTenantSpu() response:{}", response);
                if (response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMsg());
                }
                code = response.getCode();
                msg = response.getMsg();
                changedSpecType = response.getChangedSpecType();
            }
            //主档商品更新成功后，如果有需要推送的字段则创建推送任务
            createTenantProductChangeSyncTask(request, user, changedSpecType);
            return new CommonResponse(code,msg,request.getSpu());
        }catch (TException e){
            log.error("OCMSServiceWrapper.updateTenantSpu 调用tenantSpuThriftService.updateTenantSpu() 异常",e);
            throw new CommonRuntimeException(e);
        }
    }

    private String checkCartonMeasureRelation(TenantSpuAddOrUpdateApiRequest request, User user) {
        try {
            List<MerchantSkuCartonMeasureInfo> skuCartonMeasureInfos =
                    empowerProductWrapper.querySkuCartonMeasureInfo(user.getTenantId(), request.getSpu());
            if (CollectionUtils.isEmpty(skuCartonMeasureInfos)) {
                return StringUtils.EMPTY;
            }

            Map<String, TenantSkuVO> skuVOMap = Fun.toMapQuietly(request.getSkus(), TenantSkuVO::getSkuId);
            for (MerchantSkuCartonMeasureInfo info : skuCartonMeasureInfos) {
                TenantSkuVO tenantSkuVO = skuVOMap.get(info.getSkuId());
                if (Objects.isNull(tenantSkuVO) || CollectionUtils.isEmpty(info.getSkuCartonMeasureList())
                        || CollectionUtils.isEmpty(skuVOMap.get(info.getSkuId()).getCartonMeasureConvertFactorList())) {
                    return StringUtils.EMPTY;
                }
                Map<String, CartonMeasureConvertFactorVO> cartonMeasureMap =
                        tenantSkuVO.getCartonMeasureConvertFactorList().stream()
                                .filter(cartonMeasure -> StringUtils.isNotBlank(cartonMeasure.getCartonMeasureCode()))
                                .collect(Collectors.toMap(CartonMeasureConvertFactorVO::getCartonMeasureCode, Function.identity()));
                for (MerchantSkuCartonMeasure merchantSkuCartonMeasure : info.getSkuCartonMeasureList()) {
                    if (cartonMeasureMap.containsKey(merchantSkuCartonMeasure.getCode())) {
                        continue;
                    }
                    PageResultV2<SupplyRelationVO> supplyRelationResult =
                            purchaseBizServiceWrapper.queryRelationListByUnitId(info.getSkuId(), merchantSkuCartonMeasure.getCode(),
                                    1, 10, user);
                    if (CollectionUtils.isNotEmpty(supplyRelationResult.getList())) {
                        return String.format("%s的货品单位[%s]暂时无法删除，还存在供货关系或配销价数据", tenantSkuVO.getSpec(),
                                merchantSkuCartonMeasure.getName());
                    }
                }
            }
        } catch (Exception e) {
            // 此逻辑为特殊逻辑，低概率事件，此处直接按空返回，不阻塞主流程
            log.warn("按照箱规编码查询供货关系异常, request {}", request, e);
        }
        return StringUtils.EMPTY;
    }

    private void createTenantProductChangeSyncTask(TenantSpuAddOrUpdateApiRequest request, User user, Boolean changedSpecType) {
        try {
            if (tenantWrapper.isMerchantChargeSpu(user.getTenantId())) {
                return;
            }

            List<String> needPushFieldList = Optional.ofNullable(request.getNeedPushFieldList()).orElse(new ArrayList<>());

            // 修改规格，需要增加渠道类目覆盖门店
            if (BooleanUtils.isTrue(changedSpecType)) {
                needPushFieldList.add(PushFieldEnum.MT_CHANNEL_CATEGORY.name());
                needPushFieldList.add(PushFieldEnum.MT_CATEGORY_PROPERTIES.name());
                needPushFieldList.add(PushFieldEnum.SPEC_TYPE.name());

                List<Integer> openChannels = tenantWrapper.queryChannelIds(user.getTenantId()).stream().filter(Objects::nonNull)
                        .map(ChannelTypeEnumBo::getChannelId).collect(Collectors.toList());
                if (openChannels.contains(EnhanceChannelType.ELEM.getChannelId())) {
                    needPushFieldList.add(PushFieldEnum.ELM_CHANNEL_CATEGORY.name());
                    needPushFieldList.add(PushFieldEnum.ELM_CATEGORY_PROPERTIES.name());
                }
            }
            if (CollectionUtils.isEmpty(needPushFieldList)) {
                return;
            }

            needPushFieldList = needPushFieldList.stream().distinct().collect(Collectors.toList());

            TenantProductChangeSyncRequest rpcReq = new TenantProductChangeSyncRequest();
            rpcReq.setTenantId(user.getTenantId());
            TenantProductChangeSyncDto syncDto = new TenantProductChangeSyncDto();
            syncDto.setSpuId(request.getSpu());
            syncDto.setNeedUpdateFields(needPushFieldList);
            rpcReq.setSyncDtoList(com.google.common.collect.Lists.newArrayList(syncDto));
            rpcReq.setPushType(TenantProductChangeSyncTypeEnum.ALL_POI_SPU_AND_CHANNEL_SPU.getCode());
            rpcReq.setOperatorId(user.getAccountId());
            rpcReq.setOperatorName(user.getOperatorName());
            TaskSubmitResponse response = productBizTaskThriftService.submitTenantProductChangeSyncTask(rpcReq);
            if (!response.getStatus().isSuccess()) {
                log.error("提交主档商品变更后同步门店及渠道任务失败, request:{}, response:{}", rpcReq, response);
            }
        } catch (Exception e) {
            log.error("提交主档商品变更后同步门店及渠道任务失败, request:{}", request, e);
        }
    }

    public CommonResponse deleteTenantSpu(TenantSpuDeleteApiRequest request, User user) {
        try{
            DeleteTenantSpuRequest rpcRequest = TenantSpuDeleteApiRequest.toRpcRequest(request, user);
            log.info("OCMSServiceWrapper.deleteTenantSpu 调用tenantSpuThriftService.deleteSpu() request:{}",rpcRequest);
            DeleteTenantSpuResponse response = tenantSpuThriftService.deleteSpu(rpcRequest);
            log.info("OCMSServiceWrapper.deleteTenantSpu 调用tenantSpuThriftService.deleteSpu() response:{}",response);
            if(response.getCode() != ResponseCodeEnum.SUCCESS.getValue()){
                return CommonResponse.fail(ResultCode.FAIL.getCode(),response.getMsg());
            }
            return CommonResponse.success(null);
        }catch (TException e){
            log.error("OCMSServiceWrapper.deleteTenantSpu 调用tenantSpuThriftService.deleteSpu() 异常",e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<TenantSpuQueryFilterCountResponseVO> queryFilterTenantSpuCount(TenantSpuQueryFilterCountApiRequest request, User user) {
        try{
            TenantSpuQueryFilterCountRequest rpcRequest = TenantSpuQueryFilterCountApiRequest.toRpcRequest(request, user);
            if (BooleanUtils.isTrue(request.getHasNoSaleAbnormal())){
                List<String> noSaleAbnormalCodes = MccConfigUtil.noSaleAbnormalCodes();
                rpcRequest.setNoSaleAbnormalCodes(noSaleAbnormalCodes);
            }
            log.info("OCMSServiceWrapper.queryFilterTenantSpuCount 调用tenantSpuThriftService.queryFilterTenantSpuCount() request:{}",rpcRequest);
            TenantSpuQueryFilterCountResponse response = tenantSpuThriftService.queryFilterTenantSpuCount(rpcRequest);
            log.info("OCMSServiceWrapper.queryFilterTenantSpuCount 调用tenantSpuThriftService.queryFilterTenantSpuCount() response:{}",response);
            if(response.getCode() != ResponseCodeEnum.SUCCESS.getValue() || response.getCountInfo()==null){
                return CommonResponse.fail(ResultCode.FAIL.getCode(),response.getMsg());
            }
            return CommonResponse.success(TenantSpuQueryFilterCountResponseVO.convertResponse(response.getCountInfo()));
        }catch (TException e){
            log.error("OCMSServiceWrapper.queryFilterTenantSpuCount 调用tenantSpuThriftService.queryFilterTenantSpuCount() 异常",e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 查询箱规列表
     * @param request
     * @return
     */
    public CommonResponse<List<CartonMeasureVO>> queryCartonMeasure(QueryCartonMeasureRequest request) {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            QueryTenantSkuCartonMeasureResponse response =
                    tenantProductThriftService.queryTenantSkuCartonMeasureInfo(request.toQueryTenantSkuCartonMeasure(user.getTenantId()));
            log.info("OCMSServiceWrapper.queryCartonMeasure request: {}, response:{}", request, response);

            if (response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMsg());
            }
            return CommonResponse.success(CartonMeasureVO.toCartonMeasureVOList(response.getSkuCartonMeasureList()));
        } catch (TException e) {
            log.error("OCMSServiceWrapper.queryCartonMeasure 异常, request: {}.", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<ProductAbnormalVO> getSpuAbnormalList(QueryTenantSpuAbnormalRequest request, User user) {
        try {
            // 判断是否加盟主，非加盟主暂时返回空
            ChainRelationEnum tenantChainRelationMode = tenantWrapper.getTenantChainRelationMode(user.getTenantId());
            if (!ChainRelationEnum.FRANCHISOR_ROLE.equals(tenantChainRelationMode)) {
                return CommonResponse.success(new ProductAbnormalVO());
            }

            QueryMerchantSpuListResult response = empowerMerchantSpuThriftService.queryMerchantSpuByIds(request.toMerchantSpuIdListCommand(request, user.getTenantId()));
            log.info("OCMSServiceWrapper.getSpuAbnormalList request: {}, response:{}", request, response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMsg());
            }
            return CommonResponse.success(buildProductAbnormalVO(response.getSpuList()));
        } catch (TException e) {
            log.error("OCMSServiceWrapper.getSpuAbnormalList 异常, request: {}.", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    private ProductAbnormalVO buildProductAbnormalVO(List<MerchantSpuInfo> merchantSpuInfoList) {
        ProductAbnormalVO productAbnormalVO = new ProductAbnormalVO();
        if (CollectionUtils.isEmpty(merchantSpuInfoList)){
            return productAbnormalVO;
        }
        MerchantSpuInfo merchantSpuInfo = merchantSpuInfoList.get(0);
        // 加盟门店
        List<AbnormalProductInfoDTO> franchiseeAbnormalProductInfoDTOS = AbnormalInfoUtils.buildAbnormalProductInfoList(merchantSpuInfo.getFranchiseeMtAuditStatusList(),
                merchantSpuInfo.getFranchiseeMtNormAuditStatusList(), merchantSpuInfo.getFranchiseeMtStopSellingStatusList(),
                merchantSpuInfo.getFranchiseeMtSoldOutFlagList());
        // 去重
        franchiseeAbnormalProductInfoDTOS = new ArrayList<>(franchiseeAbnormalProductInfoDTOS.stream()
                .collect(Collectors.toMap(AbnormalProductInfoDTO::getAbnormalCode, item->item, (k1,k2)->k1)).values());
        List<ProductAbnormalDetailVO> abnormalDetails = new ArrayList<>(ProductAbnormalDetailVO
                .ofList(franchiseeAbnormalProductInfoDTOS, TenantSpuAbnormalSourceTypeEnum.FRANCHISEE_STORE_BY_FRANCHISOR_TENANT.getCode(), null));

        // 自营门店
        List<AbnormalProductInfoDTO> selfAbnormalProductInfoDTOS = AbnormalInfoUtils.buildAbnormalProductInfoList(merchantSpuInfo.getAuditStatusList(), merchantSpuInfo.getNormAuditStatusList(), null, null);
        // 去重
        selfAbnormalProductInfoDTOS = new ArrayList<>(selfAbnormalProductInfoDTOS.stream()
                .collect(Collectors.toMap(AbnormalProductInfoDTO::getAbnormalCode, item->item, (k1,k2)->k1)).values());
        abnormalDetails.addAll(ProductAbnormalDetailVO.ofList(selfAbnormalProductInfoDTOS, TenantSpuAbnormalSourceTypeEnum.SELF_STORE.getCode(), null));

        productAbnormalVO.setAbnormalDetails(abnormalDetails);
        return productAbnormalVO;
    }

    public boolean isErpTenant(Long tenantId) {

        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setSubjectId(tenantId);
        configQueryRequest.setConfigId(ConfigItemEnum.HAS_ERP.getKey());
        TenantConfigResponse response = RpcInvoker.invoke(() -> configThriftService.queryTenantConfig(configQueryRequest));
        Object hasErp = JacksonUtils.fromJsonToMap(response.getConfig().getConfigContent()).get("hasErp");
        return "YES".equals(hasErp);
    }

    public boolean isErpTenantWithCache(Long tenantId){
        String key = String.format("%s_%d", ERP_KEY, tenantId);
        try {
            Object result = LOCAL_CACHE.get(key, () -> isErpTenant(tenantId));
            return (boolean) result;
        } catch (ExecutionException e) {
            log.error("获取ERP租户配置异常 tenantId:{}", tenantId, e);
            throw new IllegalStateException("获取ERP租户配置异常", e);
        }
    }

    public boolean isMedicineUnmannedMode(Long tenantId) {
        Preconditions.checkNotNull(tenantId, "租户ID不能为空");
        Optional<TenantBizModeEnum> bizMode = getTenantBizMode(tenantId);
        return bizMode.filter(tenantBizModeEnum -> tenantBizModeEnum == TenantBizModeEnum.MEDICINE_UNMANNED_WAREHOUSE).isPresent();
    }

    private Optional<TenantBizModeEnum> getTenantBizMode(Long tenantId) {
        try {
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setConfigId(ConfigItemEnum.TENANT_BIZ_MODE.getKey());
            configQueryRequest.setSubjectId(tenantId);
            TenantConfigResponse tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
            if (tenantConfigResponse.getStatus().code != com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum.SUCCESS.getCode()) {
                log.error("查询租户配置失败,msg:{}", tenantConfigResponse.getStatus().getMessage());
                return Optional.empty();
            }
            //该租户没有选择业务模式
            if (tenantConfigResponse.getConfig() == null) {
                return Optional.empty();
            }
            HashMap<String, Object> configContentMap = JacksonUtils.fromJsonToMap(tenantConfigResponse.getConfig().getConfigContent());
            String bizMode = (String) configContentMap.get("biz_mode");
            return Optional.of(TenantBizModeEnum.codeOf(bizMode));
        } catch (Exception e) {
            log.error("查询租户配置失败:tenantId = {}", tenantId, e);
            return Optional.empty();
        }
    }
}
