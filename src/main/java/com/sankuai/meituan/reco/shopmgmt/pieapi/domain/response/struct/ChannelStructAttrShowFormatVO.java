package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.structattr.ChannelStructAttrShowFormatDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/7/15 14:26
 */
@TypeDoc(
        name = "结构化属性展示格式",
        description = "结构化属性展示格式"
)
@Data
@ApiModel("结构化属性展示格式")
public class ChannelStructAttrShowFormatVO {
    @FieldDoc(
            description = "展示规则顺序，根据Format中的sequence来组合展示"
    )
    private List<Integer> valueFormatSeqList;

    public static List<ChannelStructAttrShowFormatDTO> toBizDTOList(List<ChannelStructAttrShowFormatVO> showFormatVOList) {
        return Fun.map(showFormatVOList, ChannelStructAttrShowFormatVO::toBizDTO);
    }

    private static ChannelStructAttrShowFormatDTO toBizDTO(ChannelStructAttrShowFormatVO showFormatVO) {
        if (showFormatVO == null) {
            return null;
        }
        ChannelStructAttrShowFormatDTO showFormatDTO = new ChannelStructAttrShowFormatDTO();
        showFormatDTO.setValueFormatSeqList(showFormatVO.getValueFormatSeqList());
        return showFormatDTO;
    }

    public static List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrShowFormatDTO> toOcmsBizDTOList(List<ChannelStructAttrShowFormatVO> showFormatVOList) {
        return Fun.map(showFormatVOList, ChannelStructAttrShowFormatVO::toOcmsBizDTO);
    }

    private static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrShowFormatDTO toOcmsBizDTO(ChannelStructAttrShowFormatVO showFormatVO) {
        if (showFormatVO == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrShowFormatDTO showFormatDTO
                = new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrShowFormatDTO();
        showFormatDTO.setValueFormatSeqList(showFormatVO.getValueFormatSeqList());
        return showFormatDTO;
    }

    public static List<ChannelStructAttrShowFormatVO> ofPlatformDTOList(List<com.sankuai.meituan.shangou.empower
            .productplatform.thrift.channelcategory.dto.ChannelStructAttrShowFormatDTO> showFormatDTOList) {
        return Fun.map(showFormatDTOList, ChannelStructAttrShowFormatVO::ofPlatformDTO);
    }

    private static ChannelStructAttrShowFormatVO ofPlatformDTO(com.sankuai.meituan.shangou.empower.productplatform
                                                                       .thrift.channelcategory.dto.ChannelStructAttrShowFormatDTO showFormatDTO) {
        if (showFormatDTO == null) {
            return null;
        }
        ChannelStructAttrShowFormatVO showFormatVO = new ChannelStructAttrShowFormatVO();
        showFormatVO.setValueFormatSeqList(showFormatDTO.getValueFormatSeqList());
        return showFormatVO;
    }

    public static List<ChannelStructAttrShowFormatVO> ofBizDTOList(List<ChannelStructAttrShowFormatDTO> showFormatDTOList) {
        return Fun.map(showFormatDTOList, ChannelStructAttrShowFormatVO::ofBizDTO);
    }

    private static ChannelStructAttrShowFormatVO ofBizDTO(ChannelStructAttrShowFormatDTO showFormatDTO) {
        if (showFormatDTO == null) {
            return null;
        }
        ChannelStructAttrShowFormatVO showFormatVO = new ChannelStructAttrShowFormatVO();
        showFormatVO.setValueFormatSeqList(showFormatDTO.getValueFormatSeqList());
        return showFormatVO;
    }

    public static ChannelStructAttrShowFormatVO ofOcmsDTO(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrShowFormatDTO showFormatDTO) {
        if (showFormatDTO == null) {
            return null;
        }
        ChannelStructAttrShowFormatVO showFormatVO = new ChannelStructAttrShowFormatVO();
        showFormatVO.setValueFormatSeqList(showFormatDTO.getValueFormatSeqList());
        return showFormatVO;
    }
}
