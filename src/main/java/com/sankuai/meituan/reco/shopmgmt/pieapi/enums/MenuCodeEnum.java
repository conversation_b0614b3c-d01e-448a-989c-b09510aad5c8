/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.enums;

import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 菜单code， 接入文档参考：https://km.sankuai.com/collabpage/1301202764
 *
 * <AUTHOR>
 * @since 2021/7/21
 */
@Getter
@Slf4j
public enum MenuCodeEnum {

    WORKBENCH_DATA("WP_TODAY_DATA", "工作台数据模块", null, null),

    TOTAL_TURNOVER("WP_TOTAL_TURNOVER", "总营业额", null, null),
    EXPECTED_REVENUE("WP_ESTIMATED_INCOME", "预计收入", null, null),
    EFFECTIVE_ORDER_COUNT("WP_EFFECTIVE_ORDER", "有效订单量", null, null),
    PICK_OVERTIME_RATE("WP_OVERTIME_RATE", "履约超时率", null, null),
    SALE_VOLUME("WP_SALE_VOLUME", "商品销售额", null, null),
    SALE_INCOME("WP_SALE_INCOME", "销售收入", null, null),
    WORKBENCH_DATA_JUMP("WP_HISTORY_DATA", "历史数据跳转", null, null),
    WORKBENCH_DATA_JUMP_NEW("WP_HISTORY_DATA_NEW", "历史数据跳转-新版", null, null),


    WORKBENCH_PENDING_TASK("WP_PROCESSING_TASK", "工作台待处理任务模块", null, null),

    //商品组
    SKU_OFF_SALE("WP_WAITING_ON_SHELF", "热销商品待上架", TaskTypeEnum.SKU_OFF_SALE, MenuGroupEnum.GOODS),
    HOT_SALE_SKU_SOLD_OUT("WP_WAITING_REPLENISHMENT", "热销商品待补货", TaskTypeEnum.HOT_SALE_SKU_SOLD_OUT,
            MenuGroupEnum.GOODS),
    WP_HAS_STOCK_SHELF("ASSISTANT_OFF_SALE_IN_STOCK", "有库存待上架", TaskTypeEnum.SKU_OFF_SALE_IN_STOCK,
            MenuGroupEnum.GOODS),
    NOT_ALLOW_SELL("WP_NOT_ALLOWED_SELL", "不可售商品", TaskTypeEnum.NOT_ALLOW_SELL, MenuGroupEnum.GOODS),
    STOP_SELLING("WP_STOP_SELLING", "平台停售商品", TaskTypeEnum.STOP_SELLING_SPU, MenuGroupEnum.GOODS),
    AUDIT_REJECTED("WP_AUDIT_REJECTED", "审核驳回商品", TaskTypeEnum.AUDIT_REJECTED, MenuGroupEnum.GOODS),
    INCONSISTENT_SPU("WP_INCONSISTENT_SPU", "商品信息异常", TaskTypeEnum.INCONSISTENT_SPU, MenuGroupEnum.GOODS),
    AUDITING("WP_AUDITING", "审核中商品", TaskTypeEnum.AUDITING, MenuGroupEnum.GOODS),
    PLATFORM_SOLD_OUT("WP_OFF_SHELVES", "平台下架商品", TaskTypeEnum.PLATFORM_SOLD_OUT, MenuGroupEnum.GOODS),
    EXPOSURE_ABNORMAL("WP_EXPOSURE_ABNORMAL", "曝光异常", TaskTypeEnum.EXPOSURE_ABNORMAL, MenuGroupEnum.GOODS),
    MERCHANT_AUDITING("CREATE_MERCHANDISE_ADUIT", "新建商品审核", TaskTypeEnum.MERCHANT_AUDITING, MenuGroupEnum.GOODS),
    RETAIL_PRICE_AUDITING("RETAIL_PRICE_AUDITING", "零售价审核中", TaskTypeEnum.RETAIL_PRICE_AUDITING, MenuGroupEnum.GOODS),
    WP_MISSING_APPLY_GOODS_INFORMATION("WP_MISSING_APPLY_GOODS_INFORMATION", "未上线提报商品", TaskTypeEnum.WP_MISSING_APPLY_GOODS_INFORMATION, MenuGroupEnum.GOODS),

    // 采购
    PURCHASE_NEED_REPLENISH("PURCHASE_NEED_REPLENISH", "待补货", TaskTypeEnum.NEED_REPLENISH_GOODS, MenuGroupEnum.PURCHASE),
    PURCHASE_NEED_REPLENISH_NUM("PURCHASE_NEED_REPLENISH_NUM", "待补货量", TaskTypeEnum.NEED_REPLENISH_GOODS_NUM, MenuGroupEnum.PURCHASE),
    PURCHASE_SOLD_OUT_NOT_REPLENISH("PURCHASE_SOLD_OUT_NOT_REPLENISH", "售罄未补货", TaskTypeEnum.PURCHASE_SOLD_OUT_NOT_REPLENISH, MenuGroupEnum.PURCHASE),
    PURCHASE_SOLD_OUT_NOT_REPLENISH_NUM("PURCHASE_SOLD_OUT_NOT_REPLENISH_NUM", "售罄未补货量", TaskTypeEnum.PURCHASE_SOLD_OUT_NOT_REPLENISH_NUM, MenuGroupEnum.PURCHASE),
    SUPPLY_CHAIN_ORDER("SUPPLY_CHAIN_ORDER", "要货", null, null),

    // 库存组
    UN_RECEIVED("WP_WAITING_RECEIPT", "待收货", TaskTypeEnum.UN_RECEIVED_EXCLUDE_SALE_RETURN, MenuGroupEnum.INVENTORY),
    WAIT_TO_STOCK_TAKE("WP_STOCK_TAKE", "待盘点", TaskTypeEnum.WAIT_TO_STOCK_TAKE, MenuGroupEnum.INVENTORY),
    PENDING_SALE_RETURN_ORDER("WP_PENDING_SALE_RETURN_ORDERS", "销售退货回库", TaskTypeEnum.PENDING_SALE_RETURN_ORDERS, MenuGroupEnum.INVENTORY),
    ABNORMAL_WAIT_TO_STOCK_TAKE("WP_ABNORMAL_STOCK_TAKE", "异常盘点", TaskTypeEnum.ABNORMAL_WAIT_TO_STOCK_TAKE, MenuGroupEnum.INVENTORY),
    ALLOCATE_PICKING_MENU("ALLOCATE_PICKING_MENU", "待拣货", TaskTypeEnum.ALLOCATE_OUT_WAIT_PICKING, MenuGroupEnum.INVENTORY),
    ALLOCATE_STOCK_OUT_MENU("ALLOCATE_STOCK_OUT_MENU", "待出库", TaskTypeEnum.ALLOCATE_STOCK_OUT_MENU, MenuGroupEnum.INVENTORY),
    ABNORMAL_BATCH_STOCK("VALIDITY_WARNING","效期预警商品", TaskTypeEnum.ABNORMAL_BATCH_STOCK, MenuGroupEnum.INVENTORY),
    ALLOCATE_MERGE_ORDER_MENU("ALLOCATE_MERGE_ORDER_MENU", "待集单", TaskTypeEnum.ALLOCATE_OUT_WAIT_COLLECT, MenuGroupEnum.INVENTORY),
    ALLOCATE_WAIT_RECEIVE_MENU("ALLOCATE_RECEIVE_MENU", "待领取", TaskTypeEnum.ALLOCATE_OUT_WAIT_RECEIVE, MenuGroupEnum.INVENTORY),
    ALLOCATE_WAIT_SEED_MENU("ALLOCATE_SORTING_PICKING_MENU", "待分拣", TaskTypeEnum.ALLOCATE_OUT_WAIT_SEED, MenuGroupEnum.INVENTORY),
    ALLOCATE_WAIT_REVIEW_MENU("ALLOCATE_PICKING_REVIEW_MENU", "待复核装箱", TaskTypeEnum.ALLOCATE_OUT_WAIT_REVIEW, MenuGroupEnum.INVENTORY),

    PENDING_PUTAWAY_TASK("PENDING_PUTAWAY_TASK", "待上架", TaskTypeEnum.PENDING_PUTAWAY_TASK, MenuGroupEnum.INVENTORY),
    WAREHOUSE_SUPPLY_TASK("WAREHOUSE_SUPPLY_TASK", "仓内补货", TaskTypeEnum.WAREHOUSE_SUPPLY_TASK, MenuGroupEnum.INVENTORY),
    STOCK_OVER_PERIOD_TASK("STOCK_OVER_PERIOD_TASK", "低温奶库存转移", TaskTypeEnum.STOCK_OVER_PERIOD_TASK, MenuGroupEnum.INVENTORY),
	UN_RECEIVED_MERGED("TAKE-DELIVERY-PENDING", "待收货（新）", TaskTypeEnum.WMS_INBOUND_RECEIVE_PENDING, MenuGroupEnum.INVENTORY),

    // 订单履约
    UN_PICKED("WP_WAITING_PICK", "待拣货", TaskTypeEnum.UN_PICKED, MenuGroupEnum.ORDER),
    UN_PICK_SETTLED("WP_PICK_SETTLEMENT", "拣货结算", TaskTypeEnum.UN_PICK_SETTLED, MenuGroupEnum.ORDER),
    UN_REVIEWED("WP_PICK_WAITING_CONFIRM", "待复核", TaskTypeEnum.UN_MERGE, MenuGroupEnum.ORDER),
    HANDLE_OUT_OF_STOCK("WP_STOCK_OUT", "缺货处理", TaskTypeEnum.LACK_PROCESS, MenuGroupEnum.ORDER),
    DELIVERY_ERROR("WP_DELIVERY_ERROR", "配送异常", TaskTypeEnum.DELIVERY_ERROR, MenuGroupEnum.ORDER),
    BAD_COMMENT("WP_BAD_EVALUATION_DEAL", "中差评处理", TaskTypeEnum.BAD_COMMENT, MenuGroupEnum.ORDER),
    EASY_TO_BAD_REVIEW_SKU_NOT_MARKED("WP_BAD_EVALUATION_MARK", "差评商品待标记", TaskTypeEnum.EASY_TO_BAD_REVIEW_SKU_NOT_MARKED, MenuGroupEnum.ORDER),
    UN_STOCKED("WP_STOCK_UP", "待备货", TaskTypeEnum.UN_PREPARE, MenuGroupEnum.ORDER),
    SELF_DELIVERY_TASK_MENU("SELF_DELIVERY_TASK_MENU", "自配送任务", TaskTypeEnum.SELF_DELIVERY_TASK_MENU, MenuGroupEnum.ORDER),
    PACKING_TASK("CO_PACKING_TASK", "打包任务", TaskTypeEnum.PACKING_TASK, MenuGroupEnum.ORDER),
    //融合后的自配送
    DELIVERY_SELF_TASK_WORKBENCH_MENU("DELIVERY_SELF_TASK_WORKBENCH_MENU", "自配送任务", TaskTypeEnum.SELF_DELIVERY_TASK_MENU, MenuGroupEnum.ORDER),

    // 财务
    SECURITY_DEPOSIT_UNPAID("WP_DEPOSIT_PAY", "保证金缴纳", TaskTypeEnum.SECURITY_DEPOSIT_UNPAID, MenuGroupEnum.FINANCE),

    WM_PROCESSING_TASK("WM_PROCESSING_TASK", "歪马工作台-今日待办", null, null),
    WM_SUPERVISOR_TASK("WM_SUPERVISOR_TASK", "歪马工作台-总部监管", null, null),

    // 歪马待办
    TRANSFER_APPROVAL("WP_WM_TRANSFER_APPROVAL", "转调审批", TaskTypeEnum.WP_WM_TRANSFER_APPROVAL, MenuGroupEnum.WAIMA),
    ATTENDANCE_APPROVAL("WP_WM_ATTENDANCE_APPROVE","考勤审批",TaskTypeEnum.WP_WM_ATTENDANCE_APPROVAL,MenuGroupEnum.WAIMA),
    CANDIDATE_APPROVAL("WP_WM_ONBOARD_APPROVE","入职审批",TaskTypeEnum.WP_WM_CANDIDATE_APPROVAL,MenuGroupEnum.WAIMA),
    RESIGN_APPROVAL("WP_WM_RESIGN_APPROVE","离职审批",TaskTypeEnum.WP_WM_RESIGN_APPROVAL,MenuGroupEnum.WAIMA),
    WM_PROHIBITED_SALES("WM_PROHIBITED_SALES","禁售任务",TaskTypeEnum.WM_PROHIBITED_SALES,MenuGroupEnum.WAIMA),
    WM_UN_RECEIVED("WM_UN_RECEIVED","收货未完成",TaskTypeEnum.WM_UN_RECEIVED,MenuGroupEnum.WAIMA),
    WM_BIZREVIEW("WM_BIZREVIEW","门店巡检",TaskTypeEnum.WM_BIZREVIEW,MenuGroupEnum.WAIMA),
    WM_BIZREVIEW_CORRECT("WM_BIZREVIEW_CORRECT","巡检整改",TaskTypeEnum.WM_BIZREVIEW_CORRECT,MenuGroupEnum.WAIMA),
    WM_BREAK_ORDER_UNFINISHED("WM_BREAK_ORDER_UNFINISHED", "报损未完成", TaskTypeEnum.WM_BREAK_ORDER_UNFINISHED, MenuGroupEnum.WAIMA),
    WM_INVENTORY_UNFINISHED("WM_INVENTORY_UNFINISHED", "盘点未完成", TaskTypeEnum.WM_INVENTORY_UNFINISHED, MenuGroupEnum.WAIMA),
    WM_REVERSE_SHIPPING_UNFINISHED("WM_REVERSE_SHIPPING_UNFINISHED", "采退未出库", TaskTypeEnum.WM_REVERSE_SHIPPING_UNFINISHED, MenuGroupEnum.WAIMA),
    WM_ALLOT_DIFF_ORDER_TO_BE_PROCESS("WM_ALLOT_DIFF_ORDER_TO_BE_PROCESS", "调拨差异待处理", TaskTypeEnum.WM_ALLOT_DIFF_ORDER_TO_BE_PROCESS, MenuGroupEnum.WAIMA),
    WM_COLD_ZONE_OUT_OF_STOCK_ALERT_PROCESS("WM_COLD_ZONE_OUT_OF_STOCK_ALERT_PROCESS", "冷藏缺货处理", TaskTypeEnum.WM_COLD_ZONE_OUT_OF_STOCK_ALERT_PROCESS, MenuGroupEnum.WAIMA),
    WM_RETURN_ZONE_PROCESS("WM_RETURN_ZONE_PROCESS", "销退处理", TaskTypeEnum.WM_RETURN_ZONE_PROCESS, MenuGroupEnum.WAIMA),
    WM_RETURN_TASK_PROCESS("WM_RETURN_TASK_PROCESS", "客退待处理", TaskTypeEnum.WM_RETURN_TASK_PROCESS, MenuGroupEnum.WAIMA),
    WM_PUNISH_WAITING_APPEAL("WM_PUNISH_WAITING_APPEAL", "违规待申诉", TaskTypeEnum.WM_PUNISH_WAITING_APPEAL, MenuGroupEnum.WAIMA),
    WM_HEALTH_APPEAL("WM_HEALTH_APPEAL", "健康度申诉", TaskTypeEnum.WM_HEALTH_APPEAL, MenuGroupEnum.WAIMA),
    WM_HEALTH_BELOW_STANDARDS("WM_HEALTH_BELOW_STANDARDS", "健康度不达标", TaskTypeEnum.WM_HEALTH_BELOW_STANDARDS, MenuGroupEnum.WAIMA),
    WM_SALE_STOCK_LACK("WP_WM_SALE_STOCK_LACK", "可售库存不足", TaskTypeEnum.WM_ABN_ORDER, MenuGroupEnum.WAIMA),
    WM_STALL_EXPIRE("WM_STALL_EXPIRE", "摆摊未归还", TaskTypeEnum.WM_STALL_EXPIRE, MenuGroupEnum.WAIMA),
    PRICE_SET_ABNORMAL("WP_PRICE_SET_ABNORMAL", "商品价格异常", TaskTypeEnum.PRICE_SET_ABNORMAL, MenuGroupEnum.GOODS),
    WM_WAREHOUSE_OUTBOUND_PROCESS("WM_WAREHOUSE_OUTBOUND_PROCESS", "调拨出库未完成", TaskTypeEnum.WM_WAREHOUSE_OUTBOUND_PROCESS, MenuGroupEnum.WAIMA),
    SMILE_ACT_WAIT_COLLECT("SMILE_ACT_WAIT_COLLECT", "微笑行动", null, MenuGroupEnum.SMILE),
	WM_UN_RECEIVED_MERGED("WAIMA-TAKE-DELIVERY-PENDING", "待收货（新）", TaskTypeEnum.WM_WMS_INBOUND_RECEIVE_PENDING, MenuGroupEnum.WAIMA),
    ;

    /**
     * 在etops中配置的菜单编码
     */
    private final String code;

    /**
     * 描述信息，前端显示用
     */
    private final String desc;

    /**
     * 在调用munich的thrift接口时，用的taskType，建议与MenuCodeEnum的name使用相同的名字
     */
    private final TaskTypeEnum taskTypeEnum;

    /**
     * menu对应的组关系，如果是待办事项，必须填写
     *
     * @see MenuGroupEnum
     */
    private final MenuGroupEnum groupEnum;

    MenuCodeEnum(String auth, String desc, TaskTypeEnum taskTypeEnum, MenuGroupEnum groupEnum) {
        this.code = auth;
        this.desc = desc;
        this.taskTypeEnum = taskTypeEnum;
        this.groupEnum = groupEnum;
    }

    public static MenuCodeEnum ofAuthCode(String authCode) {
        for (MenuCodeEnum moduleEnum : MenuCodeEnum.values()) {
            if (moduleEnum.code.equals(authCode)) {
                return moduleEnum;
            }
        }
        log.warn("工作台未找到权限码对应的MenuCode, authCode:{}", authCode);
        return null;
    }

    public static MenuCodeEnum ofTaskType(TaskTypeEnum taskType) {
        for (MenuCodeEnum moduleEnum : MenuCodeEnum.values()) {
            if (Objects.nonNull(moduleEnum.getTaskTypeEnum()) && moduleEnum.taskTypeEnum.equals(taskType)) {
                return moduleEnum;
            }
        }
        log.warn("工作台未找到任务类型对应的MenuCode, taskType:{}", taskType);
        return null;
    }
}
