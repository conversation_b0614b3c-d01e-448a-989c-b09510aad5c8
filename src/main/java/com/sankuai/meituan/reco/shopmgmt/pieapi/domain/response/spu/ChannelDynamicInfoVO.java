package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct.ChannelStructAttrTemplateVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct.ChannelStructAttrValueInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct.ChannelStructAttrValueVO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelDynamicInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelDynamicInfoValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.CategoryAttrValueTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelDynamicInfoBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelDynamicInfoOptionBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelDynamicInfoValueBizDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoOptionDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSkuAttrDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSkuAttrValueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/6/17 14:21
 **/
@TypeDoc(
        name = "渠道动态信息VO对象",
        description = "渠道动态信息VO对象"
)
@Data
@ApiModel("渠道动态信息")
public class ChannelDynamicInfoVO {

    @FieldDoc(
            description = "属性id"
    )
    @ApiModelProperty(name = "属性id")
    private Long attrId;

    @FieldDoc(
            description = "属性名称"
    )
    @ApiModelProperty(name = "属性名称")
    private String attrName;

    @FieldDoc(
            description = "属性值类型，1-单选,2-多选,3-文本,4-搜索单选,5-级联单选"
    )
    @ApiModelProperty(name = "属性值类型")
    private String attrValueType;

    @FieldDoc(
            description = "属性值可录入的字符类型，参考值：1-中文；2-字母；3-数字；4-标点符号"
    )
    @ApiModelProperty(name = "属性值可录入的字符类型")
    private String characterType;

    @FieldDoc(
            description = "是否必传， 1-必传,2-非必传"
    )
    @ApiModelProperty(name = "是否必传， 1-必传,2-非必传")
    private Integer isRequired;

    @FieldDoc(
            description = "属性值类型为文本时，允许录入的最大字符长度"
    )
    @ApiModelProperty(name = "属性值类型为文本时，允许录入的最大字符长度")
    private Integer maxTextLength;

    @FieldDoc(
            description = "属性的顺序值"
    )
    @ApiModelProperty(name = "属性的顺序值")
    private Integer attrSequence;

    @FieldDoc(
            description = "属性值列表"
    )
    @ApiModelProperty(name = "属性值列表")
    private List<ChannelDynamicInfoValueVO> attrValueList;

    @FieldDoc(
            description = "用户所选的值"
    )
    @ApiModelProperty(name = "用户所选的值")
    private List<ChannelDynamicInfoValueVO> customValue;

    @FieldDoc(
            description = "属性为'文本+选项'时，选项值"
    )
    @ApiModelProperty("属性为'文本+选项'时，选项值")
    public List<ChannelDynamicInfoOptionVO> optionList;

    @FieldDoc(
            description = "结构化属性模板列表-组合规则，仅当attrValueType=10时有值"
    )
    private List<ChannelStructAttrTemplateVO> structAttrTemplateList;

    @FieldDoc(
            description = "原始填写值，拼接customValue，用于前端展示。当前仅attrValueType=10有效，当最新模板不兼容时有值，如果最新模板可以兼容则无值"
    )
    private String originalInputValue;

    @TypeDoc(
            name = "属性值选项",
            description = "属性值选项"
    )
    @Data
    public static class ChannelDynamicInfoOptionVO{

        private Long optionId;

        private String optionValue;

        public static List<ChannelDynamicInfoOptionVO> ofPlatformDTOList(List<ChannelDynamicInfoOptionDTO> options) {
            return Fun.map(options, option -> {
                ChannelDynamicInfoOptionVO channelDynamicInfoOptionVO = new ChannelDynamicInfoOptionVO();
                channelDynamicInfoOptionVO.setOptionId(option.getOptionId());
                channelDynamicInfoOptionVO.setOptionValue(option.getOptionValue());
                return channelDynamicInfoOptionVO;
            });
        }

        public static List<ChannelDynamicInfoOptionVO> ofBizDTOList(List<ChannelDynamicInfoOptionBizDTO> options) {
            return Fun.map(options, option -> {
                ChannelDynamicInfoOptionVO channelDynamicInfoOptionVO = new ChannelDynamicInfoOptionVO();
                channelDynamicInfoOptionVO.setOptionId(option.getOptionId());
                channelDynamicInfoOptionVO.setOptionValue(option.getOptionValue());
                return channelDynamicInfoOptionVO;
            });
        }

        public static List<ChannelDynamicInfoOptionBizDTO> toBizDTOList(List<ChannelDynamicInfoOptionVO> optionList) {
            return Fun.map(optionList, option -> {
                ChannelDynamicInfoOptionBizDTO channelDynamicInfoOptionBizDTO = new ChannelDynamicInfoOptionBizDTO();
                channelDynamicInfoOptionBizDTO.setOptionId(option.getOptionId());
                channelDynamicInfoOptionBizDTO.setOptionValue(option.getOptionValue());
                return channelDynamicInfoOptionBizDTO;
            });
        }

        public static List<ChannelDynamicInfoOptionVO> ofDTOList(List<com.sankuai.meituan.shangou.empower.ocms.client.
                product.dto.ChannelDynamicInfoOptionDTO> optionList) {
            return Fun.map(optionList, option -> {
                ChannelDynamicInfoOptionVO channelDynamicInfoOptionVO = new ChannelDynamicInfoOptionVO();
                channelDynamicInfoOptionVO.setOptionId(option.getOptionId());
                channelDynamicInfoOptionVO.setOptionValue(option.getOptionValue());
                return channelDynamicInfoOptionVO;
            });
        }

        public static List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.
                ChannelDynamicInfoOptionDTO> toDTOList(List<ChannelDynamicInfoOptionVO> optionList) {
            return Fun.map(optionList, option -> {
                com.sankuai.meituan.shangou.empower.ocms.client.product.dto.
                        ChannelDynamicInfoOptionDTO optionDTO = new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelDynamicInfoOptionDTO();
                optionDTO.setOptionId(option.getOptionId());
                optionDTO.setOptionValue(option.getOptionValue());
                return optionDTO;
            });
        }
    }

    public static List<ChannelDynamicInfoVO> ofPlatformSkuDTOList(List<ChannelSkuAttrDTO> channelSkuAttrList) {
        if(CollectionUtils.isEmpty(channelSkuAttrList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoVO> dynamicInfoVOList = new ArrayList<>();
        for(com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSkuAttrDTO categoryAttrDTO : channelSkuAttrList){
            ChannelDynamicInfoVO dynamicInfoVO = new ChannelDynamicInfoVO();
            dynamicInfoVO.setAttrId(Long.parseLong(categoryAttrDTO.getAttrId()));
            dynamicInfoVO.setAttrName(categoryAttrDTO.getAttrName());
            dynamicInfoVO.setAttrValueType(categoryAttrDTO.getAttrValueType());
            dynamicInfoVO.setIsRequired(categoryAttrDTO.getIsRequired());
            dynamicInfoVO.setCharacterType(categoryAttrDTO.getCharacterType());
            dynamicInfoVO.setMaxTextLength(categoryAttrDTO.getMaxTextLength());
            dynamicInfoVO.setAttrSequence(categoryAttrDTO.getAttrSequence());
            dynamicInfoVO.setAttrValueList(ChannelDynamicInfoValueVO.ofPlatformSkuDTOList(categoryAttrDTO.getValueList()));
            dynamicInfoVOList.add(dynamicInfoVO);
        }
        return dynamicInfoVOList;
    }

    @TypeDoc(
            name = "渠道动态信息属性值",
            description = "渠道动态信息属性值"
    )
    @Data
    public static class ChannelDynamicInfoValueVO{
        @FieldDoc(
                description = "属性值id"
        )
        private String attrValueId;

        @FieldDoc(
                description = "属性值"
        )
        private String attrValue;

        /**
         * [模板1属性值,模板2属性值]
         * 目前只有一个模板，此结构为了后续兼容后续可能多个模板拼接一个属性值的情况
         */
        @FieldDoc(
                description = "结构化属性值"
        )
        private List<ChannelStructAttrValueInfoVO> structAttrValueList;

        public static List<ChannelDynamicInfoValueVO> ofDTOList(List<ChannelDynamicInfoValueDTO> categoryAttrValueDTOList){
            if(CollectionUtils.isEmpty(categoryAttrValueDTOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList = new ArrayList<>();
            for(ChannelDynamicInfoValueDTO categoryAttrValueDTO : categoryAttrValueDTOList){
                ChannelDynamicInfoValueVO dynamicInfoValueVO = new ChannelDynamicInfoValueVO();
                dynamicInfoValueVO.setAttrValueId(categoryAttrValueDTO.getAttrValueId());
                dynamicInfoValueVO.setAttrValue(categoryAttrValueDTO.getAttrValue());
                dynamicInfoValueVO.setStructAttrValueList(Fun.map(categoryAttrValueDTO.getStructAttrValueList(), ChannelStructAttrValueInfoVO::ofOcmsDto));
                dynamicInfoValueVOList.add(dynamicInfoValueVO);
            }
            return dynamicInfoValueVOList;
        }

        public static List<ChannelDynamicInfoValueVO> ofPlatformDTOList(List<com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoValueDTO> categoryAttrValueDTOList){
            if(CollectionUtils.isEmpty(categoryAttrValueDTOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList = new ArrayList<>();
            for(com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoValueDTO categoryAttrValueDTO : categoryAttrValueDTOList){
                ChannelDynamicInfoValueVO dynamicInfoValueVO = new ChannelDynamicInfoValueVO();
                dynamicInfoValueVO.setAttrValueId(categoryAttrValueDTO.getAttrValueId());
                dynamicInfoValueVO.setAttrValue(categoryAttrValueDTO.getAttrValue());
                dynamicInfoValueVOList.add(dynamicInfoValueVO);
            }
            return dynamicInfoValueVOList;
        }

        public static List<ChannelDynamicInfoValueDTO> toDTOList(List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList){
            if(CollectionUtils.isEmpty(dynamicInfoValueVOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueDTO> dynamicInfoValueDTOList = new ArrayList<>();
            for(ChannelDynamicInfoValueVO dynamicInfoValueVO : dynamicInfoValueVOList){
                ChannelDynamicInfoValueDTO dynamicInfoValueDTO = new ChannelDynamicInfoValueDTO();
                dynamicInfoValueDTO.setAttrValueId(dynamicInfoValueVO.getAttrValueId());
                dynamicInfoValueDTO.setAttrValue(dynamicInfoValueVO.getAttrValue());
                dynamicInfoValueDTO.setStructAttrValueList(ChannelStructAttrValueInfoVO
                        .toOcmsBizDTOList(dynamicInfoValueVO.getStructAttrValueList()));
                dynamicInfoValueDTOList.add(dynamicInfoValueDTO);
            }
            return  dynamicInfoValueDTOList;
        }

        public static List<ChannelDynamicInfoValueBizDTO> toBizDTOList(List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList){
            if(CollectionUtils.isEmpty(dynamicInfoValueVOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueBizDTO> dynamicInfoValueDTOList = new ArrayList<>();
            for(ChannelDynamicInfoValueVO dynamicInfoValueVO : dynamicInfoValueVOList){
                ChannelDynamicInfoValueBizDTO dynamicInfoValueDTO = new ChannelDynamicInfoValueBizDTO();
                dynamicInfoValueDTO.setAttrValueId(dynamicInfoValueVO.getAttrValueId());
                dynamicInfoValueDTO.setAttrValue(dynamicInfoValueVO.getAttrValue());
                dynamicInfoValueDTO.setStructAttrValueList(ChannelStructAttrValueInfoVO
                        .toBizDTOList(dynamicInfoValueVO.getStructAttrValueList()));
                dynamicInfoValueDTOList.add(dynamicInfoValueDTO);
            }
            return dynamicInfoValueDTOList;
        }

        public static List<ChannelDynamicInfoValueVO> ofBizDTOList(List<ChannelDynamicInfoValueBizDTO> categoryAttrValueDTOList){
            if(CollectionUtils.isEmpty(categoryAttrValueDTOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList = new ArrayList<>();
            for(ChannelDynamicInfoValueBizDTO categoryAttrValueDTO : categoryAttrValueDTOList){
                ChannelDynamicInfoValueVO dynamicInfoValueVO = new ChannelDynamicInfoValueVO();
                dynamicInfoValueVO.setAttrValueId(categoryAttrValueDTO.getAttrValueId());
                dynamicInfoValueVO.setAttrValue(categoryAttrValueDTO.getAttrValue());
                dynamicInfoValueVO.setStructAttrValueList(ChannelStructAttrValueInfoVO
                        .ofBizDTOList(categoryAttrValueDTO.getStructAttrValueList()));
                dynamicInfoValueVOList.add(dynamicInfoValueVO);
            }
            return dynamicInfoValueVOList;
        }

        public static List<ChannelDynamicInfoValueVO> ofPlatformSkuDTOList(List<ChannelSkuAttrValueDTO> valueList) {
            if(CollectionUtils.isEmpty(valueList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList = new ArrayList<>();
            for(com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSkuAttrValueDTO categoryAttrValueDTO : valueList){
                ChannelDynamicInfoValueVO dynamicInfoValueVO = new ChannelDynamicInfoValueVO();
                dynamicInfoValueVO.setAttrValueId(categoryAttrValueDTO.getAttrValueId());
                dynamicInfoValueVO.setAttrValue(categoryAttrValueDTO.getAttrValue());
                dynamicInfoValueVOList.add(dynamicInfoValueVO);
            }
            return dynamicInfoValueVOList;
        }
    }

    public static List<ChannelDynamicInfoVO> ofDTOList(List<ChannelDynamicInfoDTO> categoryAttrDTOList){
        if(CollectionUtils.isEmpty(categoryAttrDTOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoVO> dynamicInfoVOList = new ArrayList<>();
        for(ChannelDynamicInfoDTO categoryAttrDTO : categoryAttrDTOList){
            ChannelDynamicInfoVO dynamicInfoVO = new ChannelDynamicInfoVO();
            dynamicInfoVO.setAttrId(Long.parseLong(categoryAttrDTO.getAttrId()));
            dynamicInfoVO.setAttrName(categoryAttrDTO.getAttrName());
            dynamicInfoVO.setAttrValueType(categoryAttrDTO.getAttrValueType());
            dynamicInfoVO.setIsRequired(categoryAttrDTO.getIsRequired());
            dynamicInfoVO.setCharacterType(categoryAttrDTO.getCharacterType());
            dynamicInfoVO.setMaxTextLength(categoryAttrDTO.getMaxTextLength());
            dynamicInfoVO.setAttrSequence(categoryAttrDTO.getAttrSequence());
            dynamicInfoVO.setAttrValueList(ChannelDynamicInfoValueVO.ofDTOList(categoryAttrDTO.getAttrValueList()));
            dynamicInfoVO.setCustomValue(ChannelDynamicInfoValueVO.ofDTOList(categoryAttrDTO.getCustomValue()));
            dynamicInfoVO.setOptionList(ChannelDynamicInfoOptionVO.ofDTOList(categoryAttrDTO.getOptionList()));
            dynamicInfoVO.setStructAttrTemplateList(Fun.map(categoryAttrDTO.getStructAttrTemplateList(), ChannelStructAttrTemplateVO::ofOcmsDTO));
            dynamicInfoVO.setOriginalInputValue(categoryAttrDTO.getOriginalInputValue());
            // 前端逻辑，当判断有原始值时不展示原结构化值
            if (StringUtils.isNotBlank(categoryAttrDTO.getOriginalInputValue()) && CollectionUtils.isNotEmpty(dynamicInfoVO.getCustomValue())){
                dynamicInfoVO.getCustomValue().get(0).setStructAttrValueList(null);
            }
            dynamicInfoVOList.add(dynamicInfoVO);
        }
        return dynamicInfoVOList;
    }

    public static List<ChannelDynamicInfoVO> ofPlatformDTOList(List<com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoDTO> categoryAttrDTOList){
        if(CollectionUtils.isEmpty(categoryAttrDTOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoVO> dynamicInfoVOList = new ArrayList<>();
        for(com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoDTO categoryAttrDTO : categoryAttrDTOList){
            ChannelDynamicInfoVO dynamicInfoVO = new ChannelDynamicInfoVO();
            dynamicInfoVO.setAttrId(Long.parseLong(categoryAttrDTO.getAttrId()));
            dynamicInfoVO.setAttrName(categoryAttrDTO.getAttrName());
            dynamicInfoVO.setAttrValueType(categoryAttrDTO.getAttrValueType());
            dynamicInfoVO.setIsRequired(categoryAttrDTO.getIsRequired());
            dynamicInfoVO.setCharacterType(categoryAttrDTO.getCharacterType());
            dynamicInfoVO.setMaxTextLength(categoryAttrDTO.getMaxTextLength());
            dynamicInfoVO.setAttrSequence(categoryAttrDTO.getAttrSequence());
            dynamicInfoVO.setAttrValueList(ChannelDynamicInfoValueVO.ofPlatformDTOList(categoryAttrDTO.getAttrValueList()));
            dynamicInfoVO.setCustomValue(ChannelDynamicInfoValueVO.ofPlatformDTOList(categoryAttrDTO.getCustomValue()));
            dynamicInfoVO.setOptionList(ChannelDynamicInfoOptionVO.ofPlatformDTOList(categoryAttrDTO.getOptionList()));
            dynamicInfoVO.setStructAttrTemplateList(ChannelStructAttrTemplateVO.ofPlatformDTOList(categoryAttrDTO.getStructAttrTemplateList()));
            dynamicInfoVOList.add(dynamicInfoVO);
        }
        return dynamicInfoVOList;
    }

    public static List<ChannelDynamicInfoDTO> toDTOList(List<ChannelDynamicInfoVO> dynamicInfoVOList){
        if(CollectionUtils.isEmpty(dynamicInfoVOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoDTO> dynamicInfoDTOS = new ArrayList<>();
        for(ChannelDynamicInfoVO dynamicInfoVO : dynamicInfoVOList){
            ChannelDynamicInfoDTO dynamicInfoDTO = new ChannelDynamicInfoDTO();
            dynamicInfoDTO.setAttrId(String.valueOf(dynamicInfoVO.getAttrId()));
            dynamicInfoDTO.setAttrName(dynamicInfoVO.getAttrName());
            dynamicInfoDTO.setAttrValueType(dynamicInfoVO.getAttrValueType());
            dynamicInfoDTO.setIsRequired(dynamicInfoVO.getIsRequired());
            dynamicInfoDTO.setCharacterType(dynamicInfoVO.getCharacterType());
            dynamicInfoDTO.setMaxTextLength(dynamicInfoVO.getMaxTextLength());
            dynamicInfoDTO.setAttrSequence(dynamicInfoVO.getAttrSequence());
            dynamicInfoDTO.setAttrValueList(ChannelDynamicInfoValueVO.toDTOList(dynamicInfoVO.getAttrValueList()));
            dynamicInfoDTO.setCustomValue(ChannelDynamicInfoValueVO.toDTOList(dynamicInfoVO.getCustomValue()));
            dynamicInfoDTO.setOptionList(ChannelDynamicInfoOptionVO.toDTOList(dynamicInfoVO.getOptionList()));
            dynamicInfoDTO.setStructAttrTemplateList(ChannelStructAttrTemplateVO.toOcmsBizDTOList(dynamicInfoVO.getStructAttrTemplateList()));
            dynamicInfoDTOS.add(dynamicInfoDTO);
        }
        return dynamicInfoDTOS;
    }

    public static List<ChannelDynamicInfoBizDTO> toBizDTOList(List<ChannelDynamicInfoVO> dynamicInfoVOList){
        if(CollectionUtils.isEmpty(dynamicInfoVOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoBizDTO> dynamicInfoDTOS = new ArrayList<>();
        for(ChannelDynamicInfoVO dynamicInfoVO : dynamicInfoVOList){
            ChannelDynamicInfoBizDTO dynamicInfoDTO = new ChannelDynamicInfoBizDTO();
            dynamicInfoDTO.setAttrId(String.valueOf(dynamicInfoVO.getAttrId()));
            dynamicInfoDTO.setAttrName(dynamicInfoVO.getAttrName());
            dynamicInfoDTO.setAttrValueType(dynamicInfoVO.getAttrValueType());
            dynamicInfoDTO.setIsRequired(dynamicInfoVO.getIsRequired());
            dynamicInfoDTO.setCharacterType(dynamicInfoVO.getCharacterType());
            dynamicInfoDTO.setMaxTextLength(dynamicInfoVO.getMaxTextLength());
            dynamicInfoDTO.setAttrSequence(dynamicInfoVO.getAttrSequence());
            dynamicInfoDTO.setAttrValueList(ChannelDynamicInfoValueVO.toBizDTOList(dynamicInfoVO.getAttrValueList()));
            dynamicInfoDTO.setCustomValue(ChannelDynamicInfoValueVO.toBizDTOList(dynamicInfoVO.getCustomValue()));
            dynamicInfoDTO.setOptionList(ChannelDynamicInfoOptionVO.toBizDTOList(dynamicInfoVO.getOptionList()));
            dynamicInfoDTO.setStructAttrTemplateList(ChannelStructAttrTemplateVO.toBizDTOList(dynamicInfoVO.getStructAttrTemplateList()));
            dynamicInfoDTOS.add(dynamicInfoDTO);
        }
        return dynamicInfoDTOS;
    }

    public static List<ChannelDynamicInfoVO> ofBizDTOList(List<ChannelDynamicInfoBizDTO> categoryAttrDTOList){
        if(CollectionUtils.isEmpty(categoryAttrDTOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoVO> dynamicInfoVOList = new ArrayList<>();
        for(ChannelDynamicInfoBizDTO categoryAttrDTO : categoryAttrDTOList){
            ChannelDynamicInfoVO dynamicInfoVO = new ChannelDynamicInfoVO();
            dynamicInfoVO.setAttrId(Long.parseLong(categoryAttrDTO.getAttrId()));
            dynamicInfoVO.setAttrName(categoryAttrDTO.getAttrName());
            dynamicInfoVO.setAttrValueType(categoryAttrDTO.getAttrValueType());
            dynamicInfoVO.setIsRequired(categoryAttrDTO.getIsRequired());
            dynamicInfoVO.setCharacterType(categoryAttrDTO.getCharacterType());
            dynamicInfoVO.setMaxTextLength(categoryAttrDTO.getMaxTextLength());
            dynamicInfoVO.setAttrSequence(categoryAttrDTO.getAttrSequence());
            dynamicInfoVO.setAttrValueList(ChannelDynamicInfoValueVO.ofBizDTOList(categoryAttrDTO.getAttrValueList()));
            dynamicInfoVO.setCustomValue(ChannelDynamicInfoValueVO.ofBizDTOList(categoryAttrDTO.getCustomValue()));
            dynamicInfoVO.setOptionList(ChannelDynamicInfoOptionVO.ofBizDTOList(categoryAttrDTO.getOptionList()));
            dynamicInfoVO.setStructAttrTemplateList(ChannelStructAttrTemplateVO
                    .ofBizDTOList(categoryAttrDTO.getStructAttrTemplateList()));
            dynamicInfoVO.setOriginalInputValue(categoryAttrDTO.getOriginalInputValue());
            // 前端逻辑，当判断有原始值时不展示原结构化值
            if (StringUtils.isNotBlank(categoryAttrDTO.getOriginalInputValue()) && CollectionUtils.isNotEmpty(dynamicInfoVO.getCustomValue())){
                dynamicInfoVO.getCustomValue().get(0).setStructAttrValueList(null);
            }
            dynamicInfoVOList.add(dynamicInfoVO);
        }
        return dynamicInfoVOList;
    }

    public static String toCategoryProperties(List<ChannelDynamicInfoVO> dynamicInfoVOList){
        if(CollectionUtils.isEmpty(dynamicInfoVOList)){
            return "";
        }
        JSONArray attrJa = new JSONArray();
        for(ChannelDynamicInfoVO dynamicInfoVO : dynamicInfoVOList){
            if(CollectionUtils.isEmpty(dynamicInfoVO.getCustomValue())){
                continue;
            }
            JSONObject attrJo = new JSONObject();
            JSONArray attrValueJa = new JSONArray();
            for(ChannelDynamicInfoValueVO customValue : dynamicInfoVO.getCustomValue()){
                JSONObject attrValueJo = new JSONObject();
                if(CategoryAttrValueTypeEnum.TEXT.getCode().equals(dynamicInfoVO.getAttrValueType())
                        || CategoryAttrValueTypeEnum.TEXT_AND_OPTION.getCode().equals(dynamicInfoVO.getAttrValueType()) ) {
                    attrValueJo.put("value", customValue.getAttrValue());
                    attrValueJa.add(attrValueJo);
                    break;
                }
                if (com.sankuai.meituan.shangou.empower.productbiz.client.enums.CategoryAttrValueTypeEnum.STRUCT_ATTR.getCode().equals(dynamicInfoVO.getAttrValueType())){
                    // 值为空时跳过
                    if(CollectionUtils.isEmpty(customValue.getStructAttrValueList())){
                        break;
                    }
                    attrValueJo.put("structAttrValueList", Fun.map(customValue.getStructAttrValueList(), ChannelDynamicInfoVO::toStructAttrValueList));
                    attrValueJa.add(attrValueJo);
                    break;
                }
                attrValueJo.put("valueId", Long.parseLong(customValue.getAttrValueId()));
                attrValueJo.put("value", customValue.getAttrValue());
                attrValueJa.add(attrValueJo);
            }
            attrJo.put("attrId", dynamicInfoVO.getAttrId());
            attrJo.put("attrName", dynamicInfoVO.getAttrName());
            attrJo.put("valueList", attrValueJa);
            attrJa.add(attrJo);
        }
        if(CollectionUtils.isEmpty(attrJa)){
            return "";
        }
        return attrJa.toJSONString();
    }

    private static JSONObject toStructAttrValueList(ChannelStructAttrValueInfoVO attrValueInfoVO) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("formatType", attrValueInfoVO.getFormatType());
        List<List<ChannelStructAttrValueVO>> valueList = attrValueInfoVO.getValueList();
        if (CollectionUtils.isNotEmpty(valueList)) {
            JSONArray outerArray = new JSONArray();
            for (List<ChannelStructAttrValueVO> domainRow : valueList) {
                if (CollectionUtils.isNotEmpty(domainRow)) {
                    JSONArray innerArray = new JSONArray();
                    for (ChannelStructAttrValueVO valueDomain : domainRow) {
                        JSONObject valueJson = new JSONObject();
                        valueJson.put("value", valueDomain.getValue());
                        valueJson.put("optionId", valueDomain.getOptionId());
                        valueJson.put("sequence", valueDomain.getSequence());
                        valueJson.put("optionValue", valueDomain.getOptionValue());
                        innerArray.add(valueJson);
                    }
                    outerArray.add(innerArray);
                }
            }
            jsonObject.put("valueList", outerArray);
        }
        return jsonObject;
    }
}
