package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ShowTagEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.ShowTagVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/8/28 15:30
 **/
@Data
public class ThirdDeliveryOrderVO extends OrderVO {
    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达截止时间")
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达剩余时间")
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达超时时间")
    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送平台Code")
    private Integer deliveryPlatformCode;

    @FieldDoc(
            description = "", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送平台描述")
    private String deliveryPlatformDesc;

    @FieldDoc(
            description = "是否有缺货情况", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否有缺货情况", required = false)
    private Boolean hasLackGoods;

    @FieldDoc(
            description = "展示标签，包括 大范围标签等", requiredness = Requiredness.OPTIONAL
    )
    private List<ShowTagVO> showTags;

    @FieldDoc(
            description = "考核配送时间", requiredness = Requiredness.OPTIONAL
    )
    private Long assessDeliveryTime;


    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;

    public static ThirdDeliveryOrderVO createFromOrderVO(OrderVO orderVO) {
        ThirdDeliveryOrderVO thirdDeliveryOrderVO = new ThirdDeliveryOrderVO();
        thirdDeliveryOrderVO.setTenantId(orderVO.getTenantId());
        thirdDeliveryOrderVO.setChannelId(orderVO.getChannelId());
        thirdDeliveryOrderVO.setChannelName(orderVO.getChannelName());
        thirdDeliveryOrderVO.setStoreId(orderVO.getStoreId());
        thirdDeliveryOrderVO.setStoreName(orderVO.getStoreName());
        thirdDeliveryOrderVO.setChannelOrderId(orderVO.getChannelOrderId());
        thirdDeliveryOrderVO.setUserId(orderVO.getUserId());
        thirdDeliveryOrderVO.setSerialNo(orderVO.getSerialNo());
        thirdDeliveryOrderVO.setItemCount(orderVO.getItemCount());
        thirdDeliveryOrderVO.setActualPayAmt(orderVO.getActualPayAmt());
        thirdDeliveryOrderVO.setBizReceiveAmt(orderVO.getBizReceiveAmt());
        thirdDeliveryOrderVO.setDistributeStatus(orderVO.getDistributeStatus());
        thirdDeliveryOrderVO.setDistributeStatusDesc(orderVO.getDistributeStatusDesc());
        thirdDeliveryOrderVO.setEmpowerOrderId(orderVO.getEmpowerOrderId());
        thirdDeliveryOrderVO.setRealDistributeStatus(orderVO.getRealDistributeStatus());
        thirdDeliveryOrderVO.setCancelStatus(orderVO.getCancelStatus());
        thirdDeliveryOrderVO.setDeliveryChannel(orderVO.getDeliveryChannel());
        thirdDeliveryOrderVO.setDeliveryChannelId(orderVO.getDeliveryChannelId());
        thirdDeliveryOrderVO.setMigrateFlag(orderVO.getMigrateFlag());
        thirdDeliveryOrderVO.setDeliveryChannelType(orderVO.getDeliveryChannelType());
        thirdDeliveryOrderVO.setDeliveryOperateItem(orderVO.getDeliveryOperateItem());
        thirdDeliveryOrderVO.setDeliveryExceptionDesc(orderVO.getDeliveryExceptionDesc());
        thirdDeliveryOrderVO.setDeliveryExceptionType(orderVO.getDeliveryExceptionType());
        thirdDeliveryOrderVO.setDeliveryDistance(orderVO.getDeliveryDistance());
        thirdDeliveryOrderVO.setDeliveryFee(orderVO.getDeliveryFee());
        thirdDeliveryOrderVO.setCurrentDeliveryStatusStartTime(orderVO.getCurrentDeliveryStatusStartTime());
        thirdDeliveryOrderVO.setDeliveryInfoSource(orderVO.getDeliveryInfoSource());
        thirdDeliveryOrderVO.setDeliveryMethod(orderVO.getDeliveryMethod());
        thirdDeliveryOrderVO.setSelfDelivery(orderVO.getSelfDelivery());
        thirdDeliveryOrderVO.setSupportDeliveryUserPrivacyPhone(orderVO.getSupportDeliveryUserPrivacyPhone());
        thirdDeliveryOrderVO.setOrderSource(orderVO.getOrderSource());
        thirdDeliveryOrderVO.setDeliveryMethodDesc(orderVO.getDeliveryMethodDesc());
        thirdDeliveryOrderVO.setSelfFetchStatus(orderVO.getSelfFetchStatus());
        thirdDeliveryOrderVO.setDeliveryUserName(orderVO.getDeliveryUserName());
        thirdDeliveryOrderVO.setDeliveryUserPhone(orderVO.getDeliveryUserPhone());
        thirdDeliveryOrderVO.setReceiverName(orderVO.getReceiverName());
        thirdDeliveryOrderVO.setReceiverPhone(orderVO.getReceiverPhone());
        thirdDeliveryOrderVO.setReceiverAddress(orderVO.getReceiverAddress());
        thirdDeliveryOrderVO.setOrderStatus(orderVO.getOrderStatus());
        thirdDeliveryOrderVO.setOrderStatusDesc(orderVO.getOrderStatusDesc());
        thirdDeliveryOrderVO.setCreateTime(orderVO.getCreateTime());
        thirdDeliveryOrderVO.setDeliveryOrderType(orderVO.getDeliveryOrderType());
        thirdDeliveryOrderVO.setDeliveryOrderTypeName(orderVO.getDeliveryOrderTypeName());
        thirdDeliveryOrderVO.setEstimateArriveTimeStart(orderVO.getEstimateArriveTimeStart());
        thirdDeliveryOrderVO.setEstimateArriveTimeEnd(orderVO.getEstimateArriveTimeEnd());
        thirdDeliveryOrderVO.setPickStatus(orderVO.getPickStatus());
        thirdDeliveryOrderVO.setPickCompleteTime(orderVO.getPickCompleteTime());
        thirdDeliveryOrderVO.setUpdateTime(orderVO.getUpdateTime());
        thirdDeliveryOrderVO.setChannelExtraOrderId(orderVO.getChannelExtraOrderId());
        thirdDeliveryOrderVO.setComments(orderVO.getComments());
        thirdDeliveryOrderVO.setProductList(orderVO.getProductList());
        thirdDeliveryOrderVO.setTotalOfflinePrice(orderVO.getTotalOfflinePrice());
        thirdDeliveryOrderVO.setGiftVOList(orderVO.getGiftVOList());
        thirdDeliveryOrderVO.setGiftCount(orderVO.getGiftCount());
        thirdDeliveryOrderVO.setPrintStatus(orderVO.getPrintStatus());
        thirdDeliveryOrderVO.setOrderRefundInfo(orderVO.getOrderRefundInfo());
        thirdDeliveryOrderVO.setOrderCouldOperateItems(orderVO.getOrderCouldOperateItems());
        thirdDeliveryOrderVO.setRevenueDetail(orderVO.getRevenueDetail());
        thirdDeliveryOrderVO.setUserTags(orderVO.getUserTags());
        thirdDeliveryOrderVO.setSortedTagList(orderVO.getSortedTagList());
        thirdDeliveryOrderVO.setDeliveryPlatformCode(orderVO.getDeliveryPlatformCode());
        thirdDeliveryOrderVO.setDeliveryRedirectModule(orderVO.getDeliveryRedirectModule());
        thirdDeliveryOrderVO.setPayTime(orderVO.getPayTime());
        thirdDeliveryOrderVO.setIsNeedInvoice(orderVO.getIsNeedInvoice());
        thirdDeliveryOrderVO.setActionTagList(orderVO.getActionTagList());
        thirdDeliveryOrderVO.setViewStatus(orderVO.getViewStatus());
        thirdDeliveryOrderVO.setDeliveryStatusChangeTime(orderVO.getDeliveryStatusChangeTime());
        thirdDeliveryOrderVO.setDeliveryCount(orderVO.getDeliveryCount());
        thirdDeliveryOrderVO.setWarehouseId(orderVO.getWarehouseId());
        thirdDeliveryOrderVO.setWarehouseName(orderVO.getWarehouseName());
        thirdDeliveryOrderVO.setDeliveryStatusLocked(orderVO.getDeliveryStatusLocked());
        thirdDeliveryOrderVO.setDeliveryExceptionSummaryVOS(orderVO.getDeliveryExceptionSummaryVOS());
        thirdDeliveryOrderVO.setIsHasCanRefundGoods(orderVO.getIsHasCanRefundGoods());
        thirdDeliveryOrderVO.setOrderUserType(orderVO.getOrderUserType());
        thirdDeliveryOrderVO.setWholeTimeout(orderVO.getWholeTimeout());
        thirdDeliveryOrderVO.setDeliveryOperateItems(orderVO.getDeliveryOperateItems());
        thirdDeliveryOrderVO.setAllowLatestAuditTime(orderVO.getAllowLatestAuditTime());
        thirdDeliveryOrderVO.setExceptionCode(orderVO.getExceptionCode());
        thirdDeliveryOrderVO.setErpShopCode(orderVO.getErpShopCode());
        thirdDeliveryOrderVO.setSelfPickPullNewOrder(orderVO.getSelfPickPullNewOrder());
        // 名酒馆标签
        thirdDeliveryOrderVO.setIsMtFamousTavern(orderVO.getIsMtFamousTavern());

        List<ShowTagVO> showTags = Lists.newArrayList();
        if (Objects.nonNull(orderVO.getIsWiderShippingArea()) && orderVO.getIsWiderShippingArea()) {
            ShowTagVO widerTag = new ShowTagVO();
            widerTag.setTagDesc(MccConfigUtil.getTagHint(ShowTagEnum.WIDER_SHIPPING_AREA.getLionKey(), ShowTagEnum.WIDER_SHIPPING_AREA.getDefaultValue()));
            widerTag.setTagCode(ShowTagEnum.WIDER_SHIPPING_AREA.getCode());
            showTags.add(widerTag);
        }
        thirdDeliveryOrderVO.setShowTags(showTags);

        thirdDeliveryOrderVO.setReceiverTailPhoneNumber(orderVO.getReceiverTailPhoneNumber());
        thirdDeliveryOrderVO.setGoodsItemList(orderVO.getGoodsItemList());
        thirdDeliveryOrderVO.setNeedWineBottleOpener(orderVO.getNeedWineBottleOpener());
        return thirdDeliveryOrderVO;
    }
}
