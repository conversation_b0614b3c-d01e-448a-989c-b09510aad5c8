/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.dianping.cat.util.MetricHelper;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListOrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListOrderResponse;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.QueryWaitToConfirmOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSOrderServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class OrderWaitToConfirmPendingTaskService extends AbstractSinglePendingTaskService {

    @Autowired
    private OCMSOrderServiceWrapper ocmsOrderServiceWrapper;
    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        OCMSListOrderRequest ocmsListOrderRequest = ocmsOrderServiceWrapper.buildOCMSListOrderRequest(param.getTenantId(), Lists.newArrayList(param.getEntityId()), 1, 1, param.getEntityType());
        int count=0;
        try {
            log.info("OCMSOrderServiceWrapper.queryWaitToConfirmOrder  调用ocmsQueryThriftService.listOrder request:{}", ocmsListOrderRequest);
            OCMSListOrderResponse response = ocmsQueryThriftService.listOrder(ocmsListOrderRequest);
            log.info("OCMSOrderServiceWrapper.queryWaitToConfirmOrder  调用ocmsQueryThriftService.listOrder response:{}", response);
            if (response.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                count = response.getTotalCount();
            }
        }catch (Exception e){
            log.error("OCMSOrderServiceWrapper.queryWaitToConfirmOrder  调用ocmsQueryThriftService.listOrder error", e);
            MetricHelper.build().name("order.wait2confirm.err").tag("tenantId", String.valueOf(param.getTenantId())).count();
        }
        return PendingTaskResult.createNumberMarker(count);    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.ORDER_WAIT_TO_CONFIRM;
    }


}
