package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.structattr.ChannelStructAttrFormatTypeDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.StructAttrValueFormatTypesDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/7/15 14:21
 */
@TypeDoc(
        name = "结构化属性格式类型",
        description = "结构化属性格式类型"
)
@Data
@ApiModel("结构化属性格式类型")
public class ChannelStructAttrFormatTypeVO {
    @FieldDoc(
            description = "销售属性值组合格式1:单品 2:组合"
    )
    @ApiModelProperty(name = "销售属性值组合格式1:单品 2:组合")
    private Integer formatType;

    @FieldDoc(
            description = "录入值示例"
    )
    @ApiModelProperty(name = "录入值示例")
    private String tips;

    @FieldDoc(
            description = "组合限制 当formatType=2时，最多可以增加多少个单品"
    )
    @ApiModelProperty(name = "组合限制 当formatType=2时，最多可以增加多少个单品")
    private Integer combinationLimit;

    public static List<ChannelStructAttrFormatTypeDTO> toBizDTOList(List<ChannelStructAttrFormatTypeVO> formatTypeVOList) {
        return Fun.map(formatTypeVOList, ChannelStructAttrFormatTypeVO::toBizDTO);
    }

    private static ChannelStructAttrFormatTypeDTO toBizDTO(ChannelStructAttrFormatTypeVO formatTypeVO) {
        if (formatTypeVO == null) {
            return null;
        }
        ChannelStructAttrFormatTypeDTO formatTypeDTO = new ChannelStructAttrFormatTypeDTO();
        formatTypeDTO.setFormatType(formatTypeVO.getFormatType());
        formatTypeDTO.setTips(formatTypeVO.getTips());
        formatTypeDTO.setCombinationLimit(formatTypeVO.getCombinationLimit());
        return formatTypeDTO;
    }

    public static List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrFormatTypeDTO> toOcmsBizDTOList(List<ChannelStructAttrFormatTypeVO> formatTypeVOList) {
        return Fun.map(formatTypeVOList, ChannelStructAttrFormatTypeVO::toOcmsBizDTO);
    }

    private static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrFormatTypeDTO toOcmsBizDTO(ChannelStructAttrFormatTypeVO formatTypeVO) {
        if (formatTypeVO == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrFormatTypeDTO formatTypeDTO
                = new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrFormatTypeDTO();
        formatTypeDTO.setFormatType(formatTypeVO.getFormatType());
        formatTypeDTO.setTips(formatTypeVO.getTips());
        formatTypeDTO.setCombinationLimit(formatTypeVO.getCombinationLimit());
        return formatTypeDTO;
    }

    public static List<ChannelStructAttrFormatTypeVO> ofPlatformDTOList(List<StructAttrValueFormatTypesDTO> formatTypeDTOList) {
        return Fun.map(formatTypeDTOList, ChannelStructAttrFormatTypeVO::ofPlatformDTO);
    }

    private static ChannelStructAttrFormatTypeVO ofPlatformDTO(StructAttrValueFormatTypesDTO formatTypeDTO) {
        if (formatTypeDTO == null) {
            return null;
        }
        ChannelStructAttrFormatTypeVO formatTypeVO = new ChannelStructAttrFormatTypeVO();
        formatTypeVO.setFormatType(formatTypeDTO.getFormatType());
        formatTypeVO.setTips(formatTypeDTO.getTips());
        formatTypeVO.setCombinationLimit(formatTypeDTO.getCombinationLimit());
        return formatTypeVO;
    }

    public static List<ChannelStructAttrFormatTypeVO> ofBizDTOList(List<ChannelStructAttrFormatTypeDTO> formatTypeDTOList) {
        return Fun.map(formatTypeDTOList, ChannelStructAttrFormatTypeVO::ofBizDTO);
    }

    private static ChannelStructAttrFormatTypeVO ofBizDTO(ChannelStructAttrFormatTypeDTO formatTypeDTO) {
        if (formatTypeDTO == null) {
            return null;
        }
        ChannelStructAttrFormatTypeVO formatTypeVO = new ChannelStructAttrFormatTypeVO();
        formatTypeVO.setFormatType(formatTypeDTO.getFormatType());
        formatTypeVO.setTips(formatTypeDTO.getTips());
        formatTypeVO.setCombinationLimit(formatTypeDTO.getCombinationLimit());
        return formatTypeVO;
    }

    public static ChannelStructAttrFormatTypeVO ofOcmsDTO(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrFormatTypeDTO formatTypeDTO) {
        if (formatTypeDTO == null) {
            return null;
        }
        ChannelStructAttrFormatTypeVO formatTypeVO = new ChannelStructAttrFormatTypeVO();
        formatTypeVO.setFormatType(formatTypeDTO.getFormatType());
        formatTypeVO.setTips(formatTypeDTO.getTips());
        formatTypeVO.setCombinationLimit(formatTypeDTO.getCombinationLimit());
        return formatTypeVO;
    }
}
