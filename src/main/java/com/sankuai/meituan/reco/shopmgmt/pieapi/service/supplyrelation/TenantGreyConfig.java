package com.sankuai.meituan.reco.shopmgmt.pieapi.service.supplyrelation;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class TenantGreyConfig {
    /**
     * 全部切流
     */
    private boolean all;
    /**
     * 灰度租户ID / 白名单租户
     */
    private Set<Long> tenantIds;

    /**
     * 黑名单租户ID
     */

    private Set<Long> blackTenantIds;

    public boolean isHitGreyWithBlack(Long tenantId) {
        if (CollectionUtils.isNotEmpty(this.getBlackTenantIds()) && this.getBlackTenantIds().contains(tenantId)) {
            return false;
        }
        if (isAll()) {
            return true;
        }
        return CollectionUtils.isNotEmpty(getTenantIds())
                && Objects.nonNull(tenantId)
                && getTenantIds().contains(tenantId);
    }

    public boolean isHitBlackTenant(Long tenantId){
        return !(Optional.ofNullable(getBlackTenantIds()).map(t -> t.contains(tenantId)).orElse(false));
    }
}
