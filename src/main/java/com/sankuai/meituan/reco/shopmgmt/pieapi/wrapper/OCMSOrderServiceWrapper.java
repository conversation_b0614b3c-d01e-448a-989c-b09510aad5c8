package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Degrade;
import com.google.common.base.Splitter;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.request.*;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.order.management.client.dto.request.online.*;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.MerchantOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.*;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderStatusLog;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.MerchantOrderListRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.*;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.management.client.utils.ExchangeUtil;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.*;
import com.sankuai.meituan.reco.pickselect.dh.enums.TemperaturePropertyEnum;
import com.sankuai.meituan.reco.pickselect.dto.OrderIdentifierDTO;
import com.sankuai.meituan.reco.pickselect.logic.common.Status;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.PickSelectPrintQueryThriftService;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.OrderPrintQueryDto;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.OrderPrintResultDto;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.request.OrderPrintResultQueryRequest;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.response.OrderPrintResultQueryResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.OrderListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryRedirectModuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.ParsedPropertiesVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception.DeliveryExceptionSummaryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AggDeliveryPlatformEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.OrderViewStatusEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.UserTagTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.delivery.third.DHThirdDeliveryService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SaasCrmDataWrapper.OrderProfitView;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.AppendDeliveryInfoService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.DeliveryChannelWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsDeliveryStatusDesc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderDeliveryServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.BatchStoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.COMMIT;
import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.FIRST_AUDIT_ING;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConsumableMaterialParseUtils.parseConsumableMaterialInfo;

/**
 * 中台订单服务
 *
 * <AUTHOR>
 * @since 2019/11/25
 */
@Service
@Slf4j
public class OCMSOrderServiceWrapper {

    @Resource
    private AppendDeliveryInfoService appendDeliveryInfoService;
    @Resource
    private PickSelectPrintQueryThriftService pickSelectPrintQueryThriftService;
    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Autowired
    private MerChantRevenueQueryService merChantRevenueQueryService;
    @Autowired
    private TmsServiceWrapper tmsServiceWrapper;

    @Autowired
    private SaasCrmDataWrapper saasCrmDataWrapper;
    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;


    @Resource
    private TenantWrapper tenantWrapper;
    @Resource
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;
    @Resource
    private RiderDeliveryServiceWrapper riderDeliveryServiceWrapper;

    @Resource
    private DeliveryChannelWrapper deliveryChannelWrapper;
    @Resource
    private PoiServiceFacade poiServiceFacade;

    @Resource
    private DHThirdDeliveryService dhThirdDeliveryService;

    /**
     * 拒绝退款
     */


    private static final String UN_KNOW = "未知";



    private static final int IS_SELF_DELIVERY_NO = 0;

    static final List<Integer> TO_CHECK_ITEMS = Lists.newArrayList(
            OrderCanOperateItem.COMPLETE_PICK.getValue(),
            OrderCanOperateItem.PRINT_RECEIPT.getValue(),
            OrderCanOperateItem.PART_ORDER_REFUND.getValue(),
            OrderCanOperateItem.FULL_ORDER_REFUND.getValue(),
            OrderCanOperateItem.WEIGHT_REFUND.getValue(),
            OrderCanOperateItem.AFTER_SALE_REFUND.getValue(),
            OrderCanOperateItem.SELF_FETCH_FINISH.getValue(),
            OrderCanOperateItem.MONEY_REFUND.getValue(),
            OrderCanOperateItem.DISPATCH_ORDER.getValue()
    );


    //履约标签
    private static final int FULFILLMENT_TAG = 1;

    //拣货标准
    private static final int PICK_TAG = 2;

    // TMS 系统，配送无异常的编码
    private static final Integer DELIVERY_NO_EXCEPTION = 0;

    //订单查询起始天数（截止时间前推天数）
    private static final String CONFIG_QUERY_ORDER_CREATE_TIME_BEFORE = "query.order.create.time.before";

    private static final List<OrderStatusEnum> NOT_SHOW_DELIVERY_ITEM_ORDER_STATUS = Arrays.asList(OrderStatusEnum.CANCELED,OrderStatusEnum.SUBMIT,OrderStatusEnum.PAYING,OrderStatusEnum.CLOSED);




    @Degrade(rhinoKey = "OCMSOrderServiceWrapper.queryWaitToConfirmOrder",
            fallBackMethod = "queryWaitToConfirmOrderFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<OrderListResponse> queryWaitToConfirmOrder(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType,Long currentStoreId) {
        OCMSListOrderRequest ocmsListOrderRequest = buildOCMSListOrderRequest(tenantId, storeIdList, page, size, entityType);
        try {
            log.info("OCMSOrderServiceWrapper.queryWaitToConfirmOrder  调用ocmsQueryThriftService.listOrder request:{}", ocmsListOrderRequest);
            OCMSListOrderResponse response = ocmsQueryThriftService.listOrder(ocmsListOrderRequest);
            log.info("OCMSOrderServiceWrapper.queryWaitToConfirmOrder  调用ocmsQueryThriftService.listOrder response:{}", response);
            if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            boolean showSalePrice = authThriftWrapper.isCodeHasAuth(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode());
            List<OrderRevenueDetailResponse> orderRevenueDetailList = showSalePrice ?
                    getOrderListRevenueDetail4TenantAndViewIds(tenantId, response.getOcmsOrderList())
                    : Collections.emptyList();
            Map<String, OrderProfitView> orderProfitMap = showSalePrice ?
                    getOrderProfitMap(tenantId, response.getOcmsOrderList()) : Collections.emptyMap();
            //查询打印结果
            Long storeId = CollectionUtils.isNotEmpty(ocmsListOrderRequest.getShopIdList()) ? ocmsListOrderRequest.getShopIdList().get(0) : ocmsListOrderRequest.getWarehouseIdList().get(0);
            OrderPrintResultQueryResponse orderPrintResultQueryResponse = queryOrderPrintResult(storeId,
                    ocmsListOrderRequest.getTenantId(),
                    Optional.ofNullable(response.getOcmsOrderList()).map(List::stream).orElse(Stream.empty()).map(e -> new OrderIdentifierDTO(e.getViewOrderId(), e.getOrderBizType())).collect(Collectors.toList()));
            OrderListResponse orderListResponse = setCouldOperateItems(
                    buildOrderListResponse(response, orderPrintResultQueryResponse.getPrintResultList(), orderProfitMap,
                            orderRevenueDetailList, page, size,storeId),
                    OrderCouldOperateItem.ACCEPT_ORDER,currentStoreId);
            setOrderListResponseViewStatus(orderListResponse, OrderViewStatusEnum.WAIT_TO_MERCHANT_ACCEPT);
            setOrderDeliveryStatusChangeTimeWithPayTime(orderListResponse);
            setErpCode(orderListResponse);
            return CommonResponse.success(orderListResponse);
        } catch (Exception e) {
            log.error("OCMSOrderServiceWrapper.queryWaitToConfirmOrder  调用ocmsQueryThriftService.listOrder error", e);
            MetricHelper.build().name("order.wait2confirm.err").tag("tenantId", String.valueOf(tenantId)).count();
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse<OrderListResponse> queryWaitToConfirmOrderFallback(QueryWaitToConfirmOrderRequest request) {
        log.info("OCMSOrderServiceWrapper.queryWaitToConfirmOrder  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }


    private long firstShopId(List<Long> storeIdList) {
        if (CollectionUtils.isNotEmpty(storeIdList)){
            return storeIdList.get(0);
        }
        return 0;
    }


    @Degrade(rhinoKey = "OCMSOrderServiceWrapper.queryWaitAuditRefund",
            fallBackMethod = "queryWaitAuditRefundFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<RefundApplyListResponse> queryWaitAuditRefund(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType) {
        OCMSListWaitAuditOrderRequest ocmsListWaitAuditOrderRequest = buildOCMSListWaitAuditOrderRequest(tenantId, storeIdList, page, size, entityType);
        if (ParamCheckUtils.collectionAllEmpty(ocmsListWaitAuditOrderRequest.getShopIdList(), ocmsListWaitAuditOrderRequest.getWarehouseIdList())) {
            log.error("OCMSOrderServiceWrapper.queryWaitAuditRefund 订单展示门店/仓不能同时为空");
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "订单展示门店/仓不能同时为空");
        }
        try {
            log.info("OCMSOrderServiceWrapper.queryWaitAuditRefund  调用ocmsQueryThriftService.listWaitAuditOrder request:{}", ocmsListWaitAuditOrderRequest);
            OCMSListWaitAuditOrderResponse response = ocmsQueryThriftService.listWaitAuditOrder(ocmsListWaitAuditOrderRequest);
            log.info("OCMSOrderServiceWrapper.queryWaitAuditRefund  调用ocmsQueryThriftService.listWaitAuditOrder response:{}", response);
            if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            //查询打印结果
            Long storeId = CollectionUtils.isNotEmpty(ocmsListWaitAuditOrderRequest.getShopIdList()) ? ocmsListWaitAuditOrderRequest.getShopIdList().get(0) : ocmsListWaitAuditOrderRequest.getWarehouseIdList().get(0);
            OrderPrintResultQueryResponse orderPrintResultQueryResponse = queryOrderPrintResult(CollectionUtils.isNotEmpty(ocmsListWaitAuditOrderRequest.getShopIdList()) ? ocmsListWaitAuditOrderRequest.getShopIdList().get(0) : ocmsListWaitAuditOrderRequest.getWarehouseIdList().get(0),
                    ocmsListWaitAuditOrderRequest.getTenantId(),
                    Optional.ofNullable(response.getOcmsWaitAuditOrderVOList()).map(List::stream).orElse(Stream.empty()).map(e -> new OrderIdentifierDTO(e.getViewOrderId(), e.getOrderBizType())).collect(Collectors.toList()));

            // 订单营收信息
            List<OCMSOrderVO> orders = Optional.ofNullable(response.getOcmsWaitAuditOrderVOList())
                    .map(List::stream).orElse(Stream.empty()).collect(Collectors.toList());
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(),
                    AuthCodeEnum.MALT_FARM.getAuthCode()));
            boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);

            List<OrderRevenueDetailResponse> orderRevenueDetailList = showSalePrice ?
                    getOrderListRevenueDetail4TenantAndViewIds(tenantId, orders) : Collections.emptyList();
            Map<String, OrderProfitView> orderProfitMap = showSalePrice ?
                    getOrderProfitMap(tenantId, orders) : Collections.emptyMap();
            // 设置价格展示类型、有权限退款金额展示为渠道下发的实际退款价格
            if(CollectionUtils.isNotEmpty(orders)){
                setRefundPriceDisplayType4WaitAuditOrderList(response.getOcmsWaitAuditOrderVOList(),
                        showSalePrice ? PriceDisplayType.REFUND_AMOUNT.getCode() : PriceDisplayType.OFFLINE_PRICE.getCode());
            }
            RefundApplyListResponse refundApplyListResponse = setCouldOperateItems(buildRefundApplyListResponse(response, orderPrintResultQueryResponse.getPrintResultList(), orderProfitMap,
                            orderRevenueDetailList, page, size, permissions,storeId));
            setRefundApplyListResponseViewStatus(refundApplyListResponse);
            return CommonResponse.success(refundApplyListResponse);
        } catch (Exception e) {
            MetricHelper.build().name("order.waitAuditRefund.err").tag("tenantId", String.valueOf(tenantId)).tag("storeId", String.valueOf(firstShopId(storeIdList))).count();
            log.error("OCMSOrderServiceWrapper.queryWaitAuditRefund  调用ocmsQueryThriftService.listWaitAuditOrder error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<RefundApplyListResponse> queryWaitAuditRefundFallback(QueryWaitAuditRefundRequest request) {
        log.info("OCMSOrderServiceWrapper.queryWaitAuditRefund  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }



    private OrderPrintResultQueryResponse queryOrderPrintResult(long storeId, long tenantId, List<OrderIdentifierDTO> orders) {
        if (CollectionUtils.isNotEmpty(orders)) {
            OrderPrintResultQueryRequest resultQueryRequest = new OrderPrintResultQueryRequest();
            resultQueryRequest.setOfflineStoreId(storeId);
            resultQueryRequest.setTenantId(tenantId);
            resultQueryRequest.setOrderList(orders.stream().filter(e->e.getSourceCode() != null && StringUtils.isNotBlank(e.getUnifyOrderId())).map(e -> {
                OrderPrintQueryDto orderPrintQueryDto = new OrderPrintQueryDto();
                orderPrintQueryDto.setOrderNo(e.getUnifyOrderId());
                orderPrintQueryDto.setOrderSource(e.getSourceCode());
                return orderPrintQueryDto;
            }).collect(Collectors.toList()));
            try {
                OrderPrintResultQueryResponse response = pickSelectPrintQueryThriftService.queryOrderPrintResult(resultQueryRequest);
                log.info("请求履约获取订单打印状态，request:{}, response:{}", resultQueryRequest, response);
                return response;
            } catch (Exception e) {
                log.error("请求订单打印状态失败,shopId:{}, tenantId:{}", storeId, tenantId, e);
            }
        }
        return OrderPrintResultQueryResponse.builder().status(Status.FAIL).printResultList(Lists.newArrayList()).build();
    }


    @Degrade(rhinoKey = "OCMSOrderServiceWrapper.orderListRevenueDetail", fallBackMethod = "orderListRevenueDetailFallback",
            timeoutInMilliseconds = 1000)
    public CommonResponse<MerchantOrderListRevenueDetailResponse> orderListRevenueDetail(MerchantOrderRevenueDetailRequest request) {
        CommonResponse<MerchantOrderListRevenueDetailResponse> response = new CommonResponse<>();
        try {
            log.info("OCMSOrderServiceWrapper.orderRevenueDetail->调用MerChantRevenueQueryService.orderRevenueDetail "
                    + "request:{}", request);
            MerchantOrderListRevenueDetailResponse merChantRevenueResponse = merChantRevenueQueryService
                    .orderListRevenueDetail(request);
            log.info("OCMSOrderServiceWrapper.orderRevenueDetail->调用调用MerChantRevenueQueryService.orderRevenueDetail "
                    + "response:{}", merChantRevenueResponse);
            if(merChantRevenueResponse == null){
                response.setCode(StatusCodeEnum.FAIL.getCode());
                response.setMessage(StatusCodeEnum.FAIL.getMessage());
                return response;
            }
            if (Integer.valueOf(com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum.SUCCESS.getCode())
                    .equals(merChantRevenueResponse.getStatus())) {
                response.setCode(StatusCodeEnum.SUCCESS.getCode());
                response.setMessage(StatusCodeEnum.SUCCESS.getMessage());
                response.setData(merChantRevenueResponse);
                return response;
            }
            response.setCode(merChantRevenueResponse.getStatus());
            response.setMessage(merChantRevenueResponse.getMsg());
            return response;
        } catch (Exception e) {
            log.error("OCMSOrderServiceWrapper.orderRevenueDetail->merChantRevenueQueryService.orderRevenueDetail"
                    + " error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse orderListRevenueDetailFallback(MerchantOrderRevenueDetailRequest request) {
        log.warn("OCMSOrderServiceWrapper.orderRevenueDetail->调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }


    public List<OCMSOrderVO> getOCMSOrderList(List<ViewIdCondition> viewIdConditions) {
        if (CollectionUtils.isEmpty(viewIdConditions)) {
            return Collections.emptyList();
        }

        OCMSListViewIdConditionRequest viewIdConditionRequest = new OCMSListViewIdConditionRequest();
        viewIdConditionRequest.setViewIdConditionList(viewIdConditions);
        viewIdConditionRequest.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        viewIdConditionRequest.setSort(SortByEnum.DESC);
        viewIdConditionRequest.setNeedSortedTags(Boolean.TRUE);
        // 1. 查询订单信息.
        OCMSListViewIdConditionResponse viewIdConditionResponse = null;
        try {
            viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("Call ocmsQueryThriftService#queryOrderByViewIdCondition. request:{}, response:{}", viewIdConditionRequest,
                    viewIdConditionResponse);
        } catch (Exception e) {
            log.error("Call ocmsQueryThriftService#queryOrderByViewIdCondition error. request:{}", viewIdConditionRequest, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }

        // 2. 处理返回结果
        if (viewIdConditionResponse == null || viewIdConditionResponse.getStatus() == null ||
                viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            log.warn("获取订单详情失败, viewOrderId:{}",
                    viewIdConditionRequest.getViewIdConditionList().stream().map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()));
            throw new CommonRuntimeException(Optional.ofNullable(viewIdConditionResponse).map(OCMSListViewIdConditionResponse::getStatus)
                    .map(com.meituan.shangou.saas.order.management.client.dto.Status::getMessage).orElse(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage));
        }
        List<OCMSOrderVO> ocmsOrderList = viewIdConditionResponse.getOcmsOrderList();
        if (CollectionUtils.isEmpty(ocmsOrderList)) {
            log.warn("未查询到订单, viewOrderId:{}",
                    viewIdConditionRequest.getViewIdConditionList().stream().map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()));
            throw new CommonRuntimeException("未查询到订单");
        }
        return ocmsOrderList;
    }


    public List<OCMSOrderVO> batchQueryOcmsOrderList(List<ViewIdCondition> viewIdConditions) {
        if(CollectionUtils.isEmpty(viewIdConditions)) {
            return Collections.emptyList();
        }
        Integer size = MccConfigUtil.getQueryOrderListBatchSize();
        List<List<ViewIdCondition>> partitionList = com.google.common.collect.Lists.partition(viewIdConditions, size);
        ArrayList<OCMSOrderVO> orderList = new ArrayList<>();
        for (List<ViewIdCondition> partition : partitionList) {
            orderList.addAll(getOCMSOrderList(partition));
        }

        return orderList;
    }

    /**
     * 查询订单营收信息.
     *
     * @param tenantId      租户 ID
     * @param bizType2ViewOrderIdPairs 订单列表
     * @return List<OrderRevenueDetailResponse>
     */
    public List<OrderRevenueDetailResponse> getOrderListRevenueDetailByViewOrderIds(long tenantId, List<Pair<Integer, String>> bizType2ViewOrderIdPairs) {
        if (CollectionUtils.isEmpty(bizType2ViewOrderIdPairs)) {
            return Collections.emptyList();
        }

        MerchantOrderRevenueDetailRequest request = new MerchantOrderRevenueDetailRequest();
        request.setTenantId(tenantId);
        List<ViewIdCondition> conditions = bizType2ViewOrderIdPairs.stream().map(pair -> ViewIdCondition.builder()
                        .orderBizType(pair.getLeft())
                        .viewOrderId(pair.getRight()).build())
                .collect(Collectors.toList());
        request.setViewIdConditionList(conditions);

        try {
            log.info("call MerChantRevenueQueryService.orderRevenueDetail request:{}", request);
            MerchantOrderListRevenueDetailResponse response = merChantRevenueQueryService
                    .orderListRevenueDetail(request);
            log.info("call MerChantRevenueQueryService.orderRevenueDetail esponse:{}", response);
            if (response == null) {
                log.warn("call MerChantRevenueQueryService.orderRevenueDetail wrong. request:{}, response:{}"
                        , request, response);
                return Collections.emptyList();
            }
            if (Integer.valueOf(com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum.SUCCESS.getCode()).equals(response.getStatus())) {
                List<OrderRevenueDetailResponse> orderListRevenueDetailResponse = response.getOrderListRevenueDetailResponse();
                if (orderListRevenueDetailResponse == null) {
                    return Collections.emptyList();
                }
                return orderListRevenueDetailResponse;
            } else {
                log.warn("call MerChantRevenueQueryService.orderRevenueDetail fail. request:{}, response:{}"
                        , request, response);
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.warn("call MerChantRevenueQueryService.orderRevenueDetail error. request:{}", request);
            return Collections.emptyList();
        }
    }


    private OCMSListOrderRequest buildOCMSListOrderRequestBasic(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType) {
        OCMSListOrderRequest ocmsListOrderRequest = new OCMSListOrderRequest();
        ocmsListOrderRequest.setTenantId(tenantId);
        ocmsListOrderRequest.setShopIdList(storeIdList);
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType)) {
            ocmsListOrderRequest.setShopIdList(Lists.newArrayList());
            ocmsListOrderRequest.setWarehouseIdList(storeIdList);
        }

        //查询当前时间之前7天的订单
        ocmsListOrderRequest.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(ConfigUtilAdapter.getInt(CONFIG_QUERY_ORDER_CREATE_TIME_BEFORE, 7)));
        ocmsListOrderRequest.setEndCreateTime(System.currentTimeMillis());
        ocmsListOrderRequest.setPage(page);
        ocmsListOrderRequest.setSize(size);
        return ocmsListOrderRequest;
    }

    public OCMSListOrderRequest buildOCMSListOrderRequest(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType) {
        OCMSListOrderRequest ocmsListOrderRequest = buildOCMSListOrderRequestBasic(tenantId, storeIdList, page, size, entityType);
        ocmsListOrderRequest.setOrderStatusList(Lists.newArrayList(OrderStatusEnum.SUBMIT.getValue()));
        ocmsListOrderRequest.setRealTime(true);
        ocmsListOrderRequest.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        ocmsListOrderRequest.setSort(SortByEnum.ASC);
        return ocmsListOrderRequest;
    }

    private OCMSListWaitAuditOrderRequest buildOCMSListWaitAuditOrderRequest(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType) {
        OCMSListWaitAuditOrderRequest ocmsListWaitAuditOrderRequest = new OCMSListWaitAuditOrderRequest();
        ocmsListWaitAuditOrderRequest.setTenantId(tenantId);
        ocmsListWaitAuditOrderRequest.setShopIdList(storeIdList);
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType)) {
            ocmsListWaitAuditOrderRequest.setShopIdList(Lists.newArrayList());
            ocmsListWaitAuditOrderRequest.setWarehouseIdList(storeIdList);
        }
        ocmsListWaitAuditOrderRequest.setPage(page);
        ocmsListWaitAuditOrderRequest.setSize(size);
        ocmsListWaitAuditOrderRequest.setBeginCreateTime(0L);
        ocmsListWaitAuditOrderRequest.setEndCreateTime(System.currentTimeMillis());
        ocmsListWaitAuditOrderRequest.setAfterSaleApplyStatusList(Lists.newArrayList(AfterSaleApplyStatusEnum.COMMIT.getValue()));
        ocmsListWaitAuditOrderRequest.setRealTime(true);
        ocmsListWaitAuditOrderRequest.setSort(SortByEnum.ASC);
        return ocmsListWaitAuditOrderRequest;
    }

    private OrderListResponse buildOrderListResponse(OCMSListOrderResponse response,
            List<OrderPrintResultDto> orderPrintResultDtos, Map<String, OrderProfitView> orderProfitMap,
            List<OrderRevenueDetailResponse> orderRevenueDetailResponseList, Integer page, Integer size,Long storeId) {
        PageInfoVO pageInfoVO = buildPageInfoVO(page, size, response.getTotalCount());
        return buildOrderListResponse(response.getOcmsOrderList(), orderPrintResultDtos, orderProfitMap, orderRevenueDetailResponseList, pageInfoVO,storeId);
    }

    private RefundApplyListResponse buildRefundApplyListResponse(OCMSListWaitAuditOrderResponse response,
                                                                 List<OrderPrintResultDto> orderPrintResultDtos, Map<String, OrderProfitView> orderProfitMap,
                                                                 List<OrderRevenueDetailResponse> orderRevenueDetailList,
                                                                 Integer page, Integer size, Map<String, Boolean> permissions,Long curStoreId) {
        RefundApplyListResponse refundApplyListResponse = new RefundApplyListResponse();
        Map<String, OrderRevenueDetailResponse> orderRevenueDetailResponseMap = getOrderRevenueDetailResponseMap(
                orderRevenueDetailList);
        refundApplyListResponse.setPageInfo(buildPageInfoVO(page, size, response.getTotalCount()));
        Map<String, OrderPrintResultDto> printResultDtoMap = Optional.ofNullable(orderPrintResultDtos).map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(printOrder -> printOrder.getOrderNo(), v -> v, (f, s) -> f));
        if (CollectionUtils.isNotEmpty(response.getOcmsWaitAuditOrderVOList())) {
            refundApplyListResponse.setRefundApplyRecordVOList(response.getOcmsWaitAuditOrderVOList().stream()
                    .map(ocmsWaitAuditOrder -> buildRefundApplyRecordVO(ocmsWaitAuditOrder,
                            orderRevenueDetailResponseMap.get(ocmsWaitAuditOrder.getViewOrderId()),
                            orderProfitMap == null ? null : orderProfitMap.get(ocmsWaitAuditOrder.getViewOrderId())))
                    .map(order -> {
                        //设置打印状态
                        OrderPrintResultDto printResultDto = printResultDtoMap.get(order.getOrderVO().getChannelOrderId());
                        if (printResultDto != null) {
                            OrderPrintStatusVo printStatusVo = buildPrintStatusVo(printResultDto);
                            order.setPrintStatus(printStatusVo);
                        }
                        return order;
                    })
                    .collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(refundApplyListResponse.getRefundApplyRecordVOList())) {
            List<OrderVO> orders =
                    refundApplyListResponse.getRefundApplyRecordVOList().stream().map(RefundApplyRecordVO::getOrderVO).collect(Collectors.toList());
           javafx.util.Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> deliveryDetailMap = appendDeliveryInfoService.appendDeliveryInfo(orders);
            if(MapUtils.isNotEmpty(permissions) && permissions.getOrDefault(AuthCodeEnum.MALT_FARM.getAuthCode(), Boolean.FALSE)){
                fillOrderMaltDeliveryPlatModule(orders, deliveryDetailMap.getKey(),deliveryDetailMap.getValue(),curStoreId);
            }
        }
        return refundApplyListResponse;
    }

    private Map<String, OrderRevenueDetailResponse> getOrderRevenueDetailResponseMap(
            List<OrderRevenueDetailResponse> orderRevenueDetailList) {
        return Optional.ofNullable(orderRevenueDetailList)
                .map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, revenueDetail -> revenueDetail,
                        (before, after) -> before));
    }

    private OrderPrintStatusVo buildPrintStatusVo(OrderPrintResultDto printResultDto) {
        OrderPrintStatusVo printStatusVo = new OrderPrintStatusVo();
        printStatusVo.setPrintStatus(printResultDto.getPrintStatus());
        printStatusVo.setPrintFailToast(generatePrintToast(printResultDto));
        printStatusVo.setDeviceInfo(printResultDto.getDeviceInfo());
        return printStatusVo;
    }

    private OrderListResponse buildOrderListResponse(List<OCMSOrderVO> ocmsOrderVOList,
            List<OrderPrintResultDto> orderPrintResultDtos, Map<String, OrderProfitView> orderProfitMap,
            List<OrderRevenueDetailResponse> orderRevenueDetailResponseList,
            PageInfoVO pageInfoVO,Long storeId) {
        OrderListResponse orderListResponse = new OrderListResponse();
        orderListResponse.setPageInfo(pageInfoVO);
        Map<String, OrderRevenueDetailResponse> orderRevenueDetailResponseMap = getOrderRevenueDetailResponseMap(
                orderRevenueDetailResponseList);
        Map<String, OrderPrintResultDto> printResultDtoMap = Optional.ofNullable(orderPrintResultDtos).map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(printOrder -> printOrder.getOrderNo(), v -> v, (f, s) -> f));



        if (CollectionUtils.isNotEmpty(ocmsOrderVOList)) {
            //查询骑手上报异常，查询不到不影响主流程
            Map<String, DeliveryExceptionSummaryVO> exceptionSummaryVOMap = queryDeliveryExceptionVo(ocmsOrderVOList);


            orderListResponse.setOrderList(
                    ocmsOrderVOList.stream()
                            .map(ocmsOrder -> buildOrderVO(ocmsOrder, orderRevenueDetailResponseMap.get(ocmsOrder.getViewOrderId()),
                                    orderProfitMap == null ? null : orderProfitMap.get(ocmsOrder.getViewOrderId())))
                            .map(order ->{
                                DeliveryExceptionSummaryVO exceptionSummaryVO = exceptionSummaryVOMap.get(order.getChannelOrderId());
                                if (exceptionSummaryVO != null){
                                    order.setDeliveryExceptionSummaryVOS(exceptionSummaryVO);
                                }

                                return order;
                            })
                            .map(order -> {
                                //设置打印状态
                                OrderPrintResultDto printResultDto = printResultDtoMap.get(order.getChannelOrderId());
                                if (printResultDto != null) {
                                    OrderPrintStatusVo printStatusVo = buildPrintStatusVo(printResultDto);
                                    order.setPrintStatus(printStatusVo);
                                }
                                return order;
                            })
                            .collect(Collectors.toList()));
        }
        //添加实时配送信息，和配送操作按钮
        if (orderListResponse != null) {
            javafx.util.Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> deliveryDetailMap = appendDeliveryInfoService.appendDeliveryInfo(orderListResponse.getOrderList());
            fillOrderMaltDeliveryPlatModule(orderListResponse.getOrderList(), deliveryDetailMap.getKey(), deliveryDetailMap.getValue(),storeId);
            fillDeliveryOperateItem(deliveryDetailMap.getKey(),orderListResponse.getOrderList(),storeId);
        }
        return orderListResponse;
    }

    private void fillDeliveryOperateItem(Map<Long, TDeliveryDetail> deliveryDetailMap,List<OrderVO> orderList,Long curStoreId){
        if(CollectionUtils.isEmpty(orderList)){
            return;
        }

        if(MccConfigUtil.showDeliveryOperateItemSwitch()){
            Long tenantId = orderList.get(0).getTenantId();
            ArrayListMultimap<Long,OrderVO> orderMultimap = ArrayListMultimap.create();
            for (OrderVO vo : orderList){
                if(vo.getDispatchShopId()!=null){
                    orderMultimap.put(vo.getDispatchShopId(),vo);
                }else if(vo.getWarehouseId()!=null){
                    orderMultimap.put(vo.getWarehouseId(),vo);
                }else {
                    orderMultimap.put(vo.getStoreId(),vo);
                }
                TDeliveryDetail tDeliveryDetail = deliveryDetailMap.get(vo.getEmpowerOrderId());
                if(tDeliveryDetail==null){
                    if(vo.getOriginalDistributeType()!=null && vo.getOriginalDistributeType() != 25){
                        vo.setPlatformDelivery(true);
                    }
                }else if(tDeliveryDetail.deliveryEntity != null && tDeliveryDetail.deliveryEntity == 0){
                    vo.setPlatformDelivery(true);
                }
            }

            for (Long storeId : orderMultimap.keySet()){
                List<OrderVO> orderVOList = orderMultimap.get(storeId);
                if(CollectionUtils.isEmpty(orderVOList)){
                    continue;
                }
                if(!Objects.equals(storeId,curStoreId)){
                    continue;
                }
                Map<Long, DeliveryOperateItem> operateItemMap = tmsServiceWrapper.queryDeliveryOperateItem(tenantId,storeId,orderVOList.stream().map(OrderVO::getEmpowerOrderId).collect(Collectors.toList()));
                for (OrderVO orderVO : orderVOList){
                    List<Integer> operateList = new ArrayList<>();
                    if(!operateItemMap.containsKey(orderVO.getEmpowerOrderId())){
                        continue;
                    }
                    DeliveryOperateItem item = operateItemMap.get(orderVO.getEmpowerOrderId());
                    if(item == null || CollectionUtils.isEmpty(item.getOperateItemList())){
                        continue;
                    }
                    List<DeliveryOperateItemEnum> itemEnumList = DeliveryOperateItemEnum.tmsItemListToOperateItemList(item.getOperateItemList());
                    if(CollectionUtils.isEmpty(itemEnumList)){
                        continue;
                    }
                    if(itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_SELF)){
                        operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
                    }
                    if(itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM)){
                        operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
                    }
                    if(itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_DAP)){
                        operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
                    }
                    if (itemEnumList.contains(DeliveryOperateItemEnum.DOUYIN_RECALL_DELIVERY)) {
                        operateList.add(DeliveryOperateItemEnum.DOUYIN_RECALL_DELIVERY.type);
                    }
                    if (itemEnumList.contains(DeliveryOperateItemEnum.DOUYIN_EXCEPTION_RECALL_DELIVERY)) {
                        operateList.add(DeliveryOperateItemEnum.DOUYIN_EXCEPTION_RECALL_DELIVERY.type);
                    }
                    orderVO.setDeliveryOperateItems(operateList);
                }
            }
            return;
        }


        List<TStoreConfig> tStoreConfigs = queryDeliveryStoreConfigByOrderList(orderList);
        Map<Long,Set<Integer>> tStoreConfigChannelMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(tStoreConfigs)){
            tStoreConfigs.forEach(tStoreConfig->{
                if(tStoreConfig!=null && CollectionUtils.isNotEmpty(tStoreConfig.getAggPlatformConfigs())){
                    Set<Integer> platformCodeSet=new HashSet<>();
                    tStoreConfig.getAggPlatformConfigs().forEach(config->{
                        AggDeliveryPlatformEnum platformEnum = AggDeliveryPlatformEnum.codeValueOf(config.getPlatformCode());
                        if(platformEnum!=null && config.getOpenFlag() !=null && config.getOpenFlag()==1){
                            platformCodeSet.add(platformEnum.getCode());
                        }
                    });
                    tStoreConfigChannelMap.put(tStoreConfig.getStoreId(),platformCodeSet);
                }
            });

        }

        for (OrderVO orderVO : orderList){
            TDeliveryDetail tDeliveryDetail = deliveryDetailMap.get(orderVO.getEmpowerOrderId());
            List<Integer> deliveryItemList=new ArrayList<>();

            //自提订单，不走配送
            if (orderVO.getDeliveryMethod() != null && orderVO.getDeliveryMethod() == DistributeMethodEnum.STORE_DELIVERY.getValue()) {
                continue;
            }

            OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderVO.getOrderStatus());
            if((orderStatusEnum!=null && NOT_SHOW_DELIVERY_ITEM_ORDER_STATUS.contains(orderStatusEnum)) || orderVO.getChannelId()== null ){
                continue;
            }

            DistributeStatusEnum distributeStatusEnum = DistributeStatusEnum.DISTRIBUTE_UNKNOWN;
            if (Objects.nonNull(orderVO.getDistributeStatus())) {
                distributeStatusEnum = DistributeStatusEnum.enumOf(orderVO.getDistributeStatus());
            }

            //已经转单的不容许再次转单
            if(tDeliveryDetail!=null && tDeliveryDetail.transType!=null && tDeliveryDetail.transType!=0){
                continue;
            }

            //二次配送不容许转单
            if(tDeliveryDetail!=null && tDeliveryDetail.deliveryCount!=null && tDeliveryDetail.deliveryCount>1){
                continue;
            }
            //判断平台配送
            if(tDeliveryDetail==null){
                if(orderVO.getOriginalDistributeType()!=null && orderVO.getOriginalDistributeType() != 25){
                    orderVO.setPlatformDelivery(true);
                }
            } if(tDeliveryDetail.deliveryEntity != null && tDeliveryDetail.deliveryEntity == 0){
                orderVO.setPlatformDelivery(true);
            }

            if(tDeliveryDetail==null || tDeliveryDetail.deliveryEntity==null || tDeliveryDetail.deliveryEntity == 0){
                //平台配送
                fillPlatformDeliveryOperateItem(tDeliveryDetail, distributeStatusEnum, orderVO, deliveryItemList, tStoreConfigChannelMap);
            }else if(tDeliveryDetail.deliveryEntity == 1){
                //聚合配送
                if(distributeStatusEnum == DistributeStatusEnum.RIDER_DELIVERED){
                    continue;
                }
                if(distributeStatusEnum == DistributeStatusEnum.RIDER_TAKE_GOODS &&
                        ( tDeliveryDetail.deliveryExceptionCode==null || tDeliveryDetail.deliveryExceptionCode==0) ){
                    continue;
                }
                deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
            }else {
                //商家自己送
                fillSelfDeliveryOperateItem(distributeStatusEnum, tStoreConfigChannelMap, deliveryItemList, orderVO);
            }
            orderVO.setDeliveryOperateItems(deliveryItemList);
        }
    }

    private void fillPlatformDeliveryOperateItem (TDeliveryDetail tDeliveryDetail, DistributeStatusEnum distributeStatusEnum, OrderVO orderVO, List<Integer> deliveryItemList, Map<Long,Set<Integer>> tStoreConfigChannelMap) {
        Long time = null;
        Long now =System.currentTimeMillis();
        Map<Integer,Integer> configMap = MccConfigUtil.getPlatformToSelfConfig();
        Long storeId =orderVO.getStoreId();
        if(orderVO.getWarehouseId()!=null){
            storeId = orderVO.getWarehouseId();
        }
        if(tDeliveryDetail!=null && tDeliveryDetail.deliveryEntity!=null){
            time = tDeliveryDetail.createTime;
        }
        if (orderVO.getChannelId() == ChannelTypeEnum.MEITUAN.getChannelId()) {
            if(distributeStatusEnum.getValue() >= DistributeStatusEnum.SELF_DISTRIBUTE.getValue() && distributeStatusEnum.getValue()<=DistributeStatusEnum.RIDER_DELIVERED.getValue()){
                return;
            }
            if (time == null) {
                // 拿不到时间,取订单创建时间
                time = orderVO.getCreateTime();
            }
            if(configMap.containsKey(orderVO.getChannelId())){
                Long subTime=now - time;
                Long showTime=configMap.get(orderVO.getChannelId())*60*1000L;
                if(subTime<showTime){
                    return;
                }
            }
        } else if(orderVO.getChannelId() == ChannelTypeEnum.ELEM.getChannelId()) {

            Integer deliveryExceptionCode = (tDeliveryDetail == null || tDeliveryDetail.deliveryExceptionCode == null) ? 0 : tDeliveryDetail.deliveryExceptionCode;
            Integer deliveryStatus = orderVO.getRealDistributeStatus();

            if(distributeStatusEnum.getValue() >= DistributeStatusEnum.SELF_DISTRIBUTE.getValue() && distributeStatusEnum.getValue()<=DistributeStatusEnum.RIDER_DELIVERED.getValue()) {
                return;
            }
            if(Objects.nonNull(deliveryStatus)) {

                if (deliveryStatus != TmsDeliveryStatusDesc.DELIVERY_FAILED.getCode() &&
                        deliveryStatus != TmsDeliveryStatusDesc.DELIVERY_CANCELLED.getCode() &&
                        deliveryStatus != TmsDeliveryStatusDesc.DELIVERY_REJECTED.getCode()) {
                    return;
                }
                if(deliveryExceptionCode != DeliveryExceptionCodeEnum.SELF_DELIVERY.getCode() ) {
                    return;
                }
            } else {
                    return;
            }
        } else if (orderVO.getChannelId() == ChannelTypeEnum.JD2HOME.getChannelId()) {
            if (distributeStatusEnum.getValue() != DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue()) {
                return;
            }
            if (time == null && orderVO.getPickCompleteTime() != null) {
                //取拣货完成时间
                time = orderVO.getPickCompleteTime();
            }
            if (time == null){
                time = now;
            }
            if(configMap.containsKey(orderVO.getChannelId())){
                Long subTime=now - time;
                Long showTime=configMap.get(orderVO.getChannelId())*60*1000L;
                if(subTime<showTime){
                    return;
                }
            }
        } else {
            //其他平台不支持转自送
            return;
        }
        deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
        Set<Integer> deliveryPlatformSet=new HashSet<>();
        if(tStoreConfigChannelMap.containsKey(storeId)){
            deliveryPlatformSet = tStoreConfigChannelMap.get(storeId);
        }
        if(deliveryPlatformSet.contains(AggDeliveryPlatformEnum.MALT_FARM.getCode())){
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
        }
        if(deliveryPlatformSet.contains(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode())){
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
        }
    }

    private void fillSelfDeliveryOperateItem (DistributeStatusEnum distributeStatusEnum, Map<Long,Set<Integer>> tStoreConfigChannelMap, List<Integer> deliveryItemList, OrderVO orderVO) {
        Long storeId = orderVO.getStoreId();
        if(distributeStatusEnum == DistributeStatusEnum.RIDER_DELIVERED) {
            return;
        }
        Set<Integer> deliveryPlatform = new HashSet<>();
        if(tStoreConfigChannelMap.containsKey(storeId)) {
            deliveryPlatform = tStoreConfigChannelMap.get(storeId);
        }
        if(deliveryPlatform.contains(AggDeliveryPlatformEnum.MALT_FARM.getCode())) {
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
        }
        if(deliveryPlatform.contains(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode())) {
            deliveryItemList.add(DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
        }

    }

    private Map<String, DeliveryExceptionSummaryVO> queryDeliveryExceptionVo(List<OCMSOrderVO> ocmsOrderVOList) {

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long tenantId = identityInfo.getUser().getTenantId();

        if (!isDrunkHorseTenant(tenantId) || CollectionUtils.isEmpty(ocmsOrderVOList)){
            return new HashMap<>();
        }

        return ocmsOrderVOList.stream()
                .collect(Collectors.groupingBy(OCMSOrderVO::getShopId))
                .entrySet().stream()
                .map(entry->{
                    long shopId = entry.getKey();
                    return riderDeliveryServiceWrapper.queryRiderReportException(tenantId, shopId,
                            entry.getValue().stream().map(order -> Pair.of(order.getViewOrderId(), order.getOrderBizType()))
                                    .collect(Collectors.toList()));
                })
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toMap(DeliveryExceptionSummaryVO::getChannelOrderId, java.util.function.Function.identity(), (f,s) -> f));

    }

    private Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> toPlatformConfigMap(List<TAggDeliveryPlatformConfig> aggPlatformConfigs, List<Integer> platformList){
        Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> platformConfigMap=new HashMap<>();
        ArrayListMultimap<Integer,TAggDeliveryPlatformConfig> platformChannelMultiMap=ArrayListMultimap.create();
        for (TAggDeliveryPlatformConfig config : aggPlatformConfigs){
            if(!platformList.contains(config.getPlatformCode())){
                continue;
            }
            platformChannelMultiMap.put(config.getChannelType()==null ? com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType.MEITUAN.getValue():config.getChannelType(),config);
        }
        if(platformChannelMultiMap.isEmpty()){
            return null;
        }
        for (Integer channelType : platformChannelMultiMap.keySet()){
            List<TAggDeliveryPlatformConfig> configs=platformChannelMultiMap.get(channelType);
            if(CollectionUtils.isEmpty(configs)){
                continue;
            }
            Map<Integer,TAggDeliveryPlatformConfig> configMap=new HashMap<>();
            for (TAggDeliveryPlatformConfig config : configs){
                Integer platformId=AggDeliveryPlatformEnum.MALT_FARM.getCode();
                if(config.getPlatformCode()!=null){
                    platformId = config.getPlatformCode();
                }
                configMap.put(platformId,config);
            }
            platformConfigMap.put(channelType,configMap);
        }
        return platformConfigMap;
    }

    private List<TStoreConfig> queryDeliveryStoreConfigByOrderList(List<OrderVO> orderVOList){
        if(CollectionUtils.isEmpty(orderVOList)){
            return Collections.emptyList();
        }
        Long tenantId = orderVOList.get(0).getTenantId();
        Set<Long> storeIdSet = new HashSet<>();
        for (OrderVO vo : orderVOList){
            if(vo.getDispatchShopId()!=null){
                storeIdSet.add(vo.getDispatchShopId());
            }else if(vo.getWarehouseId()!=null){
                storeIdSet.add(vo.getWarehouseId());
            }else {
                storeIdSet.add(vo.getStoreId());
            }
        }
        if(tenantId <= 0 || CollectionUtils.isEmpty(storeIdSet)){
            return Collections.emptyList();
        }
        BatchStoreConfigQueryResponse batchStoreConfigQueryResponse = tmsServiceWrapper.batchDeliveryStoreConfigSearch(tenantId, new ArrayList<>(storeIdSet));
        if(batchStoreConfigQueryResponse == null || batchStoreConfigQueryResponse.getStatus() == null
                || batchStoreConfigQueryResponse.getStatus().getCode() != 0){
            return Collections.emptyList();
        }
        List<TStoreConfig> tStoreConfigs = batchStoreConfigQueryResponse.getTStoreConfigs();
        if(CollectionUtils.isEmpty(tStoreConfigs)){
            return Collections.emptyList();
        }
        return tStoreConfigs;
    }

    private void fillOrderMaltDeliveryPlatModule(List<OrderVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap, Map<Integer, DeliveryChannelDto> channelDtoMap,Long curStoreId){
        if(CollectionUtils.isEmpty(orderVOList)){
            return;
        }
        Long tenantId = orderVOList.get(0).getTenantId();
        List<TStoreConfig> tStoreConfigs = queryDeliveryStoreConfigByOrderList(orderVOList);
        if(CollectionUtils.isEmpty(tStoreConfigs)){
            return;
        }
        List<Integer> platformEnumList=Arrays.asList(AggDeliveryPlatformEnum.MALT_FARM.getCode(),AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
        Map<Long,Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>>> tStoreConfigMap=new HashMap<>();
        for (TStoreConfig tStoreConfig : tStoreConfigs){
            List<TAggDeliveryPlatformConfig> platformConfigList=tStoreConfig.getAggPlatformConfigs();
            if(CollectionUtils.isEmpty(platformConfigList)){
                continue;
            }
            tStoreConfigMap.put(tStoreConfig.getStoreId(),toPlatformConfigMap(platformConfigList,platformEnumList));
        }

        Map<Integer, Integer> deliveryChannelMap = deliveryChannelWrapper.tratranslateToChannelIntgerMap(channelDtoMap);
        ArrayListMultimap<Long,Long> dapOrderIdMultiMap=ArrayListMultimap.create();
        ArrayListMultimap<Long,String> dapFulfillOrderIdMultiMap=ArrayListMultimap.create();
        List<Long> poiIdList = new ArrayList<>();
        for (OrderVO vo : orderVOList){
            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(vo.getEmpowerOrderId());
            if(deliveryDetail==null){
                continue;
            }
            if(!deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),vo.getTenantId(),vo.getStoreId(),deliveryChannelMap)){
                continue;
            }
            if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource, PlatformSourceEnum.OFC.getCode()) ){
                Long shopId = vo.getStoreId();
                if(vo.getDispatchShopId()!=null){
                    shopId = vo.getDispatchShopId();
                }else if(vo.getWarehouseId()!=null){
                    shopId = vo.getWarehouseId();
                }
                poiIdList.add(shopId);
                dapFulfillOrderIdMultiMap.put(shopId,PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+""));
            }else {
                if(vo.getWarehouseId()!=null){
                    dapOrderIdMultiMap.put(vo.getWarehouseId(),vo.getEmpowerOrderId());
                    poiIdList.add(vo.getWarehouseId());
                }else {
                    dapOrderIdMultiMap.put(vo.getStoreId(),vo.getEmpowerOrderId());
                    poiIdList.add(vo.getStoreId());
                }
            }
        }

        Map<String,String> urlMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(poiIdList)){
            for (Long poiId : poiIdList){
                List<Long> orderIdList=dapOrderIdMultiMap.get(poiId);
                Map<String,String> url = tmsServiceWrapper.batchQueryOrderDeliveryUrl(tenantId,poiId,orderIdList,dapFulfillOrderIdMultiMap.get(poiId));
                if(MapUtils.isEmpty(url)){
                    continue;
                }
                urlMap.putAll(url);
            }
        }

        orderVOList.forEach(order -> {
            if(order.getStoreId() == null){
                return;
            }
            TAggDeliveryPlatformConfig tAggDeliveryPlatformConfig = null;
            Long shopId=order.getStoreId();
            if(order.getWarehouseId()!=null){
                shopId=order.getWarehouseId();
            }
            if(order.getDispatchShopId()!=null){
                shopId = order.getDispatchShopId();
            }

            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(order.getEmpowerOrderId());

            if(deliveryDetail == null){
                return;
            }

            Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> platformConfigMap = tStoreConfigMap.get(shopId);
            if(MapUtils.isNotEmpty(platformConfigMap)){
                Map<Integer,TAggDeliveryPlatformConfig> configMap=new HashMap<>();
                if(platformConfigMap.containsKey(order.getChannelId())){
                    configMap=platformConfigMap.get(order.getChannelId());
                }else {
                    configMap=platformConfigMap.get(ChannelType.MEITUAN.getValue());
                }
                if(MapUtils.isNotEmpty(configMap)){
                    AggDeliveryPlatformEnum platformEnum = AggDeliveryPlatformEnum.MALT_FARM;
                    if(deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                        platformEnum = AggDeliveryPlatformEnum.DAP_DELIVERY;
                    }
                    tAggDeliveryPlatformConfig = configMap.get(platformEnum.getCode());
                }
            }
            if(tAggDeliveryPlatformConfig == null){
                tAggDeliveryPlatformConfig = new TAggDeliveryPlatformConfig();
            }

            if(tAggDeliveryPlatformConfig.getPlatformCode() == null){
                return;
            }
            String oId = order.getEmpowerOrderId().toString();
            if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource,PlatformSourceEnum.OFC.getCode())){
                oId = PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+"");
            }

            if(deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.MALT_FARM.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.MALT_FARM.getCode());
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDesc.INIT.getCode() ||
                        // 针对麦芽田，配送拒单，也不展示链接
                        order.getRealDistributeStatus() == TmsDeliveryStatusDesc.DELIVERY_REJECTED.getCode() ||
                        // 麦芽田转自配送后，不展示链接
                        deliveryDetail.deliveryChannelCode == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
                    DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                    deliveryRedirectModuleVo.setTitle("暂无配送状态");
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                    return;
                }
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.MALT_FARM.fillDeliveryRedirectModule(tAggDeliveryPlatformConfig,
                        oId, shopId,
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                if(!Objects.equals(curStoreId,shopId)){
                    deliveryRedirectModuleVo.setUrl("");
                    deliveryRedirectModuleVo.setUrlText("");
                }
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
            }else if(deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDesc.INIT.getCode() ||
                        //配送拒单，也不展示链接
                        order.getRealDistributeStatus() == TmsDeliveryStatusDesc.DELIVERY_REJECTED.getCode()) {
                    DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                    deliveryRedirectModuleVo.setTitle("暂无配送状态");
                    deliveryRedirectModuleVo.setShowButton(false);
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                    return;
                }
                String url=urlMap.get(oId);
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.DAP_DELIVERY.fillDeliveryRedirectModule(url,
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                if(!Objects.equals(curStoreId,shopId)){
                    deliveryRedirectModuleVo.setUrl("");
                    deliveryRedirectModuleVo.setUrlText("");
                    deliveryRedirectModuleVo.setShowButton(false);
                }
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
            }
        });
    }

    private String generatePrintToast(OrderPrintResultDto printResultDto) {
        String toast = null;
        /***
         * 打印状态，40，45，99，999 都归类成“打印失败”
         * **/
        PrintOpLogStatusEnum printStatusEnum = PrintOpLogStatusEnum.enumOf(printResultDto.getPrintStatus());
        if (printStatusEnum != null) {
            switch (printStatusEnum) {
                case PRINT_TIME_OUT:
                    toast = ConfigUtilAdapter.getString("order.print.timeout.tips", "打印超时，若未出票请检查打印机状态后重试");
                    break;
                case SERVICE_NOT_AVAILABLE:
                case PRINTER_OFFLINE:
                case PRINTER_ABNORMAL:
                case UNKNOWN:
                    toast = ConfigUtilAdapter.getString("order.print.fail.tips", "打印失败，请检查打印机状态后重试");
                    break;
                case PRINTING:
                case WAITING_FOR_PRINT:
                case PRINT_SUCCESS:
                    toast = null;
                    break;
                default:
                    log.error("未知打印状态码:{}", printStatusEnum);

            }
        }
        return toast;
    }

    private PageInfoVO buildPageInfoVO(Integer page, Integer size, Integer totalSize) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(page);
        pageInfoVO.setSize(size);
        pageInfoVO.setTotalSize(totalSize);
        pageInfoVO.setTotalPage(totalSize % size == 0 ? totalSize / size : totalSize / size + 1);
        return pageInfoVO;
    }

    private void setRefundApplyDetailPicurl(RefundApplyRecordVO refundApplyRecordVO){
        final Map<String, ProductVO> productItemMap = new HashMap<>();
        OrderVO orderVO = refundApplyRecordVO.getOrderVO();
        if (orderVO != null && CollectionUtils.isNotEmpty(orderVO.getProductList())) {
            productItemMap.putAll(orderVO.getProductList().stream().filter(item -> StringUtils.isNotBlank(item.getSkuId())).collect(Collectors.toMap(ProductVO::getSkuId, item -> item, (k1, k2) -> k1)));
        }
        if(MapUtils.isNotEmpty(productItemMap)){
            refundApplyRecordVO.getRefundApplyRecordDetailVOList().stream().forEach(item -> {
                item.setPicUrl(productItemMap.containsKey(item.getSkuId()) ? productItemMap.get(item.getSkuId()).getPicUrl() : null);
            });
        }
    }


    public OrderVO buildOrderVO(OCMSOrderVO ocmsOrderVO, OrderRevenueDetailResponse orderRevenueDetailResponse, OrderProfitView orderProfit) {
        List<OCMSOrderItemVO> ocmsOrderItemVOList = ocmsOrderVO.getOcmsOrderItemVOList();
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        OrderVO orderVO = new OrderVO();
        orderVO.setTenantId(ocmsOrderVO.getTenantId());
        orderVO.setIsHasCanRefundGoods(ocmsOrderVO.getIsHasCanRefundGoods());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
        orderVO.setChannelId(channelId);
        if (Objects.nonNull(DynamicChannelType.findOf(channelId))) {
            //订单页展示渠道简称
            orderVO.setChannelName(Objects.requireNonNull(DynamicChannelType.findOf(channelId)).getDesc());
        }

        // 名酒馆标签
        orderVO.setIsMtFamousTavern(ocmsOrderVO.getIsMtFamousTavern());

        orderVO.setEmpowerOrderId(ocmsOrderVO.getOrderId());
        orderVO.setUserId(ocmsOrderVO.getUserId());
        orderVO.setStoreId(ocmsOrderVO.getShopId());
        orderVO.setStoreName(ocmsOrderVO.getShopName());
        orderVO.setDispatchShopId(ocmsOrderVO.getDispatchShopId());
        orderVO.setDispatchSerialNo(ocmsOrderVO.getDispatchSerialNo());
        if(StringUtils.isNotBlank(ocmsOrderVO.getDispatchShopName())){
            orderVO.setDispatchShopName(ocmsOrderVO.getDispatchShopName());
        }
        orderVO.setDispatchTenantId(ocmsOrderVO.getDispatchTenantId());
        orderVO.setDispatchTime(ocmsOrderVO.getDispatchTime());
        orderVO.setWarehouseId(ocmsOrderVO.getWarehouseId());
        orderVO.setWarehouseName(ocmsOrderVO.getWarehouseName());
        orderVO.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        orderVO.setSerialNo(ocmsOrderVO.getOrderSerialNumber());
        orderVO.setOrderSource(ocmsOrderVO.getOrderSource());
        // 支付时间
        orderVO.setPayTime(ocmsOrderVO.getPayTime() != null ? ocmsOrderVO.getPayTime() : ocmsOrderVO.getCreateTime());
        //商品数量为所有商品数量之和

        orderVO.setActualPayAmt(ocmsOrderVO.getActualPayAmt());
        orderVO.setBizReceiveAmt(ocmsOrderVO.getMerchantAmount());
        if (isDeliveryInfoNotNull(ocmsDeliveryInfoVO)){
            orderVO.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
            orderVO.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());
            orderVO.setDeliveryUserName(ocmsDeliveryInfoVO.getRiderName());
            orderVO.setDeliveryUserPhone(ocmsDeliveryInfoVO.getRiderPhone());
            orderVO.setReceiverName(ocmsDeliveryInfoVO.getUserName());
            orderVO.setReceiverPhone(ocmsDeliveryInfoVO.getUserPhone());
            orderVO.setReceiverAddress(ocmsDeliveryInfoVO.getUserAddress());
            // 自提
            boolean selfMention = DistributeMethodEnum.STORE_DELIVERY.getDesc().equals(ocmsDeliveryInfoVO.getDistributeMethodName());
            if(selfMention && orderVO.getChannelId() != null && orderVO.getChannelId().equals(ChannelType.YOU_ZAN.getValue())){
                // 有赞渠道自提隐藏用户地址
                orderVO.setReceiverAddress("到店自取@#到店自取");
            }
            orderVO.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
            orderVO.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());
            orderVO.setPickStatus(ocmsDeliveryInfoVO.getDeliveryStatus());
            orderVO.setPickCompleteTime(ocmsDeliveryInfoVO.getCompleteTime());
            orderVO.setDistributeStatus(ocmsDeliveryInfoVO.getDistributeStatus());
            orderVO.setSelfDelivery(ocmsDeliveryInfoVO.getIsSelfDelivery());
            orderVO.setSupportDeliveryUserPrivacyPhone(judgeSupportDeliveryUserPrivacyPhone(ocmsDeliveryInfoVO.getIsSelfDelivery()));
            if (ocmsDeliveryInfoVO.getDeliveryPauseFlag() != null){
                orderVO.setDeliveryStatusLocked(ocmsDeliveryInfoVO.getDeliveryPauseFlag());
            }
            if(StringUtils.isNotEmpty(ocmsDeliveryInfoVO.getSelfFetchCode())){
                orderVO.setSelfFetchCode(ocmsDeliveryInfoVO.getSelfFetchCode());
            }
            orderVO.setSelfFetchStatus(ocmsDeliveryInfoVO.getSelfFetchStatus());
            orderVO.setDeliveryChannelId(ocmsDeliveryInfoVO.getDeliveryChannelId());
        }
        String distributeStatusDesc = Objects.isNull(ocmsDeliveryInfoVO) ||Objects.isNull(ocmsDeliveryInfoVO.getDistributeStatus())|| Objects.isNull(DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()))
                ? "" : DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()).getDesc();
        orderVO.setDistributeStatusDesc(distributeStatusDesc.equals(UN_KNOW) ? "" : distributeStatusDesc);
        orderVO.setOrderStatus(ocmsOrderVO.getOrderStatus());
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrderVO.getOrderStatus());
        orderVO.setOrderStatusDesc(orderStatus == null? "未知状态": orderStatus.getDesc());
        orderVO.setCreateTime(ocmsOrderVO.getCreateTime());
        // == 1 会出现NPE
        orderVO.setDeliveryOrderType(Integer.valueOf(1).equals(ocmsOrderVO.getIsBooking()) ? DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue() : DeliveryOrderType.DELIVERY_RIGHT_NOW.getValue());
        orderVO.setDeliveryOrderTypeName(getDeliveryOrderTypeName(orderVO.getDeliveryOrderType()));
        orderVO.setUpdateTime(ocmsOrderVO.getUpdateTime());
        orderVO.setChannelExtraOrderId(ocmsOrderVO.getExtOrderId());
        //备注不为空且不为0才展示
        if (StringUtils.isNotEmpty(ocmsOrderVO.getComments()) && !ocmsOrderVO.getComments().equals("0")) {
            orderVO.setComments(ocmsOrderVO.getComments());
        }
        orderVO.setTotalOfflinePrice(ocmsOrderVO.getTotalOfflinePrice());
        orderVO.setSelfPickPullNewOrder(ocmsOrderVO.getSelfPickPullNewOrder());
        Map<String, OCMSOrderItemVO> orderItemMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
            orderItemMap = ocmsOrderItemVOList.stream().filter(item -> StringUtils.isNotBlank(item.getInstoreSkuId2())).collect(Collectors.toMap(OCMSOrderItemVO::getInstoreSkuId2, item -> item, (k1, k2) -> k1));
            orderVO.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull).map(this::buildProductVO).filter(Objects::nonNull).collect(Collectors.toList()));
            Integer itemCount = orderVO.getProductList().stream().filter(Objects::nonNull).mapToInt(ProductVO::getCount).sum();
            orderVO.setItemCount(itemCount);
        }else{
            orderVO.setItemCount(0);
            orderVO.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull).map(this::buildProductVO).collect(Collectors.toList()));

        }
        if (CollectionUtils.isNotEmpty(ocmsOrderVO.getOnlineGiftVOS())) {
            orderVO.setGiftVOList(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).map(this::buildGiftVO).collect(Collectors.toList()));
            orderVO.setGiftCount(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).mapToInt(OnlineGiftVO::getGiftQuantity).sum());
        }
        //build refund info
        if (CollectionUtils.isNotEmpty(ocmsOrderVO.getAfterSaleApplyVOList())) {
            OrderRefundInfo orderRefundInfo = new OrderRefundInfo();
            removeInvalidAfterSaleApply(ocmsOrderVO, ocmsOrderVO.getAfterSaleApplyVOList());
            //只处理用户退款，不处理用户申述
            if (Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), ocmsOrderVO.getOrderStatus())) {
                OCMSAfterSaleApplyVO afterSaleApplyVO = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList()).map(List::stream).orElse(Stream.empty())
                        .filter(e->e.isWait2Audit() || AfterSaleApplyStatusEnum.WAIT_ASSIGN.getValue().equals(e.getStatus())).findFirst().orElse(null);
                OrderStatusLog orderStatusLog = ocmsOrderVO.getOrderStatusLogList().stream()
                        .filter(e -> Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getTargetStatus()))
                        .max(Comparator.comparingLong(e -> e.getCreateTime()))
                        .orElse(null);
                if (afterSaleApplyVO != null && orderStatusLog != null) {
                    //设置等待退款的申请，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                    orderRefundInfo.setWaitAuditRefund(buildWaitAuditRefund(orderStatusLog, afterSaleApplyVO, orderItemMap));
                }

                // 设置多条待审核售后单
                if(MccConfigUtil.isShowWaitAuditRefundList()){
                    List<OCMSAfterSaleApplyVO> afterSaleApplyVOList = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList()).map(List::stream).orElse(Stream.empty())
                            .filter(OCMSAfterSaleApplyVO::isWait2Audit).collect(Collectors.toList());
                    if (afterSaleApplyVO != null && orderStatusLog != null) {
                        //设置等待退款的申请列表，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                        orderRefundInfo.setWaitAuditRefundList(buildWaitAuditRefundList(orderStatusLog, afterSaleApplyVOList, orderItemMap));
                    }
                }
            }
            //这里的退款信息会关联订单状态，退差价不会改变订单状态
            List<RefundLog> refundLogList = ocmsOrderVO.getOrderStatusLogList().stream().filter(e -> isRefundConcernStatusChange(e))
                    .map(orderStatusLog -> {
                        boolean isAuditRefund = Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), orderStatusLog.getSourceStatus());
                        OCMSAfterSaleApplyVO afterSaleApplyVO = findNearestTimeAfterSale(orderStatusLog.getCreateTime(), ocmsOrderVO.getAfterSaleApplyVOList(), isAuditRefund);
                        //只处理处理完的售后申请, 京东的暂存售后单也不显示给前端
                        if (afterSaleApplyVO != null && !afterSaleApplyVO.isWait2Audit()
                                && AfterSaleApplyStatusEnum.DRAFT.getValue().equals(afterSaleApplyVO.getStatus())
                        && AfterSaleApplyStatusEnum.DRAFT_DONE.getValue().equals(afterSaleApplyVO.getStatus())) {
                            return RefundLog.buildRefundLog(orderStatusLog, afterSaleApplyVO);
                        }
                        return null;
                    }).filter(Objects::nonNull).sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed()).collect(Collectors.toList());
            //添加退差价信息,重排序
            refundLogList.addAll(addWeightRefundLog(ocmsOrderVO));
            refundLogList = refundLogList.stream().sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed()).collect(Collectors.toList());
            orderRefundInfo.setRefundLogs(refundLogList);
            orderVO.setOrderRefundInfo(orderRefundInfo);
        }

        // 订单营收信息
        if(orderRevenueDetailResponse != null && orderRevenueDetailResponse.getOrderAmountInfo() != null){
            OrderAmountInfo orderAmountInfo = orderRevenueDetailResponse.getOrderAmountInfo();
            RevenueDetailVo revenueDetailVo = new RevenueDetailVo();
            revenueDetailVo.setPromotionInfos(orderRevenueDetailResponse.getPromotionInfos());
            revenueDetailVo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
            if(!Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(ocmsOrderVO.getOrderSource())){
                // todo 牵牛花一期不返回活动分摊信息、二期适配后再放开
                revenueDetailVo.setBizActivityAmount(orderAmountInfo.getBizCharge());
            }
            revenueDetailVo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
            revenueDetailVo.setPackageAmount(orderAmountInfo.getPackageAmount());
            revenueDetailVo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
            if (orderProfit != null) {
                revenueDetailVo.setNetProfitOnline(orderProfit.getProfit().intValue());
                revenueDetailVo.setWithDeliveryCost(orderProfit.getWithDeliveryCost());
            }
            orderVO.setRevenueDetail(revenueDetailVo);
        }
        orderVO.setUserTags(UserTagTypeEnum.getTags(ocmsOrderVO.getTags()));
        orderVO.setSortedTagList(SortedOrderTagVO.buildListOfMng(ocmsOrderVO.getSortedTagList()));
        orderVO.setIsNeedInvoice(ocmsOrderVO.getIsNeedInvoice());
        orderVO.setActionTagList(ActionTagUtil.ocmsOrderVoConvertTag(ocmsOrderVO));
        orderVO.setOrderUserType(ocmsOrderVO.getUserType());
        orderVO.setMigrateFlag(ocmsOrderVO.getMigrateFlag());
        // 开票信息
        orderVO.setInvoiceTitle(ocmsOrderVO.getInvoiceTitle());
        orderVO.setInvoiceType(ocmsOrderVO.getInvoiceType());
        orderVO.setInvoiceTaxNo(ocmsOrderVO.getInvoiceTaxNo());
        if(ocmsOrderVO.getOcmsDeliveryInfoVO()!=null){
            orderVO.setOriginalDistributeType(ocmsOrderVO.getOcmsDeliveryInfoVO().getOriginalDistributeType());
        }
        //歪马需要额外信息：解析后的属性 + 赠品的mainSkuId
        if (MccConfigUtil.isDrunkHorseTenant(ocmsOrderVO.getTenantId()) && MccConfigUtil.isNewPickGrayStore(ocmsOrderVO.getShopId())) {
            //解析后的属性
            for (ProductVO productVO : Optional.ofNullable(orderVO.getProductList()).orElse(Lists.newArrayList())) {
                //之前逻辑是前端过滤为商品属性的，这次只替换tagInfos
                    if (CollectionUtils.isNotEmpty(productVO.getTagInfos())) {
                        List<ParsedPropertiesVO> propertiesViewVOList = productVO.getTagInfos().stream()
                                .map(TagInfoVO::getName)
                                .filter(property -> MccConfigUtil.getPropertyColorMap().containsKey(property))
                                .map(property -> new ParsedPropertiesVO(property, MccConfigUtil.getPropertyColorMap().get(property)))
                                .collect(Collectors.toList());
                        productVO.setParsedProperties(propertiesViewVOList);
                    }
                    productVO.setTagInfos(null);
            }

            //赠品的mainSkuId
            orderVO.setGiftVOList(Optional.ofNullable(ocmsOrderVO.getOnlineGiftVOS()).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).map(this::buildGiftVOWithMainSkuId).collect(Collectors.toList()));
        }

        if (MccConfigUtil.isDrunkHorseTenant(ocmsOrderVO.getTenantId()) && MccConfigUtil.isGrayWiderShippingAreaStores(ocmsOrderVO.getShopId())) {
            try {
                orderVO.setIsWiderShippingArea(ocmsOrderVO.getWiderShippingArea());
                if (Objects.equals(ocmsDeliveryInfoVO.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
                    //订单侧其他渠道的的隐私号和正常号赋值反了。
                    //微商城渠道是正确的，但其他渠道取隐私号反而是正常号码
                    orderVO.setReceiverTailPhoneNumber(
                            ocmsOrderVO.getOcmsDeliveryInfoVO().getUserPhone().substring(ocmsOrderVO.getOcmsDeliveryInfoVO().getUserPhone().length() - 4)
                    );
                } else {
                    orderVO.setReceiverTailPhoneNumber(
                            ocmsOrderVO.getRecipientPhone().substring(ocmsOrderVO.getRecipientPhone().length() - 4)
                    );
                }
            } catch (Exception e) {
                log.error("set IsWiderShippingArea && ReceiverTailPhoneNumber error", e);
            }
        }

        return orderVO;
    }


    public static List<GoodsItemVO> transfer2GoodsItemVO(List<TradeShippingOrderItemDTO> tradeShippingOrderItemDTOList) {
        try {
            return Optional.ofNullable(tradeShippingOrderItemDTOList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.groupingBy(TradeShippingOrderItemDTO::getSkuId))
                    .values()
                    .stream()
                    .map(tradeShippingOrderItemDTOS -> {
                                Map<String, Integer> temperature2CountMap = tradeShippingOrderItemDTOS
                                        .stream()
                                        .collect(Collectors.toMap(item -> Objects.isNull(item.getTemperatureZoneCode()) ? StringUtils.EMPTY : item.getTemperatureZoneCode(),
                                                item -> item.getActualQty().intValue(), Integer::sum));
                                TradeShippingOrderItemDTO orderItemDTO = tradeShippingOrderItemDTOS.get(0);
                                GoodsItemVO goodsItemVO = new GoodsItemVO();
                                goodsItemVO.setGoodsId(orderItemDTO.getSkuId());
                                goodsItemVO.setGoodsName(orderItemDTO.getSkuName());
                                goodsItemVO.setRealPicUrlList(orderItemDTO.getImgUrls());
                                goodsItemVO.setSpecification(orderItemDTO.getSpecification());
                                goodsItemVO.setIceCount(temperature2CountMap.getOrDefault(TemperaturePropertyEnum.ICE_TEMPERATURE.getDesc(), 0));
                                goodsItemVO.setNormalCount(temperature2CountMap.getOrDefault(TemperaturePropertyEnum.NORMAL_TEMPERATURE.getDesc(), 0));
                                goodsItemVO.setNoTemperatureCount(temperature2CountMap.getOrDefault(StringUtils.EMPTY, 0));
                                goodsItemVO.setDependOnRemarkCount(temperature2CountMap.getOrDefault(TemperaturePropertyEnum.USER_DEFINED.getDesc(), 0));
                                return goodsItemVO;
                            }
                    )
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("transfer2GoodsItemVO error", e);
            return Collections.emptyList();
        }

    }

    private void removeInvalidAfterSaleApply(OCMSOrderVO ocmsOrderVO, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList) {
        if (CollectionUtils.isNotEmpty(afterSaleApplyVOList)){
            Iterator<OCMSAfterSaleApplyVO> it = afterSaleApplyVOList.iterator();
            while(it.hasNext()){
                OCMSAfterSaleApplyVO applyVO = it.next();
                if (StringUtils.isBlank(applyVO.getAfterSaleId()) || applyVO.getCreateTime() == null || applyVO.getUpdateTime() == null || applyVO.getOrderId() == null){
                    log.info("售后索引，缺少必要信息，可能是Es索引写入延迟问题,serviceId:{}, order:{}", applyVO.getServiceId(), ocmsOrderVO.getViewOrderId());
                    it.remove();
                    MetricHelper.build().name("order.afterSaleRecordInvalid.err").tag("tenantId", String.valueOf(ocmsOrderVO.getTenantId())).tag("storeId", String.valueOf(ocmsOrderVO.getShopId())).count();
                }
            }
        }
    }

    private boolean isDeliveryInfoNotNull(OCMSDeliveryInfoVO ocmsDeliveryInfoVO) {
        return ocmsDeliveryInfoVO != null &&
                ocmsDeliveryInfoVO.getCreateTime() != null &&
                ocmsDeliveryInfoVO.getOrderId() != null &&
                ocmsDeliveryInfoVO.getDeliveryStatus() != null &&
                ocmsDeliveryInfoVO.getTenantId() != null &&
                ocmsDeliveryInfoVO.getShopId() != null;
    }


    private OCMSAfterSaleApplyVO findNearestTimeAfterSale(Long orderStatusCreateTime, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, boolean isAuditRefund) {
        return afterSaleApplyVOList.stream()
                .filter(e -> !(isAuditRefund && e.isWait2Audit()))//如果是审批类型orderStatusLog，去掉那些还在处于退款审批中的记录
                .min(Comparator.comparingDouble(e -> {
                    long time = isAuditRefund ? e.getUpdateTime() : e.getCreateTime();
                    return Math.abs(time - orderStatusCreateTime);
                }))
                .orElse(null);
    }

    private boolean isRefundConcernStatusChange(OrderStatusLog e) {
        if (e != null) {
            //不展示申述
            return Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getSourceStatus()) || Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getTargetStatus());
        }
        return false;
    }

    private RefundingRecordVO buildWaitAuditRefund(OrderStatusLog orderStatusLog, OCMSAfterSaleApplyVO afterSaleApplyVO, Map<String, OCMSOrderItemVO> orderItemMap) {
        RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
        refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
        refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
        refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
        refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
        refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
        refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
        refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
        refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
        refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
        refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
        refundApplyRecordVO.setAssignShopId(afterSaleApplyVO.getDispatchShopId());
        refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
        if (afterSaleApplyVO.getApplyType() != null) {
            AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
        }
        List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(this::buildRefundApplyRecordDetailVO).collect(Collectors.toList());
            // 将订单中的商品信息赋值到售后信息中
            if(MapUtils.isNotEmpty(orderItemMap)){
                refundApplyRecordDetailVOList.stream().forEach(item -> {
                    if(orderItemMap.containsKey(item.getSkuId())){
                        item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                        item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                        item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());
                    }
                });
            }
            refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
            refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
            // 退款总金额计算
            if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                        .stream().filter(Objects::nonNull)
                        .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                        .filter(Objects::nonNull).sum());
            }
        }
        refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
        setWorryFreeReturn(afterSaleApplyVO,refundApplyRecordVO);
        return refundApplyRecordVO;
    }

    private List<RefundingRecordVO> buildWaitAuditRefundList(OrderStatusLog orderStatusLog, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, Map<String, OCMSOrderItemVO> orderItemMap) {
        List<RefundingRecordVO> refundingRecordVOList = new ArrayList<>();
        Optional.ofNullable(afterSaleApplyVOList).map(List::stream).orElse(Stream.empty()).forEach(afterSaleApplyVO->{
            RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
            refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
            refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
            refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
            refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
            refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
            refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
            refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
            refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
            refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
            refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
            if (afterSaleApplyVO.getApplyType() != null) {
                AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
                refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
            }
            List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
            if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
                List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(this::buildRefundApplyRecordDetailVO).collect(Collectors.toList());
                // 将订单中的商品信息赋值到售后信息中
                if(MapUtils.isNotEmpty(orderItemMap)){
                    refundApplyRecordDetailVOList.stream().forEach(item -> {
                        if(orderItemMap.containsKey(item.getSkuId())){
                            item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                            item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                            item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());
                        }
                    });
                }
                refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
                refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
                // 退款总金额计算
                if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                    refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                            .stream().filter(Objects::nonNull)
                            .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                            .filter(Objects::nonNull).sum());
                }
            }
            refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
            refundingRecordVOList.add(refundApplyRecordVO);
        });
        return refundingRecordVOList;
    }


    public GiftVO buildGiftVO(OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            return giftVO;
        }
        return null;
    }

    public GiftVO buildGiftVOWithMainSkuId(OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            giftVO.setBelongSkuId(onlineGiftVO.getMainSkuId());
            giftVO.setSku(onlineGiftVO.getGiftSku());
            if(StringUtils.isNotBlank(onlineGiftVO.getGiftSpec())) {
                giftVO.setSpecification(onlineGiftVO.getGiftSpec());
            }
            return giftVO;
        }
        return null;
    }

    private RefundApplyRecordVO buildRefundApplyRecordVO(OCMSWaitAuditOrderVO ocmsWaitAuditOrderVO,
            OrderRevenueDetailResponse orderRevenueDetailResponse, OrderProfitView orderProfit) {
        RefundApplyRecordVO refundApplyRecordVO = new RefundApplyRecordVO();
        refundApplyRecordVO.setOrderVO(buildOrderVO(ocmsWaitAuditOrderVO, orderRevenueDetailResponse, orderProfit));

        OCMSAfterSaleApplyVO ocmsAfterSaleApplyVO = ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO();
        refundApplyRecordVO.setServiceId(ocmsAfterSaleApplyVO.getServiceId());
        refundApplyRecordVO.setAfterSaleId(ocmsAfterSaleApplyVO.getAfterSaleId());
        refundApplyRecordVO.setIsAudit(ocmsAfterSaleApplyVO.getIsAudit());
        refundApplyRecordVO.setStatus(ocmsAfterSaleApplyVO.getStatus());
        refundApplyRecordVO.setApplyReason(ocmsAfterSaleApplyVO.getApplyReason());
        refundApplyRecordVO.setAfsPattern(ocmsAfterSaleApplyVO.getAfsPattern());
        refundApplyRecordVO.setRefundAmt(ocmsAfterSaleApplyVO.getRefundAmt());
        refundApplyRecordVO.setCreateTime(ocmsAfterSaleApplyVO.getCreateTime());
        refundApplyRecordVO.setUpdateTime(ocmsAfterSaleApplyVO.getUpdateTime());
        refundApplyRecordVO.setAfsApplyType(ocmsAfterSaleApplyVO.getApplyType());
        refundApplyRecordVO.setWhoApplyType(ocmsAfterSaleApplyVO.getApplyUserType());
        refundApplyRecordVO.setDispatchShopId(ocmsAfterSaleApplyVO.getDispatchShopId());
        refundApplyRecordVO.setDealTime(ocmsAfterSaleApplyVO.getDealTime());
        List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = ocmsAfterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
            refundApplyRecordVO.setRefundApplyRecordDetailVOList(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(this::buildRefundApplyRecordDetailVO).collect(Collectors.toList()));
            // 将商品图片赋值到售后信息中
            setRefundApplyDetailPicurl(refundApplyRecordVO);

            refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
        }
        if (refundApplyRecordVO.getOrderVO() != null) {
            refundApplyRecordVO.setOrderRefundInfo(refundApplyRecordVO.getOrderVO().getOrderRefundInfo());
        }
        refundApplyRecordVO.setRefundPicList(ocmsAfterSaleApplyVO.getRefundPicList());

        if (ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO().getApplyType() != null) {
            AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO().getApplyType());
            refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
            setWorryFreeReturn(ocmsAfterSaleApplyVO,refundApplyRecordVO);
        }

        return refundApplyRecordVO;
    }
    private void setWorryFreeReturn(OCMSAfterSaleApplyVO item, RefundApplyRecordVO refundApplyRecordVO) {
        AfterSaleApplyStatusEnum afterSaleApplyStatusEnum = AfterSaleApplyStatusEnum.enumof(item.getStatus());
        AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(item.getApplyType());
        if (afterSaleApplyStatusEnum != null
                && (afterSaleApplyStatusEnum == COMMIT || afterSaleApplyStatusEnum == FIRST_AUDIT_ING)) {
            if (afterSaleTypeEnum != null && afterSaleTypeEnum == AfterSaleTypeEnum.REFUND_GOODS &&item.getPreReturnFreight()!=null && item.getPreReturnFreight() != 0) {
                refundApplyRecordVO.setDirectRefundFlag(1);
            }
        }
        refundApplyRecordVO.setPreReturnFreight(item.getPreReturnFreight());
    }

    private void setWorryFreeReturn(OCMSAfterSaleApplyVO item, RefundingRecordVO refundApplyRecordVO) {
        AfterSaleApplyStatusEnum afterSaleApplyStatusEnum = AfterSaleApplyStatusEnum.enumof(item.getStatus());
        AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(item.getApplyType());
        if (afterSaleApplyStatusEnum != null
                && (afterSaleApplyStatusEnum == COMMIT || afterSaleApplyStatusEnum == FIRST_AUDIT_ING)) {
            if (afterSaleTypeEnum != null && afterSaleTypeEnum == AfterSaleTypeEnum.REFUND_GOODS&&item.getPreReturnFreight()!=null&& item.getPreReturnFreight() != 0) {
                refundApplyRecordVO.setDirectRefundFlag(1);
            }
        }
        refundApplyRecordVO.setPreReturnFreight(item.getPreReturnFreight());
    }

    public ProductVO buildProductVO(OCMSOrderItemVO ocmsOrderItemVO) {
        ProductVO productVO = new ProductVO();
        productVO.setSkuId(ocmsOrderItemVO.getCustomerSkuId());
        productVO.setUpcCode(ocmsOrderItemVO.getSkuCode());
        productVO.setSkuName(ocmsOrderItemVO.getSkuName());
        productVO.setPicUrl(ocmsOrderItemVO.getPicUrl());
        productVO.setSpecification(ocmsOrderItemVO.getSpecification());
        productVO.setSellUnit(ocmsOrderItemVO.getSellUnit());

        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemVO.getExtData());
        //如果是换货商品，直接不展示，过滤掉
        if(exchangeDO.getExchangeSourceOrderItemId() > 0){
            return null;
        }
        int quantity = exchangeDO.getOrderQuantity() == 0?ocmsOrderItemVO.getQuantity():exchangeDO.getOrderQuantity();
        productVO.setCount(quantity);
        if (Objects.nonNull(ocmsOrderItemVO.getOriginalPrice())) {
            productVO.setOriginalTotalPrice(ocmsOrderItemVO.getOriginalPrice() * quantity);
        }
        productVO.setTotalPayAmount(ocmsOrderItemVO.getTotalPayAmount());
        productVO.setUnitPrice(ocmsOrderItemVO.getUnitPrice());

        productVO.setOrderItemOfflinePrice(ocmsOrderItemVO.getOfflinePrice());
        List<TagInfoVO> tagInfoVOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getFulfillmentTagList())) {
            for (String fulfillmentTag : ocmsOrderItemVO.getFulfillmentTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(fulfillmentTag);
                tagInfoVO.setType(FULFILLMENT_TAG);
                tagInfoVOS.add(tagInfoVO);
            }
        }
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPickTagList())) {
            for (String pickTag : ocmsOrderItemVO.getPickTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(pickTag);
                tagInfoVO.setType(PICK_TAG);
                tagInfoVOS.add(tagInfoVO);

            }
        }
        //商品标签(需要排除履约和拣货标签)
        productVO.setTagInfos(Optional.ofNullable(ocmsOrderItemVO.getTagInfos())
                .orElse(Collections.emptyList()).stream().map(tag -> {
            TagInfoVO tagInfoVO = new TagInfoVO();
            tagInfoVO.setName(tag.getName());
            tagInfoVO.setType(tag.getType());
            return tagInfoVO;
        }).collect(Collectors.toList()));
        productVO.setTagInfoList(tagInfoVOS);
        productVO.setCurrentPrice(ocmsOrderItemVO.getCurrentPrice());
        return productVO;
    }

    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    private RefundApplyRecordDetailVO buildRefundApplyRecordDetailVO(OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO) {
        return RefundApplyRecordDetailVO.buildRefundApplyRecordDetailVO(ocmsAfterSaleApplyDetailVO);
    }


    private OrderListResponse setCouldOperateItems(OrderListResponse orderListResponse, OrderCouldOperateItem defaultOperateItem,Long currentStoreId) {
        if (CollectionUtils.isNotEmpty(orderListResponse.getOrderList())) {
            setCouldOperateItems(orderListResponse.getOrderList(), defaultOperateItem, TO_CHECK_ITEMS,currentStoreId);
        }
        return orderListResponse;
    }

    public void setCouldOperateItems(Long tenantId, List<OrderVO> orderVOS, OrderCouldOperateItem defaultOperateItem,
                                     List<OrderCouldOperateItem> extraCheckItems,Long currentStoreId) {
        List<Integer> checkItems = Lists.newArrayList(TO_CHECK_ITEMS);
        if (MccConfigUtil.getDHTenantIdList().contains(tenantId.toString())) {
            // 歪马租户的检查按钮与其他租户不同，去掉了拣货完成按钮
            checkItems.removeIf(item -> item == OrderCouldOperateItem.COMPLETE_PICK.getValue());
        }

        if (CollectionUtils.isNotEmpty(extraCheckItems)) {
            checkItems.addAll(extraCheckItems.stream().map(OrderCouldOperateItem::getValue).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(orderVOS)) {
            setCouldOperateItems(orderVOS, defaultOperateItem, checkItems,currentStoreId);
        }
    }

    protected void setCouldOperateItems(List<OrderVO> orderVOS, OrderCouldOperateItem defaultOperateItem, List<Integer> checkItems,Long currentStoreId) {
        if (CollectionUtils.isNotEmpty(orderVOS)) {
            Map<Integer,Boolean> accountAuthCodeMap = queryAccountCode();
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            long tenantId = identityInfo.getUser().getTenantId();
            Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderVOS)) {
                OCMSOperateCheckRequest checkRequest = new OCMSOperateCheckRequest();
                checkRequest.setTenantId(tenantId);
                checkRequest.setToCheckOperateItems(checkItems);
                checkRequest.setOrderList(orderVOS.stream().map(e -> OCMSOrderKey.builder()
                        .channelOrderId(e.getChannelOrderId())
                        .channelType(e.getChannelId())
                        .build()).collect(Collectors.toList()));
                OCMSOperateCheckResponse response = null;
                try {
                    response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(checkRequest);
                    log.info("查询订单可操作项，request:{}, response:{}", checkRequest, response);
                    if (response.getCouldOperateItems() != null && !response.getCouldOperateItems().isEmpty()) {
                        ocmsOrderKeyListMap.putAll(response.getCouldOperateItems());
                    }
                } catch (TException e) {
                    log.error("查询可操作列表失败,request:{}", checkRequest, e);
                }
            }
            //设置可操作项
            orderVOS.forEach(order -> {
//                if(Objects.equals(order.getChannelId(),ChannelType.QUAN_QIU_WA.getValue())){
//                    //全球蛙不显示按钮
//
//                    return;
//                }
                List<Integer> couldOperateItems = Lists.newArrayList();
                ocmsOrderKeyListMap.entrySet().stream()
                        .filter(entry -> StringUtils.equals(entry.getKey().getChannelOrderId(), order.getChannelOrderId())
                                && entry.getKey().getChannelType() == order.getChannelId())
                        .findFirst()
                        .ifPresent(entry -> {
                            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                                couldOperateItems.addAll(entry.getValue());
                            }
                        });
                //有赞渠道不设置默认操作
                if (defaultOperateItem != null && !couldOperateItems.contains(defaultOperateItem.getValue())
                        && !Integer.valueOf(ChannelTypeEnum.YOU_ZAN.getChannelId()).equals(order.getChannelId())) {
                    couldOperateItems.add(defaultOperateItem.getValue());
                }
                //权限code与订单可操作项diff
                List<Integer> finalCouldOperateItems = couldOperateItems.stream().filter(operateItem->{
                    return Objects.isNull(accountAuthCodeMap.get(operateItem)) || BooleanUtils.isTrue(accountAuthCodeMap.get(operateItem));//没有配置，或者权限没有返回都当做有权限
                }).collect(Collectors.toList());
                log.info("权限过滤，order:{}, account:{}, couldOperateItems:{}, finalCouldOperateItems:{}", order.getChannelOrderId(), identityInfo.getUser().getAccountId(), couldOperateItems, finalCouldOperateItems);
                recordOrderOperatorMetrics(finalCouldOperateItems);
                if(MccConfigUtil.isDrunkHorseTenant(order.getTenantId())) {
                    List<Integer> deliveryChannelButtons = dhThirdDeliveryService.getDeliveryChannelButtons(order.getTenantId(), order.getStoreId(), order);
                    finalCouldOperateItems.addAll(deliveryChannelButtons);
                }
                if( order.getDispatchShopId()!=null){

                }
                if(order.isPlatformDelivery()){
                    finalCouldOperateItems = finalCouldOperateItems.stream().filter(e->e!=OrderCanOperateItem.DISPATCH_ORDER.getValue()).collect(Collectors.toList());
                }
                if(currentStoreId !=null && order.getDispatchShopId() != null && !Objects.equals(currentStoreId,order.getDispatchShopId())){
                    finalCouldOperateItems = finalCouldOperateItems.stream().filter(e->Objects.equals(e,OrderCanOperateItem.PRINT_RECEIPT.getValue())).collect(Collectors.toList());
                }
                order.setOrderCouldOperateItems(finalCouldOperateItems);
            });
        }
    }

    private void recordOrderOperatorMetrics(List<Integer> finalCouldOperateItems) {
        MetricHelper.build().name("order.operateItem.show").tag("type", "apiCnt").count();
        if (CollectionUtils.isNotEmpty(finalCouldOperateItems)){
            finalCouldOperateItems.stream().forEach(operateItem->{
                MetricHelper.build().name("order.operateItem.show").tag("type", String.valueOf(operateItem)).count();
            });
        }
    }

    private Map<Integer, Boolean> queryAccountCode() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        try {
            Map<String, Integer> permissionCodeMap = fetchPermissionFromConfig();
            if (identityInfo != null
                    && identityInfo.getUser() != null
                    && permissionCodeMap != null
                    && permissionCodeMap.size() > 0
                    && identityInfo.getStoreIdList().size() == 1){
                User user = identityInfo.getUser();
                long storeId = identityInfo.getStoreId();
                long accountId = user.getAccountId();
                Map<String, Boolean> authMap = authThriftWrapper.authPermissionAndDataAuth(accountId, storeId, Lists.newArrayList(permissionCodeMap.keySet()));
                log.info("请求订单元素权限：{}， result:{}", accountId, authMap);
                if(MapUtils.isEmpty(authMap)){
                    return Maps.newHashMap();
                }
                return authMap.entrySet().stream().collect(Collectors.toMap(entry->permissionCodeMap.get(entry.getKey()), entry->entry.getValue(), (f,s)->s));
            }
        }catch (Exception e){
            log.error("查询权限元素权限失败", e);
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }

    private Map<String, Integer> fetchPermissionFromConfig() {
        String permissionCodes = ConfigUtilAdapter.getString("order.operator.auth.codes");
        if (StringUtils.isNotBlank(permissionCodes)){
            /**
             * 解析  ACCEPT_ORDER,1;REFUND,2;.....
             * ***/
            List<String> permissionMap = Splitter.on(";").splitToList(permissionCodes);
            return permissionMap.stream().map(e-> Splitter.on(",").splitToList(e)).filter(e->e != null && e.size() >= 2).collect(Collectors.toMap(e->e.get(0), e-> NumberUtils.toInt(e.get(1)), (f, s)->s));
        }
        return Maps.newHashMap();
    }



    private RefundApplyListResponse setCouldOperateItems(RefundApplyListResponse refundApplyListResponse) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (CollectionUtils.isNotEmpty(refundApplyListResponse.getRefundApplyRecordVOList()) && identityInfo.getUser() != null) {
            long tenantId = identityInfo.getUser().getTenantId();
            Map<Integer,Boolean> accountAuthCodeMap = queryAccountCode();
            Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(refundApplyListResponse.getRefundApplyRecordVOList())) {
                OCMSOperateCheckRequest checkRequest = new OCMSOperateCheckRequest();
                checkRequest.setTenantId(tenantId);
                checkRequest.setToCheckOperateItems(TO_CHECK_ITEMS);
                checkRequest.setOrderList(refundApplyListResponse.getRefundApplyRecordVOList().stream().map(e -> OCMSOrderKey.builder()
                        .channelOrderId(e.getOrderVO().getChannelOrderId())
                        .channelType(e.getOrderVO().getChannelId())
                        .build()).collect(Collectors.toList()));
                try {
                    OCMSOperateCheckResponse response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(checkRequest);
                    log.info("查询退款单可操作项，request:{}, response:{}", checkRequest, response);
                    if (response.getCouldOperateItems() != null && !response.getCouldOperateItems().isEmpty()) {
                        ocmsOrderKeyListMap.putAll(response.getCouldOperateItems());
                    }
                } catch (TException e) {
                    log.error("查询可操作列表失败,request:{}", checkRequest, e);
                }
            }
            //设置可操作项
            refundApplyListResponse.getRefundApplyRecordVOList().forEach(refundApplyRecordVO -> {
                List<Integer> couldOperateItems = Lists.newArrayList();
                ocmsOrderKeyListMap.entrySet().stream()
                        .filter(entry -> StringUtils.equals(entry.getKey().getChannelOrderId(), refundApplyRecordVO.getOrderVO().getChannelOrderId())
                                && entry.getKey().getChannelType() == refundApplyRecordVO.getOrderVO().getChannelId())
                        .findFirst()
                        .ifPresent(entry -> {
                            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                                couldOperateItems.addAll(entry.getValue());
                            }
                        });
                //权限code与订单可操作项diff
                List<Integer> finalCouldOperateItems = couldOperateItems.stream().filter(operateItem->{
                    return Objects.isNull(accountAuthCodeMap.get(operateItem)) || BooleanUtils.isTrue(accountAuthCodeMap.get(operateItem));//没有配置，或者权限没有返回都当做有权限
                }).collect(Collectors.toList());
                log.info("权限过滤，afterSaleId:{}, account:{}, couldOperateItems:{}, finalCouldOperateItems:{}", refundApplyRecordVO.getAfterSaleId(), identityInfo.getUser().getAccountId(), couldOperateItems, finalCouldOperateItems);
                recordOrderOperatorMetrics(finalCouldOperateItems);
                refundApplyRecordVO.setOrderCouldOperateItems(finalCouldOperateItems);
            });
        }
        return refundApplyListResponse;
    }


    /**
     * 插入退差价到退款日志流
     *
     * @param ocmsOrderVO
     * @return
     */
    private List<RefundLog> addWeightRefundLog(OCMSOrderVO ocmsOrderVO) {
        List<OCMSAfterSaleApplyVO> weightRefundAfterSaleApplyList = ocmsOrderVO.getAfterSaleApplyVOList().stream().filter(afs -> Objects.nonNull(afs.getAfsPattern())&&afs.getAfsPattern()== AfterSalePatternEnum.WEIGHT.getValue() && AfterSaleApplyStatusEnum.AUDITED.getValue().equals(afs.getStatus())).collect(Collectors.toList());
        List<RefundLog> refundLogs = Lists.newArrayList();
        for (OCMSAfterSaleApplyVO applyVO : weightRefundAfterSaleApplyList) {
            OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.enumOf(applyVO.getApplyUserType());
            RefundLog refundLog = new RefundLog();
            refundLog.setOperator("");
            refundLog.setOptTime(applyVO.getCreateTime());
            refundLog.setOptContent("商家按重量退差价");
            refundLog.setAuditType(OcmsRefundAuditType.WeightRefund.getCode());
            refundLog.setOperatorType(operatorTypeEnum.getValue());
            refundLog.setRefundAmount(applyVO.getRefundAmt());
            refundLog.setOptDesc(applyVO.getApplyReason());
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOS = Lists.newArrayList();
            for (OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO : applyVO.getOcmsAfterSaleApplyDetailVOList()) {
                RefundApplyRecordDetailVO recordDetailVO = RefundApplyRecordDetailVO.buildRefundApplyRecordDetailVO(ocmsAfterSaleApplyDetailVO);
                //如果是克重退款展示渠道退款价格，如果是部分退款老逻辑展示线下价格
                recordDetailVO.setRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                recordDetailVO.setTotalRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                refundApplyRecordDetailVOS.add(recordDetailVO);
            }
            refundLog.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOS);
            refundLog.setRefundPicList(applyVO.getRefundPicList());
            refundLogs.add(refundLog);
        }


        return refundLogs;
    }


    private List<OrderRevenueDetailResponse> getOrderListRevenueDetail4TenantAndViewIds(Long tenantId,
            List<OCMSOrderVO> orderVOS) {

        if(CollectionUtils.isEmpty(orderVOS)){
            return Collections.emptyList();
        }

        MerchantOrderRevenueDetailRequest request = new MerchantOrderRevenueDetailRequest();
        request.setTenantId(tenantId);
        List<ViewIdCondition> conditions = orderVOS.stream().map(order -> ViewIdCondition.builder()
                .orderBizType(order.getOrderBizType())
                .viewOrderId(order.getViewOrderId()).build())
                .collect(Collectors.toList());
        request.setViewIdConditionList(conditions);

        try {
            MerchantOrderListRevenueDetailResponse revenueDetailResponse = orderListRevenueDetail(request).getData();
            return Optional.ofNullable(revenueDetailResponse)
                    .map(MerchantOrderListRevenueDetailResponse::getOrderListRevenueDetailResponse)
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            // 查询营收数据异常、返回空、前端不显示订单零售金额相关数据
            return Collections.emptyList();
        }
    }

    private Map<String, OrderProfitView> getOrderProfitMap(Long tenantId, List<OCMSOrderVO> orderVOS) {
        try {
            // 获取租户业务模式，只处理便利店租户
            List<OCMSOrderVO> orders = needOrderProfit(tenantId, orderVOS);
            if (CollectionUtils.isEmpty(orders)) {
                return Collections.emptyMap();
            }

            Map<String, OrderProfitView> profitMap = saasCrmDataWrapper.queryNetProfit(orders);
            log.info("查询订单毛利 orders:{},profitMap:{}", Fun.map(orders, OCMSOrderVO::getViewOrderId), profitMap);
            return profitMap;
        }
        catch (Exception e) {
            log.warn("获取门店预计毛利失败 tenantId:{},orderVOS:{}", tenantId, Fun.map(orderVOS, OCMSOrderVO::getViewOrderId));
            return Collections.emptyMap();
        }
    }

    private List<OCMSOrderVO> needOrderProfit(Long tenantId, List<OCMSOrderVO> orderVOS) {
        String channels = Lion.getConfigRepository().get(MccKeyEnum.SHOW_ORDER_NET_PROFIT_CHANNEL.key,
                String.valueOf(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue()));
        if (StringUtils.isBlank(channels)) {
            return Collections.emptyList();
        }
        List<String> channelList = Arrays.asList(channels.trim().split(","));
        List<OCMSOrderVO> orders = Fun.filter(orderVOS, orderVO -> channelList.contains(String.valueOf(orderVO.getOrderBizType())));
        if (CollectionUtils.isNotEmpty(orders)) {
            TenantBusinessModeEnum tenantBizMode = tenantWrapper.getTenantBizModeV2(tenantId);
            log.info("tenantId:{}; tenantBizMode: {}", tenantId, tenantBizMode);
            if (TenantBusinessModeEnum.CONVENIENCE_STORE.equals(tenantBizMode) || TenantBusinessModeEnum.MTSG_FLAGSHIP_STORE.equals(tenantBizMode)) {
                return orders;
            }
        }
        return Collections.emptyList();
    }

    private void setRefundPriceDisplayType4WaitAuditOrderList(List<OCMSWaitAuditOrderVO> waitAuditOrderVOS, int type) {
        waitAuditOrderVOS.stream()
                .filter(Objects::nonNull)
                .map(auditOrder -> {
                    List<OCMSAfterSaleApplyVO> afterSaleList = new LinkedList<>();
                    if(auditOrder.getOcmsAfterSaleApplyVO() != null){
                        afterSaleList.add(auditOrder.getOcmsAfterSaleApplyVO());
                    }
                    if(CollectionUtils.isNotEmpty(auditOrder.getAfterSaleApplyVOList())){
                        afterSaleList.addAll(auditOrder.getAfterSaleApplyVOList());
                    }
                    return afterSaleList;
                })
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(OCMSAfterSaleApplyVO::getOcmsAfterSaleApplyDetailVOList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(detailVO -> detailVO.setPriceDisplayType(type));
    }


    /**
     * 判断是否支持拨打骑手隐私号（目前仅平台配送支持查询骑手隐私号）
     *
     * @param isSelfDelivery 是否为商家自配送 1:是  0:否
     * @return 是否支持拨打骑手隐私号
     */
    private static boolean judgeSupportDeliveryUserPrivacyPhone(Integer isSelfDelivery) {
        if (Objects.equals(isSelfDelivery, IS_SELF_DELIVERY_NO)) {
            // 平台配送，支持查询骑手隐私号
            return true;
        }
        return false;
    }

    private boolean isDrunkHorseTenant(Long tenantId) {
        List<Long> drunkHorseTenants = Lion.getConfigRepository().getList("drunkhorse.tenantIds", Long.class, new ArrayList<>());
        if (tenantId == null || CollectionUtils.isEmpty(drunkHorseTenants)) {
            return false;
        }
        return drunkHorseTenants.contains(tenantId);
    }

    private void setOrderListResponseViewStatus(OrderListResponse response, OrderViewStatusEnum orderViewStatusEnum) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList()) || Objects.isNull(orderViewStatusEnum)) {
            return;
        }
        for (OrderVO each : response.getOrderList()) {
            each.setViewStatus(orderViewStatusEnum.getCode());
        }
    }

    private void setRefundApplyListResponseViewStatus(RefundApplyListResponse response) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getRefundApplyRecordVOList())) {
            return;
        }
        for (RefundApplyRecordVO each : response.getRefundApplyRecordVOList()) {
            if (Objects.isNull(each.getAfsPattern()) || Objects.isNull(each.getOrderVO())) {
                continue;
            }
            if (Objects.equals(RefundTypeEnum.ALL.getValue(), each.getAfsPattern())) {
                each.getOrderVO().setViewStatus(OrderViewStatusEnum.ALL_REFUND.getCode());
            } else if (Objects.equals(RefundTypeEnum.PART.getValue(), each.getAfsPattern())) {
                each.getOrderVO().setViewStatus(OrderViewStatusEnum.PART_REFUND.getCode());
            } else {
                log.warn("退款审核订单设置展示状态时退款类型不属于整单退款或部分退款, 渠道id:{}, 渠道订单号:{}, 退款类型:{}", each.getOrderVO().getChannelId(), each.getOrderVO().getChannelOrderId(), each.getAfsPattern());
            }
        }
    }

    private void setOrderDeliveryStatusChangeTimeWithPayTime(OrderListResponse response) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList())) {
            return;
        }
        for (OrderVO each : response.getOrderList()) {
            each.setDeliveryStatusChangeTime(each.getPayTime());
        }
    }

    private void setErpCode(OrderListResponse response) {
        if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getOrderList())) {

            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            List<Long> storeIdList = identityInfo.getStoreIdList();
            Long tenantId = identityInfo.getUser().getTenantId();

            Map<Long, String> shopId2ErpShopCode = poiServiceFacade.queryShopId2ErpShopCode(tenantId, storeIdList);

            log.info("search erp code tenantId:{}, storeIds :{} ,resp:{} ",tenantId,storeIdList,shopId2ErpShopCode);

            response.getOrderList().forEach(orderVO -> orderVO.setErpShopCode(StringUtils.isBlank(shopId2ErpShopCode.get(orderVO.getStoreId())) ?
                                                                                      Strings.EMPTY :
                                                                                      shopId2ErpShopCode.get(orderVO.getStoreId())));
        }
    }

}