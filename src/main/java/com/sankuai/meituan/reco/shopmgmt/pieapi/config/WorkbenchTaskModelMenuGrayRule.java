package com.sankuai.meituan.reco.shopmgmt.pieapi.config;

import com.beust.jcommander.internal.Lists;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.commons.utils.json.JsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class WorkbenchTaskModelMenuGrayRule {

    public static final String CONFIG_KEY = "workbench.task.model.menu.gray.rule";

    private List<String> newMenuCodeList;
    private List<String> oldMenuCodeList;
    private List<Long> grayTenantIdList;
    private boolean released;

    public static List<WorkbenchTaskModelMenuGrayRule> getRuleList() {
        String json = Lion.getConfigRepository().get(CONFIG_KEY);
        if (StringUtils.isBlank(json)) {
            return Lists.newArrayList();
        }
        return JsonUtils.toObject(json, new TypeReference<List<WorkbenchTaskModelMenuGrayRule>>() {
        });
    }
}
