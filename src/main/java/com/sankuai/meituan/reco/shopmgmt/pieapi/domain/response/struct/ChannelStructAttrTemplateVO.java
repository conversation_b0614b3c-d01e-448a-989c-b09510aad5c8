package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.structattr.ChannelStructAttrTemplateDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.StructAttrTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/7/15 14:19
 */
@TypeDoc(
        name = "结构化属性值模板-组合规则",
        description = "结构化属性值模板-组合规则"
)
@Data
@ApiModel("结构化属性值模板-组合规则")
public class ChannelStructAttrTemplateVO {
    @FieldDoc(
            description = "值组合格式"
    )
    @ApiModelProperty(name = "值组合格式")
    private List<ChannelStructAttrFormatTypeVO> valueFormatTypeList;

    @FieldDoc(
            description = "值格式"
    )
    @ApiModelProperty(name = "值格式")
    private List<ChannelStructAttrFormatVO> valueFormatList;

    @FieldDoc(
            description = "展示规则"
    )
    @ApiModelProperty(name = "展示规则")
    private List<ChannelStructAttrShowFormatVO> showFormatList;

    public static List<ChannelStructAttrTemplateDTO> toBizDTOList(List<ChannelStructAttrTemplateVO> attrTemplateVOList) {
        return Fun.map(attrTemplateVOList, ChannelStructAttrTemplateVO::toBizDTO);
    }

    private static ChannelStructAttrTemplateDTO toBizDTO(ChannelStructAttrTemplateVO attrTemplateVO) {
        if (attrTemplateVO == null) {
            return null;
        }
        ChannelStructAttrTemplateDTO templateDTO = new ChannelStructAttrTemplateDTO();
        templateDTO.setValueFormatTypeList(ChannelStructAttrFormatTypeVO.toBizDTOList(attrTemplateVO.getValueFormatTypeList()));
        templateDTO.setValueFormatList(ChannelStructAttrFormatVO.toBizDTOList(attrTemplateVO.getValueFormatList()));
        templateDTO.setShowFormatList(ChannelStructAttrShowFormatVO.toBizDTOList(attrTemplateVO.getShowFormatList()));
        return templateDTO;
    }

    public static List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrTemplateDTO> toOcmsBizDTOList(List<ChannelStructAttrTemplateVO> attrTemplateVOList) {
        return Fun.map(attrTemplateVOList, ChannelStructAttrTemplateVO::toOcmsBizDTO);
    }

    private static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrTemplateDTO toOcmsBizDTO(ChannelStructAttrTemplateVO attrTemplateVO) {
        if (attrTemplateVO == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrTemplateDTO templateDTO = new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrTemplateDTO();
        templateDTO.setValueFormatTypeList(ChannelStructAttrFormatTypeVO.toOcmsBizDTOList(attrTemplateVO.getValueFormatTypeList()));
        templateDTO.setValueFormatList(ChannelStructAttrFormatVO.toOcmsBizDTOList(attrTemplateVO.getValueFormatList()));
        templateDTO.setShowFormatList(ChannelStructAttrShowFormatVO.toOcmsBizDTOList(attrTemplateVO.getShowFormatList()));
        return templateDTO;
    }

    public static List<ChannelStructAttrTemplateVO> ofPlatformDTOList(List<StructAttrTemplateDTO> attrTemplateDTOList) {
        return Fun.map(attrTemplateDTOList, ChannelStructAttrTemplateVO::ofPlatformDTO);
    }

    private static ChannelStructAttrTemplateVO ofPlatformDTO(StructAttrTemplateDTO attrTemplateDTO) {
        if (attrTemplateDTO == null) {
            return null;
        }
        ChannelStructAttrTemplateVO templateVO = new ChannelStructAttrTemplateVO();
        templateVO.setValueFormatTypeList(ChannelStructAttrFormatTypeVO.ofPlatformDTOList(attrTemplateDTO.getValueFormatTypeList()));
        templateVO.setValueFormatList(ChannelStructAttrFormatVO.ofPlatformDTOList(attrTemplateDTO.getValueFormatList()));
        templateVO.setShowFormatList(ChannelStructAttrShowFormatVO.ofPlatformDTOList(attrTemplateDTO.getShowFormatList()));
        return templateVO;
    }

    public static List<ChannelStructAttrTemplateVO> ofBizDTOList(List<ChannelStructAttrTemplateDTO> attrTemplateDTOList) {
        return Fun.map(attrTemplateDTOList, ChannelStructAttrTemplateVO::ofBizDTO);
    }

    private static ChannelStructAttrTemplateVO ofBizDTO(ChannelStructAttrTemplateDTO attrTemplateDTO) {
        if (attrTemplateDTO == null) {
            return null;
        }
        ChannelStructAttrTemplateVO templateVO = new ChannelStructAttrTemplateVO();
        templateVO.setValueFormatTypeList(ChannelStructAttrFormatTypeVO.ofBizDTOList(attrTemplateDTO.getValueFormatTypeList()));
        templateVO.setValueFormatList(ChannelStructAttrFormatVO.ofBizDTOList(attrTemplateDTO.getValueFormatList()));
        templateVO.setShowFormatList(ChannelStructAttrShowFormatVO.ofBizDTOList(attrTemplateDTO.getShowFormatList()));
        return templateVO;
    }

    public static ChannelStructAttrTemplateVO ofOcmsDTO(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrTemplateDTO dto) {
        if (dto == null) {
            return null;
        }
        ChannelStructAttrTemplateVO vo = new ChannelStructAttrTemplateVO();
        vo.setShowFormatList(Fun.map(dto.getShowFormatList(), ChannelStructAttrShowFormatVO::ofOcmsDTO));
        vo.setValueFormatList(Fun.map(dto.getValueFormatList(), ChannelStructAttrFormatVO::ofOcmsDTO));
        vo.setValueFormatTypeList(Fun.map(dto.getValueFormatTypeList(), ChannelStructAttrFormatTypeVO::ofOcmsDTO));
        return vo;
    }
}
