package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.structattr.ChannelStructAttrValueDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.SpuStructPropertyValueDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/7/15 14:31
 */
@TypeDoc(
        name = "结构化属性值",
        description = "结构化属性值"
)
@Data
@ApiModel("结构化属性值")
public class ChannelStructAttrValueVO {
    @FieldDoc(
            description = "录入值"
    )
    @ApiModelProperty("录入值")
    private String value;

    @FieldDoc(
            description = "用户选择的选项id,inputType=5使用"
    )
    @ApiModelProperty("用户选择的选项id,inputType=5使用")
    private Long optionId;

    @FieldDoc(
            description = "顺序"
    )
    @ApiModelProperty("顺序")
    private Integer sequence;

    @FieldDoc(
            description = "用户选择的选项值,inputType=5使用"
    )
    @ApiModelProperty("用户选择的选项值,inputType=5使用")
    private String optionValue;

    @FieldDoc(
            description = "这个值用于质量分判断时才需要传入 1-文本，3-固定文本（商家无需修改，直接传），4-数字，5-下拉",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty("这个值用于质量分判断时才需要传入 1-文本，3-固定文本（商家无需修改，直接传），4-数字，5-下拉")
    private Integer inputType;

    public static List<ChannelStructAttrValueDTO> toBizDTOList(List<ChannelStructAttrValueVO> valueVOList) {
        return Fun.map(valueVOList, ChannelStructAttrValueVO::toBizDTO);
    }

    private static ChannelStructAttrValueDTO toBizDTO(ChannelStructAttrValueVO attrValueVO) {
        if (attrValueVO == null) {
            return null;
        }
        ChannelStructAttrValueDTO dto = new ChannelStructAttrValueDTO();
        dto.setValue(attrValueVO.getValue());
        dto.setOptionId(attrValueVO.getOptionId());
        dto.setSequence(attrValueVO.getSequence());
        dto.setOptionValue(attrValueVO.getOptionValue());
        return dto;
    }

    public static List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrValueDTO> toOcmsBizDTOList(List<ChannelStructAttrValueVO> valueVOList) {
        return Fun.map(valueVOList, ChannelStructAttrValueVO::toOcmsBizDTO);
    }

    private static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrValueDTO toOcmsBizDTO(ChannelStructAttrValueVO attrValueVO) {
        if (attrValueVO == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrValueDTO dto =
                new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrValueDTO();
        dto.setValue(attrValueVO.getValue());
        dto.setOptionId(attrValueVO.getOptionId());
        dto.setSequence(attrValueVO.getSequence());
        dto.setOptionValue(attrValueVO.getOptionValue());
        return dto;
    }

    public static List<ChannelStructAttrValueVO> ofBizDTOList(List<ChannelStructAttrValueDTO> dtoList) {
        return Fun.map(dtoList, ChannelStructAttrValueVO::ofBizDTO);
    }

    private static ChannelStructAttrValueVO ofBizDTO(ChannelStructAttrValueDTO dto) {
        if (dto == null) {
            return null;
        }
        ChannelStructAttrValueVO vo = new ChannelStructAttrValueVO();
        vo.setValue(dto.getValue());
        vo.setOptionId(dto.getOptionId());
        vo.setSequence(dto.getSequence());
        vo.setOptionValue(dto.getOptionValue());
        return vo;
    }

    public SpuStructPropertyValueDto toQualityPlatFormDto() {
        SpuStructPropertyValueDto to = new SpuStructPropertyValueDto();
        to.setInputType(this.inputType);
        to.setOptionId(this.optionId);
        // 质量分接口需要将单位名合并到value里
        to.setValue(StringUtils.isNotEmpty(this.value) ? this.value : this.optionValue);
        return to;
    }

    public static ChannelStructAttrValueVO ofDto(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrValueDTO from) {
        if (from == null){
            return null;
        }
        ChannelStructAttrValueVO to = new ChannelStructAttrValueVO();
        to.setValue(from.getValue());
        to.setOptionId(from.getOptionId());
        to.setSequence(from.getSequence());
        to.setOptionValue(from.getOptionValue());
        return to;
    }
}
