package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.PageResultV2;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.PriceConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.SupplierGoodsConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SimpleUser;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.SupplyRelationAndPurchaseSkuReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.SupplierGoodsVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.SupplierVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.SupplyRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.PoiServiceFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.PoiService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.bo.PoiInfoBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.supplychain.purchase.client.common.RequestSourceEnum;
import com.sankuai.meituan.reco.supplychain.purchase.client.common.SupplyRelationTypeEnum;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.PageInfoReq;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.PageInfoResp;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.Status;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.TOperator;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.ThriftResult;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.UserParam;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.dto.goods.SupplierDTO;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.SupplyRelationOperateThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.SupplyRelationThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.request.RelationBatchInsertReq;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.request.SkuRelationInsertReq;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.request.SpuRelationInsertReq;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.request.SupplyRelationListByUnitIdRequest;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.response.RelationBatchInsertResp;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.response.SupplyRelationListByUnitIdResponse;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.response.dto.SupplyRelationByUnitIdPageDTO;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.response.dto.SupplyRelationDTO;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.distribution.distributionprice.franchisor.franchisee.FranchisorFranchiseeDistributionPriceThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.distribution.distributionprice.franchisor.franchisee.request.FranchisorFranchiseeDistributionPriceQueryBySkuUnitRequest;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.distribution.distributionprice.franchisor.franchisee.response.FranchisorFranchiseeDistributionPriceQueryBySkuUnitResponse;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.PurchaseSkuOperateThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.dto.PurchaseSetSkuPropertyDto;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.request.PurchaseSkuBatchCreateReq;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.response.PurchaseSkuBatchCreateResp;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.exception.ParamInvalidException;
import com.sankuai.meituan.shangou.xsupply.scm.supplynetwork.client.common.BaseResponse;
import com.sankuai.meituan.shangou.xsupply.scm.supplynetwork.client.thrift.common.UserParamDTO;
import com.sankuai.meituan.shangou.xsupply.scm.supplynetwork.client.thrift.supplyrelation.dto.SupplyRelationInsertDTO;
import com.sankuai.meituan.shangou.xsupply.scm.supplynetwork.client.thrift.supplyrelation.request.RelationBatchInsertRequest;
import com.sankuai.meituan.shangou.xsupply.scm.supplynetwork.client.thrift.supplyrelation.response.RelationBatchInsertBySpuResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.collections4.ListUtils.emptyIfNull;

/**
 * <AUTHOR>
 * @date 2022/7/8 11:35 AM
 */
@Service
@Slf4j
public class PurchaseBizServiceWrapper {

    private static final BigDecimal HUNDRED = new BigDecimal(100);
    private final static Integer DEFAULT_AUTH_TYPE = 2;
    // B端商家的appId
    public static final Integer SAAS_B_APP_ID = 3;

    @Resource
    private SupplyRelationOperateThriftService supplyRelationOperateThriftService;
    @Resource
    private PurchaseSkuOperateThriftService purchaseSkuOperateThriftService;
    @Autowired
    private SupplyRelationThriftService supplyRelationThriftService;

    @Resource
    private com.sankuai.meituan.shangou.xsupply.scm.supplynetwork.client.thrift.supplyrelation.SupplyRelationOperateThriftService supplyRelationOperateThriftServiceV2;

    @Autowired
    private PoiService poiService;
    @Autowired
    private AuthThriftWrapper authThriftWrapper;
    @Autowired
    private FranchisorFranchiseeDistributionPriceThriftService franchisorFranchiseeDistributionPriceThriftService;

    public CommonResponse<Void> batchCreateSupplyRelation(SupplyRelationAndPurchaseSkuReq request, SimpleUser user, boolean fromReview) {
        if (request == null) {
            return CommonResponse.fail(ResultCode.PARAM_ERR);
        }
        try {
            request.selfCheck();
        } catch (ParamInvalidException e) {
            log.error("batchCreateSupplyRelation param exception:", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR, e.getMessage());
        }

        CommonResponse<Void> response = null;
        // 供货关系新增
        if (CollectionUtils.isNotEmpty(request.getSupplyRelationSetVoList())) {
            try {
                if (!MccConfigUtil.getMergeRelationWriteSwitch(user.getTenantId()) || fromReview) {
                    createSupplyRelation(request, user);
                }
                else {
                    createSupplyRelationGrey(request, user);
                }
            } catch (Exception e) {
                log.error("batchCreateSupplyRelation operate exception:{}", e.getMessage(), e);
                response = CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
            }
        }

        //采购属性新增
        if (CollectionUtils.isNotEmpty(request.getPurchaseSkuSetPropertyVos())) {
            PurchaseSkuBatchCreateReq req = convertToBatchCreatePurchaseSkuProperty(request.getPoiId(),
                    request.getPurchaseSkuSetPropertyVos(), user);
            PurchaseSkuBatchCreateResp resp;
            try {
                resp = purchaseSkuOperateThriftService.batchCreate(req);
                ResponseHandler.checkResponseAndStatus(resp, rr -> rr.getStatus().getCode(),
                        rr -> rr.getStatus().getMsg(), ResultCode.FAIL);
            } catch (Exception e) {
                log.error("batchCreatePurchaseSkuProperty operate exception:{}", e.getMessage(), e);
                response =  CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
            }
        }

        return response == null ? CommonResponse.success(null) : response;
    }

    private void createSupplyRelation(SupplyRelationAndPurchaseSkuReq request, SimpleUser user) {
        RelationBatchInsertResp resp;
        RelationBatchInsertReq req = convertBatchInsertRequest2BatchCreate(request.getPoiId(), request.getSpuId(),
                request.getSupplyRelationSetVoList(), user);
        resp = supplyRelationOperateThriftService.batchInsert(req);
        ResponseHandler.checkResponseAndStatus(resp, rr -> rr.getStatus().getCode(),
                rr -> rr.getStatus().getMsg(), ResultCode.FAIL);
    }

    private void createSupplyRelationGrey(SupplyRelationAndPurchaseSkuReq request, SimpleUser user) {
        RelationBatchInsertRequest req = convertBatchInsertRequest2BatchCreateGrey(request.getPoiId(),
                request.getSupplyRelationSetVoList(), user);
        BaseResponse<RelationBatchInsertBySpuResponse> resp = supplyRelationOperateThriftServiceV2.batchInsertBySpu(req);
        ResponseHandler.checkResponseAndStatus(resp, rr -> rr.getStatus().getCode(),
                rr -> rr.getStatus().getMsg(), ResultCode.FAIL);
    }



    private PurchaseSkuBatchCreateReq convertToBatchCreatePurchaseSkuProperty(Long poiId,
                                                                              List<SupplyRelationAndPurchaseSkuReq.PurchaseSkuSetPropertyVo> purchaseSkuSetPropertyVos, SimpleUser user) {

        return PurchaseSkuBatchCreateReq.builder()
                .tenantId(user.getTenantId())
                .purchaseSetSkuPropertyDtos(emptyIfNull(purchaseSkuSetPropertyVos).stream()
                        .map(item -> PurchaseSetSkuPropertyDto.builder()
                                .poiId(poiId)
                                .skuId(item.getSkuId())
                                .purchaseStatus(item.getPurchaseStatus())
                                .purchaseType(item.getPurchaseType())
                                .build())
                        .collect(Collectors.toList()))

                .userParam(getUserParam(user))
                .build();
    }

    private RelationBatchInsertReq convertBatchInsertRequest2BatchCreate(Long poiId, String spuId,
                                                                         List<SupplyRelationAndPurchaseSkuReq.SupplyRelationSetVo> supplyRelationSetVos, SimpleUser user) {

        List<SkuRelationInsertReq> skuRelationInsertReqList = supplyRelationSetVos.stream()
                .map(supplyRelationSetVo -> new SkuRelationInsertReq(
                        supplyRelationSetVo.getSkuId(),
                        supplyRelationSetVo.getSupplyUnit(),
                        translatePrice(supplyRelationSetVo.getSupplyUnitPrice()),
                        supplyRelationSetVo.getMinOrderQuantity(),
                        supplyRelationSetVo.getSupplyUnitRatio(),
                        supplyRelationSetVo.getMasterFlag(),
                        supplyRelationSetVo.getSupplierId(),
                        null,
                        null,
                        null,
                        null,
                        supplyRelationSetVo.getSupplyUnitCode(),
                        supplyRelationSetVo.getOrderMultiple(),
                        // 支持单位元，6位小数
                        String.valueOf(supplyRelationSetVo.getSupplyUnitPrice())

                )).collect(Collectors.toList());

        SpuRelationInsertReq spuRelationInsertReq = new SpuRelationInsertReq(spuId, skuRelationInsertReqList);
        long tenantId = user.getTenantId();
        return new RelationBatchInsertReq(
                SupplyRelationTypeEnum.VENDOR_2_STORE.getCode(),
                tenantId,
                poiId,
                Arrays.asList(spuRelationInsertReq),
                getCurrentOperator(user),
                RequestSourceEnum.APP.getCode()
        );
    }

    private RelationBatchInsertRequest convertBatchInsertRequest2BatchCreateGrey(Long poiId,
                                                                         List<SupplyRelationAndPurchaseSkuReq.SupplyRelationSetVo> supplyRelationSetVos, SimpleUser user) {

        List<SupplyRelationInsertDTO> skuRelationInsertReqList = supplyRelationSetVos.stream()
                .map(supplyRelationSetVo -> SupplyRelationInsertDTO.builder()
                        .goodsId(supplyRelationSetVo.getSkuId())
                        .supplierId(supplyRelationSetVo.getSupplierId())
                        .supplyUnit(supplyRelationSetVo.getSupplyUnit())
                        .supplyUnitCode(supplyRelationSetVo.getSupplyUnitCode())
                        .supplyUnitRatio(Optional.ofNullable(supplyRelationSetVo.getSupplyUnitRatio()).map(Objects::toString).orElse(null))
                        .masterFlag(supplyRelationSetVo.getMasterFlag())
                        .minOrderQuantity(supplyRelationSetVo.getMinOrderQuantity())
                        .orderMultiple(supplyRelationSetVo.getOrderMultiple())
                        .baseSupplyUnitPrice(Optional.ofNullable(supplyRelationSetVo.getBaseSupplyUnitPrice()).map(Objects::toString).orElse(null))
                        .enableSupplierGoodsInfo(false)
                        .orderId(poiId)
                        .build()).collect(Collectors.toList());

        RelationBatchInsertRequest insertRequest = new RelationBatchInsertRequest();
        long tenantId = user.getTenantId();
        insertRequest.setPoiId(poiId);
        insertRequest.setTenantId(tenantId);
        insertRequest.setRelations(skuRelationInsertReqList);
        insertRequest.setRequestSource(RequestSourceEnum.APP.getCode());
        insertRequest.setUserParam(getUserParamDTO(user));

        return insertRequest;
    }


    /**
     * 金额转换：元 -> 分
     */
    private Integer translatePrice(Double price) {
        return new BigDecimal(String.valueOf(price)).multiply(HUNDRED).intValue();
    }

    private TOperator getCurrentOperator(SimpleUser user) {
        // User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        return new TOperator(user.getEmployeeId(), user.getAccountId(),
                String.valueOf(user.getTenantId()), DEFAULT_AUTH_TYPE);
    }

    private UserParam getUserParam(SimpleUser user) {
        UserParam userParam = new UserParam();
        userParam.setAccountId(user.getAccountId());
        userParam.setEmployeeId(user.getEmployeeId());
        userParam.setUserName(user.getAccountName());
        return userParam;
    }

    private com.sankuai.meituan.shangou.xsupply.scm.supplynetwork.client.thrift.common.UserParamDTO getUserParamDTO(SimpleUser user) {
        UserParamDTO userParam = new UserParamDTO();
        userParam.setAccountId(user.getAccountId());
        userParam.setAccountName(user.getAccountName());
        return userParam;
    }

    /**
     * 箱规的供货关系查询，由WEB端服务迁移而来 SupplyRelationService.queryRelationListByUnitId()
     * @return
     */
    public PageResultV2<SupplyRelationVO> queryRelationListByUnitId(String skuId, String supplyUnitCode, int pageNo, int pageSize, User user) {
        Map<Long, PoiInfoDto> poiInfoMap = queryId2PoiMap(user);

        SupplyRelationListByUnitIdRequest rpcReq = SupplyRelationListByUnitIdRequest.builder()
                .tenantId(user.getTenantId())
                .skuId(skuId)
                .supplyUnitCode(supplyUnitCode)
                .pageInfo(new PageInfoReq(pageNo, pageSize))
                .build();
        SupplyRelationListByUnitIdResponse result = supplyRelationThriftService.queryRelationListByUnitId(rpcReq);
        ResponseHandler.checkResponseAndStatus(result, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
        SupplyRelationByUnitIdPageDTO response = result.getData();
        List<SupplyRelationVO> voList = Optional.ofNullable(response)
                .map(SupplyRelationByUnitIdPageDTO::getRelationList)
                .map(list -> list.stream().map(d -> this.convert(d, poiInfoMap)).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
        FranchisorFranchiseeDistributionPriceQueryBySkuUnitRequest query = FranchisorFranchiseeDistributionPriceQueryBySkuUnitRequest
                .builder()
                .skuId(skuId)
                .tenantId(user.getTenantId())
                .unitCode(supplyUnitCode)
                .build();
        ThriftResult<FranchisorFranchiseeDistributionPriceQueryBySkuUnitResponse> franchiseedistributePriceResult =
                franchisorFranchiseeDistributionPriceThriftService.queryFranchiseeDistributionPriceBySkuUnit(query);
        ResponseHandler.checkResponseAndStatus(franchiseedistributePriceResult, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
        FranchisorFranchiseeDistributionPriceQueryBySkuUnitResponse distributePriceResp = franchiseedistributePriceResult.getData();

        PageInfoResp pageInfo = Optional.ofNullable(response)
                .map(SupplyRelationByUnitIdPageDTO::getPageInfo)
                .orElse(new PageInfoResp());
        //这个字段不准确，当加盟商配销价单位和店仓配销价单位相同时，会多算出一条
        Long totalCount = Optional.ofNullable(pageInfo.getTotalCount()).orElse(0L) + Optional.ofNullable(distributePriceResp.getTotalCount()).orElse(0L);
        pageInfo.setTotalCount(totalCount);
        pageInfo.setTotalPage((totalCount + pageSize - 1) / pageSize);

        return PageResultV2.success(voList, pageInfo.getPageNo(), pageInfo.getPageSize(),
                pageInfo.getTotalCount(), pageInfo.getTotalPage());
    }

    private Map<Long/*storeId*/, PoiInfoDto> queryId2PoiMap(User user) {
        // 增补共享仓
        List<Integer> entityTypes = Arrays.asList(PoiEntityTypeEnum.STORE.code(),
                PoiEntityTypeEnum.REGIONAL_WAREHOUSE.code(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code());

        List<PoiInfoBO> poiInfoBoList = poiService.queryPois(user.getTenantId(), null, null, entityTypes, true);

        if (CollectionUtils.isNotEmpty(poiInfoBoList)) {
            // 这里租户会返回共享仓的门店，故结果含：共享仓门店、共享仓、中心仓、非共享仓门店
            List<Long> poiIds = authThriftWrapper.queryAllPoiPermissionOfCurrentUser(user, SAAS_B_APP_ID);

            poiInfoBoList = poiInfoBoList.stream().filter(poi -> poiIds.contains(poi.getPoiId())).collect(Collectors.toList());
        }

        return poiInfoBoList.stream()
                .map(PoiInfoBO::toPoiInfoDto)
                .collect(Collectors.toMap(PoiInfoDto::getPoiId, Function.identity()));
    }

    private SupplyRelationVO convert(SupplyRelationDTO relation, Map<Long, PoiInfoDto> poiInfoMap) {
        return Optional.ofNullable(relation)
                .map(r -> SupplyRelationVO.builder()
                        .relationId(Optional.ofNullable(r.getId()).map(Object::toString).orElse(null))
                        .skuId(r.getSkuId())
                        .orderId(r.getOrderId())
                        .supplier(buildSupplier(r.getSupplier()))
                        .supplierName(Optional.ofNullable(r.getSupplier()).map(SupplierDTO::getSupplierName).orElse(null))
                        .payMethod(r.getPayMethod())
                        .settleMethod(r.getSettleMethod())
                        .supplyUnit(r.getSupplyUnit())
                        .supplyUnitPrice(Optional.ofNullable(r.getSupplyUnitPrice()).map(PriceConverter::calcDivide100PriceValue).orElse(BigDecimal.ZERO.toPlainString()))
                        .supplyUnitRatio(r.getSupplyUnitRatio())
                        .scmChannelGoodsRatio(r.getSupplierGoodsRatio())
                        .minOrderQuantity(r.getMinOrderQuantity())
                        .masterFlag(r.getMasterFlag())
                        .supplierGoods(SupplierGoodsConverter.buildSupplierGoods(r.getSupplierGoods()))
                        .lastSyncStores(buildLastSyncStores(r.getLastSyncStores(), poiInfoMap))
                        .baseUnit(r.getBaseUnit())
                        .supplyUnitCode(Optional.ofNullable(r.getSupplyUnitCode())
                                .filter(StringUtils::isNotBlank)
                                .orElse(null))
                        .controlType(r.getControlType())
                        .build())
                .map(vo -> {
                    vo.setEnableSupplierGoodsInfo(Optional.ofNullable(vo.getSupplierGoods()).map(SupplierGoodsVO::getSupplierGoodsInfo).isPresent());
                    return vo;
                })
                .orElse(null);
    }

    private SupplyRelationVO.LastSyncStores buildLastSyncStores(String lastSyncStoresStr, Map<Long, PoiInfoDto> poiInfoMap) {
        if (StringUtils.isBlank(lastSyncStoresStr)) {
            return SupplyRelationVO.LastSyncStores.getDefault();
        }
        Set<Long> allLastSyncStores = JacksonUtils.fromJson(lastSyncStoresStr, new TypeReference<Set<Long>>() {
        });
        return getAuthLastSyncStores(allLastSyncStores, poiInfoMap);
    }

    private SupplyRelationVO.LastSyncStores getAuthLastSyncStores(Set<Long> allLastSyncStores, Map<Long, PoiInfoDto> poiInfoMap) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(allLastSyncStores)) {
            return SupplyRelationVO.LastSyncStores.getDefault();
        }
        List<Long> authStoreList = allLastSyncStores.stream()
                .filter(poiInfoMap::containsKey)
                .collect(Collectors.toList());

        List<Long> lackAuthStoreList = allLastSyncStores.stream()
                .filter(poiId -> !poiInfoMap.containsKey(poiId))
                .collect(Collectors.toList());

        return new SupplyRelationVO.LastSyncStores(authStoreList, lackAuthStoreList);
    }

    private SupplierVO buildSupplier(SupplierDTO supplier) {
        return Optional.ofNullable(supplier)
                .map(s -> SupplierVO.builder()
                        .supplierId(s.getSupplierId())
                        .supplierName(s.getSupplierName())
                        .supplierChannelId(s.getSupplierChannelId())
                        .arrivalDays(Optional.ofNullable(s.getArrivalDays()).orElse(0))
                        .build())
                .orElse(null);
    }



}
