package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP门店商品SPU信息
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(
        description = "ERP门店商品SPU信息"
)
@Data
@ApiModel("ERP门店商品SPU信息")
public class ErpStoreSpuVO {
    
    @FieldDoc(description = "租户ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户ID")
    private Long tenantId;

    @FieldDoc(description = "门店ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "spu编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "spu编码")
    private String spuId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品名称")
    private String name;

    @FieldDoc(description = "主图", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "主图")
    private String mainImageUrl;

    @FieldDoc(description = "所有图片", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "所有图片")
    private List<String> imageUrls;

    @FieldDoc(description = "上线状态 1-上线 2-自动下线 3-手动下线", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "上线状态 1-上线 2-自动下线 3-手动下线")
    private Integer onlineStatus;

    @FieldDoc(description = "上架状态 1-上架 2-自动下架 3-手动下架", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "上架状态 1-上架 2-自动下架 3-手动下架")
    private Integer saleStatus;

    @FieldDoc(description = "价格状态", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "价格状态")
    private Integer priceStatus;

    @FieldDoc(description = "库存状态", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "库存状态")
    private Integer stockStatus;

    @FieldDoc(description = "是否必卖 1-必卖 2-非必卖", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "是否必卖 1-必卖 2-非必卖")
    private Integer mustSale;

    @FieldDoc(description = "总部店内分类", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "总部店内分类")
    private List<FrontCategorySimpleVO> frontCategories;

    @FieldDoc(description = "门店商品SKU列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店商品SKU列表")
    private List<ErpStoreSkuVO> storeSkuList;

    @FieldDoc(description = "渠道商品SPU列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道商品SPU列表")
    private List<ErpChannelSpuVO> channelSpuList;

    @FieldDoc(description = "月总销量", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "月总销量")
    private Integer monthSaleAmount;

    @FieldDoc(description = "规格类型 1：单规格，2：多规格", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "规格类型")
    public Integer specType;

}
