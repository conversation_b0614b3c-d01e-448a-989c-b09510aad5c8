package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.StoreSpuController;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SimpleUser;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.RequestConvertUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ChannelActivityWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSProductServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OcmsTaggedSpuCoverageWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ProductBizServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PurchaseBizServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SaasPriceServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StockWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StoreProductWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantProductWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuBasicDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuBasicDTO;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QueryMerchantSkuListBySkuIds;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuCartonMeasure;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuPvExtend;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.MerchantSkuInfoListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMasterCheckService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品创建维护供货关系，为了被提报逻辑调用，从原来StoreSpuController中下沉
 *
 * <AUTHOR>
 * @since 2024/08/20
 */
@Service
@Slf4j
public class SupplyRelationService {

    @Autowired
    private ProductBizServiceWrapper productBizServiceWrapper;
    @Autowired
    private PurchaseBizServiceWrapper purchaseBizServiceWrapper;
    @Autowired
    private EmpowerMasterCheckService.Iface empowerMasterCheckService;


    public CommonResponse<Void> createSpuSupplyRelation(Long tenantId, Long storeId, String spuId, List<SupplyRelationAndPurchaseVO> purchaseInfo,
                                                        Boolean manualCreate, SimpleUser user, boolean fromReview) {
        try {
            Asserts.notNull(tenantId, "tenantId");
            Asserts.notNull(storeId, "storeId");
            Asserts.notBlank(spuId, "spuId");
            Asserts.check(CollectionUtils.isNotEmpty(purchaseInfo), "purchaseInfo");

            //查询spu信息
            StoreSpuBasicDTO storeSpuBasicDTO = productBizServiceWrapper.queryStoreSpuBasicInfo(tenantId, storeId,
                    spuId, true);
            List<StoreSkuBasicDTO> storeSkuList = storeSpuBasicDTO.getStoreSkuList();
            if (CollectionUtils.isNotEmpty(storeSkuList)) {
                //过滤skuId为空的进行填值
                purchaseInfo.stream()
                        .filter(item -> StringUtils.isBlank(item.getSkuId()))
                        .forEach(item -> {
                            //单规格直接设置skuId
                            String specName = item.getSpec();
                            if (StringUtils.isBlank(specName)) {
                                item.setSkuId(storeSkuList.get(0).getSkuId());
                            } else {
                                //多规格根据规格名称匹配sku,并设置skuId
                                Optional<StoreSkuBasicDTO> first = storeSkuList.stream().filter(sku -> StringUtils.equals(sku.getSpec(), specName)).findFirst();
                                item.setSkuId(first.map(StoreSkuBasicDTO::getSkuId).orElse(null));
                            }
                        });
                //手动创建，设置箱规编码
                if (Boolean.TRUE.equals(manualCreate)) {
                    //手动创建的，没有箱规编码，需要通过名称匹配
                    Set<String> skuIds = purchaseInfo.stream()
                            .map(SupplyRelationAndPurchaseVO::getSkuId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());
                    Map<String, List<MerchantSkuCartonMeasure>> skuUnitMap;
                    try {
                        MerchantSkuInfoListResult response = empowerMasterCheckService.queryMerchantSkuListByIds(new QueryMerchantSkuListBySkuIds(tenantId, Lists.newArrayList(skuIds)));
                        ResponseHandler.checkResponseAndStatus(response, merchantSkuInfoListResult -> merchantSkuInfoListResult.getStatus().getCode(),
                                merchantSkuInfoListResult -> merchantSkuInfoListResult.getStatus().getMsg());
                        skuUnitMap = Optional.ofNullable(response.getMerchantSkuInfoList())
                                .map(list -> list.stream().collect(Collectors.toMap(MerchantSkuInfo::getSkuId, merchantSkuInfo -> Optional.of(merchantSkuInfo)
                                        .map(MerchantSkuInfo::getExtend)
                                        .map(MerchantSkuPvExtend::getCartonMeasureList)
                                        .orElse(Lists.newArrayList()), (v1, v2) -> v1)))
                                .orElse(Maps.newHashMap());
                        log.info("手动创建门店商品，箱规列表查询结果:{}", JacksonUtils.toJson(skuUnitMap));
                    } catch (TException e) {
                        log.error("queryTenantSkuCartonMeasureInfo error, skuIds:{}, msg:{}", skuIds, e.getMessage(), e);
                        throw new BizException("查询门店商品箱规列表失败");
                    }
                    purchaseInfo.forEach(pr -> {
                        if (StringUtils.isBlank(pr.getSkuId())) {
                            return;
                        }
                        if (CollectionUtils.isEmpty(pr.getSupplyRelations())) {
                            return;
                        }

                        pr.getSupplyRelations().forEach(relation -> {
                            if (StringUtils.isNotBlank(relation.getSupplyUnitCode())) {
                                return;
                            }
                            if (StringUtils.isBlank(relation.getSupplyUnit())) {
                                return;
                            }
                            List<MerchantSkuCartonMeasure> unitList = skuUnitMap.get(pr.getSkuId());
                            if (org.apache.commons.collections.CollectionUtils.isEmpty(unitList)) {
                                return;
                            }
                            Map<String, MerchantSkuCartonMeasure> unitNameMap = unitList.stream().collect(Collectors.toMap(MerchantSkuCartonMeasure::getName, Function.identity(), (v1, v2) -> v1));
                            MerchantSkuCartonMeasure unit = unitNameMap.get(relation.getSupplyUnit());
                            if (unit == null) {
                                return;
                            }
                            relation.setSupplyUnitCode(unit.getCode());
                            relation.setSupplyUnitRatio((double) unit.getQuantity());
                        });
                    });
                }
                // User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
                CommonResponse<Void> commonResponse = purchaseBizServiceWrapper.batchCreateSupplyRelation(
                        RequestConvertUtils.convertToSupplyRelationAndPurchaseSkuReq(storeId, spuId, purchaseInfo), user, fromReview);
                if (!commonResponse.isSuccess()) {
                    throw new BizException(commonResponse.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("createSupplyRelation failed,  spuId:{}, msg:{}", spuId, e.getMessage(), e);
            return CommonResponse.fail(ResultCode.FAIL, "创建供货关系或采购属性失败");
        }
        return CommonResponse.success(null);
    }
}
