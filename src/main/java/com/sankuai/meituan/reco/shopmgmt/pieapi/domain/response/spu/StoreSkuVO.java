package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelSaleAttrValueInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CombineChildSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuImageInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrValueVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SkuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuAttrValueInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSkuDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang.BooleanUtils;

/**
 * @Title: StoreSkuVO
 * @Description: 门店商品SKU信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 1:08 下午
 */
@TypeDoc(
        description = "门店商品SKU信息"
)
@Data
@ApiModel("门店商品SKU信息")
public class StoreSkuVO extends SkuContainer {

    @FieldDoc(
            description = "sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku编码", required = true)
    private String skuId;

    @FieldDoc(
            description = "UPC信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "UPC信息", required = true)
    private List<String> upcInfo;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String spec;

    @FieldDoc(
            description = "以克(g)为单位的重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "以克(g)为单位的重量")
    private Integer weight;

    @FieldDoc(
            description = "带单位的重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "带单位的重量", required = true)
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "重量单位", required = true)
    private String weightUnit;

    @FieldDoc(
            description = "报价价格，单位：分", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "进货价，单位：分")
    private Long storePrice;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String saleUnit;

    @FieldDoc(
            description = "自定义库存标记  0：非自定义库存 1：自定义库存, 非无限库存模式下传0,非无限库存模式下这个字段不需要读取", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义库存标记 ", required = true)
    private Integer customizeStockFlag;

    @FieldDoc(
            description = "第二天是否自动恢复无限库存 0-不自动恢复 1-自动恢复", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存", required = true)
    private Integer autoResumeInfiniteStock;

    @FieldDoc(
            description = "自定义库存数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义库存数量", required = true)
    private Integer customizeStockQuantity;

    @FieldDoc(
            description = "月销量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "月销量")
    private Integer monthSaleAmount;

    @FieldDoc(
            description = "报价审核状态 1-待审核", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "报价审核状态")
    private Integer reviewStatus;

    @FieldDoc(
            description = "报价价格，单位：分", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "报价价格，单位：分")
    private Long quotePrice;


    @FieldDoc(
            description = "是否组合商品 值为1时是组合商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否组合商品 值为1时是组合商品")
    private Integer composeSkuFlag;

    @FieldDoc(
            description = "分渠道定价(1不分渠道2分渠道)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道定价(1不分渠道2分渠道)")
    private Integer channelPriceType;

    @FieldDoc(
            description = "统一渠道价格channelPriceType=1时存在", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "统一渠道价格channelPriceType=1时存在")
    private String channelOnlinePrice;

    @FieldDoc(
            description = "分渠道价格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道价格")
    private List<ChannelPriceVO> channelOnlinePriceList;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "是否组合商品 值为1时是组合商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否组合商品 值为1时是组合商品")
    private Integer isComposeSku;

    @FieldDoc(
            description = "最近采购价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "最近采购价")
    private String latestPurchasePrice;

    @FieldDoc(
            description = "默认主供采购价(基本单位)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "默认主供采购价(基本单位)")
    private String masterPurchasePrice;

    @FieldDoc(
            description = "链接最新价(基本单位)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "链接最新价(基本单位)")
    private String baseChannelGoodsPrice;

    @FieldDoc(
            description = "最近下单价(基本单位)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "最近下单价(基本单位)")
    private String lastDeliverPrice;

    @FieldDoc(
            description = "最近下单价单位(基本单位)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "最近下单价单位(基本单位)")
    private String lastDeliverPriceUnit;

    @FieldDoc(
            description = "加权库存成本价(基本单位)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "加权库存成本价(基本单位)")
    private String weightedInventoryCostPrice;

    @FieldDoc(
            description = "加权库存成本价单位(基本单位)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "加权库存成本价单位(基本单位)")
    private String weightedInventoryCostPriceUnit;


    @FieldDoc(
            description = "采购最近更新时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "采购最近更新时间")
    private String latestTime;

    @FieldDoc(
            description = "采购商名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "采购商名称")
    private String supplierName;

    @FieldDoc(description = "建议零售价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "建议零售价")
    protected Double suggestPrice;

    @FieldDoc(description = "京东销售属性", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "京东销售属性")
    protected List<SaleAttrValueVo> jdAttrValues;

    @FieldDoc(
            description = "是否开启效期检查，0-不检查 1-检查"
    )
    @ApiModelProperty(value = "是否开启效期检查，0-不检查 1-检查")
    private Integer enableExpiredCheck;

    @FieldDoc(
            description = "产地 1-国产 2-进口"
    )
    @ApiModelProperty(value = "产地 1-国产 2-进口")
    private Integer originPlace;

    @FieldDoc(
            description = "保质期天数"
    )
    @ApiModelProperty(value = "保质期天数")
    private Integer expirationDays;

    @FieldDoc(
            description = "外部编码"
    )
    @ApiModelProperty(value = "外部编码")
    private String externalCode;

    @FieldDoc(
            description = "加盟商总部编码"
    )
    @ApiModelProperty(value = "加盟商总部编码")
    private String franchiseeHeadquartersCode;

    @FieldDoc(
            description = "采购平台编码"
    )
    @ApiModelProperty(value = "采购平台编码")
    private String purchasePlatformCode;

    @FieldDoc(
            description = "加盟主采购平台编码信息"
    )
    @ApiModelProperty(value = "加盟主采购平台编码信息")
    private List<FranchisorPurchasePlatformInfoVO> franchisorPurchasePlatformInfoList;

    @FieldDoc(
            description = "控货方式  0 中心仓配送   1 供应商直送",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "控货方式 0 中心仓配送   1 供应商直送")
    private Integer controlGoodsType = 0;

    @FieldDoc(
            description = "备货区划列表"
    )
    @ApiModelProperty(value = "备货区划列表")
    private List<Integer> controlGoodsAreaIdList;

    @FieldDoc(
            description = "箱规单位转换系数"
    )
    @ApiModelProperty(value = "箱规单位转换系数")
    private List<CartonMeasureConvertFactorVO> cartonMeasureConvertFactorList;

    @FieldDoc(
            description = "采购模式"
    )
    @ApiModelProperty(value = "采购模式")
    private Integer purchaseType;

    @FieldDoc(
            description = "渠道sku属性值信息"
    )
    @ApiModelProperty(value = "渠道sku属性值信息")
    private List<ChannelSkuAttrValueInfoVo> channelSkuAttrValueInfoList;

    @FieldDoc(
            description = "sku类型 1单品 2组合品"
    )
    @ApiModelProperty(name = "sku类型 1单品 2组合品")
    private Integer skuSaleType;

    @FieldDoc(
            description = "组合品的子sku信息"
    )
    @ApiModelProperty(name = "组合品的子sku信息")
    private List<CombineChildSkuVo> childSkuList;

    @FieldDoc(
            description = "售卖状态 1-售卖 2-自动停售 3-手工停售"
    )
    @ApiModelProperty(value = "售卖状态")
    private Integer onlineStatus;

    @FieldDoc(
            description = "渠道售罄标记列表"
    )
    @ApiModelProperty(value = "渠道售罄标记列表")
    private List<Integer> channelSellOutFlagList;

    public static List<StoreSkuVO> ofDTOList(List<StoreSkuDTO> storeSkuDTOList) {
        if (CollectionUtils.isEmpty(storeSkuDTOList)) {
            return Lists.newArrayList();
        }

        return storeSkuDTOList.stream().filter(Objects::nonNull).map(StoreSkuVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static StoreSkuVO ofDTO(StoreSkuDTO storeSkuDTO) {
        if (storeSkuDTO == null) {
            return null;
        }

        StoreSkuVO storeSkuVO = new StoreSkuVO();
        storeSkuVO.setSkuId(storeSkuDTO.getSkuId());
        storeSkuVO.setSpec(storeSkuDTO.getSpec());
        storeSkuVO.setWeight(storeSkuDTO.getWeight());
        storeSkuVO.setWeightForUnit(storeSkuDTO.getWeightForUnit());
        storeSkuVO.setWeightUnit(storeSkuDTO.getWeightUnit());
        storeSkuVO.setSaleUnit(storeSkuDTO.getUnit());
        storeSkuVO.setUpcInfo(storeSkuDTO.getUpcList());
        storeSkuVO.setCustomizeStockFlag(storeSkuDTO.getCustomizeStockFlag());
        storeSkuVO.setAutoResumeInfiniteStock(storeSkuDTO.getAutoResumeInfiniteStock());
        storeSkuVO.setCustomizeStockQuantity(storeSkuDTO.getCustomizeStockQuantity());
        storeSkuVO.setMonthSaleAmount(storeSkuDTO.getMonthSaleAmount());
        storeSkuVO.setReviewStatus(storeSkuDTO.getReviewStatus());
        if (storeSkuDTO.getStorePrice() != null) {
            storeSkuVO.setStorePrice(MoneyUtils.yuanToCent(storeSkuDTO.getStorePrice()));
        }
        if (storeSkuDTO.getQuotePrice() != null) {
            storeSkuVO.setQuotePrice(MoneyUtils.yuanToCent(storeSkuDTO.getQuotePrice()));
        }
        if (storeSkuDTO.composeSkuFlag != null) {
            storeSkuVO.setComposeSkuFlag(BooleanUtils.isTrue(storeSkuDTO.composeSkuFlag) ? 1 : 0);
        }

        storeSkuVO.setSuggestPrice(storeSkuDTO.getSuggestPrice());
        storeSkuVO.setJdAttrValues(ConverterUtils.convertList(storeSkuDTO.getSaleAttributeValueDTOS(),SaleAttrValueVo::ofDTO));

        storeSkuVO.setStoreId(storeSkuDTO.getStoreId());
        storeSkuVO.setEnableExpiredCheck(storeSkuDTO.getEnableExpiredCheck());
        storeSkuVO.setOriginPlace(storeSkuDTO.getOriginPlace());
        storeSkuVO.setExpirationDays(storeSkuDTO.getExpirationDays());
        storeSkuVO.setExternalCode(storeSkuDTO.getExternalCode());
        storeSkuVO.setFranchiseeHeadquartersCode(storeSkuDTO.getFranchiseeHeadquartersCode());
        storeSkuVO.setPurchasePlatformCode(storeSkuDTO.getPurchasePlatformCode());
        if (CollectionUtils.isNotEmpty(storeSkuDTO.getCartonMeasureDtoList())) {
            storeSkuVO.setCartonMeasureConvertFactorList(storeSkuDTO.getCartonMeasureDtoList().stream().map(cm ->
                        new CartonMeasureConvertFactorVO(cm.getCartonMeasureCode(), cm.getCartonMeasureName(), cm.getBasicUnitConvertFactor())
                    ).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(storeSkuDTO.getFranchisorPurchasePlatformInfoList())) {
            storeSkuVO.setFranchisorPurchasePlatformInfoList(ConverterUtils.convertList(storeSkuDTO.getFranchisorPurchasePlatformInfoList(),FranchisorPurchasePlatformInfoVO::of));
        }
        storeSkuVO.setControlGoodsType(storeSkuDTO.getControlGoodsType());
        storeSkuVO.setControlGoodsAreaIdList(storeSkuDTO.getControlGoodsAreaIdList());
        storeSkuVO.setChannelSaleAttrValueInfoList(Fun.map(storeSkuDTO.getChannelSaleAttributeValues(), ChannelSaleAttrValueInfoVO::of));
        storeSkuVO.setImageInfo(SkuImageInfo.of(storeSkuDTO));

        storeSkuVO.setOnlineStatus(storeSkuDTO.getOnlineStatus());

        storeSkuVO.setChannelSkuAttrValueInfoList(ChannelSkuAttrValueInfoVo.ofOcmsDtoList(storeSkuDTO.getChannelSkuAttrValueInfoList()));
        storeSkuVO.setSkuSaleType(storeSkuDTO.getSkuSaleType());
        if (CollectionUtils.isNotEmpty(storeSkuDTO.getChildSkuList())) {
            storeSkuVO.setChildSkuList(JacksonUtils.convertList(storeSkuDTO.getChildSkuList(), CombineChildSkuVo.class));
        }
        storeSkuVO.setChannelSellOutFlagList(storeSkuDTO.getChannelSellOutFlags());
        return storeSkuVO;
    }


    public static List<StoreSkuVO> ofBizDTOList(List<com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuDTO > storeSkuDTOList) {
        if (CollectionUtils.isEmpty(storeSkuDTOList)) {
            return Lists.newArrayList();
        }

        return storeSkuDTOList.stream().filter(Objects::nonNull).map(StoreSkuVO::ofBizDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static StoreSkuVO ofBizDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuDTO  storeSkuDTO) {
        if (storeSkuDTO == null) {
            return null;
        }

        StoreSkuVO storeSkuVO = new StoreSkuVO();
        storeSkuVO.setSkuId(storeSkuDTO.getSkuId());
        storeSkuVO.setSpec(storeSkuDTO.getSpec());
        storeSkuVO.setWeight(storeSkuDTO.getWeight());
        storeSkuVO.setWeightForUnit(storeSkuDTO.getWeightForUnit());
        storeSkuVO.setWeightUnit(storeSkuDTO.getWeightUnit());
        storeSkuVO.setSaleUnit(storeSkuDTO.getSaleUnit());
        storeSkuVO.setUpcInfo(storeSkuDTO.getUpcList());
        storeSkuVO.setCustomizeStockFlag(storeSkuDTO.getCustomizedStockFlag());
        storeSkuVO.setAutoResumeInfiniteStock(storeSkuDTO.getAutoResumeInfiniteStock());
        storeSkuVO.setCustomizeStockQuantity(storeSkuDTO.getCustomizeStockQuantity());
        storeSkuVO.setMonthSaleAmount(storeSkuDTO.getMonthSaleAmount());
        storeSkuVO.setReviewStatus(storeSkuDTO.getReviewStatus());
        if (storeSkuDTO.getStorePrice() != null) {
            storeSkuVO.setStorePrice(storeSkuDTO.getStorePrice());
        }
        if (storeSkuDTO.getQuotePrice() != null) {
            storeSkuVO.setQuotePrice(MoneyUtils.yuanToCent(storeSkuDTO.getQuotePrice()));
        }
        if (storeSkuDTO.getComposeSkuFlag() != null) {
            storeSkuVO.setComposeSkuFlag(BooleanUtils.isTrue(storeSkuDTO.getComposeSkuFlag()) ? 1 : 0);
        }

        storeSkuVO.setSuggestPrice(storeSkuDTO.getSuggestPrice());
        storeSkuVO.setJdAttrValues(ConverterUtils.convertList(storeSkuDTO.getJdSaleAttributeValueDTOS(),SaleAttrValueVo::ofDTO));

        storeSkuVO.setStoreId(storeSkuDTO.getStoreId());
        storeSkuVO.setEnableExpiredCheck(storeSkuDTO.getEnableExpiredCheck());
        storeSkuVO.setOriginPlace(storeSkuDTO.getOriginPlace());
        storeSkuVO.setExpirationDays(storeSkuDTO.getExpirationDays());
        storeSkuVO.setExternalCode(storeSkuDTO.getExternalCode());
        storeSkuVO.setFranchiseeHeadquartersCode(storeSkuDTO.getFranchiseeHeadquartersCode());
        storeSkuVO.setPurchasePlatformCode(storeSkuDTO.getPurchasePlatformCode());
        if (CollectionUtils.isNotEmpty(storeSkuDTO.getCartonMeasureList())) {
            storeSkuVO.setCartonMeasureConvertFactorList(storeSkuDTO.getCartonMeasureList().stream().map(cm ->
                    new CartonMeasureConvertFactorVO(cm.getCartonMeasureCode(), cm.getCartonMeasureName(), cm.getBasicUnitConvertFactor())
            ).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(storeSkuDTO.getFranchisorPurchasePlatformInfoList())) {
            storeSkuVO.setFranchisorPurchasePlatformInfoList(ConverterUtils.convertList(storeSkuDTO.getFranchisorPurchasePlatformInfoList(),
                 FranchisorPurchasePlatformInfoVO::of));
        }
        storeSkuVO.setControlGoodsAreaIdList(storeSkuDTO.getControlGoodsAreaIdList());
        storeSkuVO.setChannelSaleAttrValueInfoList(Fun.map(storeSkuDTO.getChannelSaleAttributeValues(), ChannelSaleAttrValueInfoVO::of));
        storeSkuVO.setImageInfo(SkuImageInfo.of(storeSkuDTO));
        storeSkuVO.setSkuSaleType(storeSkuDTO.getSkuSaleType());
        storeSkuVO.setOnlineStatus(storeSkuDTO.getOnlineStatus());
        return storeSkuVO;
    }
}
