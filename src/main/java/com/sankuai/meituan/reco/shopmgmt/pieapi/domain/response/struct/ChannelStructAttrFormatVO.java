package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelDynamicInfoVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.structattr.ChannelStructAttrFormatDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/7/15 14:23
 */
@TypeDoc(
        name = "结构化属性格式",
        description = "结构化属性格式"
)
@Data
@ApiModel("结构化属性格式")
public class ChannelStructAttrFormatVO {
    @FieldDoc(
            description = "录入值 1-文本，3-固定文本（商家无需修改，直接传），4-数字，5-下拉"
    )
    @ApiModelProperty("录入值 1-文本，3-固定文本（商家无需修改，直接传），4-数字，5-下拉")
    private Integer inputType;

    @FieldDoc(
            description = "inputType=1时，录入要求 表示文本录入的字符类型组合。 1-中文，2-英文，3-数字，4-标点符合（含符号）"
    )
    @ApiModelProperty("inputType=1时，录入要求 表示文本录入的字符类型组合。 1-中文，2-英文，3-数字，4-标点符合（含符号）")
    private String characterType;

    @FieldDoc(
            description = "是否必传， 1-必传,2-非必传"
    )
    @ApiModelProperty("是否必传， 1-必传,2-非必传")
    private Integer isRequired;

    @FieldDoc(
            description = "录入时的提示文案"
    )
    @ApiModelProperty("录入时的提示文案")
    private String placeholder;

    @FieldDoc(
            description = "当为inputType=1-文本时表示字符串类型为最小字符数，当为4-数字类型时表示数字部分可录入的最小值(闭区间，可以是小数)"
    )
    @ApiModelProperty("当为inputType=1-文本时表示字符串类型为最小字符数，当为4-数字类型时表示数字部分可录入的最小值(闭区间，可以是小数)")
    private String min;

    @FieldDoc(
            description = "当为inputType=1-文本时表示字符串类型为最大字符数，当为4-数字类型时表示数字部分可录入的最大值(闭区间，可以是小数)"
    )
    @ApiModelProperty("当为inputType=1-文本时表示字符串类型为最大字符数，当为4-数字类型时表示数字部分可录入的最大值(闭区间，可以是小数)")
    private String max;

    @FieldDoc(
            description = "小数点位数 inputType=4时有值，表示数字部分可录入的小数点位数，当返回为空或者为0是表示整数"
    )
    @ApiModelProperty("小数点位数 inputType=4时有值，表示数字部分可录入的小数点位数，当返回为空或者为0是表示整数")
    private Integer precision;

    @FieldDoc(
            description = "inputType=5时，单位的枚举列表"
    )
    @ApiModelProperty("inputType=5时，单位的枚举列表")
    private List<ChannelDynamicInfoVO.ChannelDynamicInfoOptionVO> optionList;

    @FieldDoc(
            description = "inputType=3时，固定值，固定连接符"
    )
    @ApiModelProperty("inputType=3时，固定值，固定连接符")
    private String fixValue;

    @FieldDoc(
            description = "顺序"
    )
    @ApiModelProperty("顺序")
    private Integer sequence;

    @FieldDoc(
            description = "仅inputType=1，characterType包含4时有效，是否有符号限制。0=没有 1=有"
    )
    @ApiModelProperty("仅inputType=1，characterType包含4时有效，是否有符号限制。0=没有 1=有")
    private Integer hasSymbolLimit;

    @FieldDoc(
            description = "仅inputType=1，characterType包含4时有效，hasSymbolLimit=1时允许录入的符号"
    )
    @ApiModelProperty("仅inputType=1，characterType包含4时有效，hasSymbolLimit=1时允许录入的符号")
    private List<String> allowSymbolList;

    public static List<ChannelStructAttrFormatDTO> toBizDTOList(List<ChannelStructAttrFormatVO> voList) {
        return Fun.map(voList, ChannelStructAttrFormatVO::toBizDTO);
    }

    private static ChannelStructAttrFormatDTO toBizDTO(ChannelStructAttrFormatVO attrFormatVO) {
        if (attrFormatVO == null) {
            return null;
        }
        ChannelStructAttrFormatDTO dto = new ChannelStructAttrFormatDTO();
        dto.setInputType(attrFormatVO.getInputType());
        dto.setCharacterType(attrFormatVO.getCharacterType());
        dto.setIsRequired(attrFormatVO.getIsRequired());
        dto.setPlaceholder(attrFormatVO.getPlaceholder());
        dto.setMin(attrFormatVO.getMin());
        dto.setMax(attrFormatVO.getMax());
        dto.setPrecision(attrFormatVO.getPrecision());
        dto.setFixValue(attrFormatVO.getFixValue());
        dto.setSequence(attrFormatVO.getSequence());
        dto.setHasSymbolLimit(attrFormatVO.getHasSymbolLimit());
        dto.setAllowSymbolList(attrFormatVO.getAllowSymbolList());
        dto.setOptionList(ChannelDynamicInfoVO.ChannelDynamicInfoOptionVO.toBizDTOList(attrFormatVO.getOptionList()));
        return dto;
    }

    public static List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrFormatDTO> toOcmsBizDTOList(List<ChannelStructAttrFormatVO> voList) {
        return Fun.map(voList, ChannelStructAttrFormatVO::toOcmsBizDTO);
    }

    private static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrFormatDTO toOcmsBizDTO(ChannelStructAttrFormatVO attrFormatVO) {
        if (attrFormatVO == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrFormatDTO dto =
                new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrFormatDTO();
        dto.setInputType(attrFormatVO.getInputType());
        dto.setCharacterType(attrFormatVO.getCharacterType());
        dto.setIsRequired(attrFormatVO.getIsRequired());
        dto.setPlaceholder(attrFormatVO.getPlaceholder());
        dto.setMin(attrFormatVO.getMin());
        dto.setMax(attrFormatVO.getMax());
        dto.setPrecision(attrFormatVO.getPrecision());
        dto.setFixValue(attrFormatVO.getFixValue());
        dto.setSequence(attrFormatVO.getSequence());
        dto.setHasSymbolLimit(attrFormatVO.getHasSymbolLimit());
        dto.setAllowSymbolList(attrFormatVO.getAllowSymbolList());
        dto.setOptionList(ChannelDynamicInfoVO.ChannelDynamicInfoOptionVO.toDTOList(attrFormatVO.getOptionList()));
        return dto;
    }

    public static List<ChannelStructAttrFormatVO> ofPlatformDTOList(List<com.sankuai.meituan.shangou.empower
            .productplatform.thrift.channelcategory.dto.ChannelStructAttrFormatDTO> dtoList) {
        return Fun.map(dtoList, ChannelStructAttrFormatVO::fPlatformDTO);
    }

    private static ChannelStructAttrFormatVO fPlatformDTO(com.sankuai.meituan.shangou.empower.productplatform
                                                                  .thrift.channelcategory.dto.ChannelStructAttrFormatDTO attrFormatDTO) {
        if (attrFormatDTO == null) {
            return null;
        }
        ChannelStructAttrFormatVO formatVO = new ChannelStructAttrFormatVO();
        formatVO.setInputType(attrFormatDTO.getInputType());
        formatVO.setCharacterType(attrFormatDTO.getCharacterType());
        formatVO.setIsRequired(attrFormatDTO.getIsRequired());
        formatVO.setPlaceholder(attrFormatDTO.getPlaceholder());
        formatVO.setMin(attrFormatDTO.getMin());
        formatVO.setMax(attrFormatDTO.getMax());
        formatVO.setPrecision(attrFormatDTO.getPrecision());
        formatVO.setFixValue(attrFormatDTO.getFixValue());
        formatVO.setSequence(attrFormatDTO.getSequence());
        formatVO.setHasSymbolLimit(attrFormatDTO.getHasSymbolLimit());
        formatVO.setAllowSymbolList(attrFormatDTO.getAllowSymbolList());
        formatVO.setOptionList(ChannelDynamicInfoVO.ChannelDynamicInfoOptionVO.ofPlatformDTOList(attrFormatDTO.getOptionList()));
        return formatVO;
    }

    public static List<ChannelStructAttrFormatVO> ofBizDTOList(List<ChannelStructAttrFormatDTO> dtoList) {
        return Fun.map(dtoList, ChannelStructAttrFormatVO::ofBizDTO);
    }

    private static ChannelStructAttrFormatVO ofBizDTO(ChannelStructAttrFormatDTO attrFormatDTO) {
        if (attrFormatDTO == null) {
            return null;
        }
        ChannelStructAttrFormatVO formatVO = new ChannelStructAttrFormatVO();
        formatVO.setInputType(attrFormatDTO.getInputType());
        formatVO.setCharacterType(attrFormatDTO.getCharacterType());
        formatVO.setIsRequired(attrFormatDTO.getIsRequired());
        formatVO.setPlaceholder(attrFormatDTO.getPlaceholder());
        formatVO.setMin(attrFormatDTO.getMin());
        formatVO.setMax(attrFormatDTO.getMax());
        formatVO.setPrecision(attrFormatDTO.getPrecision());
        formatVO.setFixValue(attrFormatDTO.getFixValue());
        formatVO.setSequence(attrFormatDTO.getSequence());
        formatVO.setHasSymbolLimit(attrFormatDTO.getHasSymbolLimit());
        formatVO.setAllowSymbolList(attrFormatDTO.getAllowSymbolList());
        formatVO.setOptionList(ChannelDynamicInfoVO.ChannelDynamicInfoOptionVO
                .ofBizDTOList(attrFormatDTO.getOptionList()));
        return formatVO;
    }

    public static ChannelStructAttrFormatVO ofOcmsDTO(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrFormatDTO attrFormatDTO) {
        if (attrFormatDTO == null) {
            return null;
        }
        ChannelStructAttrFormatVO formatVO = new ChannelStructAttrFormatVO();
        formatVO.setInputType(attrFormatDTO.getInputType());
        formatVO.setCharacterType(attrFormatDTO.getCharacterType());
        formatVO.setIsRequired(attrFormatDTO.getIsRequired());
        formatVO.setPlaceholder(attrFormatDTO.getPlaceholder());
        formatVO.setMin(attrFormatDTO.getMin());
        formatVO.setMax(attrFormatDTO.getMax());
        formatVO.setPrecision(attrFormatDTO.getPrecision());
        formatVO.setFixValue(attrFormatDTO.getFixValue());
        formatVO.setSequence(attrFormatDTO.getSequence());
        formatVO.setHasSymbolLimit(attrFormatDTO.getHasSymbolLimit());
        formatVO.setAllowSymbolList(attrFormatDTO.getAllowSymbolList());
        formatVO.setOptionList(ChannelDynamicInfoVO.ChannelDynamicInfoOptionVO
                .ofDTOList(attrFormatDTO.getOptionList()));
        return formatVO;
    }
}
