package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.quality;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct.ChannelStructAttrValueInfoVO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.SpuPropertyInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "已选择的商品动态属性",
        description = "已选择的商品动态属性"
)
@Data
public class SelectedCategoryAttrInfoVo {

    @FieldDoc(
            description = "属性ID"
    )
    @ApiModelProperty(name = "属性ID")
    private Long attrId;

    @FieldDoc(
            description = "属性名称"
    )
    @ApiModelProperty(name = "属性名称")
    private String attrName;

    @FieldDoc(
            description = "属性值ID"
    )
    @ApiModelProperty(name = "属性值ID")
    private Long attrValueId;

    @FieldDoc(
            description = "属性值"
    )
    @ApiModelProperty(name = "属性值")
    private String attrValue;

    /**
     * [模板1属性值,模板2属性值]
     * 目前只有一个模板，此结构为了后续兼容后续可能多个模板拼接一个属性值的情况
     */
    @FieldDoc(
            description = "结构化属性值"
    )
    private List<ChannelStructAttrValueInfoVO> structAttrValueList;

    public SpuPropertyInfoDto convert2SpuPropertyInfoDto() {
        SpuPropertyInfoDto infoDto = new SpuPropertyInfoDto();
        infoDto.setPropertyId(attrId);
        infoDto.setPropertyName(attrName);
        infoDto.setPropertyValueId(attrValueId);
        infoDto.setPropertyValueName(attrValue);
        if (CollectionUtils.isNotEmpty(structAttrValueList) && CollectionUtils.isNotEmpty(structAttrValueList.get(0).getValueList())){
            infoDto.setStructPropertyList(Fun.map(structAttrValueList, ChannelStructAttrValueInfoVO::toQualityPlatFormDto));
        }

        return infoDto;
    }
}
