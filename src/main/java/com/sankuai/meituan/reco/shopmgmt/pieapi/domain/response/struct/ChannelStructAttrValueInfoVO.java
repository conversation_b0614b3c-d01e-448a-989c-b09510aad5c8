package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.struct;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.structattr.ChannelStructAttrValueInfoDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.SpuStructPropertyDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/7/15 14:29
 */
@TypeDoc(
        name = "结构化属性值信息",
        description = "结构化属性值信息"
)
@Data
@ApiModel("结构化属性值信息")
public class ChannelStructAttrValueInfoVO {
    @FieldDoc(
            description = "用户选择的选项id,inputType=5使用"
    )
    @ApiModelProperty("用户选择的选项id,inputType=5使用")
    private Integer formatType;

    /**
     * 格式：
     * [ // 可以有1行 or 多行数据
     *  [ // 一行内的结构化组合
     *      { inputType: 1 文本 }, { inputType: 5 下拉 }
     *  ]
     * ]
     */
    @FieldDoc(
            description = "属性值"
    )
    @ApiModelProperty("属性值")
    private List<List<ChannelStructAttrValueVO>> valueList;

    public static List<ChannelStructAttrValueInfoDTO> toBizDTOList(List<ChannelStructAttrValueInfoVO> attrValueInfoVOList) {
        return Fun.map(attrValueInfoVOList, ChannelStructAttrValueInfoVO::toBizDTO);
    }

    private static ChannelStructAttrValueInfoDTO toBizDTO(ChannelStructAttrValueInfoVO attrValueInfoVO) {
        if (attrValueInfoVO == null) {
            return null;
        }
        ChannelStructAttrValueInfoDTO dto = new ChannelStructAttrValueInfoDTO();
        dto.setFormatType(attrValueInfoVO.getFormatType());
        dto.setValueList(Fun.map(attrValueInfoVO.getValueList(), ChannelStructAttrValueVO::toBizDTOList));
        return dto;
    }

    public static List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrValueInfoDTO> toOcmsBizDTOList(List<ChannelStructAttrValueInfoVO> attrValueInfoVOList) {
        return Fun.map(attrValueInfoVOList, ChannelStructAttrValueInfoVO::toOcmsBizDTO);
    }

    private static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct
            .ChannelStructAttrValueInfoDTO toOcmsBizDTO(ChannelStructAttrValueInfoVO attrValueInfoVO) {
        if (attrValueInfoVO == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrValueInfoDTO dto =
                new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrValueInfoDTO();
        dto.setFormatType(attrValueInfoVO.getFormatType());
        dto.setValueList(Fun.map(attrValueInfoVO.getValueList(), ChannelStructAttrValueVO::toOcmsBizDTOList));
        return dto;
    }

    public static List<ChannelStructAttrValueInfoVO> ofBizDTOList(List<ChannelStructAttrValueInfoDTO> attrValueInfoDTOList) {
        return Fun.map(attrValueInfoDTOList, ChannelStructAttrValueInfoVO::ofBizDTO);
    }

    private static ChannelStructAttrValueInfoVO ofBizDTO(ChannelStructAttrValueInfoDTO attrValueInfoDTO) {
        if (attrValueInfoDTO == null) {
            return null;
        }
        ChannelStructAttrValueInfoVO vo = new ChannelStructAttrValueInfoVO();
        vo.setFormatType(attrValueInfoDTO.getFormatType());
        vo.setValueList(Fun.map(attrValueInfoDTO.getValueList(), ChannelStructAttrValueVO::ofBizDTOList));
        return vo;
    }

    public SpuStructPropertyDto toQualityPlatFormDto() {
        SpuStructPropertyDto to = new SpuStructPropertyDto();
        to.setFormatType(this.getFormatType());
        to.setValueList(Fun.map(this.getValueList(), valueLine -> Fun.map(valueLine, ChannelStructAttrValueVO::toQualityPlatFormDto)));
        return to;
    }

    public static ChannelStructAttrValueInfoVO ofOcmsDto(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.struct.ChannelStructAttrValueInfoDTO from) {
        if (from == null){
            return null;
        }
        ChannelStructAttrValueInfoVO to = new ChannelStructAttrValueInfoVO();
        to.setFormatType(from.getFormatType());
        to.setValueList(Fun.map(from.getValueList(), line -> Fun.map(line, ChannelStructAttrValueVO::ofDto)));
        return to;
    }
}
