<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">


    <bean id="rpcStoreFulfillConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreFulfillConfigThriftService"/>
        <property name="timeout" value="500"/>
        <property name="connTimeout" value="200"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="dapChannelAggDeliveryThriftServiceClient"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.DapChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('dap_channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="maltChannelAggDeliveryThriftServiceClient"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.rpc.service.MaltChannelAggDeliveryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="warehouseCoreThriftServiceClient"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.sgfnqnh.poi.base.client.thrift.WarehouseCoreThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('malt_channel_delivery_thrift_timeout','8000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfnqnh.poi.base"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>
</beans>