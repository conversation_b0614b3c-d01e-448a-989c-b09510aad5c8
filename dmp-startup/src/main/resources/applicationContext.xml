<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <import resource="classpath:freemarker-context.xml" />

    <import resource="applicationContext-thrift-client.xml"/>


    <bean id="grayManagementThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftServerPublisher" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService"/>
        <property name="serviceImpl" ref="grayManagementServiceProcessor"/>
        <property name="appKey" value="${app.name}"/>

        <!-- isNotEmpty(rhinoThreadPoolKey)时，MTthrift将会启用动态线程池。 -->
        <!-- value值格式 thrift.${serviceInterface}，如果是单端口，可以使用thrift.multiPort.${beanID} -->
        <property name="rhinoThreadPoolKey" value="com.sankuai.shangou.logistics.delivery.gray.GrayManagementThriftService"/>
    </bean>


</beans>