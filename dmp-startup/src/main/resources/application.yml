# Zebra属性配置
zebra:
  - jdbcRef: dhorderfulfillment_sdms_test # jdbcRef配置, 通过profile隔离
    default-transaction-manager: true
    poolType: druid
    minPoolSize: 5
    maxPoolSize: 30
    initialPoolSize: 5
    checkoutTimeout: 1000
    maxIdleTime: 1800
    idleConnectionTestPeriod: 60
    sqlSessionFactory:
      typeAliasesPackage: com.sankuai.shangou.logistics.delivery.domain
      plugins:
        - className: com.github.pagehelper.PageInterceptor
          properties:
            helperDialect: mysql # 数据库方言配置，默认为mysql。当使用其他数据库时请对应修改，例如：PostgreSQL数据库配置为postgresql、DB2数据配置db2等。
    zebraMapperScannerConfigurer:
      basePackage: com.sankuai.shangou.logistics.delivery.mapper
    transactionManagerName: sdmsTransactionManager
    routerType: master-only

#  - dataSourceBeanName: zebraDataSource # 上面生命的数据源的Bean名称
#    basePackage: com.sankuai.shangou.logistics.delivery.statistics.dao
#    mapperLocations: classpath*:/com/sankuai/shangou/logistics/delivery/statistics/dao/mapper/*.xml
#    optimizeShardKeyInSql: ture
    # 配送数据源
  - ruleName: qnh_delivery_test # jdbcRef配置, 通过profile隔离
    # 使用了xml形式的mapper实现时需要配置,多个以逗号分隔
    mapperLocations: classpath*:/com/sankuai/shangou/logistics/delivery/statistics/dao/mapper/*.xml,classpath*:/com/sankuai/shangou/logistics/delivery/dao/config/mapper/*.xml
    basePackage: com.sankuai.shangou.logistics.delivery.statistics.dao,com.sankuai.shangou.logistics.delivery.dao.config
    poolType: druid
    minPoolSize: 5
    maxPoolSize: 30
    initialPoolSize: 5
    checkoutTimeout: 1000
    maxIdleTime: 1800
    idleConnectionTestPeriod: 60
    zebraMapperScannerConfigurer:
      basePackage: com.sankuai.shangou.logistics.delivery.statistics.dao.mapper,com.sankuai.shangou.logistics.delivery.dao.config.mapper
    sqlSessionFactory:
      typeAliasesPackage: com.sankuai.shangou.logistics.delivery.statistics.dao,com.sankuai.shangou.logistics.delivery.dao.config.mapper
      plugins:
        - className: com.github.pagehelper.PageInterceptor
          properties:
            helperDialect: mysql # 数据库方言配置，默认为mysql。当使用其他数据库时请对应修改，例如：PostgreSQL数据库配置为postgresql、DB2数据配置db2等。
delivery:
  zebra:
    dhRuleName: migrate_shangousaasorder_test_saas_delivery_test_qnh_delivery_test_18152

# web服务端口号
server.port: 8080
management:
  endpoints:
    web:
      base-path: /monitor
      path-mapping:
        health: /alive

spring:
  profiles:
    active: '@active-profile@'

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-local.xml

---
spring:
  profiles: test

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-offline.xml

es:
  clusterGroupName: shangou_dmp_default
  accessKey: FBC889EE74C8771756A30D969BC0847E
  indicatorIndex: delivery_load_indicator
  # shangou_tms_default
  deliveryOrderClusterName: shangou_tms_default
  deliveryOrderAccessKey: FBC889EE74C8771756A30D969BC0847E
  deliveryOrderIndex: delivery_order

squirrel:
  clusterName: redis-sg-drunkhorse_qa
  businessClusterName: redis-sg-drunkhorse-business_qa
  ofcClusterName: redis-sg-newsupply-ofc_qa

zebra:
  - jdbcRef: dhorderfulfillment_sdms_test #替换为自己dev环境的jdbcRef
  - ruleName: qnh_delivery_test

delivery:
  zebra:
    dhRuleName: migrate_shangousaasorder_test_saas_delivery_test_qnh_delivery_test_18152

---

spring:
  profiles: staging

squirrel:
  clusterName: redis-sg-drunkhorse_product
  businessClusterName: redis-sg-drunkhorse-business_stage
  ofcClusterName: redis-sg-newsupply-ofc_stage

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml

es:
  clusterGroupName: shangou_eaglenode-es-dmp_default
  accessKey: FBC889EE74C8771756A30D969BC0847E
  indicatorIndex: delivery_load_indicator
  # shangou_tms_default
  deliveryOrderClusterName: shangou_eaglenode-es-tms_default
  deliveryOrderAccessKey: FBC889EE74C8771756A30D969BC0847E
  deliveryOrderIndex: delivery_order

zebra:
  - jdbcRef: dhorderfulfillment_sdms_product #替换为自己dev环境的jdbcRef
  - ruleName: tms_delivery_product

delivery:
  zebra:
    dhRuleName: migrate_shangousaasorder_saas_delivery_product_tms_delivery_product_6181
---

spring:
  profiles: prod

squirrel:
  clusterName: redis-sg-drunkhorse_product
  businessClusterName: redis-sg-drunkhorse-business_product
  ofcClusterName: redis-sg-newsupply-ofc_product

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-online.xml

es:
  clusterGroupName: shangou_eaglenode-es-dmp_default
  accessKey: FBC889EE74C8771756A30D969BC0847E
  indicatorIndex: delivery_load_indicator
  # shangou_tms_default
  deliveryOrderClusterName: shangou_eaglenode-es-tms_default
  deliveryOrderAccessKey: FBC889EE74C8771756A30D969BC0847E
  deliveryOrderIndex: delivery_order

zebra:
  - jdbcRef: dhorderfulfillment_sdms_product #替换为自己dev环境的jdbcRef
  - ruleName: tms_delivery_product

delivery:
  zebra:
    dhRuleName: migrate_shangousaasorder_saas_delivery_product_tms_delivery_product_6181