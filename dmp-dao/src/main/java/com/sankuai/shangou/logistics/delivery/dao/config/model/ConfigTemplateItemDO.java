package com.sankuai.shangou.logistics.delivery.dao.config.model;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 配置模版明细
 * config_template_item
 */
@Data
public class ConfigTemplateItemDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 配置模版id
     */
    private Long configTemplateId;

    /**
     * 模板类型
     */
    private Integer templateType;

    /**
     * 维度类型 1-租户, 2-经营模式
     */
    private Integer dimensionType;

    /**
     * 维度id
     */
    private String dimensionId;

    /**
     * 配置信息
     */
    private String configContent;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 创建人(牵牛花账号)
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}