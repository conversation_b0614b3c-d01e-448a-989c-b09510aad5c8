package com.sankuai.shangou.logistics.delivery.dao.config.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 配置任务明细表
 * config_task_item
 */
@Data
public class ConfigTaskItemDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 渠道门店id
     */
    private String channelStoreId;

    /**
     * 状态: 0-初始化 1-处理中 2-已完成 3-处理失败
     */
    private Integer status;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 完成时间
     */
    private Date finishedAt;

    private static final long serialVersionUID = 1L;
}