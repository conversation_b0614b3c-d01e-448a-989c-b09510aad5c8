package com.sankuai.shangou.logistics.delivery.dao.config.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class ConfigTemplateItemDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ConfigTemplateItemDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdIsNull() {
            addCriterion("config_template_id is null");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdIsNotNull() {
            addCriterion("config_template_id is not null");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdEqualTo(Long value) {
            addCriterion("config_template_id =", value, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdNotEqualTo(Long value) {
            addCriterion("config_template_id <>", value, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdGreaterThan(Long value) {
            addCriterion("config_template_id >", value, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("config_template_id >=", value, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdLessThan(Long value) {
            addCriterion("config_template_id <", value, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("config_template_id <=", value, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdIn(List<Long> values) {
            addCriterion("config_template_id in", values, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdNotIn(List<Long> values) {
            addCriterion("config_template_id not in", values, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdBetween(Long value1, Long value2) {
            addCriterion("config_template_id between", value1, value2, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andConfigTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("config_template_id not between", value1, value2, "configTemplateId");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeIsNull() {
            addCriterion("template_type is null");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeIsNotNull() {
            addCriterion("template_type is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeEqualTo(Integer value) {
            addCriterion("template_type =", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeNotEqualTo(Integer value) {
            addCriterion("template_type <>", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeGreaterThan(Integer value) {
            addCriterion("template_type >", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_type >=", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeLessThan(Integer value) {
            addCriterion("template_type <", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("template_type <=", value, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeIn(List<Integer> values) {
            addCriterion("template_type in", values, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeNotIn(List<Integer> values) {
            addCriterion("template_type not in", values, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeBetween(Integer value1, Integer value2) {
            addCriterion("template_type between", value1, value2, "templateType");
            return (Criteria) this;
        }

        public Criteria andTemplateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("template_type not between", value1, value2, "templateType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeIsNull() {
            addCriterion("dimension_type is null");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeIsNotNull() {
            addCriterion("dimension_type is not null");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeEqualTo(Integer value) {
            addCriterion("dimension_type =", value, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeNotEqualTo(Integer value) {
            addCriterion("dimension_type <>", value, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeGreaterThan(Integer value) {
            addCriterion("dimension_type >", value, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("dimension_type >=", value, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeLessThan(Integer value) {
            addCriterion("dimension_type <", value, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeLessThanOrEqualTo(Integer value) {
            addCriterion("dimension_type <=", value, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeIn(List<Integer> values) {
            addCriterion("dimension_type in", values, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeNotIn(List<Integer> values) {
            addCriterion("dimension_type not in", values, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeBetween(Integer value1, Integer value2) {
            addCriterion("dimension_type between", value1, value2, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("dimension_type not between", value1, value2, "dimensionType");
            return (Criteria) this;
        }

        public Criteria andDimensionIdIsNull() {
            addCriterion("dimension_id is null");
            return (Criteria) this;
        }

        public Criteria andDimensionIdIsNotNull() {
            addCriterion("dimension_id is not null");
            return (Criteria) this;
        }

        public Criteria andDimensionIdEqualTo(String value) {
            addCriterion("dimension_id =", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdNotEqualTo(String value) {
            addCriterion("dimension_id <>", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdGreaterThan(String value) {
            addCriterion("dimension_id >", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdGreaterThanOrEqualTo(String value) {
            addCriterion("dimension_id >=", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdLessThan(String value) {
            addCriterion("dimension_id <", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdLessThanOrEqualTo(String value) {
            addCriterion("dimension_id <=", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdLike(String value) {
            addCriterion("dimension_id like", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdNotLike(String value) {
            addCriterion("dimension_id not like", value, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdIn(List<String> values) {
            addCriterion("dimension_id in", values, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdNotIn(List<String> values) {
            addCriterion("dimension_id not in", values, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdBetween(String value1, String value2) {
            addCriterion("dimension_id between", value1, value2, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andDimensionIdNotBetween(String value1, String value2) {
            addCriterion("dimension_id not between", value1, value2, "dimensionId");
            return (Criteria) this;
        }

        public Criteria andConfigContentIsNull() {
            addCriterion("config_content is null");
            return (Criteria) this;
        }

        public Criteria andConfigContentIsNotNull() {
            addCriterion("config_content is not null");
            return (Criteria) this;
        }

        public Criteria andConfigContentEqualTo(String value) {
            addCriterion("config_content =", value, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentNotEqualTo(String value) {
            addCriterion("config_content <>", value, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentGreaterThan(String value) {
            addCriterion("config_content >", value, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentGreaterThanOrEqualTo(String value) {
            addCriterion("config_content >=", value, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentLessThan(String value) {
            addCriterion("config_content <", value, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentLessThanOrEqualTo(String value) {
            addCriterion("config_content <=", value, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentIn(List<String> values) {
            addCriterion("config_content in", values, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentNotIn(List<String> values) {
            addCriterion("config_content not in", values, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentBetween(String value1, String value2) {
            addCriterion("config_content between", value1, value2, "configContent");
            return (Criteria) this;
        }

        public Criteria andConfigContentNotBetween(String value1, String value2) {
            addCriterion("config_content not between", value1, value2, "configContent");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(LocalDateTime value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(LocalDateTime value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(LocalDateTime value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(LocalDateTime value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<LocalDateTime> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<LocalDateTime> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(LocalDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(LocalDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(LocalDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(LocalDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(LocalDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<LocalDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<LocalDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(LocalDateTime value1, LocalDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}