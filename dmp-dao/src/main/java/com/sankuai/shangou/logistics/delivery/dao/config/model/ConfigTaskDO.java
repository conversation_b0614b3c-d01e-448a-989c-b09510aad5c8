package com.sankuai.shangou.logistics.delivery.dao.config.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 配置任务表
 * config_task
 */
@Data
public class ConfigTaskDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 配置模版id
     */
    private Long configTemplateId;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 同步的渠道。多个渠道因为逗号隔开
     */
    private String channelType;

    /**
     * 状态: 0-初始化 1-处理中 2-已完成 3-处理失败
     */
    private Integer status;

    /**
     * 配置详情
     */
    private String configDetail;

    /**
     * 成功数量
     */
    private Integer successNum;

    /**
     * 失败数量
     */
    private Integer failNum;

    /**
     * 任务总数
     */
    private Integer totalNum;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 完成时间
     */
    private Date finishedAt;

    /**
     * 操作人牵牛花账号
     */
    private String operator;

    /**
     * 操作人姓名
     */
    private String operatorName;

    private static final long serialVersionUID = 1L;
}