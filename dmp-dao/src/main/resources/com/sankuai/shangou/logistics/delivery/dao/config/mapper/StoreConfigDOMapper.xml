<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreConfigDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="address" jdbcType="CHAR" property="address" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="delivery_strategy" jdbcType="TINYINT" property="deliveryStrategy" />
    <result column="delivery_strategy_config" jdbcType="CHAR" property="deliveryStrategyConfig" />
    <result column="delivery_launch_point" jdbcType="TINYINT" property="deliveryLaunchPoint" />
    <result column="delivery_launch_delay_minutes" jdbcType="INTEGER" property="deliveryLaunchDelayMinutes" />
    <result column="booking_order_delivery_launch_point" jdbcType="TINYINT" property="bookingOrderDeliveryLaunchPoint" />
    <result column="booking_order_delivery_launch_minutes" jdbcType="INTEGER" property="bookingOrderDeliveryLaunchMinutes" />
    <result column="order_platform_delivery_config" jdbcType="CHAR" property="orderPlatformDeliveryConfig" />
    <result column="enabled" jdbcType="TINYINT" property="enabled" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_auto_launch" jdbcType="TINYINT" property="isAutoLaunch" />
    <result column="delivery_launch_rule" jdbcType="TINYINT" property="deliveryLaunchRule" />
    <result column="open_aggr_platform" jdbcType="TINYINT" property="openAggrPlatform" />
    <result column="city_code" jdbcType="INTEGER" property="cityCode" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="last_platform_type" jdbcType="INTEGER" property="lastPlatformType" />
    <result column="is_show_item_number" jdbcType="TINYINT" property="isShowItemNumber" />
    <result column="self_assess_delivery_config" jdbcType="CHAR" property="selfAssessDeliveryConfig" />
    <result column="second_delivery_platform" jdbcType="VARCHAR" property="secondDeliveryPlatform" />
    <result column="turn_second_delivery_platform_condition" jdbcType="VARCHAR" property="turnSecondDeliveryPlatformCondition" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, address, contact_phone, delivery_strategy, delivery_strategy_config, 
    delivery_launch_point, delivery_launch_delay_minutes, booking_order_delivery_launch_point, 
    booking_order_delivery_launch_minutes, order_platform_delivery_config, enabled, create_time, 
    update_time, is_auto_launch, delivery_launch_rule, open_aggr_platform, city_code, 
    channel_type, last_platform_type, is_show_item_number, self_assess_delivery_config, 
    second_delivery_platform, turn_second_delivery_platform_condition
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from store_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from store_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from store_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample">
    delete from store_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO">
    insert into store_config (id, tenant_id, store_id, 
      address, contact_phone, delivery_strategy, 
      delivery_strategy_config, delivery_launch_point, 
      delivery_launch_delay_minutes, booking_order_delivery_launch_point, 
      booking_order_delivery_launch_minutes, order_platform_delivery_config, 
      enabled, create_time, update_time, 
      is_auto_launch, delivery_launch_rule, open_aggr_platform, 
      city_code, channel_type, last_platform_type, 
      is_show_item_number, self_assess_delivery_config, 
      second_delivery_platform, turn_second_delivery_platform_condition
      )
    values (#{id,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{address,jdbcType=CHAR}, #{contactPhone,jdbcType=VARCHAR}, #{deliveryStrategy,jdbcType=TINYINT}, 
      #{deliveryStrategyConfig,jdbcType=CHAR}, #{deliveryLaunchPoint,jdbcType=TINYINT}, 
      #{deliveryLaunchDelayMinutes,jdbcType=INTEGER}, #{bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT}, 
      #{bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER}, #{orderPlatformDeliveryConfig,jdbcType=CHAR}, 
      #{enabled,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isAutoLaunch,jdbcType=TINYINT}, #{deliveryLaunchRule,jdbcType=TINYINT}, #{openAggrPlatform,jdbcType=TINYINT}, 
      #{cityCode,jdbcType=INTEGER}, #{channelType,jdbcType=INTEGER}, #{lastPlatformType,jdbcType=INTEGER}, 
      #{isShowItemNumber,jdbcType=TINYINT}, #{selfAssessDeliveryConfig,jdbcType=CHAR}, 
      #{secondDeliveryPlatform,jdbcType=VARCHAR}, #{turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO">
    insert into store_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="deliveryStrategy != null">
        delivery_strategy,
      </if>
      <if test="deliveryStrategyConfig != null">
        delivery_strategy_config,
      </if>
      <if test="deliveryLaunchPoint != null">
        delivery_launch_point,
      </if>
      <if test="deliveryLaunchDelayMinutes != null">
        delivery_launch_delay_minutes,
      </if>
      <if test="bookingOrderDeliveryLaunchPoint != null">
        booking_order_delivery_launch_point,
      </if>
      <if test="bookingOrderDeliveryLaunchMinutes != null">
        booking_order_delivery_launch_minutes,
      </if>
      <if test="orderPlatformDeliveryConfig != null">
        order_platform_delivery_config,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isAutoLaunch != null">
        is_auto_launch,
      </if>
      <if test="deliveryLaunchRule != null">
        delivery_launch_rule,
      </if>
      <if test="openAggrPlatform != null">
        open_aggr_platform,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="channelType != null">
        channel_type,
      </if>
      <if test="lastPlatformType != null">
        last_platform_type,
      </if>
      <if test="isShowItemNumber != null">
        is_show_item_number,
      </if>
      <if test="selfAssessDeliveryConfig != null">
        self_assess_delivery_config,
      </if>
      <if test="secondDeliveryPlatform != null">
        second_delivery_platform,
      </if>
      <if test="turnSecondDeliveryPlatformCondition != null">
        turn_second_delivery_platform_condition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="address != null">
        #{address,jdbcType=CHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStrategy != null">
        #{deliveryStrategy,jdbcType=TINYINT},
      </if>
      <if test="deliveryStrategyConfig != null">
        #{deliveryStrategyConfig,jdbcType=CHAR},
      </if>
      <if test="deliveryLaunchPoint != null">
        #{deliveryLaunchPoint,jdbcType=TINYINT},
      </if>
      <if test="deliveryLaunchDelayMinutes != null">
        #{deliveryLaunchDelayMinutes,jdbcType=INTEGER},
      </if>
      <if test="bookingOrderDeliveryLaunchPoint != null">
        #{bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT},
      </if>
      <if test="bookingOrderDeliveryLaunchMinutes != null">
        #{bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER},
      </if>
      <if test="orderPlatformDeliveryConfig != null">
        #{orderPlatformDeliveryConfig,jdbcType=CHAR},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isAutoLaunch != null">
        #{isAutoLaunch,jdbcType=TINYINT},
      </if>
      <if test="deliveryLaunchRule != null">
        #{deliveryLaunchRule,jdbcType=TINYINT},
      </if>
      <if test="openAggrPlatform != null">
        #{openAggrPlatform,jdbcType=TINYINT},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="channelType != null">
        #{channelType,jdbcType=INTEGER},
      </if>
      <if test="lastPlatformType != null">
        #{lastPlatformType,jdbcType=INTEGER},
      </if>
      <if test="isShowItemNumber != null">
        #{isShowItemNumber,jdbcType=TINYINT},
      </if>
      <if test="selfAssessDeliveryConfig != null">
        #{selfAssessDeliveryConfig,jdbcType=CHAR},
      </if>
      <if test="secondDeliveryPlatform != null">
        #{secondDeliveryPlatform,jdbcType=VARCHAR},
      </if>
      <if test="turnSecondDeliveryPlatformCondition != null">
        #{turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample" resultType="java.lang.Long">
    select count(*) from store_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update store_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=CHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryStrategy != null">
        delivery_strategy = #{record.deliveryStrategy,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryStrategyConfig != null">
        delivery_strategy_config = #{record.deliveryStrategyConfig,jdbcType=CHAR},
      </if>
      <if test="record.deliveryLaunchPoint != null">
        delivery_launch_point = #{record.deliveryLaunchPoint,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryLaunchDelayMinutes != null">
        delivery_launch_delay_minutes = #{record.deliveryLaunchDelayMinutes,jdbcType=INTEGER},
      </if>
      <if test="record.bookingOrderDeliveryLaunchPoint != null">
        booking_order_delivery_launch_point = #{record.bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT},
      </if>
      <if test="record.bookingOrderDeliveryLaunchMinutes != null">
        booking_order_delivery_launch_minutes = #{record.bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER},
      </if>
      <if test="record.orderPlatformDeliveryConfig != null">
        order_platform_delivery_config = #{record.orderPlatformDeliveryConfig,jdbcType=CHAR},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isAutoLaunch != null">
        is_auto_launch = #{record.isAutoLaunch,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryLaunchRule != null">
        delivery_launch_rule = #{record.deliveryLaunchRule,jdbcType=TINYINT},
      </if>
      <if test="record.openAggrPlatform != null">
        open_aggr_platform = #{record.openAggrPlatform,jdbcType=TINYINT},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=INTEGER},
      </if>
      <if test="record.channelType != null">
        channel_type = #{record.channelType,jdbcType=INTEGER},
      </if>
      <if test="record.lastPlatformType != null">
        last_platform_type = #{record.lastPlatformType,jdbcType=INTEGER},
      </if>
      <if test="record.isShowItemNumber != null">
        is_show_item_number = #{record.isShowItemNumber,jdbcType=TINYINT},
      </if>
      <if test="record.selfAssessDeliveryConfig != null">
        self_assess_delivery_config = #{record.selfAssessDeliveryConfig,jdbcType=CHAR},
      </if>
      <if test="record.secondDeliveryPlatform != null">
        second_delivery_platform = #{record.secondDeliveryPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.turnSecondDeliveryPlatformCondition != null">
        turn_second_delivery_platform_condition = #{record.turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update store_config
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      address = #{record.address,jdbcType=CHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      delivery_strategy = #{record.deliveryStrategy,jdbcType=TINYINT},
      delivery_strategy_config = #{record.deliveryStrategyConfig,jdbcType=CHAR},
      delivery_launch_point = #{record.deliveryLaunchPoint,jdbcType=TINYINT},
      delivery_launch_delay_minutes = #{record.deliveryLaunchDelayMinutes,jdbcType=INTEGER},
      booking_order_delivery_launch_point = #{record.bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT},
      booking_order_delivery_launch_minutes = #{record.bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER},
      order_platform_delivery_config = #{record.orderPlatformDeliveryConfig,jdbcType=CHAR},
      enabled = #{record.enabled,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_auto_launch = #{record.isAutoLaunch,jdbcType=TINYINT},
      delivery_launch_rule = #{record.deliveryLaunchRule,jdbcType=TINYINT},
      open_aggr_platform = #{record.openAggrPlatform,jdbcType=TINYINT},
      city_code = #{record.cityCode,jdbcType=INTEGER},
      channel_type = #{record.channelType,jdbcType=INTEGER},
      last_platform_type = #{record.lastPlatformType,jdbcType=INTEGER},
      is_show_item_number = #{record.isShowItemNumber,jdbcType=TINYINT},
      self_assess_delivery_config = #{record.selfAssessDeliveryConfig,jdbcType=CHAR},
      second_delivery_platform = #{record.secondDeliveryPlatform,jdbcType=VARCHAR},
      turn_second_delivery_platform_condition = #{record.turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO">
    update store_config
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=CHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryStrategy != null">
        delivery_strategy = #{deliveryStrategy,jdbcType=TINYINT},
      </if>
      <if test="deliveryStrategyConfig != null">
        delivery_strategy_config = #{deliveryStrategyConfig,jdbcType=CHAR},
      </if>
      <if test="deliveryLaunchPoint != null">
        delivery_launch_point = #{deliveryLaunchPoint,jdbcType=TINYINT},
      </if>
      <if test="deliveryLaunchDelayMinutes != null">
        delivery_launch_delay_minutes = #{deliveryLaunchDelayMinutes,jdbcType=INTEGER},
      </if>
      <if test="bookingOrderDeliveryLaunchPoint != null">
        booking_order_delivery_launch_point = #{bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT},
      </if>
      <if test="bookingOrderDeliveryLaunchMinutes != null">
        booking_order_delivery_launch_minutes = #{bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER},
      </if>
      <if test="orderPlatformDeliveryConfig != null">
        order_platform_delivery_config = #{orderPlatformDeliveryConfig,jdbcType=CHAR},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isAutoLaunch != null">
        is_auto_launch = #{isAutoLaunch,jdbcType=TINYINT},
      </if>
      <if test="deliveryLaunchRule != null">
        delivery_launch_rule = #{deliveryLaunchRule,jdbcType=TINYINT},
      </if>
      <if test="openAggrPlatform != null">
        open_aggr_platform = #{openAggrPlatform,jdbcType=TINYINT},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="channelType != null">
        channel_type = #{channelType,jdbcType=INTEGER},
      </if>
      <if test="lastPlatformType != null">
        last_platform_type = #{lastPlatformType,jdbcType=INTEGER},
      </if>
      <if test="isShowItemNumber != null">
        is_show_item_number = #{isShowItemNumber,jdbcType=TINYINT},
      </if>
      <if test="selfAssessDeliveryConfig != null">
        self_assess_delivery_config = #{selfAssessDeliveryConfig,jdbcType=CHAR},
      </if>
      <if test="secondDeliveryPlatform != null">
        second_delivery_platform = #{secondDeliveryPlatform,jdbcType=VARCHAR},
      </if>
      <if test="turnSecondDeliveryPlatformCondition != null">
        turn_second_delivery_platform_condition = #{turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO">
    update store_config
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      address = #{address,jdbcType=CHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      delivery_strategy = #{deliveryStrategy,jdbcType=TINYINT},
      delivery_strategy_config = #{deliveryStrategyConfig,jdbcType=CHAR},
      delivery_launch_point = #{deliveryLaunchPoint,jdbcType=TINYINT},
      delivery_launch_delay_minutes = #{deliveryLaunchDelayMinutes,jdbcType=INTEGER},
      booking_order_delivery_launch_point = #{bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT},
      booking_order_delivery_launch_minutes = #{bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER},
      order_platform_delivery_config = #{orderPlatformDeliveryConfig,jdbcType=CHAR},
      enabled = #{enabled,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_auto_launch = #{isAutoLaunch,jdbcType=TINYINT},
      delivery_launch_rule = #{deliveryLaunchRule,jdbcType=TINYINT},
      open_aggr_platform = #{openAggrPlatform,jdbcType=TINYINT},
      city_code = #{cityCode,jdbcType=INTEGER},
      channel_type = #{channelType,jdbcType=INTEGER},
      last_platform_type = #{lastPlatformType,jdbcType=INTEGER},
      is_show_item_number = #{isShowItemNumber,jdbcType=TINYINT},
      self_assess_delivery_config = #{selfAssessDeliveryConfig,jdbcType=CHAR},
      second_delivery_platform = #{secondDeliveryPlatform,jdbcType=VARCHAR},
      turn_second_delivery_platform_condition = #{turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into store_config
    (id, tenant_id, store_id, address, contact_phone, delivery_strategy, delivery_strategy_config, 
      delivery_launch_point, delivery_launch_delay_minutes, booking_order_delivery_launch_point, 
      booking_order_delivery_launch_minutes, order_platform_delivery_config, enabled, 
      create_time, update_time, is_auto_launch, delivery_launch_rule, open_aggr_platform, 
      city_code, channel_type, last_platform_type, is_show_item_number, self_assess_delivery_config, 
      second_delivery_platform, turn_second_delivery_platform_condition)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.tenantId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, 
        #{item.address,jdbcType=CHAR}, #{item.contactPhone,jdbcType=VARCHAR}, #{item.deliveryStrategy,jdbcType=TINYINT}, 
        #{item.deliveryStrategyConfig,jdbcType=CHAR}, #{item.deliveryLaunchPoint,jdbcType=TINYINT}, 
        #{item.deliveryLaunchDelayMinutes,jdbcType=INTEGER}, #{item.bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT}, 
        #{item.bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER}, #{item.orderPlatformDeliveryConfig,jdbcType=CHAR}, 
        #{item.enabled,jdbcType=TINYINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isAutoLaunch,jdbcType=TINYINT}, #{item.deliveryLaunchRule,jdbcType=TINYINT}, 
        #{item.openAggrPlatform,jdbcType=TINYINT}, #{item.cityCode,jdbcType=INTEGER}, #{item.channelType,jdbcType=INTEGER}, 
        #{item.lastPlatformType,jdbcType=INTEGER}, #{item.isShowItemNumber,jdbcType=TINYINT}, 
        #{item.selfAssessDeliveryConfig,jdbcType=CHAR}, #{item.secondDeliveryPlatform,jdbcType=VARCHAR}, 
        #{item.turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>