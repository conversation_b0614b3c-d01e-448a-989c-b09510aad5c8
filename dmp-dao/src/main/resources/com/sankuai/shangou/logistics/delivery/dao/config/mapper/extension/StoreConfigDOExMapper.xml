<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.StoreConfigDOExMapper">

    <update id="batchUpdateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO">
        <foreach collection="list" item="item" separator=";">
            update store_config
            set
                address = #{item.address,jdbcType=CHAR},
                contact_phone = #{item.contactPhone,jdbcType=VARCHAR},
                delivery_strategy = #{item.deliveryStrategy,jdbcType=TINYINT},
                delivery_strategy_config = #{item.deliveryStrategyConfig,jdbcType=CHAR},
                delivery_launch_point = #{item.deliveryLaunchPoint,jdbcType=TINYINT},
                delivery_launch_delay_minutes = #{item.deliveryLaunchDelayMinutes,jdbcType=INTEGER},
                booking_order_delivery_launch_point = #{item.bookingOrderDeliveryLaunchPoint,jdbcType=TINYINT},
                booking_order_delivery_launch_minutes = #{item.bookingOrderDeliveryLaunchMinutes,jdbcType=INTEGER},
                order_platform_delivery_config = #{item.orderPlatformDeliveryConfig,jdbcType=CHAR},
                enabled = #{item.enabled,jdbcType=TINYINT},
                update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                is_auto_launch = #{item.isAutoLaunch,jdbcType=TINYINT},
                delivery_launch_rule = #{item.deliveryLaunchRule,jdbcType=TINYINT},
                open_aggr_platform = #{item.openAggrPlatform,jdbcType=TINYINT},
                city_code = #{item.cityCode,jdbcType=INTEGER},
                last_platform_type = #{item.lastPlatformType,jdbcType=INTEGER},
                is_show_item_number = #{item.isShowItemNumber,jdbcType=TINYINT},
                self_assess_delivery_config = #{item.selfAssessDeliveryConfig,jdbcType=CHAR},
                second_delivery_platform = #{item.secondDeliveryPlatform,jdbcType=VARCHAR},
                turn_second_delivery_platform_condition = #{item.turnSecondDeliveryPlatformCondition,jdbcType=VARCHAR}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>