<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.StoreDimensionConfigDOExMapper">
    <update id="batchUpdateByPrimaryKey" parameterType="com.sankuai.shangou.logistics.delivery.dao.config.model.StoreDimensionConfigDO">
        <foreach collection="list" item="item" separator=";">
            update store_dimension_config
            set
                self_delivery_mode = #{item.selfDeliveryMode,jdbcType=TINYINT},
                internal_navigation_mode = #{item.internalNavigationMode,jdbcType=TINYINT},
                assess_time_config = #{item.assessTimeConfig,jdbcType=CHAR},
                delivery_complete_mode = #{item.deliveryCompleteMode,jdbcType=CHAR},
                rider_trans_roles = #{item.riderTransRoles,jdbcType=VARCHAR},
                completed_sort_mode = #{item.completedSortMode,jdbcType=TINYINT},
                delivery_remind_config = #{item.deliveryRemindConfig,jdbcType=CHAR},
                `enable` = #{item.enable,jdbcType=TINYINT},
                updated_at = #{item.updatedAt,jdbcType=TIMESTAMP}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>