<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.logistics.delivery.dao.config.mapper.extension.ConfigTemplateDOExMapper">

    <select id="queryBatchTaskListIncludeConfigType" resultMap="com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTemplateDOMapper.BaseResultMap">
        select distinct ct.id, ct.name, ct.created_at, ct.create_by, ct.create_name
        from config_template ct
        inner join config_template_item cti on ct.id = cti.config_template_id
        where ct.tenant_id = #{tenantId}
        and ct.dimension_type = #{dimensionType}
        and ct.status = 1
        and ct.is_delete = 0
        and ct.created_at between #{beginTime} and #{endTime}
        <if test="dimensionType == 2">
            and ct.dimension_id = #{dimensionId}
        </if>
        <if test="taskName != null">
            and ct.name like '%' || #{taskName} || '%'
        </if>
        <if test="operator != null">
            and (ct.create_by like '%' || #{operator} || '%' or ct.create_name like '%' || #{operator} || '%')
        </if>
        <if test="templateTypeList != null">
            and cti.template_type in
            <foreach collection="templateTypeList" item="templateType" open="(" separator="," close=")">
                #{templateType}
            </foreach>
        </if>
        order by ct.id desc
    </select>

    <select id="queryBatchTaskListExcludeConfigType" resultMap="com.sankuai.shangou.logistics.delivery.dao.config.mapper.ConfigTemplateDOMapper.BaseResultMap">
        select id, name, created_at, create_by, create_name
        from config_template
        where tenant_id = #{tenantId}
          and dimension_type = #{dimensionType}
          and status = 1
          and is_delete = 0
          and created_at between #{beginTime} and #{endTime}
        <if test="dimensionType == 2">
            and dimension_id = #{dimensionId}
        </if>
        <if test="taskName != null">
            and name like '%' || #{taskName} || '%'
        </if>
        <if test="operator != null">
            and (create_by like '%' || #{operator} || '%' or create_name like '%' || #{operator} || '%')
        </if>
        order by id desc
    </select>
</mapper>