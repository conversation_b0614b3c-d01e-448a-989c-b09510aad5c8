package com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryIsShowItemNumberEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryLaunchTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/24
 */
@Getter
@ToString
@AllArgsConstructor
public abstract class DeliveryPoi {

	@Setter
	private Long id;

	/**
	 * 赋能租户id
	 */
	private final Long tenantId;

	/**
	 * 赋能门店id
	 */
	private final Long storeId;

	/**
	 * 城市码,美团物理城市code
	 */
	@Setter
	private Integer cityCode;

	/**
	 * 门店联系方式
	 */
	@Setter
	private String contactPhone;

	/**
	 * 配送平台
	 */
	@Setter
	private DeliveryPlatformEnum deliveryPlatform;

	/**
	 * 配送发起时间点
	 */
	@Setter
	private DeliveryLaunchPoint deliveryLaunchPoint;

	/**
	 * 配送发起类型
	 * 自动发起/手动发起
	 */
	@Setter
	private DeliveryLaunchTypeEnum deliveryLaunchType;

	/**
	 * 渠道类型
	 */
	@Setter
	private Integer channelType;

	@Setter
	private DeliveryPlatformEnum lastDeliveryPlatform;

	@Setter
	private DeliveryIsShowItemNumberEnum deliveryIsShowItemNumberEnum;
	public DeliveryPoi(Long tenantId,
	                   Long storeId,
	                   Integer cityCode,
	                   String contactPhone,
	                   DeliveryPlatformEnum deliveryPlatform,
	                   DeliveryLaunchPoint deliveryLaunchPoint,
	                   DeliveryLaunchTypeEnum deliveryLaunchType,Integer channelType) {
		this.tenantId = tenantId;
		this.storeId = storeId;
		this.cityCode = cityCode;
		this.contactPhone = contactPhone;
		this.deliveryPlatform = deliveryPlatform;
		this.deliveryLaunchPoint = deliveryLaunchPoint;
		this.deliveryLaunchType = deliveryLaunchType;
		this.channelType = channelType;
	}

	public DeliveryPoi(Long id,
					   Long tenantId,
					   Long storeId,
					   Integer cityCode,
					   String contactPhone,
					   DeliveryPlatformEnum deliveryPlatform,
					   DeliveryLaunchPoint deliveryLaunchPoint,
					   DeliveryLaunchTypeEnum deliveryLaunchType,
					   Integer channelType,
					   DeliveryPlatformEnum lastDeliveryPlatform) {
		this.id = id;
		this.tenantId = tenantId;
		this.storeId = storeId;
		this.cityCode = cityCode;
		this.contactPhone = contactPhone;
		this.deliveryPlatform = deliveryPlatform;
		this.deliveryLaunchPoint = deliveryLaunchPoint;
		this.deliveryLaunchType = deliveryLaunchType;
		this.channelType = channelType;
		this.lastDeliveryPlatform = lastDeliveryPlatform;
	}
}
